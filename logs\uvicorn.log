INFO:     127.0.0.1:2971 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:2979 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类16
    完整分类数据结构: {'id': 'd388b82b-b7f8-493b-9b49-4d780a7f021a', 'series_id': '82b84b2d-63b4-4b3b-b3e0-9460f00a95bf', 'title': '分类16', 'description': '', 'price': 116.0, 'order_index': 1, 'created_at': '2025-07-08T05:04:03', 'updated_at': '2025-07-08T05:04:03', 'series_title': '系列161', 'series_published': True, 'video_count': 2, 'videos': [{'id': 'be062109-70e7-4dc0-86cf-c218746a5fd2', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': 'be296cf7-53a4-44c7-beae-247cbc24aa20'}, {'id': '7d4c6ad5-0f5a-49ea-a296-e4bd0ae971ab', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': 'be296cf7-53a4-44c7-beae-247cbc24aa20'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': 'be296cf7-53a4-44c7-beae-247cbc24aa20', 'purchase_uuid': 'd388b82b-b7f8-493b-9b49-4d780a7f021a', 'original_category_id': 'be296cf7-53a4-44c7-beae-247cbc24aa20'}
    🔑 提取的purchase_uuid: d388b82b-b7f8-493b-9b49-4d780a7f021a
    📄 category.get('id'): d388b82b-b7f8-493b-9b49-4d780a7f021a
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: be296cf7-53a4-44c7-beae-247cbc24aa20
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: be296cf7-53a4-44c7-beae-247cbc24aa20
    🏷️  id: d388b82b-b7f8-493b-9b49-4d780a7f021a
    🏷️  purchase_uuid: d388b82b-b7f8-493b-9b49-4d780a7f021a
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: be296cf7-53a4-44c7-beae-247cbc24aa20
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: be296cf7-53a4-44c7-beae-247cbc24aa20
    4️⃣ id (FALLBACK): d388b82b-b7f8-493b-9b49-4d780a7f021a
[FINAL_RESULT] 最终选择的category_id: be296cf7-53a4-44c7-beae-247cbc24aa20
[FINAL_RESULT] 购买UUID: d388b82b-b7f8-493b-9b49-4d780a7f021a
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: d388b82b-b7f8-493b-9b49-4d780a7f021a
[RETRY] UUID架构：插入分类购买记录 - 分类16 (购买UUID: d388b82b-b7f8-493b-9b49-4d780a7f021a, 分类ID: be296cf7-53a4-44c7-beae-247cbc24aa20, 价格: ¥116.0)
INFO:     127.0.0.1:2991 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3012 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3018 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5381 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:5392 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类15
    完整分类数据结构: {'id': '71b75a86-6ecc-470a-b5ef-62c0e5e485be', 'series_id': '8c295c01-7e0f-465e-ad1c-6647d24c0b15', 'title': '分类15', 'description': '', 'price': 115.0, 'order_index': 1, 'created_at': '2025-07-08T04:56:08', 'updated_at': '2025-07-08T04:56:08', 'series_title': '系列15', 'series_published': True, 'video_count': 2, 'videos': [{'id': 'a3b2101a-91c7-4473-837e-dbcc46b9249d', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '6c467ef3-c373-4977-b75c-ebeb60193e02'}, {'id': '78d763f8-b168-4ad5-a328-5eb363b317f9', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '6c467ef3-c373-4977-b75c-ebeb60193e02'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '6c467ef3-c373-4977-b75c-ebeb60193e02', 'purchase_uuid': '71b75a86-6ecc-470a-b5ef-62c0e5e485be', 'original_category_id': '6c467ef3-c373-4977-b75c-ebeb60193e02'}
    🔑 提取的purchase_uuid: 71b75a86-6ecc-470a-b5ef-62c0e5e485be
    📄 category.get('id'): 71b75a86-6ecc-470a-b5ef-62c0e5e485be
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: 6c467ef3-c373-4977-b75c-ebeb60193e02
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: 6c467ef3-c373-4977-b75c-ebeb60193e02
    🏷️  id: 71b75a86-6ecc-470a-b5ef-62c0e5e485be
    🏷️  purchase_uuid: 71b75a86-6ecc-470a-b5ef-62c0e5e485be
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: 6c467ef3-c373-4977-b75c-ebeb60193e02
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: 6c467ef3-c373-4977-b75c-ebeb60193e02
    4️⃣ id (FALLBACK): 71b75a86-6ecc-470a-b5ef-62c0e5e485be
[FINAL_RESULT] 最终选择的category_id: 6c467ef3-c373-4977-b75c-ebeb60193e02
[FINAL_RESULT] 购买UUID: 71b75a86-6ecc-470a-b5ef-62c0e5e485be
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: 71b75a86-6ecc-470a-b5ef-62c0e5e485be
[RETRY] UUID架构：插入分类购买记录 - 分类15 (购买UUID: 71b75a86-6ecc-470a-b5ef-62c0e5e485be, 分类ID: 6c467ef3-c373-4977-b75c-ebeb60193e02, 价格: ¥115.0)
INFO:     127.0.0.1:5405 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3968 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:3975 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类14
    完整分类数据结构: {'id': '17e8ddef-c67d-4513-b83d-f83ae7940023', 'series_id': 'b0cccc0b-c88f-4ed1-8162-52eb7064f528', 'title': '分类14', 'description': '', 'price': 114.0, 'order_index': 1, 'created_at': '2025-07-08T04:48:41', 'updated_at': '2025-07-08T04:48:41', 'series_title': '系列141', 'series_published': True, 'video_count': 2, 'videos': [{'id': 'c9265fbb-5d23-4558-a0f3-67879537ce72', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '4c266690-49d5-4612-9a67-4563314fcd4a'}, {'id': 'ebdb7d06-058b-4065-8fb5-73a7280ead1a', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '4c266690-49d5-4612-9a67-4563314fcd4a'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '4c266690-49d5-4612-9a67-4563314fcd4a', 'purchase_uuid': '17e8ddef-c67d-4513-b83d-f83ae7940023', 'original_category_id': '4c266690-49d5-4612-9a67-4563314fcd4a'}
    🔑 提取的purchase_uuid: 17e8ddef-c67d-4513-b83d-f83ae7940023
    📄 category.get('id'): 17e8ddef-c67d-4513-b83d-f83ae7940023
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: 4c266690-49d5-4612-9a67-4563314fcd4a
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: 4c266690-49d5-4612-9a67-4563314fcd4a
    🏷️  id: 17e8ddef-c67d-4513-b83d-f83ae7940023
    🏷️  purchase_uuid: 17e8ddef-c67d-4513-b83d-f83ae7940023
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: 4c266690-49d5-4612-9a67-4563314fcd4a
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: 4c266690-49d5-4612-9a67-4563314fcd4a
    4️⃣ id (FALLBACK): 17e8ddef-c67d-4513-b83d-f83ae7940023
[FINAL_RESULT] 最终选择的category_id: 4c266690-49d5-4612-9a67-4563314fcd4a
[FINAL_RESULT] 购买UUID: 17e8ddef-c67d-4513-b83d-f83ae7940023
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: 17e8ddef-c67d-4513-b83d-f83ae7940023
[RETRY] UUID架构：插入分类购买记录 - 分类14 (购买UUID: 17e8ddef-c67d-4513-b83d-f83ae7940023, 分类ID: 4c266690-49d5-4612-9a67-4563314fcd4a, 价格: ¥114.0)
INFO:     127.0.0.1:3990 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:47470 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:6374 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:6382 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类13
    完整分类数据结构: {'id': '7d5a614d-36f6-432f-8891-7e90805f7050', 'series_id': '8c03aa2c-1c9c-48c1-9140-8e9390df53e8', 'title': '分类13', 'description': '', 'price': 113.0, 'order_index': 1, 'created_at': '2025-07-08T04:44:27', 'updated_at': '2025-07-08T04:44:27', 'series_title': '系列13', 'series_published': True, 'video_count': 2, 'videos': [{'id': '22d21648-9a3b-43d1-be94-8d8e7a221c86', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '6c109c61-5cbf-4367-8b17-aa724d5a3549'}, {'id': 'ef4d1e72-b902-4efd-b01f-6d6523945768', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '6c109c61-5cbf-4367-8b17-aa724d5a3549'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '6c109c61-5cbf-4367-8b17-aa724d5a3549', 'purchase_uuid': '7d5a614d-36f6-432f-8891-7e90805f7050', 'original_category_id': '6c109c61-5cbf-4367-8b17-aa724d5a3549'}
    🔑 提取的purchase_uuid: 7d5a614d-36f6-432f-8891-7e90805f7050
    📄 category.get('id'): 7d5a614d-36f6-432f-8891-7e90805f7050
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: 6c109c61-5cbf-4367-8b17-aa724d5a3549
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: 6c109c61-5cbf-4367-8b17-aa724d5a3549
    🏷️  id: 7d5a614d-36f6-432f-8891-7e90805f7050
    🏷️  purchase_uuid: 7d5a614d-36f6-432f-8891-7e90805f7050
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: 6c109c61-5cbf-4367-8b17-aa724d5a3549
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: 6c109c61-5cbf-4367-8b17-aa724d5a3549
    4️⃣ id (FALLBACK): 7d5a614d-36f6-432f-8891-7e90805f7050
[FINAL_RESULT] 最终选择的category_id: 6c109c61-5cbf-4367-8b17-aa724d5a3549
[FINAL_RESULT] 购买UUID: 7d5a614d-36f6-432f-8891-7e90805f7050
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: 7d5a614d-36f6-432f-8891-7e90805f7050
[RETRY] UUID架构：插入分类购买记录 - 分类13 (购买UUID: 7d5a614d-36f6-432f-8891-7e90805f7050, 分类ID: 6c109c61-5cbf-4367-8b17-aa724d5a3549, 价格: ¥113.0)
INFO:     127.0.0.1:6392 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:47496 - "GET /docs HTTP/1.1" 200 OK
INFO:     127.0.0.1:8047 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:8064 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:8075 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类12
    完整分类数据结构: {'id': 'c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e', 'series_id': '78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e', 'title': '分类12', 'description': '', 'price': 112.0, 'order_index': 1, 'created_at': '2025-07-08T04:31:46', 'updated_at': '2025-07-08T04:31:46', 'series_title': '系列12', 'series_published': True, 'video_count': 2, 'videos': [{'id': '4fcbe28c-a7a3-4d1e-bc62-d8d018eda1ff', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b'}, {'id': '37d2276f-9996-45be-a929-8e56699bd7fe', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b', 'purchase_uuid': 'c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e', 'original_category_id': '54c5606d-b241-419a-ada8-10df092d392b'}
    🔑 提取的purchase_uuid: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
    📄 category.get('id'): c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: 54c5606d-b241-419a-ada8-10df092d392b
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: 54c5606d-b241-419a-ada8-10df092d392b
    🏷️  id: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
    🏷️  purchase_uuid: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: 54c5606d-b241-419a-ada8-10df092d392b
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: 54c5606d-b241-419a-ada8-10df092d392b
    4️⃣ id (FALLBACK): c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
[FINAL_RESULT] 最终选择的category_id: 54c5606d-b241-419a-ada8-10df092d392b
[FINAL_RESULT] 购买UUID: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
[RETRY] UUID架构：插入分类购买记录 - 分类12 (购买UUID: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e, 分类ID: 54c5606d-b241-419a-ada8-10df092d392b, 价格: ¥112.0)
INFO:     127.0.0.1:8089 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
