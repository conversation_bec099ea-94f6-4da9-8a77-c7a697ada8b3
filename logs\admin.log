[2025-07-24  6:43:01.58] === NEW ADMIN SESSION START === 
✅ 数据库管理器初始化完成
✅ UUID生成器初始化完成 - 无需映射机制
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ 主窗口乐观更新功能初始化成功
🚀 同步工作线程启动
🚀 CRUD工作线程启动
✅ 内容区域清理完成
🚀 开始初始化课程数据...
📡 API模式: True (强制使用服务器数据，确保数据一致性)
✅ 新的数据同步管理器初始化成功
🔄 异步同步后台线程已启动
✅ CourseService API导入成功: API_AVAILABLE = True
🔧 CourseService初始化:
   use_api参数: True
   API_AVAILABLE: True
   最终use_api: True
   use_cache: True
✅ 同步管理器API客户端已设置
🚀 开始一次性加载所有数据到内存...
📂 获取所有分类数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有分类完成: 46 个
📹 获取所有视频数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有视频完成: 67 个
🔍 预加载用户详情进度: 1/6
✅ 用户详情数据预加载完成: 6 个用户
📊 购买记录预加载统计: 6/6 个用户成功
🎉 所有数据一次性加载完成！
📊 数据统计: 系列=52, 分类=46, 视频=67, 用户=6
✅ 所有数据已一次性加载到内存
🚫 后续操作将完全使用内存数据，禁止网络请求
📊 内存数据统计: 系列=52, 分类=46, 视频=67
🎉 数据加载完成
🔍 检查全局数据管理器状态: loaded=True
📊 更新用户菜单数据: 找到 6 个用户
✅ 用户菜单数据更新完成
📊 概览数据更新成功
🔍 检查全局数据管理器状态: loaded=True
📊 更新用户菜单数据: 找到 6 个用户
✅ 用户菜单数据更新完成
✅ 数据库连接成功: localhost:3306
📊 概览数据更新成功
✅ 主窗口乐观更新信号连接成功
✅ 使用Qt原生事件循环启动
🚀 开始预加载课程管理页面...
✅ CourseService API导入成功: API_AVAILABLE = True
✅ 配置文件加载成功: 2 项配置
🔧 CourseService初始化:
   use_api参数: True
   API_AVAILABLE: True
   最终use_api: True
   use_cache: True
✅ 同步管理器API客户端已设置
🔧 CourseManagementWindow: API模式=True (来自配置文件)
✅ 信号管理器初始化完成
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ UUID架构已启用，无需依赖等待机制
✅ Qt原生乐观更新功能初始化成功
🚀 同步工作线程启动
🚀 CRUD工作线程启动
✅ UUID架构已启用，无需依赖等待机制
🚀 开始初始化课程数据...
📍 当前标签页索引: 0
📹 初始化视频管理数据...
✅ 视频数据已初始化: 67 个
📱 状态: ✅ 课程数据加载完成
✅ 课程管理初始化完成
✅ 课程管理页面预加载完成
🔄 更新筛选器...
📚 获取系列数据更新视频筛选器...
📚 从内存获取系列列表（无网络请求）
📚 使用预加载的系列数据填充分类筛选下拉框...
🔍 下拉框默认选中: 全部系列, 值: None
✅ 系列筛选下拉框填充完成: 52 个系列
✅ 筛选器更新完成
✅ 内容区域清理完成
🔧 用户详情页面：配置文件路径=D:\01-shuimu_01\shuimu-admin\config.ini
🔧 用户详情页面：配置文件中use_api='true' -> True
🔍 用户详情窗口初始化：user_id = 'test-api-001'
🔔 [调试] 购买添加通知信号已连接到_on_purchase_added方法
✅ 从缓存获取用户 test-api-001 购买记录: 35 条
🔔 [调试] add_purchase_record方法开始执行
✅ [删除事件监听] 已为用户 test-api-001 设置删除事件监听器
✅ 从缓存获取用户 test-api-001 购买记录: 35 条
🔄 [异步刷新回调] 缓存刷新结果: 成功
📊 [筛选结果] 共52个系列，筛选出6个可购买系列
📋 [首项预览] 第一个可购买系列：系列12 (¥112.00)
📊 [系列加载] 共52个系列，筛选出6个可购买，首项: 💰 系列12 (¥112.00)
📊 [缓存刷新] 已购买35项，系列选项6个
🔄 系列选择变化: 系列12 (ID: 78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e)
✅ 分类数据加载成功: 1 个分类
[DEBUG] 系列购买分析 - 系列ID: 78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e
[DEBUG] 可购买分类数量: 1
[DEBUG] 分类1: 分类12 (ID: 54c5606d-b241-419a-ada8-10df092d392b)
🎯 生成UUID: purchase -> c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e (分类1: 分类12)
🎯 UUID架构：使用购买对话框生成的UUID: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e - 分类: 分类12
🔑 UUID架构修复：购买记录UUID=c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e, 分类ID=54c5606d-b241-419a-ada8-10df092d392b - 分类12
🚀 [第1层] 开始UI更新，当前表格行数=35
🎯 购买整个系列：系列12，包含 1 个分类
🔍 调试分类数据: {'id': 'c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e', 'series_id': '78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e', 'title': '分类12', 'description': '', 'price': 112.0, 'order_index': 1, 'created_at': '2025-07-08T04:31:46', 'updated_at': '2025-07-08T04:31:46', 'series_title': '系列12', 'series_published': True, 'video_count': 2, 'videos': [{'id': '4fcbe28c-a7a3-4d1e-bc62-d8d018eda1ff', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b'}, {'id': '37d2276f-9996-45be-a929-8e56699bd7fe', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '54c5606d-b241-419a-ada8-10df092d392b', 'purchase_uuid': 'c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e', 'original_category_id': '54c5606d-b241-419a-ada8-10df092d392b'}
🔍 数据流调试: 真实分类ID=54c5606d-b241-419a-ada8-10df092d392b, 购买UUID=c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
  📦 添加分类购买记录：分类12 (UUID: c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e)
🔍 分类 54c5606d-b241-419a-ada8-10df092d392b 视频数量: 2
✅ 调试：表格数据填充完成，新记录已添加到顶部第0行，UUID=c8e81be1-2c3f-4cd4-b6ed-87ffd7cd4e0e
✅ 调试：表格强制刷新完成，已选中新记录
✅ 系列购买完成：已为 1 个分类创建购买记录
✅ [第1层] UI更新成功：购买记录已添加到表格，新行数=36
🔔 [调试] _start_async_purchase_process开始执行
🔔 [调试] 已连接purchase_completed信号到_on_async_purchase_completed
🔔 [调试] 已保存_current_purchase_data: series_all_categories
🔔 [调试] 已启动异步购买处理
🚀 开始四层同步购买记录添加: user_id=test-api-001
🚀 第2层API：开始API购买记录添加
✅ 第2层API：购买记录已成功提交到服务器
🚀 第3层缓存：开始更新全局缓存中的购买记录
✅ 第3层缓存：购买记录缓存已更新
🔔 [调试] ThreadPoolPurchaseManager: 准备发送purchase_completed信号(True)
🔔 [调试] _on_async_purchase_completed被调用: success=True, message=购买记录添加成功
✅ [异步回调] 购买流程完成: 购买记录添加成功
🧹 [待同步状态] 清理完成
🔔 准备发送购买添加通知: user_id=test-api-001, purchase_data=series_all_categories
📢 收到购买添加通知: test-api-001
🔄 开始级联更新所有相关标签页...
📋 已清除用户进度缓存
📋 已清除用户收藏缓存
📋 已清除用户设备缓存
🔄 刷新学习进度数据...
✅ 学习进度数据已刷新
🔄 刷新收藏管理数据...
✅ 收藏管理数据已刷新
🔄 刷新设备缓存数据...
✅ 设备缓存数据已刷新
✅ 级联更新完成：所有相关标签页已刷新
✅ 已发送购买添加通知: test-api-001
🔔 [调试] ThreadPoolPurchaseManager: purchase_completed信号已发送
