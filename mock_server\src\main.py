from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
import time
from .api import auth, series, categories, videos, payments, users, share, analytics, search, playlist, products, cache, users_crud, admin_videos, admin_users, user_data, admin_database, admin_categories, admin_series
from .utils.log_manager import global_log_manager, log_info, log_error

app = FastAPI(
    title="Shuimu Video Course API",
    description="""
    # 水幕视频课程API服务 v2.0

    ## 🚀 RESTful架构 - 版本化API

    本API采用版本化设计，确保向后兼容和稳定性。

    ### 📋 API版本说明
    - **v1**: 当前稳定版本，推荐使用
    - 所有端点均采用 `/api/v1/` 或 `/api/admin/v1/` 前缀

    ### 🔗 主要功能模块
    - **用户管理**: 用户注册、登录、资料管理
    - **内容管理**: 系列、分类、视频的CRUD操作
    - **学习数据**: 观看进度、收藏、缓存管理
    - **管理功能**: 后台管理、数据统计、系统配置

    ### 🛡️ 认证说明
    - 使用 `X-User-Id` 和 `X-Is-Admin` 请求头进行身份验证
    - 管理端API需要管理员权限

    ### 📖 使用指南
    1. 选择对应的API版本（推荐v1）
    2. 根据功能模块查找相应端点
    3. 参考示例代码进行集成
    """,
    version="2.0.0"
)

# ==================== 日志系统初始化 ====================

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化日志系统"""
    # 🔧 确保日志系统初始化并强制写入启动日志
    import time
    time.sleep(0.1)  # 短暂延迟确保日志系统完全初始化
    log_info("🚀 Shuimu Video Course API Server Starting...")
    log_info(f"📚 API Version: 2.0.0")
    log_info(f"🔗 Documentation: http://localhost:8000/docs")
    global_log_manager.log_file.flush() if global_log_manager.log_file else None

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理日志系统"""
    log_info("🛑 Shuimu Video Course API Server Shutting Down...")
    global_log_manager.close()

@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有HTTP请求和响应"""
    start_time = time.time()
    
    # 记录请求
    client_ip = request.client.host if request.client else "unknown"
    log_info(f"📥 {request.method} {request.url.path} - Client: {client_ip}")
    
    # 处理请求
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # 记录响应
        log_info(f"📤 {request.method} {request.url.path} - Status: {response.status_code} - Time: {process_time:.3f}s")
        
        return response
    except Exception as e:
        process_time = time.time() - start_time
        log_error(f"❌ {request.method} {request.url.path} - Error: {str(e)} - Time: {process_time:.3f}s")
        raise

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# ==================== API路由配置 ====================
# 🎯 版本化API架构 - 清晰简洁的结构

# ==================== 📱 用户端API v1 ====================
app.include_router(user_data.router, prefix="/api/v1", tags=["📊 User Data"])
app.include_router(users_crud.router, prefix="/api/v1", tags=["👤 Users"])
app.include_router(auth.router, prefix="/api/v1", tags=["🔐 Authentication"])
app.include_router(series.router, prefix="/api/v1", tags=["📚 Series"])
app.include_router(categories.router, prefix="/api/v1", tags=["📂 Categories"])
app.include_router(videos.router, prefix="/api/v1", tags=["🎥 Videos"])
app.include_router(payments.router, prefix="/api/v1", tags=["💳 Payments"])
app.include_router(users.router, prefix="/api/v1", tags=["👥 User Profiles"])
app.include_router(share.router, prefix="/api/v1", tags=["🔗 Share"])
app.include_router(analytics.router, prefix="/api/v1", tags=["📈 Analytics"])
app.include_router(search.router, prefix="/api/v1", tags=["🔍 Search"])
app.include_router(playlist.router, prefix="/api/v1", tags=["📋 Playlist"])
app.include_router(products.router, prefix="/api/v1", tags=["🛒 Products"])
app.include_router(cache.router, prefix="/api/v1", tags=["💾 Cache"])

# ==================== 🛠️ 管理端API v1 ====================
app.include_router(admin_series.router, prefix="/api/admin/v1", tags=["🔧 Admin: Series"])
app.include_router(admin_categories.router, prefix="/api/admin/v1", tags=["🔧 Admin: Categories"])
app.include_router(admin_videos.router, prefix="/api/admin/v1", tags=["🔧 Admin: Videos"])
app.include_router(admin_users.router, prefix="/api/admin/v1", tags=["🔧 Admin: Users"])
app.include_router(admin_database.router, prefix="/api/admin/v1", tags=["🔧 Admin: Database"])

@app.get("/", tags=["🏠 Root"])
def read_root():
    return {
        "message": "Welcome to the Shuimu Video Course API!",
        "version": "2.0.0",
        "api_version": "v1",
        "updated": "2025-07-12",
        "status": "is_active",
        "documentation": "/docs",
        "health_check": "/api/health",
        "features": [
            "RESTful API Design",
            "Version Control",
            "User Management",
            "Content Management",
            "Learning Progress Tracking",
            "Admin Dashboard"
        ]
    }

@app.get("/api/health", tags=["🏥 Health"])
def health_check():
    """健康检查端点 - 用于确认服务状态"""
    return {
        "status": "healthy",
        "created_at": "2025-07-12T09:00:00Z",
        "version": "2.0.0",
        "api_version": "v1",
        "database": "connected",
        "services": {
            "user_service": "is_active",
            "content_service": "is_active",
            "admin_service": "is_active"
        }
    }

@app.get("/api/version", tags=["ℹ️ Info"])
def get_api_version():
    """获取API版本信息"""
    return {
        "current_version": "v1",
        "available_versions": ["v1"],
        "deprecated_versions": [],
        "latest_update": "2025-07-12",
        "breaking_changes": [],
        "migration_guide": "/docs#migration"
    }

@app.get("/api/health", tags=["Health"])
def health_check():
    """健康检查端点 - 用于确认服务状态"""
    from datetime import datetime
    try:
        # 简单的数据库连接测试
        from .database.mysql_manager import mysql_manager
        mysql_manager.execute_query("SELECT 1 as test;")
        
        return {
            "status": "healthy",
            "created_at": datetime.now().isoformat(),
            "version": "2.0.0",
            "database": "connected",
            "services": {
                "api": "running",
                "database": "connected",
                "admin_endpoints": "available"
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy", 
            "created_at": datetime.now().isoformat(),
            "error": str(e),
            "database": "disconnected"
        }

@app.get("/api/test/sync-check", tags=["Test"])
def sync_check():
    """检查代码同步状态"""
    return {
        "status": "synced",
        "created_at": "2025-07-01T07:00:00Z",
        "message": "8000端口代码已同步 - RESTful修复已应用",
        "new_endpoints_available": True,
        "restful_endpoints_active": True
    }

@app.get("/api/test/restful-check", tags=["Test"])
def restful_check():
    """检查RESTful端点状态"""
    return {
        "new_payment_endpoint": "/api/payments",
        "new_share_endpoint": "/api/shares", 
        "new_cache_endpoint": "/api/cache/config (PUT)",
        "status": "应该可用",
        "created_at": "2025-07-01T07:00:00Z"
    }

# To run this server:
# 1. Make sure you have fastapi and uvicorn installed:
#    pip install fastapi "uvicorn[standard]"
# 2. Navigate to the `01-shuimu_01/mock_server` directory in your terminal.
# 3. Run the server:
#    uvicorn src.main:app --reload
# The server will be available at http://localhost:8000
