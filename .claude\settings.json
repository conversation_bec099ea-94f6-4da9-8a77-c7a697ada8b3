{"project": {"name": "水幕课程管理系统", "version": "1.0.0", "description": "基于三端架构的视频课程管理系统，采用四层同步乐观更新机制", "architecture": "三端架构 (Android App + PyQt6 管理端 + FastAPI 服务端)"}, "commands": {"check-system": {"description": "全面检查三端系统健康状态", "category": "诊断", "priority": "high", "estimated_time": "2-3分钟"}, "fix-sync": {"description": "自动修复四层同步架构问题", "category": "修复", "priority": "high", "estimated_time": "3-5分钟"}, "test-crud": {"description": "验证CRUD操作和乐观更新机制", "category": "测试", "priority": "medium", "estimated_time": "5-10分钟"}, "deploy-check": {"description": "部署前代码质量和配置检查", "category": "质量", "priority": "high", "estimated_time": "3-5分钟"}, "project-status": {"description": "项目状态总览和改进建议", "category": "分析", "priority": "medium", "estimated_time": "2-3分钟"}}, "workflows": {"daily-check": {"description": "每日开发前检查流程", "commands": ["check-system", "project-status"], "estimated_time": "5分钟"}, "pre-commit": {"description": "代码提交前检查流程", "commands": ["test-crud", "deploy-check"], "estimated_time": "10分钟"}, "troubleshooting": {"description": "问题排查和修复流程", "commands": ["check-system", "fix-sync", "test-crud"], "estimated_time": "15分钟"}, "release-prep": {"description": "发布准备检查流程", "commands": ["check-system", "test-crud", "deploy-check", "project-status"], "estimated_time": "20分钟"}}, "context": {"key_files": ["CLAUDE.md", "DOCS/四层同步乐观更新规则_v4.mdc", "shared_config.ini", "mock_server/src/main.py", "shuimu-admin/src/main.py", "app/build.gradle.kts"], "architecture_patterns": ["四层同步乐观更新", "UUID客户端生成架构", "Qt信号槽机制", "级联操作管理", "跨表格联动更新"], "critical_concepts": ["立即UI更新", "异步API调用", "失败回滚机制", "数据一致性保证", "并发操作处理"]}, "quality_gates": {"code_quality": {"syntax_errors": 0, "style_violations": 10, "type_errors": 0, "security_issues": 0}, "test_coverage": {"unit_tests": 80, "integration_tests": 90, "core_functions": 95}, "performance": {"ui_response_time": 50, "api_response_time": 200, "cascade_operation_time": 500, "batch_operation_time": 2000}, "architecture": {"sync_layer_health": 100, "uuid_consistency": 100, "signal_slot_integrity": 100, "cascade_operation_success": 95}}, "monitoring": {"health_check_interval": "daily", "performance_baseline": {"api_response_avg": 150, "ui_update_avg": 30, "memory_usage_mb": 200, "cpu_usage_percent": 15}, "alert_thresholds": {"api_response_max": 1000, "ui_update_max": 100, "memory_usage_max": 500, "error_rate_max": 1}}, "development": {"coding_standards": "PEP8", "type_checking": "mypy", "testing_framework": "pytest", "documentation": "Markdown + 中文注释", "version_control": "Git", "ci_cd": "GitHub Actions"}, "deployment": {"environments": ["development", "staging", "production"], "deployment_strategy": "蓝绿部署", "rollback_strategy": "自动回滚", "health_check_endpoints": ["http://localhost:8000/health", "http://localhost:8000/api/admin/v1/health"]}}