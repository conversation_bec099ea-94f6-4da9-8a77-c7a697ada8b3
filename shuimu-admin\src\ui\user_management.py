#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户管理界面
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QComboBox, QTreeWidget,
                            QTreeWidgetItem, QFrame, QMessageBox, QHeaderView,
                            QMenu, QInputDialog)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QAction, QFont
from typing import Dict, List, Any, Optional
import threading
from datetime import datetime

class UserManagementFrame(QWidget):
    """用户管理界面"""
    
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.users_data = []
        self.current_page = 1
        self.page_size = 20
        self.total_pages = 1
        
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 主标题
        title_label = QLabel("用户管理")
        title_font = QFont("Arial", 16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        main_layout.addLayout(toolbar_layout)
        
        # 搜索框
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索:"))
        self.search_entry = QLineEdit()
        self.search_entry.setMaximumWidth(300)
        self.search_entry.returnPressed.connect(self.search_users)
        search_layout.addWidget(self.search_entry)
        
        search_btn = QPushButton("搜索")
        search_btn.clicked.connect(self.search_users)
        search_layout.addWidget(search_btn)
        
        reset_btn = QPushButton("重置")
        reset_btn.clicked.connect(self.reset_search)
        search_layout.addWidget(reset_btn)
        
        toolbar_layout.addLayout(search_layout)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        create_btn = QPushButton("创建用户")
        create_btn.clicked.connect(self.create_user)
        button_layout.addWidget(create_btn)
        
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_users)
        button_layout.addWidget(refresh_btn)
        
        toolbar_layout.addStretch()
        toolbar_layout.addLayout(button_layout)
        
        # 筛选选项
        filter_layout = QHBoxLayout()
        main_layout.addLayout(filter_layout)
        
        filter_layout.addWidget(QLabel("状态筛选:"))
        self.status_combo = QComboBox()
        self.status_combo.addItems(["全部", "活跃", "禁用"])
        self.status_combo.currentTextChanged.connect(self.filter_users)
        filter_layout.addWidget(self.status_combo)
        
        filter_layout.addWidget(QLabel("角色筛选:"))
        self.role_combo = QComboBox()
        self.role_combo.addItems(["全部", "管理员", "用户"])
        self.role_combo.currentTextChanged.connect(self.filter_users)
        filter_layout.addWidget(self.role_combo)
        
        filter_layout.addStretch()
        
        # 用户列表
        self.setup_user_list(main_layout)
        
        # 分页控件
        self.setup_pagination(main_layout)
        
        # 状态栏
        self.status_bar = QLabel("就绪")
        self.status_bar.setFrameStyle(QFrame.Shape.StyledPanel)
        main_layout.addWidget(self.status_bar)
    
    def setup_user_list(self, main_layout):
        """设置用户列表"""
        # 创建QTreeWidget
        self.tree = QTreeWidget()
        headers = ["ID", "用户名", "邮箱", "显示名", "状态", "角色", "最后登录", "创建时间"]
        self.tree.setHeaderLabels(headers)
        
        # 设置列宽
        column_widths = [100, 120, 200, 120, 80, 80, 150, 150]
        for i, width in enumerate(column_widths):
            self.tree.setColumnWidth(i, width)
        
        # 设置树形控件属性
        self.tree.setRootIsDecorated(False)
        self.tree.setAlternatingRowColors(True)
        self.tree.setSortingEnabled(True)
        
        # 添加到布局
        main_layout.addWidget(self.tree)
        
        # 绑定事件
        self.tree.itemDoubleClicked.connect(self.on_user_double_click)
        self.tree.itemSelectionChanged.connect(self.on_user_select)
        self.tree.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tree.customContextMenuRequested.connect(self.show_context_menu)
        
        # 右键菜单
        self.setup_context_menu()
    
    def setup_context_menu(self):
        """设置右键菜单"""
        self.context_menu = QMenu(self)
        
        view_action = QAction("查看详情", self)
        view_action.triggered.connect(self.view_user_details)
        self.context_menu.addAction(view_action)
        
        edit_action = QAction("编辑用户", self)
        edit_action.triggered.connect(self.edit_user)
        self.context_menu.addAction(edit_action)
        
        self.context_menu.addSeparator()
        
        enable_action = QAction("启用用户", self)
        enable_action.triggered.connect(self.enable_user)
        self.context_menu.addAction(enable_action)
        
        disable_action = QAction("禁用用户", self)
        disable_action.triggered.connect(self.disable_user)
        self.context_menu.addAction(disable_action)
        
        self.context_menu.addSeparator()
        
        delete_action = QAction("删除用户", self)
        delete_action.triggered.connect(self.delete_user)
        self.context_menu.addAction(delete_action)
    
    def setup_pagination(self, main_layout):
        """设置分页控件"""
        pagination_layout = QHBoxLayout()
        main_layout.addLayout(pagination_layout)
        
        # 页面信息
        self.page_info_label = QLabel("第 1 页，共 1 页")
        pagination_layout.addWidget(self.page_info_label)
        
        pagination_layout.addStretch()
        
        # 分页按钮
        self.prev_button = QPushButton("上一页")
        self.prev_button.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_button)
        
        self.next_button = QPushButton("下一页")
        self.next_button.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_button)
        
        # 跳转页面
        pagination_layout.addWidget(QLabel("跳转到:"))
        self.page_entry = QLineEdit()
        self.page_entry.setMaximumWidth(50)
        self.page_entry.returnPressed.connect(self.goto_page)
        pagination_layout.addWidget(self.page_entry)
        
        goto_btn = QPushButton("跳转")
        goto_btn.clicked.connect(self.goto_page)
        pagination_layout.addWidget(goto_btn)
    
    def load_users(self, page: int = 1, search: str = None, is_active: bool = None, is_admin: bool = None):
        """加载用户数据"""
        def load_in_thread():
            try:
                self.update_status("正在加载用户数据...")
                
                # 构建查询参数
                params = {
                    'page': page,
                    'page_size': self.page_size
                }
                
                if search:
                    params['search'] = search
                
                # 状态筛选 - 优先使用参数，否则从UI获取
                if is_active is not None:
                    params['is_active'] = is_active
                elif self.status_combo.currentText() == "活跃":
                    params['is_active'] = True
                elif self.status_combo.currentText() == "禁用":
                    params['is_active'] = False
                
                # 角色筛选 - 优先使用参数，否则从UI获取
                if is_admin is not None:
                    params['is_admin'] = is_admin
                elif self.role_combo.currentText() == "管理员":
                    params['is_admin'] = True
                elif self.role_combo.currentText() == "用户":
                    params['is_admin'] = False
                
                # 调用API
                response = self.api_client.get_users(**params)
                
                if response.get('success'):
                    self.users_data = response.get('data', [])
                    pagination = response.get('pagination', {})
                    self.current_page = pagination.get('page', 1)
                    self.total_pages = pagination.get('pages', 1)
                    
                    # 更新UI
                    self.update_user_list()
                    self.update_pagination_info()
                    self.update_status(f"加载完成，共 {len(self.users_data)} 个用户")
                else:
                    error_msg = response.get('message', '加载用户失败')
                    self.update_status(f"错误: {error_msg}")
                    QMessageBox.critical(self, "错误", error_msg)
                    
            except Exception as e:
                error_msg = f"加载用户失败: {str(e)}"
                self.update_status(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def update_user_list(self):
        """更新用户列表显示"""
        # 清空现有数据
        self.tree.clear()
        
        # 添加新数据
        for user in self.users_data:
            item = QTreeWidgetItem([
                user.get('id', '')[:8] + '...' if len(user.get('id', '')) > 8 else user.get('id', ''),
                user.get('username', ''),
                user.get('email', ''),
                user.get('display_name', ''),
                "活跃" if user.get('is_active') else "禁用",
                "管理员" if user.get('is_admin') else "用户",
                self.format_datetime(user.get('last_login_at')),
                self.format_datetime(user.get('created_at'))
            ])
            
            # 根据状态设置颜色
            from PyQt6.QtGui import QColor
            if not user.get('is_active'):
                item.setForeground(4, QColor('gray'))  # 状态列设置为灰色
            if user.get('is_admin'):
                item.setForeground(5, QColor('blue'))  # 角色列设置为蓝色
            
            self.tree.addTopLevelItem(item)
    
    def format_datetime(self, dt_str: str) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "从未"
        try:
            # 尝试解析ISO格式
            if 'T' in dt_str:
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M")
            return dt_str
        except:
            return dt_str
    
    def update_pagination_info(self):
        """更新分页信息"""
        info_text = f"第 {self.current_page} 页，共 {self.total_pages} 页"
        self.page_info_label.setText(info_text)
        
        # 更新按钮状态
        self.prev_button.setEnabled(self.current_page > 1)
        self.next_button.setEnabled(self.current_page < self.total_pages)
    
    def update_status(self, message: str):
        """更新状态栏"""
        self.status_bar.setText(message)
    
    # 事件处理方法
    def search_users(self):
        """搜索用户"""
        search_text = self.search_entry.text().strip()
        self.current_page = 1
        self.load_users(page=1, search=search_text if search_text else None)
    
    def reset_search(self):
        """重置搜索"""
        self.search_entry.clear()
        self.status_combo.setCurrentIndex(0)
        self.role_combo.setCurrentIndex(0)
        self.current_page = 1
        self.load_users(page=1)
    
    def filter_users(self):
        """筛选用户"""
        self.current_page = 1
        search_text = self.search_entry.text().strip()
        
        # 获取筛选条件
        status_filter = self.status_combo.currentText()
        role_filter = self.role_combo.currentText()
        
        # 转换筛选条件为API参数
        is_active = None
        if status_filter == "活跃":
            is_active = True
        elif status_filter == "禁用":
            is_active = False
        
        is_admin = None
        if role_filter == "管理员":
            is_admin = True
        elif role_filter == "用户":
            is_admin = False
        
        self.load_users(
            page=1, 
            search=search_text if search_text else None,
            is_active=is_active,
            is_admin=is_admin
        )
    
    def refresh_users(self):
        """刷新用户列表"""
        search_text = self.search_entry.text().strip()
        self.load_users(page=self.current_page, search=search_text if search_text else None)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 1:
            search_text = self.search_entry.text().strip()
            self.load_users(page=self.current_page - 1, search=search_text if search_text else None)
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages:
            search_text = self.search_entry.text().strip()
            self.load_users(page=self.current_page + 1, search=search_text if search_text else None)
    
    def goto_page(self):
        """跳转到指定页面"""
        try:
            page = int(self.page_entry.text())
            if 1 <= page <= self.total_pages:
                search_text = self.search_entry.text().strip()
                self.load_users(page=page, search=search_text if search_text else None)
                self.page_entry.clear()
            else:
                QMessageBox.warning(self, "警告", f"页面号必须在 1 到 {self.total_pages} 之间")
        except ValueError:
            QMessageBox.warning(self, "警告", "请输入有效的页面号")
    
    def sort_column(self, col):
        """排序列"""
        # TODO: 实现列排序功能
        pass
    
    def get_selected_user(self) -> Optional[Dict]:
        """获取选中的用户"""
        current_item = self.tree.currentItem()
        if not current_item:
            return None
        
        # 根据用户名查找完整的用户数据
        username = current_item.text(1)  # 用户名在第1列
        for user in self.users_data:
            if user.get('username') == username:
                return user
        return None
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        item = self.tree.itemAt(position)
        if item:
            self.tree.setCurrentItem(item)
            self.context_menu.exec(self.tree.mapToGlobal(position))
    
    def on_user_select(self):
        """用户选择事件 - 打开右侧详情标签页"""
        user = self.get_selected_user()
        if user and hasattr(self.parent, 'open_user_detail_tab'):
            # 乐观更新：立即打开详情标签页
            self.parent.open_user_detail_tab(user)
    
    def on_user_double_click(self, item, column):
        """双击用户事件"""
        self.view_user_details()
    
    
    def view_user_details(self):
        """查看用户详情"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "警告", "请选择一个用户")
            return
        
        # 创建用户详情窗口
        from .user_detail_dialog import UserDetailDialog
        dialog = UserDetailDialog(self, user, self.api_client)
        dialog.show()
    
    def edit_user(self):
        """编辑用户"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "警告", "请选择一个用户")
            return
        
        # 创建用户编辑窗口
        from .user_edit_dialog import UserEditDialog
        dialog = UserEditDialog(self, user, self.api_client)
        if dialog.show():
            self.refresh_users()
    
    def create_user(self):
        """创建用户"""
        from .user_create_dialog import UserCreateDialog
        dialog = UserCreateDialog(self, self.api_client)
        if dialog.show():
            self.refresh_users()
    
    def enable_user(self):
        """启用用户"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "警告", "请选择一个用户")
            return
        
        if user.get('is_active'):
            QMessageBox.information(self, "信息", "用户已经是活跃状态")
            return
        
        reply = QMessageBox.question(self, "确认", f"确定要启用用户 '{user.get('username')}' 吗？")
        if reply == QMessageBox.StandardButton.Yes:
            self.update_user_status(user.get('id'), True)
    
    def disable_user(self):
        """禁用用户"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "警告", "请选择一个用户")
            return
        
        if not user.get('is_active'):
            QMessageBox.information(self, "信息", "用户已经是禁用状态")
            return
        
        reply = QMessageBox.question(self, "确认", f"确定要禁用用户 '{user.get('username')}' 吗？")
        if reply == QMessageBox.StandardButton.Yes:
            self.update_user_status(user.get('id'), False)
    
    def delete_user(self):
        """删除用户"""
        user = self.get_selected_user()
        if not user:
            QMessageBox.warning(self, "警告", "请选择一个用户")
            return
        
        username = user.get('username')
        reply = QMessageBox.question(self, "确认删除", 
                              f"确定要删除用户 '{username}' 吗？\n\n"
                              f"此操作不可撤销，用户的所有数据将被删除。")
        if reply == QMessageBox.StandardButton.Yes:
            self.perform_delete_user(user.get('id'))
    
    def update_user_status(self, user_id: str, is_active: bool):
        """更新用户状态"""
        def update_in_thread():
            try:
                self.update_status("正在更新用户状态...")
                
                response = self.api_client.update_user(user_id, {'is_active': is_active})
                
                if response.get('success'):
                    action = "启用" if is_active else "禁用"
                    self.update_status(f"用户{action}成功")
                    self.refresh_users()
                else:
                    error_msg = response.get('message', '更新用户状态失败')
                    self.update_status(f"错误: {error_msg}")
                    QMessageBox.critical(self, "错误", error_msg)
                    
            except Exception as e:
                error_msg = f"更新用户状态失败: {str(e)}"
                self.update_status(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
        
        threading.Thread(target=update_in_thread, daemon=True).start()
    
    def perform_delete_user(self, user_id: str):
        """执行删除用户"""
        def delete_in_thread():
            try:
                self.update_status("正在删除用户...")
                
                response = self.api_client.delete_user(user_id)
                
                if response.get('success'):
                    self.update_status("用户删除成功")
                    self.refresh_users()
                    QMessageBox.information(self, "成功", "用户删除成功")
                else:
                    error_msg = response.get('message', '删除用户失败')
                    self.update_status(f"错误: {error_msg}")
                    QMessageBox.critical(self, "错误", error_msg)
                    
            except Exception as e:
                error_msg = f"删除用户失败: {str(e)}"
                self.update_status(error_msg)
                QMessageBox.critical(self, "错误", error_msg)
        
        threading.Thread(target=delete_in_thread, daemon=True).start()
