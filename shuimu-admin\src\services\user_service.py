#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户业务服务
"""

import re
import hashlib
# 🔑 UUID架构：移除uuid导入，使用管理端统一UUID生成器
from typing import List, Optional, Dict, Any, Tuple
from database.dao import UserDAO
from database.models import DatabaseManager
from cache.data_manager import DataManager
import logging

logger = logging.getLogger(__name__)

class UserService:
    """用户业务服务类"""

    def __init__(self, db_manager: DatabaseManager, use_api: bool = False, use_cache: bool = True):
        self.db_manager = db_manager
        self.use_api = use_api
        self.use_cache = use_cache

        # 初始化API客户端
        if self.use_api:
            from utils.api_client import APIClient
            self.api_client = APIClient()
            logger.info("用户服务使用API模式")

        # 初始化数据管理器
        if self.use_cache:
            self.data_manager = DataManager()
            logger.info("用户服务使用缓存优先模式")
        else:
            self.data_manager = None
    
    def get_user_list(self, page: int = 1, page_size: int = 20,
                     search: str = None, is_active: bool = None) -> Dict[str, Any]:
        """获取用户列表"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    logger.info("使用全局缓存获取用户列表")
                    return global_data_manager.get_users(page=page, page_size=page_size,
                                                       search=search, is_active=is_active)
            except Exception as e:
                logger.warning(f"全局缓存获取用户失败: {e}")

            # 备选：使用本地缓存数据
            if self.use_cache and self.data_manager and self.data_manager._initialized:
                logger.info("使用本地缓存获取用户列表")
                return self.data_manager.get_data('users', page=page, page_size=page_size,
                                                search=search, is_active=is_active)

            # 使用API模式
            if self.use_api:
                logger.info("使用API获取用户列表")
                params = {
                    'page': page,
                    'page_size': page_size
                }
                if search:
                    params['search'] = search
                if is_active is not None:
                    params['is_active'] = is_active

                response = self.api_client.get('/api/admin/v1/users', params=params)
                if response and response.get('success'):
                    # 转换分页格式以匹配预期
                    if 'pagination' in response:
                        pagination = response['pagination']
                        # 转换API返回的分页格式
                        response['pagination'] = {
                            'current_page': pagination.get('page', page),
                            'page_size': pagination.get('page_size', page_size),
                            'total_records': pagination.get('total', 0),
                            'total_pages': pagination.get('pages', 0),
                            'has_next': pagination.get('page', page) < pagination.get('pages', 0),
                            'has_prev': pagination.get('page', page) > 1
                        }
                    return response
                else:
                    logger.error(f"API获取用户列表失败: {response}")
                    return {'success': False, 'message': 'API获取用户列表失败', 'data': [], 'pagination': {}}

            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)

            users, total = user_dao.get_all(page, page_size, search, is_active)

            # 转换为字典格式
            user_list = [user.to_dict() for user in users]

            # 计算分页信息
            total_pages = (total + page_size - 1) // page_size

            session.close()

            return {
                'success': True,
                'data': user_list,
                'pagination': {
                    'current_page': page,
                    'page_size': page_size,
                    'total_records': total,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return {
                'success': False,
                'message': f'获取用户列表失败: {str(e)}',
                'data': [],
                'pagination': {}
            }
    
    def get_user_detail(self, user_id: int) -> Dict[str, Any]:
        """获取用户详情"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            user = user_dao.get_by_id(user_id)
            if not user:
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 获取用户订单
            orders = user_dao.get_user_orders(user_id)
            order_list = [order.to_dict() for order in orders]
            
            user_data = user.to_dict()
            user_data['orders'] = order_list
            user_data['order_count'] = len(order_list)
            
            # 计算总消费金额
            total_spent = sum(float(order.amount) for order in orders if order.status == 'completed')
            user_data['total_spent'] = total_spent
            
            session.close()
            
            return {
                'success': True,
                'data': user_data,
                'message': '获取用户详情成功'
            }
        except Exception as e:
            logger.error(f"获取用户详情失败: {e}")
            return {
                'success': False,
                'message': f'获取用户详情失败: {str(e)}',
                'data': None
            }
    
    def create_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户名是否已存在
            existing_user = user_dao.get_by_username(user_data['username'])
            if existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户名已存在',
                    'data': None
                }
            
            # 检查邮箱是否已存在
            existing_email = user_dao.get_by_email(user_data['email'])
            if existing_email:
                session.close()
                return {
                    'success': False,
                    'message': '邮箱已存在',
                    'data': None
                }
            
            # 🎯 修复：保存明文密码，不进行哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = user_data['password']  # 直接保存明文
                del user_data['password']
            
            # 创建用户
            user = user_dao.create(user_data)

            # 在关闭session之前获取用户数据
            user_dict = user.to_dict()

            session.close()

            return {
                'success': True,
                'message': '用户创建成功',
                'data': user_dict
            }
        except Exception as e:
            logger.error(f"创建用户失败: {e}")
            return {
                'success': False,
                'message': f'创建用户失败: {str(e)}',
                'data': None
            }
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户"""
        try:
            # 数据验证
            validation_result = self._validate_user_data(user_data, is_update=True)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'message': validation_result['message'],
                    'data': None
                }
            
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 检查用户是否存在
            existing_user = user_dao.get_by_id(user_id)
            if not existing_user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在',
                    'data': None
                }
            
            # 检查用户名是否被其他用户使用
            if 'username' in user_data:
                username_user = user_dao.get_by_username(user_data['username'])
                if username_user and username_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '用户名已被其他用户使用',
                        'data': None
                    }
            
            # 检查邮箱是否被其他用户使用
            if 'email' in user_data:
                email_user = user_dao.get_by_email(user_data['email'])
                if email_user and email_user.id != user_id:
                    session.close()
                    return {
                        'success': False,
                        'message': '邮箱已被其他用户使用',
                        'data': None
                    }
            
            # 🎯 修复：保存明文密码，不进行哈希处理
            if 'password' in user_data:
                user_data['password_hash'] = user_data['password']  # 直接保存明文
                del user_data['password']
            
            # 更新用户
            user = user_dao.update(user_id, user_data)
            
            session.close()
            
            return {
                'success': True,
                'message': '用户更新成功',
                'data': user.to_dict() if user else None
            }
        except Exception as e:
            logger.error(f"更新用户失败: {e}")
            return {
                'success': False,
                'message': f'更新用户失败: {str(e)}',
                'data': None
            }
    
    def delete_user(self, user_id, hard_delete: bool = False) -> Dict[str, Any]:
        """删除用户 - 支持UUID字符串和API模式"""
        try:
            # 🎯 关键修复：使用API模式
            if self.use_api:
                logger.info(f"使用API删除用户: {user_id}")
                response = self.api_client.delete(f'/api/admin/v1/users/{user_id}')
                if response and response.get('success'):
                    # 清理全局缓存中的用户数据
                    self.remove_user_from_cache(user_id)
                    return response
                else:
                    logger.error(f"API删除用户失败: {response}")
                    return {'success': False, 'message': response.get('message', 'API删除用户失败')}
            
            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            # 🎯 修复：直接使用get_by_id，因为UserDAO没有get_by_user_id方法
            user = user_dao.get_by_id(user_id)
                
            if not user:
                session.close()
                return {
                    'success': False,
                    'message': '用户不存在'
                }
            
            # 执行删除
            if hard_delete:
                success = user_dao.hard_delete(user.id)  # 使用数据库主键
                message = '用户删除成功' if success else '用户删除失败'
            else:
                success = user_dao.delete(user.id)  # 使用数据库主键
                message = '用户禁用成功' if success else '用户禁用失败'
            
            session.close()
            
            return {
                'success': success,
                'message': message
            }
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return {
                'success': False,
                'message': f'删除用户失败: {str(e)}'
            }
    
    def get_user_statistics(self) -> Dict[str, Any]:
        """获取用户统计信息"""
        try:
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)
            
            stats = user_dao.get_statistics()
            
            session.close()
            
            return {
                'success': True,
                'data': stats,
                'message': '获取统计信息成功'
            }
        except Exception as e:
            logger.error(f"获取用户统计失败: {e}")
            return {
                'success': False,
                'message': f'获取用户统计失败: {str(e)}',
                'data': {}
            }
    
    def _validate_user_data(self, user_data: Dict[str, Any], is_update: bool = False) -> Dict[str, Any]:
        """验证用户数据"""
        if not is_update:
            # 创建时必填字段
            required_fields = ['username', 'email']
            for field in required_fields:
                if field not in user_data or not user_data[field]:
                    return {
                        'valid': False,
                        'message': f'{field} 是必填字段'
                    }
        
        # 用户名验证
        if 'username' in user_data:
            username = user_data['username']
            if len(username) < 3 or len(username) > 50:
                return {
                    'valid': False,
                    'message': '用户名长度必须在3-50个字符之间'
                }
            
            if not re.match(r'^[a-zA-Z0-9_]+$', username):
                return {
                    'valid': False,
                    'message': '用户名只能包含字母、数字和下划线'
                }
        
        # 邮箱验证
        if 'email' in user_data:
            email = user_data['email']
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            if not re.match(email_pattern, email):
                return {
                    'valid': False,
                    'message': '邮箱格式不正确'
                }
        
        # 密码验证
        if 'password' in user_data:
            password = user_data['password']
            if len(password) < 6:
                return {
                    'valid': False,
                    'message': '密码长度至少6个字符'
                }
        
        return {
            'valid': True,
            'message': '数据验证通过'
        }
    
    def _hash_password(self, password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()

    def get_user_by_id(self, user_id: str) -> Dict[str, Any]:
        """根据ID获取用户详细信息"""
        try:
            # 🎯 修复：优先使用全局缓存的用户详情数据（包含password_hash）
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    # 🎯 修复：优先从用户详情缓存中查找（包含password_hash字段）
                    user_detail = global_data_manager.get_user_detail(user_id)
                    if user_detail:
                        return {'success': True, 'data': user_detail}

                    # 🎯 降级：从用户列表数据中查找（不包含password_hash）
                    for user in global_data_manager._user_data:
                        # 支持多种ID匹配方式
                        if (user.get('user_id') == user_id or
                            user.get('id') == user_id or
                            str(user.get('id')) == str(user_id) or
                            str(user.get('user_id')) == str(user_id)):
                            return {'success': True, 'data': user}

                    # 如果没找到，记录调试信息
                    logger.warning(f"在缓存中未找到用户: {user_id}")
                    logger.debug(f"缓存中的用户ID列表: {[u.get('user_id', u.get('id')) for u in global_data_manager._user_data]}")
            except Exception as e:
                logger.warning(f"全局缓存获取用户详情失败: {e}")

            # 使用API模式
            if self.use_api:
                logger.info(f"使用API获取用户详情: {user_id}")
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}')
                if response and response.get('success'):
                    return response
                else:
                    logger.error(f"API获取用户详情失败: {response}")
                    return {'success': False, 'message': 'API获取用户详情失败', 'data': None}

            # 使用本地数据库模式
            session = self.db_manager.get_session()
            user_dao = UserDAO(session)

            user = user_dao.get_by_id(user_id)
            if user:
                return {
                    'success': True,
                    'data': {
                        'id': user.id,
                        'user_id': user.user_id,
                        'username': user.username,
                        'email': user.email,
                        'display_name': user.display_name,
                        'phone': user.phone,
                        'avatar': user.avatar_url,
                        'is_active': user.is_active,
                        'is_admin': user.is_admin,
                        'last_login_at': user.last_login_at.isoformat() if user.last_login_at else None,
                        'created_at': user.created_at.isoformat() if user.created_at else None,
                        'updated_at': user.updated_at.isoformat() if user.updated_at else None
                    }
                }
            else:
                return {'success': False, 'message': '用户不存在', 'data': None}

        except Exception as e:
            logger.error(f"获取用户详情失败: {str(e)}")
            return {'success': False, 'message': f'获取用户详情失败: {str(e)}', 'data': None}

    def get_user_purchases(self, user_id: str) -> Dict[str, Any]:
        """获取用户购买记录"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_purchases(user_id)
                    if cached_data:
                        if cached_data.get('success'):
                            print(f"✅ 从缓存获取用户 {user_id} 购买记录: {len(cached_data.get('data', []))} 条")
                        else:
                            print(f"⚠️ 缓存中用户 {user_id} 购买记录获取失败: {cached_data.get('message', '未知原因')}")
                        return cached_data
                    else:
                        print(f"⚠️ 缓存中未找到用户 {user_id} 的购买记录")
            except Exception as e:
                logger.warning(f"全局缓存获取用户购买记录失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/purchases')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取购买记录失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'获取用户购买记录失败: {str(e)}', 'data': []}

    def get_user_progress(self, user_id: str) -> Dict[str, Any]:
        """获取用户观看进度"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_progress(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户观看进度失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/progress')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取观看进度失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户观看进度失败: {str(e)}")
            return {'success': False, 'message': f'获取用户观看进度失败: {str(e)}', 'data': []}

    def get_user_favorites(self, user_id: str) -> Dict[str, Any]:
        """获取用户收藏"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_favorites(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户收藏失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/favorites')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取收藏失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户收藏失败: {str(e)}")
            return {'success': False, 'message': f'获取用户收藏失败: {str(e)}', 'data': []}

    def get_user_settings(self, user_id: str) -> Dict[str, Any]:
        """获取用户设置"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_settings(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户设置失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/settings')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取用户设置失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户设置失败: {str(e)}")
            return {'success': False, 'message': f'获取用户设置失败: {str(e)}', 'data': []}

    def get_user_cache(self, user_id: str) -> Dict[str, Any]:
        """获取用户缓存信息"""
        try:
            # 优先使用全局缓存数据
            try:
                from cache.global_data_manager import global_data_manager
                if global_data_manager._data_loaded:
                    cached_data = global_data_manager.get_user_cache(user_id)
                    if cached_data:
                        return cached_data
            except Exception as e:
                logger.warning(f"全局缓存获取用户缓存失败: {e}")

            # 使用API模式
            if self.use_api:
                response = self.api_client.get(f'/api/admin/v1/users/{user_id}/cache')
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API获取缓存信息失败', 'data': []}

            # 使用本地数据库模式（暂时返回空数据）
            return {'success': True, 'data': []}

        except Exception as e:
            logger.error(f"获取用户缓存信息失败: {str(e)}")
            return {'success': False, 'message': f'获取用户缓存信息失败: {str(e)}', 'data': []}

    def update_user_info(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户信息 - 四层同步乐观更新模型"""
        try:
            # 🎯 四层同步模型实现
            sync_status = {
                'server_db': False,
                'local_db': False,
                'memory_cache': False,
                'api_response': None
            }
            
            # 🎯 修复：预处理密码字段映射
            processed_data = user_data.copy()
            if 'password' in processed_data:
                processed_data['password_hash'] = processed_data['password']
                # API层保留password字段，本地同步时使用password_hash
            
            # 第2层：API层 - 异步提交到服务器
            if self.use_api:
                response = self.api_client.put(f'/api/admin/v1/users/{user_id}', user_data)
                sync_status['api_response'] = response
                
                
                if response and response.get('success'):
                    sync_status['server_db'] = True
                    
                    # 🎯 新增：验证服务器数据是否真的更新了
                    try:
                        # 验证服务器更新结果（已移除详细日志）
                        pass
                    except Exception as verify_e:
                        pass  # 验证过程异常，静默处理
                    
                    # 第3层：持久层 - 同步本地MySQL（可配置跳过）
                    try:
                        local_result = self.update_user_local_only(user_id, processed_data)
                        if local_result and local_result.get('success'):
                            sync_status['local_db'] = True
                        else:
                            error_msg = local_result.get('message', '未知错误')
                            error_type = local_result.get('error_type', '')
                            
                            # 🎯 新增：如果是表结构不一致，跳过但不影响整体成功
                            if error_type == 'schema_mismatch':
                                sync_status['local_db'] = 'skipped_schema_mismatch'
                            else:
                                sync_status['local_db'] = False
                    except Exception as local_e:
                        sync_status['local_db'] = False
                    
                    # 第3层：内存缓存同步
                    try:
                        self.update_user_cache(user_id, processed_data)
                        sync_status['memory_cache'] = True
                    except Exception as cache_e:
                        pass  # 缓存异常，静默处理

                    # 🎯 严格的成功验证：所有层都必须成功
                    all_layers_success = (
                        sync_status['server_db'] and 
                        sync_status['local_db'] and 
                        sync_status['memory_cache']
                    )

                    return {
                        'success': all_layers_success,
                        'message': '四层同步完成' if all_layers_success else '部分层同步失败',
                        'sync_status': sync_status
                    }
                else:
                    error_msg = response.get('message', 'API调用失败') if response else 'API无响应'
                    return {'success': False, 'message': f'API更新失败: {error_msg}', 'sync_status': sync_status}

            # 使用本地数据库模式
            return self.update_user_local_only(user_id, user_data)

        except Exception as e:
            logger.error(f"更新用户信息失败: {str(e)}")
            return {'success': False, 'message': f'更新用户信息失败: {str(e)}'}

    def reset_user_password(self, user_id: str) -> Dict[str, Any]:
        """重置用户密码"""
        try:
            # 🎯 调试：打印用户ID

            # 使用API模式
            if self.use_api:
                api_url = f'/api/admin/v1/users/{user_id}/reset-password'
                response = self.api_client.post(api_url)
                if response and response.get('success'):
                    return response
                else:
                    return {'success': False, 'message': 'API重置密码失败'}

            # 使用本地数据库模式
            return {'success': False, 'message': '本地数据库重置密码功能待实现'}

        except Exception as e:
            logger.error(f"重置用户密码失败: {str(e)}")
            return {'success': False, 'message': f'重置用户密码失败: {str(e)}'}

    def toggle_user_status(self, user_id: str) -> Dict[str, Any]:
        """切换用户状态 - 完整同步流程"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/toggle-status')
                if response and response.get('success'):
                    new_status = response.get('new_status')

                    # 更新内存缓存（API模式下跳过本地数据库同步）
                    self.update_user_status_cache(user_id, new_status)

                    return {
                        'success': True,
                        'message': f'用户状态切换成功',
                        'new_status': new_status,
                        'sync_status': {
                            'server_db': True,
                            'local_db': 'skipped_api_mode',  # API模式下跳过
                            'memory_cache': True
                        }
                    }
                else:
                    return {'success': False, 'message': 'API切换用户状态失败'}

            # 使用本地数据库模式
            return self.toggle_user_status_local_only(user_id)

        except Exception as e:
            logger.error(f"切换用户状态失败: {str(e)}")
            return {'success': False, 'message': f'切换用户状态失败: {str(e)}'}

    def toggle_user_admin(self, user_id: str) -> Dict[str, Any]:
        """切换用户管理员权限"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.post(f'/api/admin/v1/users/{user_id}/toggle-admin')
                if response and response.get('success'):
                    # 更新全局缓存中的用户权限
                    self.update_user_admin_cache(user_id, response.get('new_admin_status'))
                    return response
                else:
                    return {'success': False, 'message': 'API切换用户权限失败'}

            # 使用本地数据库模式
            return {'success': False, 'message': '本地数据库权限切换功能待实现'}

        except Exception as e:
            logger.error(f"切换用户权限失败: {str(e)}")
            return {'success': False, 'message': f'切换用户权限失败: {str(e)}'}

    def update_user_cache(self, user_id: str, user_data: Dict[str, Any]):
        """更新全局缓存中的用户信息"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                
                # 更新用户基本信息缓存
                user_found_in_basic = False
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user.update(user_data)
                        user_found_in_basic = True
                        break
                
                if not user_found_in_basic:
                    pass  # 用户基本信息缓存中未找到

                # 🎯 修复：更新用户详情缓存 - 增强逻辑
                
                detail_found = False
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id].update(user_data)
                    detail_found = True
                else:
                    # 🎯 新增：尝试不同的键匹配方式
                    for key in global_data_manager._user_details.keys():
                        if (str(key) == str(user_id) or 
                            key == user_id or
                            (isinstance(key, str) and isinstance(user_id, str) and key == user_id)):
                            global_data_manager._user_details[key].update(user_data)
                            detail_found = True
                            break
                
                if not detail_found:
                    # 🎯 新增：如果不存在则创建
                    global_data_manager._user_details[user_id] = user_data.copy()

        except Exception as e:
            logger.warning(f"更新用户缓存失败: {e}")

    def update_user_status_cache(self, user_id: str, new_status: bool):
        """更新全局缓存中的用户状态"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 更新用户基本信息缓存
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user['is_active'] = new_status
                        break

                # 更新用户详情缓存
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id]['is_active'] = new_status

        except Exception as e:
            logger.warning(f"更新用户状态缓存失败: {e}")

    def update_user_admin_cache(self, user_id: str, new_admin_status: bool):
        """更新全局缓存中的用户权限"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 更新用户基本信息缓存
                for user in global_data_manager._user_data:
                    if (user.get('user_id') == user_id or
                        user.get('id') == user_id or
                        str(user.get('id')) == str(user_id) or
                        str(user.get('user_id')) == str(user_id)):
                        user['is_admin'] = new_admin_status
                        break

                # 更新用户详情缓存
                if user_id in global_data_manager._user_details:
                    global_data_manager._user_details[user_id]['is_admin'] = new_admin_status

        except Exception as e:
            logger.warning(f"更新用户权限缓存失败: {e}")

    def sync_user_to_local_db(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """同步用户信息到本地数据库"""
        try:
            if not self.db_manager:
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 构建更新SQL
            update_fields = []
            params = []

            if 'username' in user_data:
                update_fields.append("username = %s")
                params.append(user_data['username'])

            if 'email' in user_data:
                update_fields.append("email = %s")
                params.append(user_data['email'])

            if 'display_name' in user_data:
                update_fields.append("display_name = %s")
                params.append(user_data['display_name'])

            if 'phone' in user_data:
                update_fields.append("phone = %s")
                params.append(user_data['phone'])

            if 'is_active' in user_data:
                update_fields.append("is_active = %s")
                params.append(1 if user_data['is_active'] else 0)

            if 'is_admin' in user_data:
                update_fields.append("is_admin = %s")
                params.append(1 if user_data['is_admin'] else 0)

            # 🎯 修复：密码字段处理 - 避免重复，优先处理password字段
            if 'password' in user_data:
                update_fields.append("password_hash = %s")
                params.append(user_data['password'])
            elif 'password_hash' in user_data:
                update_fields.append("password_hash = %s")
                params.append(user_data['password_hash'])

            if not update_fields:
                return {'success': True, 'message': '没有需要更新的字段'}

            # 🎯 修复：添加更新时间（不需要参数）
            update_fields.append("updated_at = NOW()")
            
            # 🎯 修复：user_id是WHERE子句的参数，要放在最后
            params.append(user_id)

            # 🎯 修复：SQLAlchemy需要命名参数字典，不是位置参数元组
            # 构建命名参数字典
            param_dict = {}
            param_placeholders = []
            
            param_index = 0
            for field in update_fields[:-1]:  # 排除最后的updated_at = NOW()
                if '=' in field:
                    field_name = field.split('=')[0].strip()
                    param_name = f"param_{param_index}"
                    param_dict[param_name] = params[param_index]
                    param_placeholders.append(f"{field_name} = :{param_name}")
                    param_index += 1
            
            # 添加updated_at字段（不需要参数）
            param_placeholders.append("updated_at = NOW()")
            
            # 添加WHERE条件参数
            param_dict['user_id'] = params[-1]  # 最后一个参数是user_id
            
            sql = f"""
                UPDATE users
                SET {', '.join(param_placeholders)}
                WHERE id = :user_id
            """


            # 🎯 新增：检查本地数据库表结构
            try:
                desc_sql = "DESCRIBE users"
                table_info = self.db_manager.execute_query(desc_sql)
            except Exception as desc_e:
                print(f"⚠️ 无法获取本地数据库表结构: {desc_e}")

            # 执行更新 - 使用参数字典
            affected_rows = self.db_manager.execute_update(sql, param_dict)

            if affected_rows > 0:
                return {'success': True, 'message': f'本地数据库更新成功，影响 {affected_rows} 行'}
            else:
                return {'success': False, 'message': '本地数据库更新失败，没有影响任何行'}

        except Exception as e:
            logger.error(f"同步用户信息到本地数据库失败: {str(e)}")
            # 🎯 新增：如果是表结构问题，提供更友好的错误信息
            if "List argument must consist only of tuples or dictionaries" in str(e):
                return {
                    'success': False, 
                    'message': '本地数据库表结构可能不一致，建议跳过本地同步',
                    'error_type': 'schema_mismatch'
                }
            return {'success': False, 'message': f'本地数据库同步失败: {str(e)}'}

    def update_user_local_only(self, user_id: str, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """仅更新本地数据库和缓存（不使用API）"""
        try:
            # 第1步：更新本地数据库
            local_result = self.sync_user_to_local_db(user_id, user_data)

            if local_result.get('success'):
                # 第2步：更新内存缓存
                self.update_user_cache(user_id, user_data)

                return {
                    'success': True,
                    'message': '用户信息更新成功（仅本地）',
                    'sync_status': {
                        'server_db': False,
                        'local_db': True,
                        'memory_cache': True
                    }
                }
            else:
                return local_result

        except Exception as e:
            logger.error(f"本地更新用户信息失败: {str(e)}")
            return {'success': False, 'message': f'本地更新失败: {str(e)}'}

    def toggle_user_status_local_only(self, user_id: str) -> Dict[str, Any]:
        """仅在本地切换用户状态"""
        try:
            if not self.db_manager:
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 获取当前状态
            current_user = self.db_manager.execute_query(
                "SELECT is_active FROM users WHERE id = %s", (user_id,)
            )

            if not current_user:
                return {'success': False, 'message': '用户不存在'}

            current_status = bool(current_user[0]['is_active'])
            new_status = not current_status

            # 更新状态
            local_result = self.sync_user_to_local_db(user_id, {'is_active': new_status})

            if local_result.get('success'):
                # 更新内存缓存
                self.update_user_status_cache(user_id, new_status)

                return {
                    'success': True,
                    'message': f'用户状态切换成功（仅本地）',
                    'new_status': new_status,
                    'sync_status': {
                        'server_db': False,
                        'local_db': True,
                        'memory_cache': True
                    }
                }
            else:
                return local_result

        except Exception as e:
            logger.error(f"本地切换用户状态失败: {str(e)}")
            return {'success': False, 'message': f'本地状态切换失败: {str(e)}'}

    def add_user_purchase(self, user_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """添加用户购买记录 - 🎯 四层同步乐观更新模型"""
        try:
            print(f"🚀 开始四层同步购买记录添加: user_id={user_id}")
            
            # 🎯 四层同步状态跟踪
            sync_status = {
                'ui_layer': True,  # UI层已经乐观更新
                'api_layer': False,
                'local_db_layer': False,
                'cache_layer': False
            }
            
            # 使用API模式
            if self.use_api:
                print(f"🚀 第2层API：开始API购买记录添加")
                try:
                    response = self.api_client.post(f'/api/admin/v1/users/{user_id}/purchases', purchase_data)
                    
                    if response and response.get('success'):
                        sync_status['api_layer'] = True
                        print(f"✅ 第2层API：购买记录已成功提交到服务器")
                        
                        # 🎯 第3层：更新本地缓存
                        print(f"🚀 第3层缓存：开始更新全局缓存中的购买记录")
                        try:
                            self.update_purchase_cache(user_id, response.get('data'))
                            sync_status['cache_layer'] = True
                            print(f"✅ 第3层缓存：购买记录缓存已更新")
                        except Exception as cache_e:
                            print(f"⚠️ 第3层缓存：缓存更新失败: {cache_e}")
                            # 缓存失败不影响主要功能
                        
                        # 🎯 最终结果
                        success_layers = sum([sync_status[key] for key in sync_status if sync_status[key] == True])
                        
                        return {
                            'success': True,
                            'message': '购买记录添加成功',
                            'data': response.get('data'),
                            'sync_status': sync_status
                        }
                    else:
                        # 🎯 第4层补偿：API失败时自动降级到本地模式
                        error_msg = response.get('message', 'API调用失败') if response else 'API无响应'
                        logger.warning(f"API添加购买记录失败，自动降级到本地模式: {error_msg}")
                        
                        # 更新同步状态
                        sync_status['api_layer'] = False
                        
                        # 执行本地降级
                        local_result = self.add_user_purchase_local_only(user_id, purchase_data)
                        if local_result and local_result.get('success'):
                            sync_status['local_db_layer'] = True
                            success_layers = sum([sync_status[key] for key in sync_status if sync_status[key] == True])
                            print(f"   📊 详细状态: UI层✅ | API层❌ | 本地DB层✅ | 缓存层➖")
                            
                            return {
                                'success': True,
                                'message': '购买记录添加成功（本地模式）',
                                'data': local_result.get('data'),
                                'sync_status': sync_status
                            }
                        else:
                            return {
                                'success': False, 
                                'message': f'购买记录添加失败: API和本地数据库都失败',
                                'sync_status': sync_status
                            }
                            
                except Exception as api_e:
                    # 🎯 第4层补偿：API异常时降级
                    logger.error(f"API添加购买记录异常，自动降级到本地模式: {str(api_e)}")
                    
                    sync_status['api_layer'] = False
                    local_result = self.add_user_purchase_local_only(user_id, purchase_data)
                    if local_result and local_result.get('success'):
                        sync_status['local_db_layer'] = True
                        print(f"✅ 第4层补偿：本地数据库降级成功")
                        return {
                            'success': True,
                            'message': '购买记录添加成功（本地模式）',
                            'data': local_result.get('data'),
                            'sync_status': sync_status
                        }
                    else:
                        print(f"❌ 第4层补偿：本地数据库降级也失败")
                        return {
                            'success': False, 
                            'message': f'购买记录添加失败: API异常且本地数据库也失败',
                            'sync_status': sync_status
                        }

            # 使用本地数据库模式
            print(f"🚀 直接使用本地数据库模式（非API模式）")
            local_result = self.add_user_purchase_local_only(user_id, purchase_data)
            if local_result and local_result.get('success'):
                sync_status['local_db_layer'] = True
                print(f"✅ 本地数据库模式购买记录添加成功")
                return {
                    'success': True,
                    'message': '购买记录添加成功（本地模式）',
                    'data': local_result.get('data'),
                    'sync_status': sync_status
                }
            else:
                print(f"❌ 本地数据库模式购买记录添加失败")
                return local_result

        except Exception as e:
            print(f"❌ 添加用户购买记录顶层异常: {str(e)}")
            logger.error(f"添加用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'添加购买记录失败: {str(e)}'}

    def delete_user_purchase(self, user_id: str, purchase_id: str) -> Dict[str, Any]:
        """删除用户购买记录"""
        try:
            # 使用API模式
            if self.use_api:
                response = self.api_client.delete(f'/api/admin/v1/users/{user_id}/purchases/{purchase_id}')
                return response
            else:
                # 使用本地数据库
                if not self.db_manager:
                    return {'success': False, 'message': '本地数据库管理器未初始化'}

                result = self.db_manager.delete_user_purchase(user_id, purchase_id)
                return result

        except Exception as e:
            logger.error(f"删除用户购买记录失败: {str(e)}")
            return {'success': False, 'message': f'删除购买记录失败: {str(e)}'}

    def add_user_purchase_local_only(self, user_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """仅在本地添加购买记录 - 🎯 支持系列购买展开"""
        try:
            print(f"🚀 第3层持久化：开始本地数据库购买记录添加")
            if not self.db_manager:
                print(f"❌ 第3层持久化：数据库管理器未初始化")
                return {'success': False, 'message': '本地数据库管理器未初始化'}

            # 🎯 关键修复：处理系列购买展开为多个分类购买
            if purchase_data.get('type') == 'series_all_categories':
                print(f"🎯 第3层持久化：检测到系列购买，开始展开为分类购买记录")
                categories = purchase_data.get('categories', [])
                series_name = purchase_data.get('series_name', '未知系列')
                
                if not categories:
                    print(f"❌ 第3层持久化：系列下没有可购买的分类")
                    return {'success': False, 'message': '系列下没有可购买的分类'}
                
                success_ids = []
                total_amount = 0
                
                for i, category in enumerate(categories):
                    print(f"🔄 第3层持久化：处理分类 {i+1}/{len(categories)}: {category.get('title')}")
                    
                    # 🔍 调试：分析分类数据和购买UUID
                    print(f"    🔍 分类数据调试:")
                    print(f"        分类ID: {category.get('id')}")
                    print(f"        分类标题: {category.get('title')}")
                    print(f"        购买UUID: {category.get('purchase_uuid')}")
                    print(f"        分类ID: {category.get('category_id')}")
                    
                    # 🔧 修复：确保包含前端生成的购买UUID
                    purchase_uuid = category.get('purchase_uuid')
                    if not purchase_uuid:
                        # 🚫 如果前端没有提供购买UUID，这是严重错误
                        raise ValueError(f"前端必须为每个分类购买提供purchase_uuid - 分类: {category.get('title')}")
                    
                    category_purchase = {
                        'id': purchase_uuid,  # 🔧 修复：添加前端生成的购买UUID
                        'type': 'category',
                        'purchased_entity_id': category.get('category_id') or category.get('id'),  # 🔧 修复：优先使用category_id
                        'amount': float(category.get('price', 0)),
                        'series_name': series_name,
                        'category_name': category.get('title', '未知分类'),
                        'status': 'is_active'
                    }
                    
                    # 插入单个分类购买记录
                    result = self._insert_single_purchase_record(user_id, category_purchase)
                    if result and result.get('success'):
                        success_ids.append(result.get('id'))
                        total_amount += category_purchase['amount']
                        print(f"✅ 第3层持久化：分类购买记录插入成功: {category_purchase['category_name']}")
                    else:
                        print(f"❌ 第3层持久化：分类购买记录插入失败: {category_purchase['category_name']}")
                
                if success_ids:
                    print(f"✅ 第3层持久化：系列购买完成，成功添加 {len(success_ids)} 个分类购买记录")
                    return {
                        'success': True,
                        'message': f'系列购买成功：已添加{len(success_ids)}个分类购买记录',
                        'data': {
                            'purchase_ids': success_ids,
                            'categories_count': len(success_ids),
                            'total_amount': total_amount,
                            'series_name': series_name
                        }
                    }
                else:
                    print(f"❌ 第3层持久化：系列购买失败，没有成功插入任何分类购买记录")
                    return {'success': False, 'message': '系列购买失败：没有成功插入任何分类购买记录'}
            else:
                # 处理单个分类购买
                print(f"🔄 第3层持久化：处理单个分类购买")
                return self._insert_single_purchase_record(user_id, purchase_data)

        except Exception as e:
            print(f"❌ 第3层持久化：本地添加购买记录异常: {str(e)}")
            logger.error(f"本地添加购买记录失败: {str(e)}")
            return {'success': False, 'message': f'本地添加失败: {str(e)}'}
    
    def _insert_single_purchase_record(self, user_id: str, purchase_data: Dict[str, Any]) -> Dict[str, Any]:
        """插入单个购买记录到本地数据库"""
        try:
            # 🎯 兼容多种表结构，增强错误处理
            try:
                # 🔑 UUID架构：严格要求管理端提供UUID - 禁止服务端生成临时ID
                purchase_uuid = purchase_data.get('id')
                if not purchase_uuid:
                    raise ValueError("购买记录必须包含ID")
                
                insert_sql = """
                    INSERT INTO user_purchases
                    (id, user_id, purchased_entity_type, purchased_entity_id, series_name, purchase_price, status, purchase_date)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
                """
                
                params = (
                    purchase_uuid,  # UUID架构：使用UUID主键
                    user_id,
                    purchase_data.get('type', 'category'),
                    purchase_data.get('purchased_entity_id'),
                    purchase_data.get('series_name', ''),
                    purchase_data.get('amount', 0),
                    purchase_data.get('status', 'is_active')
                )
                
                
            except Exception as structure_error:
                # 降级到基础字段
                print(f"⚠️ 表结构不匹配，尝试基础字段插入: {structure_error}")
                insert_sql = """
                    INSERT INTO user_purchases
                    (user_id, purchased_entity_type, purchased_entity_id, purchase_price, status)
                    VALUES (%s, %s, %s, %s, %s)
                """
                
                params = (
                    user_id,
                    purchase_data.get('type', 'category'),
                    purchase_data.get('purchased_entity_id'),
                    purchase_data.get('amount', 0),
                    purchase_data.get('status', 'is_active')
                )

            # UUID架构：execute_insert返回影响行数，我们使用预先生成的UUID
            affected_rows = self.db_manager.execute_insert(insert_sql, params)

            if affected_rows > 0:
                # 构建返回数据 - UUID架构：使用预先生成的UUID
                result_data = {
                    'id': purchase_uuid,  # UUID架构：返回生成的UUID
                    'user_id': user_id,
                    'purchased_entity_type': purchase_data.get('type'),
                    'purchased_entity_id': purchase_data.get('purchased_entity_id'),
                    'purchased_entity_name': purchase_data.get('category_name', purchase_data.get('item_name')),
                    'purchase_price': purchase_data.get('amount'),
                    'status': purchase_data.get('status', 'is_active')
                }

                # 更新缓存
                self.update_purchase_cache(user_id, result_data)

                return {
                    'success': True,
                    'message': '购买记录添加成功（本地数据库模式）',
                    'data': result_data,
                    'id': purchase_uuid  # UUID架构：返回生成的UUID
                }
            else:
                return {'success': False, 'message': '本地数据库插入失败'}

        except Exception as e:
            logger.error(f"插入单个购买记录失败: {str(e)}")
            return {'success': False, 'message': f'本地插入失败: {str(e)}'}

    def update_purchase_cache(self, user_id: str, purchase_data: Dict[str, Any]):
        """更新购买记录缓存"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 如果用户购买记录缓存存在，添加新记录
                if user_id in global_data_manager._user_purchases:
                    current_purchases = global_data_manager._user_purchases[user_id]
                    if current_purchases.get('success') and 'data' in current_purchases:
                        current_purchases['data'].append(purchase_data)
                    else:
                        global_data_manager._user_purchases[user_id] = {
                            'success': True,
                            'data': [purchase_data]
                        }
                else:
                    global_data_manager._user_purchases[user_id] = {
                        'success': True,
                        'data': [purchase_data]
                    }

        except Exception as e:
            logger.warning(f"更新购买记录缓存失败: {e}")

    def remove_user_from_cache(self, user_id: str):
        """从全局缓存中移除用户数据"""
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 从用户基本信息缓存中移除
                global_data_manager._user_data = [
                    user for user in global_data_manager._user_data 
                    if not (user.get('user_id') == user_id or 
                           user.get('id') == user_id or
                           str(user.get('id')) == str(user_id) or
                           str(user.get('user_id')) == str(user_id))
                ]
                
                # 从用户详情缓存中移除
                if user_id in global_data_manager._user_details:
                    del global_data_manager._user_details[user_id]
                    
                # 从其他用户相关缓存中移除
                cache_keys = ['_user_purchases', '_user_progress', '_user_favorites', '_user_settings', '_user_cache']
                for cache_key in cache_keys:
                    if hasattr(global_data_manager, cache_key):
                        cache_dict = getattr(global_data_manager, cache_key)
                        if user_id in cache_dict:
                            del cache_dict[user_id]
                            
                logger.info(f"用户缓存已清理: {user_id}")
                
        except Exception as e:
            logger.warning(f"清理用户缓存失败: {e}")
