---
description: 水幕项目专用真实验证架构 - 针对Claude Code易错点的精准纠错指导
version: v2.0
scope: 水幕课程管理系统专用架构
target: PyQt6 + FastAPI + MySQL技术栈
---

# ⚡ 真实验证架构 - Claude Code纠错指南

## 🎯 核心使命
解决Claude Code在水幕项目中的5大核心问题：**伪造function calls、WSL环境误判、推断替代验证、服务状态假设、证据缺失决策**。

## 🚫 第一部分：5大关键禁止行为

### ❌ 禁止1：伪造Function Calls
**最常见错误**：使用import语句替代真实的function calls

```python
# ❌ 绝对禁止这样做
from tools import Glob, Grep, Read
files = Glob(pattern="**/*.py").execute()

# ❌ 绝对禁止这样做
import tools
result = tools.Read(file_path="/path/to/file").run()
```

```xml
<!-- ✅ 正确做法：真实的function calls -->
<function_calls>
<invoke name="Glob">
<parameter name="pattern">**/*.py</parameter>
</invoke>
</function_calls>
```

### ❌ 禁止2：WSL环境越界操作
**项目特定错误**：在WSL中尝试Windows环境特定操作

```bash
# ❌ 绝对禁止在WSL中执行
pip install PyQt6
python src/main.py
python -m pytest

# ❌ 绝对禁止假设
# "依赖应该已安装"
# "Python程序应该能运行"
```

**✅ 正确策略**：通过FastAPI间接验证Windows环境功能
```xml
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s -w "%{http_code}" http://localhost:8000/docs</parameter>
<parameter name="description">间接验证Windows环境中的服务状态</parameter>
</invoke>
</function_calls>
```

### ❌ 禁止3：推断替代验证
**典型错误表达**：
- "通常情况下这种问题是..."
- "根据经验，可能是..."  
- "应该是配置文件的问题..."
- "一般来说数据库连接..."

**✅ 强制要求**：所有结论必须基于工具调用的真实结果

### ❌ 禁止4：服务状态假设
**常见假设**：
- "服务应该在运行"
- "数据库应该可以连接"
- "API端点应该可用"

**✅ 强制验证**：每次都必须真实检查服务状态
```xml
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs</parameter>
<parameter name="description">验证FastAPI服务状态</parameter>
</invoke>
</function_calls>
```

### ❌ 禁止5：证据缺失决策
**禁止在没有证据时**：
- 提出修复方案
- 得出问题结论
- 制定后续计划

**✅ 强制要求**：每个决策都必须有具体的证据支撑

## 🏗️ 第二部分：水幕项目WSL环境操作边界

### ⚡ 水幕项目环境架构

```
Windows主机 (水幕项目实际运行)
├── shuimu-admin/ (PyQt6桌面管理端)
├── mock_server/ (FastAPI后端服务)  
├── app/ (Android移动端)
└── WSL Ubuntu (Claude Code操作环境)
    ✅ 文件编辑: Read/Edit/Write/Glob/Grep
    ✅ 服务验证: curl检查FastAPI状态
    ✅ API测试: HTTP请求验证端点
    ❌ Python运行: 不能执行python src/main.py
    ❌ 依赖安装: 不能pip install
    ❌ GUI测试: 不能测试PyQt6界面
```

### 🔧 水幕项目验证策略

#### FastAPI服务验证标准流程
```xml
<!-- 步骤1: 检查服务状态 -->
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs</parameter>
<parameter name="description">检查FastAPI服务状态</parameter>
</invoke>
</function_calls>

<!-- 步骤2: 获取API文档 -->
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s http://localhost:8000/openapi.json</parameter>
<parameter name="description">获取API端点列表</parameter>
</invoke>
</function_calls>

<!-- 步骤3: 测试关键端点 -->
<function_calls>
<invoke name="Task">
<parameter name="description">测试水幕项目API端点</parameter>
<parameter name="prompt">
基于openapi.json测试以下端点：
1. /api/admin/v1/users (管理端用户接口)
2. /api/v1/courses (课程接口)
3. /api/v1/health (健康检查)
使用curl测试并验证响应格式
</parameter>
</invoke>
</function_calls>
```

#### 数据库连接间接验证
**通过API端点间接确认MySQL连接状态**：
- API正常响应 → 数据库连接正常
- API返回数据 → 数据库查询正常
- API操作成功 → 数据库事务正常

## ⚡ 第三部分：3个强制检查点

### 🔧 检查点1：工具调用规范检查
**执行时机**：每次开始任务前强制检查

```python
def check_tool_calling_compliance():
    """工具调用规范检查"""
    errors = []
    
    # 检查是否有import伪调用
    if "from tools import" in response_text:
        errors.append("❌ 发现import伪调用，必须使用function calls")
    
    # 检查function calls格式
    if "<function_calls>" not in response_text and need_tool_calls:
        errors.append("❌ 缺少必需的function calls")
    
    # 检查并发调用机会
    if multiple_independent_tasks and single_tool_calls:
        errors.append("❌ 错失并发调用机会，应使用单次多工具调用")
    
    return {"passed": len(errors) == 0, "errors": errors}
```

### 🌐 检查点2：服务状态验证检查
**执行时机**：涉及水幕项目功能验证时强制检查

```xml
<!-- 强制执行的服务验证序列 -->
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs</parameter>
<parameter name="description">验证FastAPI服务状态</parameter>
</invoke>
<invoke name="Bash">
<parameter name="command">curl -s http://localhost:8000/openapi.json | head -10</parameter>
<parameter name="description">验证API文档可访问性</parameter>
</invoke>
</function_calls>
```

**门禁条件**：
- HTTP状态码 = 200
- OpenAPI文档可获取
- 至少发现3个可用端点

### 📊 检查点3：证据完整性检查
**执行时机**：得出任何结论前强制检查

```python
def check_evidence_completeness(conclusion, evidence_list):
    """证据完整性检查"""
    requirements = {
        "A级直接证据": lambda e: e["type"] == "direct" and e["tool_verified"],
        "具体数据支撑": lambda e: len(e["raw_data"]) > 0,
        "可重现验证": lambda e: e["reproducible"] == True
    }
    
    for req_name, req_func in requirements.items():
        if not any(req_func(evidence) for evidence in evidence_list):
            return {"passed": False, "missing": req_name}
    
    return {"passed": True}
```

## 🛠️ 第四部分：水幕项目特定验证模式

### 🎯 PyQt6+FastAPI+MySQL验证标准流程

#### 水幕项目结构分析模式
```xml
<!-- 水幕项目标准结构发现 -->
<function_calls>
<invoke name="Glob">
<parameter name="pattern">shuimu-admin/**/*.py</parameter>
</invoke>
<invoke name="Glob">
<parameter name="pattern">mock_server/**/*.py</parameter>
</invoke>
<invoke name="Glob">
<parameter name="pattern">**/*.json</parameter>
</invoke>
<invoke name="Grep">
<parameter name="pattern">FastAPI|PyQt6|SQLAlchemy</parameter>
<parameter name="include">*.py</parameter>
</invoke>
</function_calls>
```

#### 水幕项目API端点测试模式
```xml
<function_calls>
<invoke name="Task">
<parameter name="description">水幕项目API端点测试</parameter>
<parameter name="prompt">
测试水幕项目核心API端点：
1. 管理端API: /api/admin/v1/* (用户管理、课程管理)
2. 移动端API: /api/v1/* (学生接口)
3. 健康检查: /health 或 /docs
4. 数据库相关: 用户表、课程表查询接口

使用curl测试每个端点的HTTP状态码和响应格式
重点验证JSON响应结构和字段完整性
</parameter>
</invoke>
</function_calls>
```

#### 水幕项目配置验证模式
```xml
<function_calls>
<invoke name="Read">
<parameter name="file_path">mock_server/src/database/mysql_manager.py</parameter>
</invoke>
<invoke name="Read">
<parameter name="file_path">shuimu-admin/src/config.json</parameter>
</invoke>
<invoke name="Grep">
<parameter name="pattern">mysql|database|connection</parameter>
<parameter name="include">*.py</parameter>
</invoke>
</function_calls>
```

## 🚨 第五部分：应急纠错机制

### ⚡ 推断行为检测与立即纠正

#### 检测触发词
**发现以下表达时立即纠正**：
- "通常情况下..." → ❌ 立即停止，要求真实验证
- "根据经验..." → ❌ 立即停止，要求证据支撑  
- "应该是..." → ❌ 立即停止，要求具体检查
- "可能是..." → ❌ 立即停止，要求确定性证据
- "一般来说..." → ❌ 立即停止，要求项目特定验证

#### 立即纠正模板
```
🚨 检测到推断行为！

❌ 错误表达: "{detected_inference_phrase}"
✅ 纠正要求: 必须使用以下真实验证替代

<function_calls>
<invoke name="{specific_tool}">
<parameter name="{parameter_name}">{specific_verification}</parameter>
</invoke>
</function_calls>

🎯 要求: 基于工具调用的真实结果重新得出结论
```

### 🔧 验证失败快速恢复

#### 工具调用失败恢复
```python
def handle_tool_call_failure(failed_tool, error_message):
    """工具调用失败快速恢复"""
    recovery_strategies = {
        "Glob": "使用LS工具逐级查找文件",
        "Grep": "使用Read工具逐文件搜索",
        "Bash": "检查命令语法，重新构造请求",
        "Task": "拆解为更简单的Glob+Grep+Read组合"
    }
    
    recovery_action = recovery_strategies.get(failed_tool, "使用替代工具")
    
    return f"""
🔧 工具调用失败恢复：
- 失败工具: {failed_tool}
- 失败原因: {error_message}
- 恢复策略: {recovery_action}
- 立即执行: 使用替代方案重新验证
"""

#### 服务连接失败恢复
```xml
<!-- 服务不可用时的恢复流程 -->
<function_calls>
<invoke name="Read">
<parameter name="file_path">mock_server/src/main.py</parameter>
</invoke>
<invoke name="Grep">
<parameter name="pattern">port|host|uvicorn</parameter>
<parameter name="include">*.py</parameter>
</invoke>
</function_calls>
```

### 📋 检查点失败处理流程

#### 门禁失败立即处理
```python
def handle_gate_failure(failed_gate, failed_conditions):
    """门禁失败立即处理"""
    
    immediate_actions = {
        "工具调用规范检查": [
            "🔧 移除所有import语句",
            "🔧 转换为标准function calls格式", 
            "🔧 添加必需的并发调用"
        ],
        "服务状态验证检查": [
            "🔧 执行curl服务状态检查",
            "🔧 验证API文档可访问性",
            "🔧 测试至少3个核心端点"
        ],
        "证据完整性检查": [
            "🔧 补充A级直接证据",
            "🔧 提供具体验证数据",
            "🔧 确保结论有证据支撑"
        ]
    }
    
    actions = immediate_actions.get(failed_gate, ["🔧 重新执行完整验证"])
    
    return f"""
🚨 门禁失败处理：{failed_gate}

立即执行以下修正措施：
{chr(10).join(actions)}

⚠️ 修正完成前禁止继续向下执行
"""

## 📚 快速参考卡片

### 🔧 水幕项目Function Calls速查

#### 项目结构发现
```xml
<function_calls>
<invoke name="Glob">
<parameter name="pattern">shuimu-admin/**/*.py</parameter>
</invoke>
<invoke name="Glob">
<parameter name="pattern">mock_server/**/*.py</parameter>
</invoke>
</function_calls>
```

#### 服务状态检查
```xml
<function_calls>
<invoke name="Bash">
<parameter name="command">curl -s -w "%{http_code}" http://localhost:8000/docs</parameter>
<parameter name="description">检查FastAPI服务状态</parameter>
</invoke>
</function_calls>
```

#### 数据库相关文件检查
```xml
<function_calls>
<invoke name="Grep">
<parameter name="pattern">mysql|database|SQLAlchemy</parameter>
<parameter name="include">*.py</parameter>
</invoke>
<invoke name="Read">
<parameter name="file_path">mock_server/src/database/mysql_manager.py</parameter>
</invoke>
</function_calls>
```

### ⚠️ 立即禁止表达速查

| ❌ 错误表达 | ✅ 正确做法 |
|-----------|-----------|
| "通常情况下..." | 使用function calls真实检查 |
| "根据经验..." | 提供具体验证证据 |
| "应该是..." | 执行确定性验证 |
| "可能是..." | 获取明确的检查结果 |
| "from tools import..." | 使用标准function calls格式 |

### 🚨 应急纠错检查清单

**开始任务前必须检查**：
- [ ] 是否使用了推断性语言？
- [ ] 是否有import伪调用？
- [ ] 是否需要并发工具调用？
- [ ] 是否验证了服务状态？
- [ ] 是否有具体证据支撑结论？

---

## 🎯 架构总结

**本架构专用于水幕课程管理系统项目，针对Claude Code的5大易错点提供精准纠错指导。**

### 🔥 核心收益
- **消除推断行为**：强制基于真实验证而非经验猜测
- **规范工具调用**：杜绝import伪调用，确保function calls正确使用
- **适配WSL环境**：明确环境边界，避免跨环境误判
- **保证证据完整**：每个决策都有具体证据支撑
- **应急快速纠错**：发现问题立即纠正，避免错误扩散

### 📝 版本信息
- **版本**: v2.0 (精简专用版)
- **适用**: 水幕项目 (PyQt6+FastAPI+MySQL)
- **维护**: 根据项目实践持续优化

🎯 **铭记核心**: 杜绝推断，强制验证，基于证据，消除伪实现！