---
description: 基于 `$ARGUMENTS` 问题描述，按照 严格证据驱动流程-决策树版 执行
version: 09
---

# 🌳 严格证据驱动流程-决策树版

## 📊 主流程决策树

```mermaid
graph TD
    Start[接收问题描述: $ARGUMENTS] --> Phase0[阶段0: 项目全景认知]
    
    %% 阶段0: 项目全景认知 [详细展开]
    Phase0 --> P0_Project[1. 项目识别]
    P0_Project --> P0_ProjCheck{项目结构清楚?}
    P0_ProjCheck -->|否| P0_ScanProject[扫描目录结构<br/>识别主要模块]
    P0_ScanProject --> P0_ProjCheck
    P0_ProjCheck -->|是| P0_Tech[2. 技术栈确认]
    
    P0_Tech --> P0_TechCheck{技术栈明确?}
    P0_TechCheck -->|否| P0_AnalyzeTech[分析文件类型<br/>检查依赖配置<br/>识别框架版本]
    P0_AnalyzeTech --> P0_TechCheck
    P0_TechCheck -->|是| P0_WSL[3. WSL环境确认]
    
    P0_WSL --> P0_WSLCheck{路径映射正确?}
    P0_WSLCheck -->|否| P0_ConfigWSL[确认WSL路径<br/>验证Windows路径<br/>测试路径转换]
    P0_ConfigWSL --> P0_WSLCheck
    P0_WSLCheck -->|是| P0_Business[4. 业务流程理解]
    
    P0_Business --> P0_BizCheck{核心业务清晰?}
    P0_BizCheck -->|否| P0_AnalyzeBiz[识别核心实体<br/>理解业务流程<br/>分析用户场景]
    P0_AnalyzeBiz --> P0_BizCheck
    P0_BizCheck -->|是| P0_DB[5. 数据库结构理解]
    
    P0_DB --> P0_DBCheck{表结构明确?}
    P0_DBCheck -->|否| P0_AnalyzeDB[查看数据库设计<br/>理解表关系<br/>识别关键字段]
    P0_AnalyzeDB --> P0_DBCheck
    P0_DBCheck -->|是| P0_API[6. API接口理解]
    
    P0_API --> P0_APICheck{接口逻辑清楚?}
    P0_APICheck -->|否| P0_AnalyzeAPI[梳理API端点<br/>理解数据流向<br/>识别关键接口]
    P0_AnalyzeAPI --> P0_APICheck
    P0_APICheck -->|是| P0_Config[7. 配置环境理解]
    
    P0_Config --> P0_ConfigCheck{配置完整?}
    P0_ConfigCheck -->|否| P0_CheckConfig[检查配置文件<br/>确认端口设置<br/>验证日志路径]
    P0_CheckConfig --> P0_ConfigCheck
    P0_ConfigCheck -->|是| P0_M17[8. 方法17: 规范提取]
    
    P0_M17 --> P0_NormCheck{规范已加载?}
    P0_NormCheck -->|否| P0_LoadNorm[读取CLAUDE.md<br/>提取相关规范]
    P0_LoadNorm --> P0_NormCheck
    P0_NormCheck -->|是| P0_M3[9. 方法3: 服务验证]
    
    P0_M3 --> P0_ServiceCheck{服务可达?}
    P0_ServiceCheck -->|否| P0_StartService[检查服务状态<br/>必要时启动服务<br/>验证连通性]
    P0_StartService --> P0_ServiceCheck
    P0_ServiceCheck -->|是| P0_Complete{认知建立完整?}
    
    P0_Complete -->|否| P0_Project
    P0_Complete -->|是| Phase1[阶段1: 强制证据收集]
    
    %% 阶段1: 强制证据收集 [防偷懒核心]
    Phase1 --> P1_Force[🔒强制并行执行🔒<br/>不允许选择]
    P1_Force --> P1_M6[方法6: 日志收集<br/>查看所有日志文件]
    P1_Force --> P1_M8[方法8: 环境检查<br/>完整系统状态]
    P1_M6 & P1_M8 --> P1_M7[方法7: 分层症状确认<br/>基于上述结果串行执行]
    
    P1_M7 --> P1_M22[🚨方法22: 证据有效性验证🚨]
    P1_M22 --> P1_Valid{证据通过验证?<br/>完整性✓ 相关性✓ 具体性✓}
    P1_Valid -->|否| P1_Why{缺什么?}
    P1_Why -->|日志不全| P1_MoreLog[扩大日志范围]
    P1_Why -->|环境不清| P1_MoreEnv[深入环境检查]
    P1_Why -->|描述模糊| P1_MoreDetail[要求具体信息]
    P1_MoreLog & P1_MoreEnv & P1_MoreDetail --> P1_Force
    
    P1_Valid -->|是| P1_M19[🚨方法19: 证据驱动分析🚨<br/>禁止推测]
    P1_M19 --> P1_Analysis{分析结果?}
    P1_Analysis -->|无法分析| P1_Force
    P1_Analysis -->|可以分析| Phase2[阶段2: 逐层隔离验证]
    
    %% 阶段2: 逐层隔离验证
    Phase2 --> P2_Parallel[并行验证执行状态]
    P2_Parallel --> P2_Service[方法3: 服务验证<br/>+<br/>方法2: API验证]
    P2_Parallel --> P2_Path[方法9: 路径确认<br/>+<br/>方法10: 数据追踪]
    P2_Service & P2_Path --> P2_Locate{问题层次确定?}
    P2_Locate -->|未确定| P2_M11[方法11: 逐层隔离]
    P2_M11 --> P2_Layer{哪一层?}
    P2_Layer -->|UI层| P2_UI[深入UI分析]
    P2_Layer -->|API层| P2_API[深入API分析]
    P2_Layer -->|DB层| P2_DB[深入DB分析]
    P2_UI & P2_API & P2_DB --> P2_Locate
    P2_Locate -->|已确定| Phase3[阶段3: 根因假设循环]
    
    %% 阶段3: 根因假设循环 [防推理核心]
    Phase3 --> P3_M12[方法12: 单一根因优先]
    P3_M12 --> P3_M13[方法13: 代码逻辑验证<br/>只看执行路径]
    P3_M13 --> P3_Hypothesis[提出根因假设]
    
    P3_Hypothesis --> P3_M20[🚨方法20: 假设验证循环🚨<br/>强制验证]
    P3_M20 --> P3_Design[设计验证方案]
    P3_Design --> P3_Execute[执行验证]
    P3_Execute --> P3_Result{验证结果?}
    P3_Result -->|失败| P3_NewHypo[基于验证结果<br/>提出新假设]
    P3_NewHypo --> P3_M20
    P3_Result -->|部分成功| P3_Refine[细化假设]
    P3_Refine --> P3_M20
    P3_Result -->|完全成功| Phase4[阶段4: 修复实施]
    
    %% 阶段4: 修复实施验证
    Phase4 --> P4_Prepare[并行准备]
    P4_Prepare --> P4_M17[方法17: 规范提取]
    P4_Prepare --> P4_M5[方法5: 功能重复检查]
    P4_M17 & P4_M5 --> P4_Implement[实施修复<br/>基于验证的根因]
    
    P4_Implement --> P4_Verify[🔒强制立即验证🔒]
    P4_Verify --> P4_M4[方法4: 代码生效验证]
    P4_Verify --> P4_M1[方法1: 执行验证]
    P4_Verify --> P4_M2[方法2: API验证]
    P4_M4 & P4_M1 & P4_M2 --> P4_Success{修复成功?}
    
    P4_Success -->|技术失败| P4_Retry[调整实现<br/>重试]
    P4_Retry --> P4_Implement
    P4_Success -->|逻辑失败| P4_M21_Logic[方法21: 失败追溯]
    P4_M21_Logic --> Phase3
    P4_Success -->|根本失败| P4_M21_Root[方法21: 失败追溯]
    P4_M21_Root --> Phase1
    P4_Success -->|成功| Phase5[阶段5: 完整性确认]
    
    %% 阶段5: 完整性确认
    Phase5 --> P5_M14[方法14: 修复效果验证<br/>原问题必须解决]
    P5_M14 --> P5_Parallel[并行完整测试]
    P5_Parallel --> P5_M15[方法15: 功能完整性<br/>不影响其他功能]
    P5_Parallel --> P5_M16[方法16: 根本解决确认<br/>不是临时修复]
    P5_M15 & P5_M16 --> P5_M18[方法18: 合规性验证]
    
    P5_M18 --> P5_Final{全部通过?}
    P5_Final -->|任一失败| P5_M21[方法21: 失败追溯]
    P5_M21 --> P5_Where{问题在哪?}
    P5_Where -->|修复不完整| Phase4
    P5_Where -->|根因错误| Phase3
    P5_Where -->|证据不足| Phase1
    P5_Final -->|全部通过| Success[✅ 诊断修复完成]
```

## 🚨 强制检查点说明

### 检查点1：证据有效性验证（方法22）
```yaml
位置: 阶段1中部
强制要求:
  完整性:
    - 查看了所有相关日志
    - 检查了完整环境状态
    - 时间范围覆盖问题期间
  相关性:
    - 证据与问题直接相关
    - 时间戳匹配
    - 位置匹配
  具体性:
    - 有明确错误信息
    - 有具体数值/代码
    - 无模糊描述
失败后果:
  - 必须重新收集
  - 不允许继续分析
```

### 检查点2：证据驱动分析（方法19）
```yaml
位置: 阶段1末尾
强制要求:
  - 必须基于验证过的证据
  - 禁止"可能是"、"应该是"
  - 禁止经验推测
失败后果:
  - 立即停止
  - 返回证据收集
```

### 检查点3：假设验证循环（方法20）
```yaml
位置: 阶段3核心
强制要求:
  - 每个假设必须验证
  - 设计具体验证方案
  - 等待验证结果
失败后果:
  - 不允许修复
  - 必须重新假设
```

## 💡 快速决策指南

### 失败类型→回退策略
| 失败类型 | 回退目标 | 原因 |
|---------|---------|------|
| 技术性失败 | 阶段4内重试 | 实现问题 |
| 逻辑性失败 | 回到阶段3 | 根因判断错误 |
| 根本性失败 | 回到阶段1 | 证据不足 |
| 环境性失败 | 回到阶段0 | 基础环境问题 |

### 强制执行规则
1. **阶段1必须并行** - 方法6+8同时执行
2. **验证不可跳过** - 所有🚨标记必须执行
3. **失败必须追溯** - 使用方法21分析原因
4. **修复必须验证** - 立即验证不可延迟

## 📋 方法组件库

### 方法1：powershell.exe执行验证
在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作。
- 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
- 流程：执行程序 → 读取日志 → 验证功能效果

### 方法2：API请求验证  
直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。
- 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
- 流程：调用API → 分析响应 → 验证业务逻辑

### 方法3：服务状态验证
检查服务进程的真实运行状态，避免假设服务可用性。
- 命令格式：curl -s http://localhost:[端口]/[健康检查端点] || echo "服务不可用"
- 流程：检查端口 → 验证响应 → 确认服务状态

### 方法4：代码生效验证
确认代码修改已实际应用并被执行，避免假设修改生效。
- 静态验证：文件变更是否存在
- 动态验证：修改的代码路径是否被执行到

### 方法5：功能重复性检查
开发前搜索现有代码库，避免重复实现已存在的功能模块。
- 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数

### 方法6：日志优先症状收集
优先查看错误日志和异常堆栈，获取最直接证据。
- 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述

### 方法7：用户症状分层确认  
将用户描述分为表象→系统→根因三层，逐层收集症状。
- 表象层：用户直接看到的问题
- 系统层：服务和环境层面异常
- 根因层：代码和技术层面原因

### 方法8：系统环境状态检查
并行检查服务状态、配置文件、依赖环境的实际状态。
- 检查维度：服务可用性 | 配置完整性 | 依赖满足性
- 并行策略：多维度同时检查，快速定位环境问题

### 方法9：执行路径反向确认
通过输出特征和日志模式反向定位真实执行的代码路径。
- 反向策略：从输出结果追溯到具体代码位置

### 方法10：数据流完整追踪
追踪数据从输入到输出的完整生命周期，识别异常节点。
- 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
- 异常识别：数据丢失、格式错误、转换失败、存储异常

### 方法11：逐层隔离定位
按系统架构层次逐层隔离问题，避免跨层复杂化分析。
- 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次

### 方法12：单一根因优先
优先查找单一明确的根因，避免多因素复杂化假设。
- 判断原则：一个问题对应一个主要原因
- 排除策略：先验证最直接最简单的可能性

### 方法13：代码逻辑直接验证
基于实际执行路径验证代码逻辑，不分析未执行代码。
- 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支

### 方法14：修复效果实时验证
修复后立即通过相同方法验证问题是否解决。
- 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入

### 方法15：功能完整性测试
验证修复不影响相关功能的正常工作。
- 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程

### 方法16：根本解决确认
确认修复解决了原始根本问题，不是表面修复。
- 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除

### 方法17：规范动态提取应用
根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。
- 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
- 应用原则：只应用相关规范，避免无关约束干扰

### 方法18：修复合规性验证
确保所有修复完全符合项目技术规范和架构原则。
- 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则

### 方法19：证据驱动分析
所有分析必须基于实际收集的证据，禁止无证据推理。
- 分析前提：先收集证据 → 再分析原因
- 禁止行为：基于经验推测 | 基于可能性判断

### 方法20：假设验证循环
每个根因假设都必须通过实际验证才能确认。
- 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定

### 方法21：失败原因追溯
修复失败后必须基于新证据重新分析，不重复原方案。
- 追溯策略：识别失败点 → 收集新证据 → 调整分析方向

### 方法22：证据有效性验证
验证收集的证据是否满足分析要求，确保证据质量合格。
- 验证维度：完整性（覆盖范围） | 相关性（直接关联） | 具体性（明确清晰）
- 失败处理：指出缺失内容 → 返回补充收集 → 禁止不完整分析

## 🎯 执行要点

1. **强制并行** - 阶段1的方法6+8必须同时执行
2. **强制验证** - 所有🚨🔒标记不可跳过
3. **禁止推测** - 没有证据不分析，没有验证不确认
4. **失败必究** - 每次失败都要用方法21追溯原因
5. **一个流程** - 严格按照主流程决策树执行

---
> 版本：09 | 优化：展开阶段0决策树，保持各阶段一致性