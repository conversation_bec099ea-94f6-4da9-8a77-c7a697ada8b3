📋 通用修复流程模板

  阶段1: Strategic Planning (策略规划)
  ├── 问题范围界定
  ├── 修复优先级制定
  ├── 风险评估和兼容性考虑
  └── 修复原则确立

  阶段2: P0 Critical Fixes (关键修复)
  ├── 核心问题识别和修复
  ├── 影响最大的问题优先
  ├── 验证每个修复点
  └── 确保不破坏现有功能

  阶段3: P0 Extended Fixes (扩展修复)
  ├── 相关联问题修复
  ├── 跨模块一致性修复
  ├── 边界案例处理
  └── 级联影响修复

  阶段4: P1 Optimization (优化清理)
  ├── 技术债务清理
  ├── 冗余代码移除
  ├── 简化复杂逻辑
  └── 性能优化

  阶段5: Validation & Report (验证报告)
  ├── 全面一致性验证
  ├── 回归测试确认
  ├── 修复效果量化
  └── 问题解决报告

  🎯 适用场景和具体流程

  1. 代码规范统一问题

  问题类型: 命名规范、代码风格、API设计不一致

  阶段1: 策略规划
  - 明确规范标准（如snake_case vs camelCase）
  - 识别影响范围（数据库、API、前端）
  - 制定向后兼容策略
  - 确定修复优先级（核心模块 > 边缘功能）

  阶段2: P0关键修复
  - 修复核心数据模型
  - 统一主要API接口
  - 修复关键业务逻辑

  阶段3: P0扩展修复
  - 修复API响应格式
  - 统一前端调用接口
  - 修复跨模块引用

  阶段4: P1优化清理
  - 移除转换适配代码
  - 清理过时的映射逻辑
  - 简化复杂的兼容性代码

  阶段5: 验证报告
  - 全项目规范一致性检查
  - 自动化测试验证
  - 性能影响评估

  2. 架构重构问题

  问题类型: 模块耦合、分层不清、职责混乱

  阶段1: 策略规划
  - 设计目标架构
  - 分析现有依赖关系
  - 制定渐进迁移策略
  - 识别核心模块和边缘模块

  阶段2: P0关键修复
  - 重构核心业务模块
  - 建立清晰的接口边界
  - 修复主要的循环依赖

  阶段3: P0扩展修复
  - 重构相关支撑模块
  - 统一数据流向
  - 修复跨层调用问题

  阶段4: P1优化清理
  - 移除过时的适配器
  - 清理冗余的抽象层
  - 优化性能瓶颈

  阶段5: 验证报告
  - 架构一致性验证
  - 性能基准测试
  - 可维护性评估

  3. 安全漏洞修复

  问题类型: 权限控制、数据泄露、注入攻击

  阶段1: 策略规划
  - 评估安全风险等级
  - 制定修复优先级（高危 > 中危 > 低危）
  - 确定测试验证策略
  - 制定发布计划

  阶段2: P0关键修复
  - 修复高危安全漏洞
  - 加强核心权限控制
  - 修复数据泄露点

  阶段3: P0扩展修复
  - 修复中危漏洞
  - 完善输入验证
  - 加强日志监控

  阶段4: P1优化清理
  - 修复低危问题
  - 优化安全机制
  - 清理敏感信息

  阶段5: 验证报告
  - 安全扫描验证
  - 渗透测试确认
  - 合规性检查

  4. 性能优化问题

  问题类型: 响应慢、内存泄露、资源浪费

  阶段1: 策略规划
  - 性能瓶颈分析
  - 优化目标设定
  - 影响范围评估
  - 风险控制计划

  阶段2: P0关键修复
  - 修复关键路径性能问题
  - 优化核心算法
  - 修复内存泄露

  阶段3: P0扩展修复
  - 优化数据库查询
  - 改进缓存策略
  - 优化网络调用

  阶段4: P1优化清理
  - 代码重构优化
  - 资源使用优化
  - 监控告警完善

  阶段5: 验证报告
  - 性能基准测试
  - 负载测试验证
  - 资源使用分析