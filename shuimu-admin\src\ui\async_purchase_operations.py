#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步购买记录操作模块
符合四层同步乐观更新架构
"""

from typing import List, Dict, Any, Tuple
from PyQt6.QtCore import QObject, pyqtSignal, QThread
from PyQt6.QtWidgets import QTableWidget, QMessageBox
import time
from datetime import datetime

# 🎯 新增：删除完成事件信号
class PurchaseDeleteNotifier(QObject):
    """购买记录删除完成通知器"""
    deletion_completed = pyqtSignal(str, list)  # user_id, deleted_items

# 🎯 新增：添加完成事件信号
class PurchaseAddNotifier(QObject):
    """购买记录添加完成通知器"""
    addition_completed = pyqtSignal(str, dict)  # user_id, purchase_data
    
# 全局删除事件通知器实例
purchase_delete_notifier = PurchaseDeleteNotifier()

# 全局添加事件通知器实例
purchase_add_notifier = PurchaseAddNotifier()

class PurchaseDeleteWorker(QObject):
    """购买记录删除工作线程"""
    # 信号定义
    progress = pyqtSignal(int, int)  # completed, total
    single_completed = pyqtSignal(str, bool, str)  # purchase_id, success, message
    all_completed = pyqtSignal(int, int, list)  # success_count, failed_count, failed_ids
    error_occurred = pyqtSignal(str)  # error_message
    
    def __init__(self, user_service, user_id: str, purchase_ids: List[str]):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
        self.purchase_ids = purchase_ids
        self._is_running = True
        
    def run(self):
        """执行删除操作"""
        success_count = 0
        failed_ids = []
        total = len(self.purchase_ids)
        
        for i, purchase_id in enumerate(self.purchase_ids):
            if not self._is_running:
                break
                
            try:
                # 调用API删除
                result = self.user_service.delete_user_purchase(self.user_id, purchase_id)
                
                if result and result.get('success'):
                    success_count += 1
                    self.single_completed.emit(purchase_id, True, "删除成功")
                else:
                    failed_ids.append(purchase_id)
                    error_msg = result.get('message', '删除失败') if result else '删除失败'
                    self.single_completed.emit(purchase_id, False, error_msg)
                    
            except Exception as e:
                failed_ids.append(purchase_id)
                self.single_completed.emit(purchase_id, False, str(e))
                
            # 发送进度
            self.progress.emit(i + 1, total)
            
        # 发送完成信号
        self.all_completed.emit(success_count, len(failed_ids), failed_ids)
        
    def stop(self):
        """停止工作"""
        self._is_running = False


class CacheUpdateWorker(QObject):
    """缓存更新工作线程"""
    completed = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, cache_manager, operation: str, data: Dict[str, Any]):
        super().__init__()
        self.cache_manager = cache_manager
        self.operation = operation
        self.data = data
        
    def run(self):
        """执行缓存更新"""
        try:
            # 模拟缓存更新
            print(f"🔄 第3层：更新缓存 - {self.operation}")
            
            # 更新全局数据管理器的缓存
            from cache.global_data_manager import global_data_manager
            if global_data_manager.is_data_loaded():
                # 🎯 修复：使用正确的缓存结构和键名
                user_id = self.data.get('user_id')
                if user_id:
                    # 清除用户购买记录缓存，强制下次重新加载
                    if hasattr(global_data_manager, '_user_purchases'):
                        global_data_manager._user_purchases.pop(user_id, None)
                        print(f"✅ 已清除用户购买记录缓存: {user_id}")
                    
                    # 🎯 修复：同时清除其他相关的用户缓存
                    if hasattr(global_data_manager, '_user_cache'):
                        global_data_manager._user_cache.pop(user_id, None)
                        print(f"✅ 已清除用户缓存: {user_id}")
                
                # 🎯 新增：处理删除状态，清理待同步管理器
                if self.operation == 'update' and self.data.get('operation') == 'batch_delete':
                    deleted_items = self.data.get('deleted_items', [])
                    if deleted_items:
                        self._handle_purchases_deletion(deleted_items)
                        
                        # 🎯 新增：发送删除完成信号，通知其他组件
                        user_id = self.data.get('user_id')
                        if user_id:
                            purchase_delete_notifier.deletion_completed.emit(user_id, deleted_items)
                            print(f"📢 [删除完成通知] 发送删除完成信号: 用户 {user_id}, {len(deleted_items)} 个项目")
                    
            self.completed.emit(True, "缓存更新成功")
        except Exception as e:
            self.completed.emit(False, f"缓存更新失败: {str(e)}")
    
    def _handle_purchases_deletion(self, deleted_items: List[Dict]):
        """处理购买记录删除后的状态清理 - 🎯 新增功能"""
        try:
            from ui.pending_purchase_manager import pending_purchase_manager
            
            for item in deleted_items:
                user_id = item.get('user_id')
                category_id = item.get('category_id')
                series_id = item.get('series_id')
                
                # 清理待同步管理器中的相关状态
                if user_id and category_id:
                    pending_purchase_manager.clear_pending_purchase(user_id, category_id)
                    print(f"🧹 [状态清理] 清除分类待同步状态: {category_id}")
                
                if user_id and series_id:
                    pending_purchase_manager.clear_pending_series(user_id, series_id)
                    print(f"🧹 [状态清理] 清除系列待同步状态: {series_id}")
            
            print(f"✅ [状态清理] 完成 {len(deleted_items)} 个项目的状态清理")
            
        except Exception as e:
            print(f"❌ [状态清理] 处理删除状态时发生异常: {e}")


class AsyncPurchaseOperations:
    """异步购买记录操作管理器 - 符合四层架构"""
    
    def __init__(self, parent_window):
        self.parent_window = parent_window
        self.user_service = parent_window.user_service
        self.user_id = parent_window.user_id
        
        # 工作线程管理 - 🎯 修复：添加重试线程管理，防止死锁
        self.delete_thread = None
        self.cache_thread = None
        self.retry_thread = None  # 🎯 新增：重试线程实例变量
        
        # 🎯 新增：线程清理标志
        self._is_shutting_down = False
        
        # 🎯 新增：删除状态捕获器
        self.deleted_items_cache = []  # 存储被删除的项目信息
        
    def delete_purchases_async(self, selected_rows: List[int], 
                             purchases_table: QTableWidget,
                             get_row_data_func,
                             rollback_func):
        """异步删除购买记录 - 符合四层架构"""
        
        # 获取要删除的数据
        purchase_ids = []
        backup_rows = []
        
        for row in selected_rows:
            row_data = get_row_data_func(row)
            purchase_ids.append(row_data.get('id', f'row_{row}'))
            backup_rows.append((row, row_data))
            
        # 🎯 新增：捕获删除项目信息用于后续状态同步
        self._capture_deleted_items(backup_rows)
            
        count = len(selected_rows)
        print(f"🚀 开始四层乐观删除：准备删除 {count} 条购买记录")
        
        # 🎯 第1层：UI层立即删除
        for row in sorted(selected_rows, reverse=True):
            purchases_table.removeRow(row)
        print(f"✅ 第1层UI成功：立即删除 {count} 条记录")
        
        # 🎯 第2层：启动后台线程进行API调用
        self.delete_thread = QThread()
        self.delete_worker = PurchaseDeleteWorker(
            self.user_service, 
            self.user_id, 
            purchase_ids
        )
        
        # 将worker移到线程
        self.delete_worker.moveToThread(self.delete_thread)
        
        # 连接信号
        self.delete_thread.started.connect(self.delete_worker.run)
        
        # API完成后的处理
        def on_api_completed(success_count, failed_count, failed_ids):
            print(f"📊 第2层API完成：成功{success_count}条，失败{failed_count}条")
            
            if failed_count == 0:
                print("✅ 第2层API成功：所有购买记录已从服务器删除")
                
                # 🎯 第3层：启动缓存更新（并行）
                self._update_cache_async({
                    'operation': 'batch_delete',
                    'user_id': self.user_id,
                    'deleted_ids': purchase_ids,
                    'deleted_items': self.deleted_items_cache,  # 🎯 新增：传递删除项目信息
                    'created_at': datetime.now().isoformat()
                })
                
            else:
                # 🎯 第4层：部分失败，执行回滚
                print(f"❌ 第4层补偿：{failed_count}条删除失败，执行回滚")
                
                # 过滤出需要回滚的行
                failed_backup_rows = [
                    (row, data) for row, data in backup_rows 
                    if data.get('id') in failed_ids
                ]
                
                # 执行回滚
                rollback_func(failed_backup_rows)
                
                # 显示错误信息
                QMessageBox.warning(
                    self.parent_window, 
                    '删除失败', 
                    f'部分删除失败，已回滚。\n成功 {success_count} 条，失败 {failed_count} 条'
                )
                
                # 🎯 第4层：3秒后自动重试失败的项目
                if hasattr(self, 'retry_timer'):
                    self.retry_timer.stop()
                    
                from PyQt6.QtCore import QTimer
                self.retry_timer = QTimer()
                self.retry_timer.timeout.connect(
                    lambda: self._retry_failed_deletions(failed_ids, failed_backup_rows)
                )
                self.retry_timer.setSingleShot(True)
                self.retry_timer.start(3000)  # 3秒后重试
                print("⏰ 已安排3秒后重试失败的删除操作")
        
        self.delete_worker.all_completed.connect(on_api_completed)
        
        # 清理线程
        self.delete_worker.all_completed.connect(self.delete_thread.quit)
        self.delete_thread.finished.connect(self.delete_thread.deleteLater)
        self.delete_worker.all_completed.connect(self.delete_worker.deleteLater)
        
        # 启动线程
        self.delete_thread.start()
        print("🚀 第2层API调用已在后台线程启动，不阻塞UI")
        
    def _update_cache_async(self, data: Dict[str, Any]):
        """异步更新缓存 - 第3层"""
        self.cache_thread = QThread()
        self.cache_worker = CacheUpdateWorker(None, "update", data)
        
        self.cache_worker.moveToThread(self.cache_thread)
        
        self.cache_thread.started.connect(self.cache_worker.run)
        self.cache_worker.completed.connect(
            lambda success, msg: print(f"{'✅' if success else '❌'} 第3层缓存：{msg}")
        )
        
        # 清理线程
        self.cache_worker.completed.connect(self.cache_thread.quit)
        self.cache_thread.finished.connect(self.cache_thread.deleteLater)
        self.cache_worker.completed.connect(self.cache_worker.deleteLater)
        
        self.cache_thread.start()
        
    def _retry_failed_deletions(self, failed_ids: List[str], failed_backup_rows: List[Tuple]):
        """重试失败的删除操作 - 第4层补偿机制"""
        print(f"🔄 第4层重试：尝试重新删除 {len(failed_ids)} 条失败的记录")
        
        # 🎯 修复：检查是否正在关闭，避免死锁
        if self._is_shutting_down:
            print("⚠️ 正在关闭中，跳过重试操作")
            return
        
        # 🎯 修复：清理旧的重试线程
        if self.retry_thread and self.retry_thread.isRunning():
            print("🔄 停止之前的重试线程...")
            self.retry_thread.quit()
            self.retry_thread.wait(2000)  # 等待最多2秒
        
        # 🎯 修复：使用实例变量管理重试线程
        self.retry_thread = QThread()
        retry_worker = PurchaseDeleteWorker(
            self.user_service,
            self.user_id,
            failed_ids
        )
        
        retry_worker.moveToThread(self.retry_thread)
        self.retry_thread.started.connect(retry_worker.run)
        
        def on_retry_completed(success_count, failed_count, still_failed_ids):
            if failed_count == 0:
                print("✅ 第4层重试成功：所有失败的记录已被删除")
            else:
                print(f"❌ 第4层重试完成：仍有 {failed_count} 条记录删除失败")
                # 这里可以选择是否继续重试或放弃
                
        retry_worker.all_completed.connect(on_retry_completed)
        
        # 🎯 修复：使用实例变量进行线程清理
        retry_worker.all_completed.connect(self.retry_thread.quit)
        self.retry_thread.finished.connect(self.retry_thread.deleteLater)
        retry_worker.all_completed.connect(retry_worker.deleteLater)
        
        self.retry_thread.start()
    
    def cleanup_all_threads(self):
        """清理所有线程 - 🎯 修复：防止线程死锁，确保正确关闭"""
        print("🔄 开始清理所有异步操作线程...")
        self._is_shutting_down = True
        
        threads_to_cleanup = [
            ("删除线程", self.delete_thread),
            ("缓存线程", self.cache_thread), 
            ("重试线程", self.retry_thread)
        ]
        
        for thread_name, thread in threads_to_cleanup:
            if thread and thread.isRunning():
                print(f"🛑 停止{thread_name}...")
                try:
                    thread.quit()
                    if not thread.wait(3000):  # 等待最多3秒
                        print(f"⚠️ {thread_name}无法正常退出，强制终止")
                        thread.terminate()
                        thread.wait(1000)  # 再等待1秒
                    print(f"✅ {thread_name}已停止")
                except Exception as e:
                    print(f"❌ 停止{thread_name}时发生异常: {e}")
        
        print("✅ 所有线程清理完成")
    
    def _capture_deleted_items(self, backup_rows: List[Tuple]):
        """捕获被删除的购买记录信息 - 🎯 新增功能"""
        self.deleted_items_cache = []
        
        for row, data in backup_rows:
            # 解析购买记录数据，提取系列和分类信息
            item_info = {
                'purchase_id': data.get('id', ''),
                'category_name': data.get('category_name', ''),
                'series_name': data.get('series_name', ''),
                'purchase_type': data.get('purchase_type', ''),
                'amount': data.get('amount', 0),
                'user_id': self.user_id,
                'created_at': datetime.now().isoformat()
            }
            
            # 🎯 关键信息：提取分类ID和系列ID用于状态同步
            # 从数据库查询对应的分类和系列ID
            try:
                # 通过分类名称查找分类ID
                from cache.global_data_manager import global_data_manager
                if global_data_manager.is_data_loaded():
                    # 从全局数据中查找对应的分类和系列ID
                    category_result = global_data_manager.get_category_list(page=1, page_size=1000)
                    if category_result and category_result.get('success'):
                        categories = category_result.get('data', [])
                        for category in categories:
                            if category.get('title') == item_info['category_name']:
                                item_info['category_id'] = category.get('id')
                                item_info['series_id'] = category.get('series_id')
                                break
                    
                    # 如果没有找到，从系列数据中查找
                    if 'series_id' not in item_info:
                        series_result = global_data_manager.get_series_list(page=1, page_size=1000)
                        if series_result and series_result.get('success'):
                            series_list = series_result.get('data', [])
                            for series in series_list:
                                if series.get('title') == item_info['series_name']:
                                    item_info['series_id'] = series.get('id')
                                    break
                                    
            except Exception as e:
                print(f"⚠️ [删除状态捕获] 查找分类/系列ID失败: {e}")
            
            self.deleted_items_cache.append(item_info)
            print(f"📝 [删除状态捕获] 记录删除项目: {item_info['category_name']} ({item_info['series_name']})")
        
        print(f"✅ [删除状态捕获] 共捕获 {len(self.deleted_items_cache)} 个删除项目")