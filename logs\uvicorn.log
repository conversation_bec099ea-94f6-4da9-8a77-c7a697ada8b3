INFO:     127.0.0.1:44976 - "GET /health HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:45812 - "GET / HTTP/1.1" 200 OK
INFO:     127.0.0.1:47820 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:12684 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-user-purchase-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12697 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类21
    完整分类数据结构: {'id': 'ab4f644e-1303-4666-8e64-1df758080b68', 'series_id': 'afcf7e5d-ae18-4215-9936-2415d05c6457', 'title': '分类21', 'description': '', 'price': 121.0, 'order_index': 1, 'created_at': '2025-07-08T07:47:27', 'updated_at': '2025-07-08T07:47:27', 'series_title': '系列211', 'series_published': True, 'video_count': 2, 'videos': [{'id': 'b3e5e9c3-3b68-4978-b51b-9f1701293f3e', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': 'b6816ef3-5df7-418e-9661-c0024340623c'}, {'id': 'd5731a67-9672-4c7c-88b6-602fa51e9f3b', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': 'b6816ef3-5df7-418e-9661-c0024340623c'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': 'b6816ef3-5df7-418e-9661-c0024340623c', 'purchase_uuid': 'ab4f644e-1303-4666-8e64-1df758080b68', 'original_category_id': 'b6816ef3-5df7-418e-9661-c0024340623c'}
    🔑 提取的purchase_uuid: ab4f644e-1303-4666-8e64-1df758080b68
    📄 category.get('id'): ab4f644e-1303-4666-8e64-1df758080b68
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: b6816ef3-5df7-418e-9661-c0024340623c
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: b6816ef3-5df7-418e-9661-c0024340623c
    🏷️  id: ab4f644e-1303-4666-8e64-1df758080b68
    🏷️  purchase_uuid: ab4f644e-1303-4666-8e64-1df758080b68
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: b6816ef3-5df7-418e-9661-c0024340623c
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: b6816ef3-5df7-418e-9661-c0024340623c
    4️⃣ id (FALLBACK): ab4f644e-1303-4666-8e64-1df758080b68
[FINAL_RESULT] 最终选择的category_id: b6816ef3-5df7-418e-9661-c0024340623c
[FINAL_RESULT] 购买UUID: ab4f644e-1303-4666-8e64-1df758080b68
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: ab4f644e-1303-4666-8e64-1df758080b68
[RETRY] UUID架构：插入分类购买记录 - 分类21 (购买UUID: ab4f644e-1303-4666-8e64-1df758080b68, 分类ID: b6816ef3-5df7-418e-9661-c0024340623c, 价格: ¥121.0)
INFO:     127.0.0.1:12709 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12741 - "DELETE /api/admin/v1/users/84205007-33d2-4983-bfab-2e3203675176 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12741 - "DELETE /api/admin/v1/users/test-user-purchase-001 HTTP/1.1" 200 OK
🔍 [DETAILED_DEBUG] 处理分类1数据:
    分类标题: 分类20
    完整分类数据结构: {'id': '39bd41d8-2b3b-447c-b659-f752427b1f4a', 'series_id': 'acde052c-8641-4bd9-b30a-7db0e07e66eb', 'title': '分类20', 'description': '', 'price': 120.0, 'order_index': 1, 'created_at': '2025-07-08T05:33:19', 'updated_at': '2025-07-08T05:33:19', 'series_title': '系列201', 'series_published': True, 'video_count': 2, 'videos': [{'id': '3b6e9b75-d5ac-4560-88ee-c443cd59e02a', 'title': 'dao_02', 'description': '批量导入 - dao_02', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_02.mp4', 'localPath': '', 'playCount': 0, 'category_id': '5a7129d8-913b-4bb9-b13a-87637ecc0d99'}, {'id': '3f44cd56-40b6-4ae2-afe0-096b0ff05a19', 'title': 'dao_01', 'description': '批量导入 - dao_01', 'duration': 0, 'cloudUrl': 'https://vip.123pan.cn/1822199090/test_mock/道：恋爱宝典系列/恋爱宝典1/dao_01.mp4', 'localPath': '', 'playCount': 0, 'category_id': '5a7129d8-913b-4bb9-b13a-87637ecc0d99'}], 'is_free': False, 'isPurchased': False, 'defaultExpanded': False, 'totalVideos': 2, 'progress': 0.0, 'watchCount': 0, 'category_id': '5a7129d8-913b-4bb9-b13a-87637ecc0d99', 'purchase_uuid': '39bd41d8-2b3b-447c-b659-f752427b1f4a', 'original_category_id': '5a7129d8-913b-4bb9-b13a-87637ecc0d99'}
    🔑 提取的purchase_uuid: 39bd41d8-2b3b-447c-b659-f752427b1f4a
    📄 category.get('id'): 39bd41d8-2b3b-447c-b659-f752427b1f4a
    ⚠️  两者是否相同: True
[FIELD_ANALYSIS] 分析分类数据所有ID字段:
    🏷️  category_id: 5a7129d8-913b-4bb9-b13a-87637ecc0d99
    🏷️  purchased_entity_id: NOT_FOUND
    🏷️  original_category_id: 5a7129d8-913b-4bb9-b13a-87637ecc0d99
    🏷️  id: 39bd41d8-2b3b-447c-b659-f752427b1f4a
    🏷️  purchase_uuid: 39bd41d8-2b3b-447c-b659-f752427b1f4a
[FALLBACK_ANALYSIS] ID字段优先级检查:
    1️⃣ category_id: 5a7129d8-913b-4bb9-b13a-87637ecc0d99
    2️⃣ purchased_entity_id: None
    3️⃣ original_category_id: 5a7129d8-913b-4bb9-b13a-87637ecc0d99
    4️⃣ id (FALLBACK): 39bd41d8-2b3b-447c-b659-f752427b1f4a
[FINAL_RESULT] 最终选择的category_id: 5a7129d8-913b-4bb9-b13a-87637ecc0d99
[FINAL_RESULT] 购买UUID: 39bd41d8-2b3b-447c-b659-f752427b1f4a
[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: False
🔑 UUID架构：管理端生成购买记录UUID: 39bd41d8-2b3b-447c-b659-f752427b1f4a
[RETRY] UUID架构：插入分类购买记录 - 分类20 (购买UUID: 39bd41d8-2b3b-447c-b659-f752427b1f4a, 分类ID: 5a7129d8-913b-4bb9-b13a-87637ecc0d99, 价格: ¥120.0)
INFO:     127.0.0.1:12769 - "POST /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12780 - "POST /api/admin/v1/logs/clear HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/series?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/categories?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/videos?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users?page=1&page_size=100 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-api-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-plaintext-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-001/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-002/cache HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003 HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003/purchases HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003/progress HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003/favorites HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003/settings HTTP/1.1" 200 OK
INFO:     127.0.0.1:12793 - "GET /api/admin/v1/users/test-batch-003/cache HTTP/1.1" 200 OK
