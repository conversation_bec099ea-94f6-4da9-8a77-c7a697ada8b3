description: 基于 `$ARGUMENTS` 按照 完整问题诊断与修复流程 v1.2 执行

# 🔍 完整问题诊断与修复流程 v1.2

## 🎯 核心目标

**解决痛点**：
- AI容易在信息不完整时急于下结论，导致误判根因
- AI容易基于假设而非证据进行分析，导致多轮修复失败
- AI容易忽略数据流完整性和执行路径验证，导致分析错误代码
- ⭐ **v1.2新增**：AI容易症状与根因混淆，缺少系统性检查，修复验证不闭环

**设计原则**：
- ✅ **证据驱动**：所有分析必须基于实际代码、数据流、执行路径等具体证据
- ✅ **数据流追踪**：从前端输入到后端存储的完整数据生命周期分析
- ✅ **执行路径验证**：确认代码真正执行的路径，避免分析错误代码
- ✅ **双强制循环机制**：第一阶段至少2轮分析循环+第二阶段至少2轮修复循环
- ⭐ **v1.2新增**：**五大通用原则**：症状分层、证据优先、假设验证、系统检查、修复闭环

**核心诊断方法论**：
```
🎯 症状分层 → 📊 证据优先 → 🔍 假设验证 → 🛠️ 系统检查 → ✅ 修复闭环
```

---

## 🎯 v1.2核心升级：五大通用原则

### **原则1：症状分层原则**
**目标**：建立表象→中层→根因的递进挖掘框架

#### **分层结构**
- **表象层症状**：用户可见的错误现象、界面异常、功能失效
- **中层症状**：系统级异常表现、服务错误、配置问题、环境异常  
- **根因症状**：技术层面的确切原因、代码逻辑错误、数据流异常

#### **递进规则**
- **禁止跳层**：不能从表象症状直接假设根因，必须逐层深入
- **完整覆盖**：每一层的症状收集必须完整，不能遗漏关键信息
- **层次验证**：每一层的症状分析必须有具体证据支撑

#### **应用示例**
```
❌ 错误：批处理文件启动不了 → 直接假设工作目录问题
✅ 正确：批处理文件启动不了 → 查看错误日志 → Python依赖缺失 → 缺少psutil包
```

### **原则2：最直接证据优先原则**
**目标**：日志、异常、错误信息优先于推测分析

#### **证据等级排序**
1. **一级证据**：错误日志、异常堆栈、具体错误信息
2. **二级证据**：调试输出、状态信息、配置内容
3. **三级证据**：代码逻辑分析、数据流推理
4. **禁用证据**：未验证假设、理论推测、"应该"判断

#### **强制优先级机制**
- **必须先查看**：所有可用的一级证据
- **然后查看**：相关的二级证据  
- **最后分析**：三级证据和逻辑推理
- **严格禁止**：基于假设开始修复

#### **应用示例**
```
✅ 正确流程：问题报告 → 立即查看admin.log → 发现ModuleNotFoundError → 定位psutil缺失
❌ 错误流程：问题报告 → 推测工作目录问题 → 修复目录 → 发现还有问题 → 才查看日志
```

### **原则3：假设识别与验证原则**
**目标**：显性化隐含假设并建立主动验证机制

#### **假设识别技巧**
- **语言标识**：识别"应该"、"可能"、"估计"、"通常"等假设性表述
- **逻辑标识**：识别缺少证据支撑的推理链条
- **经验标识**：识别基于经验而非当前证据的判断

#### **假设标记系统**
- **[假设]**：明确标记所有假设性判断
- **[验证]**：为每个假设设计具体验证步骤
- **[确认]**：记录假设验证的结果

#### **验证机制**
- **具体验证**：每个假设必须有可操作的验证步骤
- **证据验证**：验证结果必须基于具体证据
- **结果记录**：验证结果必须明确记录

#### **应用示例**
```
❌ 隐性假设：批处理文件启动不了，应该是工作目录问题
✅ 显性处理：
[假设] 可能是工作目录问题
[验证] 检查批处理文件中的路径设置和当前工作目录
[确认] 工作目录设置正确，此假设不成立，需要查看其他证据
```

### **原则4：主动完整性检查原则**
**目标**：系统性发现问题而非被动等待错误暴露

#### **系统性检查框架**
- **环境完整性**：系统环境、运行环境、依赖环境的系统检查
- **配置完整性**：配置文件、参数设置、路径配置的系统检查
- **代码完整性**：代码逻辑、数据流、执行路径的系统检查
- **数据完整性**：数据结构、字段映射、数据一致性的系统检查

#### **并行检查策略**
- **多源并查**：同时检查多个可能的问题源
- **全面覆盖**：确保检查覆盖所有关键环节
- **优先排序**：按照问题可能性排序检查项目

#### **检查清单模式**
- **标准清单**：针对常见问题类型建立标准检查清单
- **定制清单**：根据具体问题特征定制专项检查清单
- **完整记录**：记录每个检查项目的检查结果

#### **应用示例**
```
✅ 系统性检查：
□ 检查错误日志文件
□ 检查虚拟环境状态  
□ 检查依赖包完整性
□ 检查Python版本兼容性
□ 检查配置文件完整性
□ 检查路径和权限设置
```

### **原则5：修复闭环验证原则**
**目标**：每轮修复后的强制确认和根本问题解决验证

#### **修复验证层次**
- **修复验证**：验证当前修复是否解决了预期的问题
- **功能验证**：验证修复后的功能是否正常工作
- **根本验证**：验证是否解决了原始的根本问题
- **完整验证**：验证修复没有引入新的问题

#### **强制验证机制**
- **即时验证**：每次修复后立即进行验证，不能延后
- **具体验证**：验证必须基于具体操作和具体结果
- **完整验证**：验证必须覆盖修复的所有影响范围
- **结果记录**：验证结果必须详细记录

#### **闭环确认流程**
1. **修复实施** → 2. **即时验证** → 3. **功能确认** → 4. **根本确认** → 5. **完整确认**

#### **应用示例**
```
✅ 修复闭环：
1. 修复：添加缺失的psutil依赖
2. 即时验证：pip install成功
3. 功能确认：Python应用能正常启动
4. 根本确认：解决了原始的"批处理文件启动不了"问题
5. 完整确认：没有引入新的依赖冲突或其他问题
```

---

## 📋 阶段0：基础建立（保持v1.1内容）

### **步骤0.1：数据流架构识别**

**目标**：`项目结构 + 数据流识别 + 执行路径映射 → 证据收集基础`

#### **核心步骤**
- **技术栈识别**：框架类型 + 数据库类型 + API架构 → 数据流特征
- **关键路径映射**：前端→API→服务→数据库的完整数据流路径
- **执行环境识别**：热加载机制 + 服务启动方式 + 调试能力 → 验证策略
- **日志系统定位**：日志文件位置 + 日志格式 + 调试信息输出方式

#### **v1.2增强**
- ⭐ **应用原则2**：优先查看最直接的配置和日志证据
- ⭐ **应用原则4**：系统性检查环境、配置、依赖完整性

#### **完成标准**
- [ ] 数据流架构清晰（前端→后端→数据库）
- [ ] 关键执行路径已映射
- [ ] 日志系统和调试方式已确定
- [ ] ⭐ 环境和依赖完整性已验证
- [ ] 为症状收集提供技术基础

### **步骤0.2：执行路径验证能力建立**

**目标**：`WSL环境 + 服务连通 + 执行验证 → 证据收集能力`

#### **WSL环境能力重点**

```
证据收集能力边界
├── ✅ 代码分析：Read/Grep/Glob工具深度分析
├── ✅ 日志追踪：实时日志查看和模式匹配  
├── ✅ 数据流验证：curl + API测试验证数据流，端点：curl -s http://localhost:8000/docs、curl -s http://localhost:8000/openapi.json
├── ✅ 执行路径确认：通过日志模式确认真正执行的代码
└── ❌ 不能直接运行：假设Windows环境依赖已安装
```

#### **v1.2增强**
- ⭐ **应用原则3**：明确标记环境限制的假设并设计验证方案
- ⭐ **应用原则4**：建立系统性的执行路径验证清单

#### **完成标准**
- [ ] 服务连接正常（能获取API响应）
- [ ] 日志追踪能力确认
- [ ] 执行路径验证方法建立
- [ ] ⭐ 环境限制假设已明确标记
- [ ] 为深度分析提供工具基础

### **步骤0.3：症状完整性收集**

**目标**：`用户描述 + 系统表现 + 数据异常 → 完整症状清单`

#### **v1.2重点增强：三层症状收集**

**层次1：表象症状收集**
- 用户可见现象：具体操作步骤、触发条件、错误表现
- 界面异常：显示错误、功能失效、交互异常
- 系统行为：启动失败、运行中断、响应异常

**层次2：中层症状收集**
- 系统级异常：服务错误、配置问题、环境异常
- 数据流异常：API错误、数据传输问题、状态异常
- 服务响应：错误代码、异常信息、日志记录

**层次3：根因症状收集**
- 技术层面：代码逻辑错误、数据结构问题、依赖问题
- 执行路径：代码未执行、分支错误、调用异常
- 数据完整性：字段错误、类型不匹配、一致性问题

#### **v1.2核心要求**
- ⭐ **应用原则1**：严格按照三层进行症状收集，不能跳层
- ⭐ **应用原则2**：优先收集最直接的错误证据（日志、异常等）

#### **完成标准**
- [ ] 三层症状收集完整，层次清晰
- [ ] 最直接证据优先收集
- [ ] 具体异常现象记录清晰
- [ ] 为数据流追踪提供问题线索
- [ ] 症状优先级排序完成

**阶段总结**：建立证据驱动的分析基础，应用v1.2五大原则，为第一阶段的深度诊断提供完整的技术基础和症状线索。

---

## 📋 第一阶段：证据驱动的根因诊断循环

**总目标**：`基于阶段0基础 → 证据驱动的根因定位`

## 🔄 强制循环控制器（第一阶段）

**循环要求**：
- **最少循环数**：强制执行至少2轮循环（无任何例外）
- **退出条件**：连续两轮循环无实质性新证据发现
- **最大限制**：5轮循环上限（防止无限循环）

**v1.2增强判断标准**：
- **✅ 实质性新证据**：新执行路径发现、新数据流异常、新字段映射问题、新代码逻辑错误（需有具体证据支撑）
- **❌ 非实质性信息**：重复已知信息、理论推测内容、未验证假设、常识性解释
- ⭐ **新增要求**：每轮循环必须应用v1.2五大原则

## 🔄 标准循环单元（第一阶段）

**每轮循环 = E1症状证据收集 → E2执行路径验证 → E3数据流完整追踪 → E4问题逐层隔离**

### **循环步骤E1：症状证据收集**

**目标**：`前轮发现 + 新症状线索 + 证据扩展 → 完整证据清单`

#### **v1.2重点增强**
- ⭐ **强制应用原则1**：症状分层收集，逐层深入
- ⭐ **强制应用原则2**：优先收集最直接证据（错误日志、异常堆栈）
- ⭐ **强制应用原则3**：识别并标记症状分析中的假设

#### **核心步骤**
- **前轮证据整合**：整合前轮循环发现的所有具体证据
- **分层症状追踪**：按照表象→中层→根因层次追踪症状
- **直接证据优先**：优先收集错误日志、异常信息等直接证据
- **假设识别标记**：标记症状分析中的假设性判断

#### **完成标准**
- [ ] 前轮证据完整整合
- [ ] 症状分层收集完成
- [ ] 最直接证据优先获取
- [ ] 假设性判断已明确标记
- [ ] 为执行路径验证提供线索

### **循环步骤E2：执行路径验证**

**目标**：`症状证据 + 代码分析 + 日志追踪 → 真实执行路径确认`

#### **v1.2重点增强**
- ⭐ **强制应用原则3**：明确标记路径分析中的假设
- ⭐ **强制应用原则4**：系统性检查所有可能的执行路径

#### **核心步骤**
- **日志反向追踪**：从错误日志反向找到产生日志的具体代码位置
- **系统性路径检查**：检查所有可能的代码执行路径
- **假设验证执行**：对路径分析中的假设进行具体验证
- **执行路径确认**：基于具体证据确认真实执行路径

#### **完成标准**
- [ ] 真实执行路径已确认
- [ ] 系统性路径检查完成
- [ ] 假设性分析已标记和验证
- [ ] 为数据流追踪提供准确路径

### **循环步骤E3：数据流完整追踪**

**目标**：`执行路径 + 数据结构 + 字段映射 → 数据流异常定位`

#### **v1.2重点增强**
- ⭐ **强制应用原则4**：系统性检查数据流的所有环节
- ⭐ **强制应用原则2**：优先基于实际数据和日志进行分析

#### **核心步骤**
- **数据生命周期追踪**：从前端输入→处理→传输→存储的完整生命周期
- **系统性数据检查**：系统检查数据结构、转换、传输的所有环节
- **实际数据验证**：基于实际数据和日志验证数据流正确性
- **数据异常定位**：精确定位数据流异常的具体位置

#### **完成标准**
- [ ] 数据完整生命周期已追踪
- [ ] 系统性数据流检查完成
- [ ] 基于实际数据的验证完成
- [ ] 数据异常点已精确定位

### **循环步骤E4：问题逐层隔离**

**目标**：`数据流分析 + 执行路径 + 逐层验证 → 根因精确定位`

#### **v1.2重点增强**
- ⭐ **强制应用原则1**：按照症状层次进行问题隔离
- ⭐ **强制应用原则4**：系统性检查所有相关层次

#### **逐层隔离策略**

**第1层：表象问题隔离**
- 检查用户界面和交互逻辑是否正确
- 验证用户操作和系统响应是否正常
- 确认表象问题的具体表现和触发条件

**第2层：中层问题隔离**  
- 检查系统服务和配置是否正确
- 验证环境设置和依赖关系
- 确认中层问题的具体原因和影响

**第3层：根因问题隔离**
- 检查代码逻辑和数据处理是否正确
- 验证技术实现和架构设计
- 确认根本问题的具体位置和机制

#### **完成标准**
- [ ] 问题已精确定位到具体层次和代码位置
- [ ] 按照症状层次完成系统性隔离
- [ ] 根因机制已清晰理解
- [ ] 修复策略已明确
- [ ] 循环决策判断完成（是否发现新证据）

**阶段总结**：通过证据驱动的循环诊断和v1.2五大原则应用，精确定位问题根因，为第二阶段修复提供准确输入。

---

## 📋 第二阶段：修复实施与验证强制循环

**总目标**：`基于第一阶段输出 → 强制循环深化修复`

## 🔄 强制循环控制器（第二阶段）

**循环要求**：
- **最少循环数**：强制执行至少2轮循环（无任何例外）
- **退出条件**：连续两轮循环无实质性新修复需求
- **最大限制**：5轮循环上限（防止无限循环）

**v1.2增强判断标准**：
- **✅ 实质性新修复需求**：新功能缺陷、新代码问题、新质量问题、新验证失败（需有具体证据支撑）
- **❌ 非实质性信息**：重复已知问题、理论推测内容、一般性改进建议、常识性优化、未验证假设
- ⭐ **新增要求**：每轮循环必须应用v1.2修复闭环验证原则

## 🔄 标准循环单元（第二阶段）

**每轮循环 = F1修复方案设计 → F2修复实施执行 → F3修复效果验证 → F4修复质量评估**

### **循环步骤F1：修复方案设计**

**目标**：`前轮发现 + 根因分析 + 影响评估 → 精确修复方案`

#### **v1.2重点增强**
- ⭐ **强制应用原则3**：识别并标记修复方案中的假设
- ⭐ **强制应用原则4**：系统性评估修复的完整影响

#### **核心步骤**
- **前轮修复整合**：整合前轮循环的所有修复成果和新发现
- **根因修复设计**：基于第一阶段根因分析设计针对性修复
- **系统性影响评估**：系统评估修复对其他模块和功能的影响
- **假设标记验证**：标记修复方案中的假设并设计验证方法

#### **完成标准**
- [ ] 前轮修复成果完整整合
- [ ] 修复方案针对性强，直击根因
- [ ] 系统性影响分析完成，风险可控
- [ ] 修复假设已标记并有验证方案
- [ ] 为实施执行提供清晰指导

### **循环步骤F2：修复实施执行**

**目标**：`修复方案 + 工具协作 + 渐进实施 → 实际修复交付`

#### **v1.2重点增强**
- ⭐ **强制应用原则5**：每个修复步骤后立即进行验证

#### **核心步骤**
- **实施计划执行**：按照F1方案结果执行具体修复操作
- **渐进式修复**：Level 1核心修复→Level 2关联修复→Level 3边缘修复
- **步骤即时验证**：每个修复步骤完成后立即验证效果
- **修复记录维护**：详细记录每个修复的具体内容和验证结果

#### **完成标准**
- [ ] 修复实施按计划完成
- [ ] 每个步骤都有即时验证记录
- [ ] 渐进式修复策略执行到位
- [ ] 所有变更都有详细记录
- [ ] 为效果验证提供修复基础

### **循环步骤F3：修复效果验证**

**目标**：`修复结果 + 验证方案 + 真实测试 → 效果确认`

#### **v1.2重点增强**
- ⭐ **强制应用原则5**：完整的修复闭环验证
- ⭐ **强制应用原则2**：基于实际测试结果而非假设进行验证

#### **核心步骤**
- **修复验证**：验证当前修复是否解决了预期的具体问题
- **功能验证**：验证修复后的功能是否正常工作
- **根本验证**：验证是否解决了原始的根本问题
- **完整验证**：验证修复没有引入新的问题

#### **完成标准**
- [ ] 修复验证：当前修复效果已确认
- [ ] 功能验证：相关功能正常工作
- [ ] 根本验证：原始问题已解决
- [ ] 完整验证：无新问题引入
- [ ] 所有验证基于实际测试结果

### **循环步骤F4：修复质量评估**

**目标**：`验证结果 + 质量标准 + 改进识别 → 质量确认与循环决策`

#### **v1.2重点增强禁止理论推测要求**
- ❌ **禁止行为**：基于"应该"/"理论上"的推测、基于修复内容推测效果、未验证断言
- ✅ **必须操作**：使用实际测试验证修复效果
- ✅ **必须记录**：测试命令、验证结果、具体数据
- ✅ **必须格式**：基于以上实际操作验证：[结论]

#### **v1.2增强验证操作要求**
- **修复效果验证**：实际测试+修复代码检查，记录测试结果、修复逻辑、效果数据
- **功能完整验证**：功能测试+系统级代码检查，记录功能状态、测试结果、底层修复逻辑
- **质量标准验证**：代码质量检查+依赖影响检查，记录质量指标、影响范围、改进建议
- **根本问题验证**：端到端测试+原始问题验证，记录问题解决状态、测试覆盖、完整性确认

#### **核心步骤**
- **质量标准验证**：按照预设质量标准验证修复效果
- **性能影响评估**：评估修复对系统性能的影响
- **代码质量评估**：评估修复代码的质量和可维护性
- **循环决策判断**：基于新修复需求判断是否继续循环

#### **完成标准**
- [ ] 修复质量评估基于实际操作完成
- [ ] 所有验证都有具体工具使用记录
- [ ] 无理论推测，全部基于实际验证
- [ ] ⭐ 原始问题的根本解决已确认
- [ ] 循环决策判断完成

**阶段总结**：完成强制循环修复，应用v1.2修复闭环验证原则，总结修复效果和质量评估，确认原始问题彻底解决。

---

## 🎯 **核心诊断方法框架**

### **1. 数据流追踪框架**
**目标**：追踪数据从源头到终点的完整生命周期
- 识别数据的所有生成点和变化节点
- 验证数据在各环节的转换正确性
- 检测数据传递中的异常或丢失
- 确认数据一致性的关键验证点

### **2. 执行路径验证框架**
**目标**：确认代码真正执行的路径，排除分析干扰
- 通过输出特征反向定位执行代码
- 验证条件分支的实际执行情况
- 确认函数调用链的完整性
- 排除未执行代码的分析干扰

### **3. 数据结构一致性检查框架**
**目标**：验证数据发送方和接收方的结构匹配
- 对比数据结构的字段名称、类型、格式
- 追踪数据转换点的结构变化
- 识别数据格式不匹配导致的处理异常
- 确认接口契约的一致性

### **4. 逐层隔离诊断框架**
**目标**：按系统架构层次系统性排查问题
- 按照系统架构进行层次划分
- 每层独立验证功能正确性
- 识别问题的具体层次和位置
- 避免跨层问题的复杂化分析

### **5. 问题根因定位框架**
**目标**：从现象深入到本质，找到问题的真正原因
- 优先查找单一根因，避免多因素复杂化
- 重点关注数据流中断或异常的确切位置
- 从影响最小的修复点开始分析
- 基于证据而非假设进行根因推理
