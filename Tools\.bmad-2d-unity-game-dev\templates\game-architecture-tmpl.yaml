template:
  id: game-architecture-template-v2
  name: Game Architecture Document
  version: 2.0
  output:
    format: markdown
    filename: "docs/{{game_name}}-game-architecture.md"
    title: "{{game_title}} Game Architecture Document"

workflow:
  mode: interactive

sections:
  - id: initial-setup
    instruction: |
    This template creates a comprehensive game architecture document specifically for Unity + C# projects. This should provide the technical foundation for all game development stories and epics.

    If available, review any provided documents: Game Design Document (GDD), Technical Preferences. This architecture should support all game mechanics defined in the GDD.

  - id: introduction
    title: Introduction
    instruction: Establish the document's purpose and scope for game development
    content: |
      This document outlines the complete technical architecture for {{Game Title}}, a 2D game built with Unity and C#. It serves as the technical foundation for AI-driven game development, ensuring consistency and scalability across all game systems.

      This architecture is designed to support the gameplay mechanics defined in the Game Design Document while maintaining stable performance and cross-platform compatibility.
    sections:
      - id: change-log
        title: Change Log
        instruction: Track document versions and changes
        type: table
        template: |
          | Date | Version | Description | Author |
          | :--- | :------ | :---------- | :----- |

  - id: technical-overview
    title: Technical Overview
    instruction: Present all subsections together, then apply `tasks#advanced-elicitation` protocol to the complete section.
    sections:
      - id: architecture-summary
        title: Architecture Summary
        instruction: |
          Provide a comprehensive overview covering:

          - Game engine choice and configuration
          - Project structure and organization
          - Key systems and their interactions
          - Performance and optimization strategy
          - How this architecture achieves GDD requirements
      - id: platform-targets
        title: Platform Targets
        instruction: Based on GDD requirements, confirm platform support
        template: |
          **Primary Platform:** {{primary_platform}}
          **Secondary Platforms:** {{secondary_platforms}}
          **Minimum Requirements:** {{min_specs}}
          **Target Performance:** Stable frame rate on {{target_device}}
      - id: technology-stack
        title: Technology Stack
        template: |
          **Core Engine:** Unity 2022 LTS or newer
          **Language:** C# 10+
          **Build Tool:** Unity Build Pipeline
          **Package Manager:** Unity Package Manager
          **Testing:** Unity Test Framework (NUnit)
          **Deployment:** {{deployment_platform}}

  - id: project-structure
    title: Project Structure
    instruction: Define the complete project organization that developers will follow
    sections:
      - id: repository-organization
        title: Repository Organization
        instruction: Design a clear folder structure for game development
        type: code
        language: text
        template: |
          {{game_name}}/
          ├── Assets/
          │   ├── Scenes/          # Game scenes
          │   ├── Scripts/         # C# scripts
          │   ├── Prefabs/         # Reusable game objects
          │   ├── Art/             # Art assets
          │   ├── Audio/           # Audio assets
          │   ├── Data/            # ScriptableObjects and other data
          │   └── Tests/           # Unity Test Framework tests
          ├── Packages/            # Package Manager manifest
          └── ProjectSettings/     # Unity project settings
      - id: module-organization
        title: Module Organization
        instruction: Define how TypeScript modules should be organized
        sections:
          - id: scene-structure
            title: Scene Structure
            type: bullet-list
            template: |
              - Each scene in separate file
              - Scene-specific logic contained in scripts within the scene
              - Use a loading scene for asynchronous loading
          - id: game-object-pattern
            title: Game Object Pattern
            type: bullet-list
            template: |
              - Component-based architecture using MonoBehaviours
              - Reusable game objects as prefabs
              - Data-driven design with ScriptableObjects
          - id: system-architecture
            title: System Architecture
            type: bullet-list
            template: |
              - Singleton managers for global systems (e.g., GameManager, AudioManager)
              - Event-driven communication using UnityEvents or C# events
              - Clear separation of concerns between components

  - id: core-game-systems
    title: Core Game Systems
    instruction: Detail each major system that needs to be implemented. Each system should be specific enough for developers to create implementation stories.
    sections:
      - id: scene-management
        title: Scene Management System
        template: |
          **Purpose:** Handle game flow and scene transitions

          **Key Components:**

          - Asynchronous scene loading and unloading
          - Data passing between scenes using a persistent manager or ScriptableObject
          - Loading screens with progress bars

          **Implementation Requirements:**

          - A `SceneLoader` class to manage all scene transitions
          - A loading scene to handle asynchronous loading
          - A `GameManager` to persist between scenes and hold necessary data

          **Files to Create:**

          - `Assets/Scripts/Core/SceneLoader.cs`
          - `Assets/Scenes/Loading.unity`
      - id: game-state-management
        title: Game State Management
        template: |
          **Purpose:** Track player progress and game status

          **State Categories:**

          - Player progress (levels, unlocks)
          - Game settings (audio, controls)
          - Session data (current level, score)
          - Persistent data (achievements, statistics)

          **Implementation Requirements:**

          - A `SaveManager` class to handle saving and loading data to a file
          - Use of `ScriptableObject`s to hold game state data
          - State validation and error recovery

          **Files to Create:**

          - `Assets/Scripts/Core/SaveManager.cs`
          - `Assets/Data/ScriptableObjects/GameState.cs`
      - id: asset-management
        title: Asset Management System
        template: |
          **Purpose:** Efficient loading and management of game assets

          **Asset Categories:**

          - Sprites and textures
          - Audio clips and music
          - Prefabs and scene files
          - ScriptableObjects

          **Implementation Requirements:**

          - Use of Addressables for dynamic asset loading
          - Asset bundles for platform-specific assets
          - Memory management for large assets

          **Files to Create:**

          - `Assets/Scripts/Core/AssetManager.cs` (if needed for complex scenarios)
      - id: input-management
        title: Input Management System
        template: |
          **Purpose:** Handle all player input across platforms

          **Input Types:**

          - Keyboard controls
          - Mouse/pointer interaction
          - Touch gestures (mobile)
          - Gamepad support

          **Implementation Requirements:**

          - Use the new Unity Input System
          - Create Action Maps for different input contexts
          - Use the `PlayerInput` component for easy player input handling

          **Files to Create:**

          - `Assets/Settings/InputActions.inputactions`
      - id: game-mechanics-systems
        title: Game Mechanics Systems
        instruction: For each major mechanic defined in the GDD, create a system specification
        repeatable: true
        sections:
          - id: mechanic-system
            title: "{{mechanic_name}} System"
            template: |
              **Purpose:** {{system_purpose}}

              **Core Functionality:**

              - {{feature_1}}
              - {{feature_2}}
              - {{feature_3}}

              **Dependencies:** {{required_systems}}

              **Performance Considerations:** {{optimization_notes}}

              **Files to Create:**

              - `Assets/Scripts/Mechanics/{{SystemName}}.cs`
              - `Assets/Prefabs/{{RelatedObject}}.prefab`
      - id: physics-collision
        title: Physics & Collision System
        template: |
          **Physics Engine:** Unity 2D Physics

          **Collision Categories:**

          - Player collision
          - Enemy interactions
          - Environmental objects
          - Collectibles and items

          **Implementation Requirements:**

          - Use the Layer Collision Matrix to optimize collision detection
          - Use `Rigidbody2D` for physics-based movement
          - Use `Collider2D` components for collision shapes

          **Files to Create:**

          - (No new files, but configure `ProjectSettings/DynamicsManager.asset`)
      - id: audio-system
        title: Audio System
        template: |
          **Audio Requirements:**

          - Background music with looping
          - Sound effects for actions
          - Audio settings and volume control
          - Mobile audio optimization

          **Implementation Features:**

          - An `AudioManager` singleton to play sounds and music
          - Use of `AudioMixer` to control volume levels
          - Object pooling for frequently played sound effects

          **Files to Create:**

          - `Assets/Scripts/Core/AudioManager.cs`
      - id: ui-system
        title: UI System
        template: |
          **UI Components:**

          - HUD elements (score, health, etc.)
          - Menu navigation
          - Modal dialogs
          - Settings screens

          **Implementation Requirements:**

          - Use UI Toolkit or UGUI for building user interfaces
          - Create a `UIManager` to manage UI elements
          - Use events to update UI from game logic

          **Files to Create:**

          - `Assets/Scripts/UI/UIManager.cs`
          - `Assets/UI/` (folder for UI assets and prefabs)

  - id: performance-architecture
    title: Performance Architecture
    instruction: Define performance requirements and optimization strategies
    sections:
      - id: performance-targets
        title: Performance Targets
        template: |
          **Frame Rate:** Stable frame rate, 60+ FPS on target platforms
          **Memory Usage:** <{{memory_limit}}MB total
          **Load Times:** <{{initial_load}}s initial, <{{level_load}}s per level
          **Battery Optimization:** Reduced updates when not visible
      - id: optimization-strategies
        title: Optimization Strategies
        sections:
          - id: object-pooling
            title: Object Pooling
            type: bullet-list
            template: |
              - Bullets and projectiles
              - Particle effects
              - Enemy objects
              - UI elements
          - id: asset-optimization
            title: Asset Optimization
            type: bullet-list
            template: |
              - Sprite atlases
              - Audio compression
              - Mipmaps for textures
          - id: rendering-optimization
            title: Rendering Optimization
            type: bullet-list
            template: |
              - Use the 2D Renderer
              - Batching for sprites
              - Culling off-screen objects
          - id: optimization-files
            title: Files to Create
            type: bullet-list
            template: |
              - `Assets/Scripts/Core/ObjectPool.cs`

  - id: game-configuration
    title: Game Configuration
    instruction: Define all configurable aspects of the game
    sections:
      - id: game-balance-configuration
        title: Game Balance Configuration
        instruction: Based on GDD, define configurable game parameters using ScriptableObjects
        type: code
        language: c#
        template: |
          // Assets/Scripts/Data/GameBalance.cs
          using UnityEngine;

          [CreateAssetMenu(fileName = "GameBalance", menuName = "Game/Game Balance")]
          public class GameBalance : ScriptableObject
          {
              public PlayerStats playerStats;
              public EnemyStats enemyStats;
          }

          [System.Serializable]
          public class PlayerStats
          {
              public float speed;
              public int maxHealth;
          }

          [System.Serializable]
          public class EnemyStats
          {
              public float speed;
              public int maxHealth;
              public int damage;
          }

  - id: development-guidelines
    title: Development Guidelines
    instruction: Provide coding standards specific to game development
    sections:
      - id: c#-standards
        title: C# Standards
        sections:
          - id: code-style
            title: Code Style
            type: bullet-list
            template: |
              - Follow .NET coding conventions
              - Use namespaces to organize code
              - Write clean, readable, and maintainable code
      - id: unity-best-practices
        title: Unity Best Practices
        sections:
          - id: general-best-practices
            title: General Best Practices
            type: bullet-list
            template: |
              - Use the `[SerializeField]` attribute to expose private fields in the Inspector
              - Avoid using `GameObject.Find()` in `Update()`
              - Cache component references in `Awake()` or `Start()`
          - id: component-design
            title: Component Design
            type: bullet-list
            template: |
              - Follow the Single Responsibility Principle
              - Use events for communication between components
              - Use ScriptableObjects for data
          - id: scene-management-practices
            title: Scene Management
            type: bullet-list
            template: |
              - Use a loading scene for asynchronous loading
              - Keep scenes small and focused
      - id: testing-strategy
        title: Testing Strategy
        sections:
          - id: unit-testing
            title: Unit Testing
            type: bullet-list
            template: |
              - Use the Unity Test Framework (Edit Mode tests)
              - Test C# logic in isolation
          - id: integration-testing
            title: Integration Testing
            type: bullet-list
            template: |
              - Use the Unity Test Framework (Play Mode tests)
              - Test the interaction between components and systems
          - id: test-files
            title: Files to Create
            type: bullet-list
            template: |
              - `Assets/Tests/EditMode/`
              - `Assets/Tests/PlayMode/`

  - id: deployment-architecture
    title: Deployment Architecture
    instruction: Define how the game will be built and deployed
    sections:
      - id: build-process
        title: Build Process
        sections:
          - id: development-build
            title: Development Build
            type: bullet-list
            template: |
              - Enable "Development Build" in Build Settings
              - Use the Profiler to analyze performance
          - id: production-build
            title: Production Build
            type: bullet-list
            template: |
              - Disable "Development Build"
              - Use IL2CPP for better performance
              - Configure platform-specific settings
      - id: deployment-strategy
        title: Deployment Strategy
        sections:
          - id: platform-deployment
            title: Platform Deployment
            type: bullet-list
            template: |
              - Configure player settings for each target platform
              - Use Unity Cloud Build for automated builds
              - Follow platform-specific guidelines for submission

  - id: implementation-roadmap
    title: Implementation Roadmap
    instruction: Break down the architecture implementation into phases that align with the GDD development phases
    sections:
      - id: phase-1-foundation
        title: "Phase 1: Foundation ({{duration}})"
        sections:
          - id: phase-1-core
            title: Core Systems
            type: bullet-list
            template: |
              - Project setup and configuration
              - Basic scene management
              - Asset loading pipeline
              - Input handling framework
          - id: phase-1-epics
            title: Story Epics
            type: bullet-list
            template: |
              - "Engine Setup and Configuration"
              - "Basic Scene Management System"
              - "Asset Loading Foundation"
      - id: phase-2-game-systems
        title: "Phase 2: Game Systems ({{duration}})"
        sections:
          - id: phase-2-gameplay
            title: Gameplay Systems
            type: bullet-list
            template: |
              - {{primary_mechanic}} implementation
              - Physics and collision system
              - Game state management
              - UI framework
          - id: phase-2-epics
            title: Story Epics
            type: bullet-list
            template: |
              - "{{primary_mechanic}} System Implementation"
              - "Physics and Collision Framework"
              - "Game State Management System"
      - id: phase-3-content-polish
        title: "Phase 3: Content & Polish ({{duration}})"
        sections:
          - id: phase-3-content
            title: Content Systems
            type: bullet-list
            template: |
              - Level loading and management
              - Audio system integration
              - Performance optimization
              - Final polish and testing
          - id: phase-3-epics
            title: Story Epics
            type: bullet-list
            template: |
              - "Level Management System"
              - "Audio Integration and Optimization"
              - "Performance Optimization and Testing"

  - id: risk-assessment
    title: Risk Assessment
    instruction: Identify potential technical risks and mitigation strategies
    type: table
    template: |
      | Risk                         | Probability | Impact     | Mitigation Strategy |
      | ---------------------------- | ----------- | ---------- | ------------------- |
      | Performance issues on mobile | {{prob}}    | {{impact}} | {{mitigation}}      |
      | Asset loading bottlenecks    | {{prob}}    | {{impact}} | {{mitigation}}      |
      | Cross-platform compatibility | {{prob}}    | {{impact}} | {{mitigation}}      |

  - id: success-criteria
    title: Success Criteria
    instruction: Define measurable technical success criteria
    sections:
      - id: technical-metrics
        title: Technical Metrics
        type: bullet-list
        template: |
          - All systems implemented per specification
          - Performance targets met consistently
          - Zero critical bugs in core systems
          - Successful deployment across target platforms
      - id: code-quality
        title: Code Quality
        type: bullet-list
        template: |
          - 90%+ test coverage on game logic
          - Zero C# compiler errors or warnings
          - Consistent adherence to coding standards
          - Comprehensive documentation coverage
