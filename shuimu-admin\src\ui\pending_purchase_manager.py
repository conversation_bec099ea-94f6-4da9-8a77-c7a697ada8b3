#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地待同步购买记录状态管理器
解决连续购买时的筛选时序问题
"""

from typing import Set, Dict, Any
from datetime import datetime, timedelta
import threading

class PendingPurchaseManager:
    """本地待同步购买记录管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._pending_purchases = {}  # {user_id: {category_id: timestamp}}
            self._pending_series = {}     # {user_id: {series_id: timestamp}}
            self._lock = threading.Lock()
            self._initialized = True
    
    def add_pending_purchase(self, user_id: str, purchase_data: Dict[str, Any]):
        """添加待同步购买记录"""
        with self._lock:
            current_time = datetime.now()
            
            # 确保用户数据存在
            if user_id not in self._pending_purchases:
                self._pending_purchases[user_id] = {}
                self._pending_series[user_id] = {}
            
            if purchase_data.get('purchased_entity_type') == 'series_all_categories':
                # 系列购买：添加所有分类
                series_id = purchase_data.get('series_id')
                series_name = purchase_data.get('series_name', '')
                
                if series_id:
                    self._pending_series[user_id][series_id] = current_time
                
                # 同时添加所有分类
                categories = purchase_data.get('categories', [])
                for category in categories:
                    category_id = category.get('category_id') or category.get('original_category_id')
                    if category_id:
                        self._pending_purchases[user_id][category_id] = current_time
            else:
                # 单个分类购买
                category_id = purchase_data.get('purchased_entity_id')
                category_name = purchase_data.get('category_name', '')
                
                if category_id:
                    self._pending_purchases[user_id][category_id] = current_time
    
    def is_category_pending(self, user_id: str, category_id: str) -> bool:
        """检查分类是否在待同步状态"""
        with self._lock:
            if user_id not in self._pending_purchases:
                return False
            
            if category_id in self._pending_purchases[user_id]:
                # 🎯 修复：延长超时时间到2分钟，适应连续购买场景
                timestamp = self._pending_purchases[user_id][category_id]
                if datetime.now() - timestamp > timedelta(seconds=120):
                    del self._pending_purchases[user_id][category_id]
                    return False
                return True
            return False
    
    def is_series_pending(self, user_id: str, series_id: str) -> bool:
        """检查系列是否在待同步状态"""
        with self._lock:
            if user_id not in self._pending_series:
                return False
            
            if series_id in self._pending_series[user_id]:
                # 🎯 修复：延长超时时间到2分钟，适应连续购买场景
                timestamp = self._pending_series[user_id][series_id]
                if datetime.now() - timestamp > timedelta(seconds=120):
                    del self._pending_series[user_id][series_id]
                    return False
                return True
            return False
    
    def is_series_categories_pending(self, user_id: str, series_id: str, categories: list) -> bool:
        """检查系列下的分类是否有待同步的"""
        with self._lock:
            if user_id not in self._pending_purchases:
                return False
            
            for category in categories:
                category_id = category.get('category_id') or category.get('original_category_id') or category.get('id')
                if category_id and self.is_category_pending(user_id, category_id):
                    return True
            return False
    
    def clear_pending_purchase(self, user_id: str, category_id: str):
        """清除待同步购买记录（同步完成时调用）"""
        with self._lock:
            if user_id in self._pending_purchases:
                if category_id in self._pending_purchases[user_id]:
                    del self._pending_purchases[user_id][category_id]
    
    def clear_pending_series(self, user_id: str, series_id: str):
        """清除待同步系列记录（同步完成时调用）"""
        with self._lock:
            if user_id in self._pending_series:
                if series_id in self._pending_series[user_id]:
                    del self._pending_series[user_id][series_id]
    
    def clear_user_pending(self, user_id: str):
        """清除用户所有待同步记录"""
        with self._lock:
            if user_id in self._pending_purchases:
                self._pending_purchases[user_id] = {}
            
            if user_id in self._pending_series:
                self._pending_series[user_id] = {}
    
    def cleanup_expired(self):
        """清理过期的待同步记录"""
        with self._lock:
            current_time = datetime.now()
            expired_threshold = timedelta(seconds=120)  # 🎯 修复：统一超时时间到2分钟
            
            # 清理过期的分类
            for user_id in list(self._pending_purchases.keys()):
                for category_id in list(self._pending_purchases[user_id].keys()):
                    if current_time - self._pending_purchases[user_id][category_id] > expired_threshold:
                        del self._pending_purchases[user_id][category_id]
            
            # 清理过期的系列
            for user_id in list(self._pending_series.keys()):
                for series_id in list(self._pending_series[user_id].keys()):
                    if current_time - self._pending_series[user_id][series_id] > expired_threshold:
                        del self._pending_series[user_id][series_id]
    
    def get_pending_purchases(self, user_id: str) -> Dict[str, Any]:
        """获取用户待同步购买记录 - 🎯 修复：新增缺失方法"""
        with self._lock:
            return {
                'categories': self._pending_purchases.get(user_id, {}),
                'series': self._pending_series.get(user_id, {})
            }
    
    def get_pending_stats(self, user_id: str) -> Dict[str, int]:
        """获取待同步状态统计"""
        with self._lock:
            categories_count = len(self._pending_purchases.get(user_id, {}))
            series_count = len(self._pending_series.get(user_id, {}))
            
            return {
                'pending_categories': categories_count,
                'pending_series': series_count
            }
    
    def debug_print_pending(self, user_id: str):
        """调试：打印待同步状态 - 🎯 简化输出"""
        with self._lock:
            categories_count = len(self._pending_purchases.get(user_id, {}))
            series_count = len(self._pending_series.get(user_id, {}))
            
            # 待同步状态调试完成，静默处理

# 全局单例实例
pending_purchase_manager = PendingPurchaseManager()