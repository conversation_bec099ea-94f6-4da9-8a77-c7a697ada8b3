#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的购买记录对话框
两行布局：系列下拉框 + 分类下拉框
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QFormLayout, QLabel, 
    QComboBox, QDialogButtonBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Dict, Any, Optional, List

class AddPurchaseDialog(QDialog):
    """简化的添加购买记录对话框 - 两行布局"""
    
    def __init__(self, user_id: str, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.series_data = []
        self.categories_data = []
        self.user_purchased_items = {}  # 🎯 存储用户已购买的项目 {category_id: True, ...}
        self.threadpool_manager = None  # 🎯 修复：保存线程池管理器实例，避免被垃圾回收
        
        # 🎯 修复：主动检查最近的删除状态，解决时序问题
        self._check_recent_deletions()
        
        # 🎯 新增：监听删除完成事件（保留作为补充）
        self._setup_delete_event_listener()
        
        self.setWindowTitle("添加购买记录")
        self.setFixedSize(500, 280)  # 🔧 增加高度：200→280，避免按钮重叠
        self.setModal(True)
        
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: #ffffff;
                color: #333333;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
            }
            QLabel {
                color: #333333;
                font-size: 12px;
                font-weight: bold;
            }
            QComboBox {
                border: 2px solid #d0d0d0;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                background-color: #ffffff;
                min-height: 20px;
            }
            QComboBox:focus {
                border: 2px solid #4CAF50;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        
        self.init_ui()
        # 🎯 修复：不在初始化时加载，而是提供refresh方法
    
    def refresh_data(self):
        """刷新数据 - 🎯 异步刷新，避免阻塞主线程"""
        self.user_purchased_items = {}  # 清空旧数据
        
        # 🎯 启动异步缓存刷新
        self._start_async_cache_refresh()
        
    def _start_async_cache_refresh(self):
        """启动异步缓存刷新 - 使用线程池模式"""
        try:
            from ui.threadpool_purchase_workers import ThreadPoolPurchaseManager
            
            # 🎯 修复：将线程池管理器保存为实例变量，避免被垃圾回收
            self.threadpool_manager = ThreadPoolPurchaseManager(None, self.user_id)
            self.threadpool_manager.cache_refresh_completed.connect(self._on_cache_refresh_completed)
            
            # 启动异步缓存刷新
            self.threadpool_manager.refresh_cache_async()
            
        except Exception as e:
            print(f"❌ [线程池刷新] 启动失败: {e}")
            import traceback
            traceback.print_exc()
            # 降级到同步处理
            self._fallback_sync_refresh()
    
    def _on_cache_refresh_completed(self, success):
        """异步缓存刷新完成回调 - 🎯 修复：确保UI更新在主线程执行"""
        print(f"🔄 [异步刷新回调] 缓存刷新结果: {'成功' if success else '失败'}")
        
        # 🎯 使用QTimer确保UI更新在主线程执行
        from PyQt6.QtCore import QTimer
        
        def update_ui():
            try:
                from cache.global_data_manager import global_data_manager
                
                if success:
                    self.load_user_purchased_items()
                else:
                    self._force_refresh_purchased_items()
                
                # 数据加载验证
                if not self.user_purchased_items:
                    self.load_user_purchased_items()
                
                # 强制加载全局数据（如果未加载）
                if not global_data_manager.is_data_loaded():
                    try:
                        global_data_manager.load_all_data_once()
                    except Exception as load_error:
                        print(f"❌ [缓存刷新] 强制加载异常: {load_error}")
                
                # 重新加载系列数据
                self.load_series_data()
                
                # 🎯 简化日志：只输出关键统计
                purchased_count = len(self.user_purchased_items)
                series_count = self.series_combo.count()
                print(f"📊 [缓存刷新] 已购买{purchased_count}项，系列选项{series_count-1}个")
                    
            except Exception as e:
                print(f"❌ [主线程UI更新] UI更新异常: {e}")
                import traceback
                traceback.print_exc()
                # 降级到同步刷新
                self._fallback_sync_refresh()
        
        # 在主线程中执行UI更新
        QTimer.singleShot(0, update_ui)
    
    def _fallback_sync_refresh(self):
        """降级到同步刷新 - 异步失败时的备选方案"""
        print("🔄 [降级刷新] 异步启动失败，降级到同步刷新...")
        
        # 🎯 优化：先尝试强制刷新全局缓存，然后使用缓存数据
        try:
            from cache.global_data_manager import global_data_manager
            print("🔄 [降级刷新] 强制刷新全局数据管理器中的用户购买记录缓存...")
            if global_data_manager.refresh_user_purchases(self.user_id):
                print("✅ [降级刷新] 全局缓存刷新成功，使用最新缓存数据")
                self.load_user_purchased_items()  # 从刷新后的缓存加载
            else:
                print("⚠️ [降级刷新] 全局缓存刷新失败，降级到强制数据库查询")
                self._force_refresh_purchased_items()
        except Exception as cache_refresh_error:
            print(f"❌ [降级刷新] 全局缓存刷新异常: {cache_refresh_error}，降级到强制数据库查询")
            self._force_refresh_purchased_items()
        
        # 🎯 修复：数据加载验证
        if not self.user_purchased_items:
            print("⚠️ [降级刷新] 购买记录数据为空，尝试重新加载...")
            self.load_user_purchased_items()
        
        print("🔄 [降级刷新] 开始加载系列数据...")
        self.load_series_data()  # 重新加载系列数据
        
        # 🎯 简化日志：只输出关键统计
        print(f"📊 [降级刷新] 完成，已购买{len(self.user_purchased_items)}项")
    
    def init_ui(self):
        """初始化UI - 简化两行布局"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title_label = QLabel("添加购买记录")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 表单布局
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        
        # 第一行：系列下拉框
        self.series_combo = QComboBox()
        self.series_combo.currentTextChanged.connect(self.on_series_changed)
        form_layout.addRow("📚 选择系列:", self.series_combo)
        
        # 第二行：分类下拉框
        self.category_combo = QComboBox()
        form_layout.addRow("📋 选择分类:", self.category_combo)
        
        layout.addLayout(form_layout)
        
        # 操作按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("确认添加")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("取消")
        buttons.button(QDialogButtonBox.StandardButton.Ok).setEnabled(False)  # 初始禁用
        
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        self.ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        
        layout.addWidget(buttons)
    
    def cleanup(self):
        """清理资源 - 🎯 修复：确保线程池管理器正确清理"""
        if self.threadpool_manager:
            try:
                # 断开信号连接
                self.threadpool_manager.cache_refresh_completed.disconnect()
                print("🧹 [资源清理] 断开信号连接")
            except Exception as e:
                print(f"⚠️ [资源清理] 断开信号连接失败: {e}")
            
            # 清空引用
            self.threadpool_manager = None
            print("🧹 [资源清理] 清空线程池管理器引用")
    
    def closeEvent(self, event):
        """对话框关闭事件 - 🎯 修复：确保资源正确清理"""
        print("🚪 [对话框关闭] 开始清理资源...")
        
        # 🎯 修复：断开删除事件监听器
        try:
            from ui.async_purchase_operations import purchase_delete_notifier
            purchase_delete_notifier.deletion_completed.disconnect(self._on_purchases_deleted)
            print("🚪 [对话框关闭] 已断开删除事件监听器")
        except Exception as e:
            print(f"⚠️ [对话框关闭] 断开删除事件监听器失败: {e}")
        
        self.cleanup()
        super().closeEvent(event)
    
    def _force_refresh_purchased_items(self):
        """强制从数据库刷新购买记录"""
        try:
            from database.models import DatabaseManager
            from utils.config import Config
            
            # 获取数据库配置
            config = Config()
            db_config = config.get_database_config()
            connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
            db_manager = DatabaseManager(connection_string)
            
            # 直接查询数据库
            query = """
                SELECT purchased_entity_type, purchased_entity_id, series_name, purchase_price, status, purchase_date,
                       category_name, series_name as series_title
                FROM user_purchases 
                WHERE user_id = %s AND status IN ('is_active', 'completed')
                ORDER BY purchase_date DESC
            """
            
            purchase_records = db_manager.execute_query(query, (self.user_id,))
            
            # 解析购买记录
            for record in purchase_records:
                purchased_entity_type = record.get('purchased_entity_type', 'category')
                purchased_entity_id = record.get('purchased_entity_id')
                
                if purchased_entity_type == 'category' and purchased_entity_id:
                    # 分类购买
                    key = f"category_{purchased_entity_id}"
                    self.user_purchased_items[key] = True
                    
                elif purchased_entity_type == 'series' and purchased_entity_id:
                    # 系列购买
                    key = f"series_{purchased_entity_id}"
                    self.user_purchased_items[key] = True
            
        except Exception as e:
            # 降级到原有的加载方式
            self.load_user_purchased_items()
    
    def load_user_purchased_items(self):
        """加载用户已购买的项目，用于筛选下拉框选项"""
        try:
            # 优先从API缓存获取最新数据，避免同步延迟问题
            cache_success = False
            try:
                from cache.global_data_manager import global_data_manager
                
                if global_data_manager.is_data_loaded():
                    cached_purchases = global_data_manager.get_user_purchases(self.user_id)
                    
                    if cached_purchases and cached_purchases.get('success'):
                        purchase_data = cached_purchases.get('data', [])
                        
                        if len(purchase_data) > 0:
                            for purchase in purchase_data:
                                # 使用服务端返回的关键字段
                                purchased_entity_id = purchase.get('purchased_entity_id')
                                category_id = purchase.get('category_id') or purchased_entity_id
                                
                                # 直接使用服务端返回的 category_id
                                if category_id and category_id != 'None':
                                    key = f"category_{category_id}"
                                    self.user_purchased_items[key] = True
                            
                            cache_success = True
                    
            except Exception as cache_error:
                pass
            
            # 降级：只有在缓存获取失败时才查询本地数据库
            if not cache_success:
                try:
                    from database.models import DatabaseManager
                    from utils.config import Config
                    
                    config = Config()
                    db_config = config.get_database_config()
                    connection_string = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['database']}"
                    db_manager = DatabaseManager(connection_string)
                    
                    query = """
                        SELECT purchased_entity_type, purchased_entity_id, series_name, purchase_price, status, purchase_date
                        FROM user_purchases 
                        WHERE user_id = %s AND status IN ('is_active', 'completed')
                        ORDER BY purchase_date DESC
                    """
                    
                    purchase_records = db_manager.execute_query(query, (self.user_id,))
                    
                    # 解析购买记录 - 使用规范字段名
                    for record in purchase_records:
                        purchased_entity_type = record.get('purchased_entity_type', 'category')
                        purchased_entity_id = record.get('purchased_entity_id')
                        
                        if purchased_entity_type == 'category' and purchased_entity_id:
                            # 分类购买
                            key = f"category_{purchased_entity_id}"
                            self.user_purchased_items[key] = True
                            
                        elif purchased_entity_type == 'series' and purchased_entity_id:
                            # 系列购买（整个系列）
                            key = f"series_{purchased_entity_id}"
                            self.user_purchased_items[key] = True
                    
                except Exception as db_error:
                    pass
                
        except Exception as e:
            self.user_purchased_items = {}
    
    
    def load_series_data(self):
        """加载系列数据 - 使用全局数据管理器"""
        try:
            # 🎯 修复：使用全局数据管理器，避免服务初始化问题
            from cache.global_data_manager import global_data_manager
            
            # 检查数据是否已加载
            if global_data_manager.is_data_loaded():
                series_result = global_data_manager.get_series_list(page=1, page_size=100)
                
                if series_result and series_result.get('success'):
                    self.series_data = series_result.get('data', [])
                    
                    # 清空下拉框并重新填充
                    self.series_combo.clear()
                    self.populate_series_combo()
                    
                    # 🎯 简化日志：只输出统计和第一个项目
                    final_count = self.series_combo.count()
                    first_item = self.series_combo.itemText(1) if final_count > 1 else "无"
                    print(f"📊 [系列加载] 共{len(self.series_data)}个系列，筛选出{final_count-1}个可购买，首项: {first_item}")
                else:
                    print("❌ [系列加载] 数据获取失败")
                    self.series_combo.clear()
                    self.series_combo.addItem("❌ 数据加载失败")
            else:
                # 尝试加载数据
                try:
                    load_result = global_data_manager.load_all_data_once()
                    if load_result:
                        self.load_series_data()  # 递归重试
                    else:
                        print("❌ [系列加载] 数据加载失败")
                        self.series_combo.clear()
                        self.series_combo.addItem("❌ 数据加载失败")
                except Exception as load_error:
                    print(f"❌ [系列加载] 数据加载异常: {load_error}")
                    self.series_combo.clear()
                    self.series_combo.addItem("❌ 数据加载异常")
                
        except Exception as e:
            print(f"❌ [load_series_data] 加载系列数据异常: {e}")
            import traceback
            traceback.print_exc()
            self.series_combo.clear()
            self.series_combo.addItem("❌ 加载失败")
    
    def populate_series_combo(self):
        """填充系列下拉框 - 🎯 优化：减少重复计算，提高筛选效率"""
        # 筛选前数据验证
        if not self.user_purchased_items:
            self._force_refresh_purchased_items()
        
        # 🎯 优化：预构建购买状态缓存，避免重复计算
        self._series_purchase_cache = {}
        
        self.series_combo.clear()
        self.series_combo.addItem("-- 请选择系列 --", None)  # 默认选项
        
        available_series_count = 0
        total_series_count = len(self.series_data)
        skipped_purchased = 0
        skipped_free = 0
        first_available_series = None
        
        for i, series in enumerate(self.series_data):
            series_id = series.get('id')
            title = series.get('title', '未知系列')
            price = float(series.get('price', 0))
            is_free = series.get('is_free', False)
            
            # 筛选1：跳过免费系列
            if is_free or price == 0:
                skipped_free += 1
                continue
            
            # 筛选2：使用缓存优化的函数检查系列是否已购买
            try:
                # 使用缓存避免重复计算
                if series_id in self._series_purchase_cache:
                    is_purchased = self._series_purchase_cache[series_id]
                else:
                    is_purchased = self.is_series_purchased(series_id)
                    self._series_purchase_cache[series_id] = is_purchased
                
                if is_purchased:
                    skipped_purchased += 1
                    continue
                    
            except Exception as check_error:
                # 发生错误时，保守起见仍然显示该系列
                print(f"❌ [购买状态检查异常] {title} - {check_error}")
                pass
            
            # 只显示收费且未购买的系列
            display_text = f"💰 {title} (¥{price:.2f})"
            
            # 将完整的系列数据存储在UserRole中
            self.series_combo.addItem(display_text, series)
            available_series_count += 1
            
            # 记录第一个可用系列
            if first_available_series is None:
                first_available_series = f"{title} (¥{price:.2f})"
        
        # 🎯 输出简化的筛选结果
        print(f"📊 [筛选结果] 共{total_series_count}个系列，筛选出{available_series_count}个可购买系列")
        if first_available_series:
            print(f"📋 [首项预览] 第一个可购买系列：{first_available_series}")
        elif available_series_count == 0:
            print(f"⚠️ [筛选结果] 无可购买系列 - 免费:{skipped_free}个，已购买:{skipped_purchased}个")
        
    def is_series_purchased(self, series_id: str) -> bool:
        """判断系列是否已完全购买 - 包含已购买和待同步状态"""
        try:
            # 🎯 修复：强制刷新缓存检查，确保连续购买场景下状态一致性
            if not self.user_purchased_items:
                self.load_user_purchased_items()
            
            # Step1: 检查直接购买整个系列的情况 (purchased_entity_type = 'series')
            series_key = f"series_{series_id}"
            if series_key in self.user_purchased_items:
                return True
            
            # Step1.5: 检查系列是否在待同步状态
            from ui.pending_purchase_manager import pending_purchase_manager
            if pending_purchase_manager.is_series_pending(self.user_id, series_id):
                return True
            
            # Step2: 检查系列下所有收费分类是否都已购买或待同步
            from cache.global_data_manager import global_data_manager
            
            if not global_data_manager.is_data_loaded():
                return False
            
            # 获取系列下的分类
            category_result = global_data_manager.get_category_list(
                page=1, page_size=100, series_id=series_id
            )
            
            if not category_result or not category_result.get('success'):
                return False
                
            categories = category_result.get('data', [])
            if not categories:
                return False
            
            # 核心逻辑：检查所有收费分类是否都已购买或待同步
            paid_categories = [cat for cat in categories if float(cat.get('price', 0)) > 0]
            
            if not paid_categories:
                return False
            
            purchased_paid_categories = 0
            total_paid_categories = len(paid_categories)
            
            for category in paid_categories:
                # 与购买记录存储保持一致，优先使用category_id
                category_id = category.get('category_id') or category.get('original_category_id') or category.get('id')
                category_key = f"category_{category_id}"
                
                # 检查已购买 OR 待同步
                is_purchased = category_key in self.user_purchased_items
                is_pending = pending_purchase_manager.is_category_pending(self.user_id, category_id)
                
                if is_purchased or is_pending:
                    purchased_paid_categories += 1
            
            # 判断系列是否完全购买（包含待同步）
            is_fully_purchased = (purchased_paid_categories == total_paid_categories)
            return is_fully_purchased
            
        except Exception as e:
            print(f"❌ [购买状态检查异常] 系列 {series_id}: {e}")
            return False
    
    def on_series_changed(self):
        """系列选择变化事件 - 更新分类下拉框"""
        current_index = self.series_combo.currentIndex()
        
        if current_index <= 0:  # 选择了默认选项
            self.category_combo.clear()
            self.category_combo.addItem("-- 先选择系列 --", None)
            self.ok_button.setEnabled(False)
            return
        
        # 获取选中的系列数据
        selected_series = self.series_combo.itemData(current_index)
        if not selected_series:
            return
        
        series_id = selected_series.get('id')
        print(f"🔄 系列选择变化: {selected_series.get('title')} (ID: {series_id})")
        
        # 加载该系列下的分类
        self.load_categories_for_series(series_id)
        
        # 启用确认按钮（选择系列后就可以确认，即使不选分类）
        self.ok_button.setEnabled(True)
    
    def load_categories_for_series(self, series_id: str):
        """加载指定系列下的分类 - 使用全局数据管理器"""
        try:
            from cache.global_data_manager import global_data_manager
            
            if global_data_manager.is_data_loaded():
                # 获取该系列下的分类
                category_result = global_data_manager.get_category_list(
                    page=1, page_size=100, series_id=series_id
                )
                
                if category_result and category_result.get('success'):
                    self.categories_data = category_result.get('data', [])
                    self.populate_categories_combo()
                    print(f"✅ 分类数据加载成功: {len(self.categories_data)} 个分类")
                else:
                    print("❌ 获取分类数据失败")
                    self.category_combo.clear()
                    self.category_combo.addItem("❌ 分类加载失败", None)
            else:
                print("❌ 全局数据未加载")
                self.category_combo.clear()
                self.category_combo.addItem("❌ 数据未加载", None)
                
        except Exception as e:
            print(f"❌ 加载分类数据异常: {e}")
            self.category_combo.clear()
            self.category_combo.addItem("❌ 加载失败", None)
    
    def populate_categories_combo(self):
        """填充分类下拉框 - 🎯 过滤已购买的分类和免费分类"""
        self.category_combo.clear()
        
        # 筛选前数据验证
        if not self.user_purchased_items:
            self._force_refresh_purchased_items()
        
        # 🎯 检查是否还有可购买的分类（收费且未购买）
        available_categories = []
        total_categories = len(self.categories_data)
        skipped_purchased = 0
        skipped_free = 0
        
        for category in self.categories_data:
            # 🎯 修复：与购买记录存储保持一致，优先使用category_id
            category_id = category.get('category_id') or category.get('original_category_id') or category.get('id')
            price = float(category.get('price', 0))
            
            # 跳过已购买的分类
            if f"category_{category_id}" in self.user_purchased_items:
                skipped_purchased += 1
                continue
                
            # 跳过免费分类
            if price == 0:
                skipped_free += 1
                continue
                
            available_categories.append(category)
        
        # 🎯 只有在有可购买分类时才显示"购买整个系列"选项
        if available_categories:
            self.category_combo.addItem("🎯 购买整个系列", None)  # 默认选项：购买整个系列
        
        available_categories_count = 0
        for category in self.categories_data:
            # 🎯 修复：与购买记录存储保持一致，优先使用category_id
            category_id = category.get('category_id') or category.get('original_category_id') or category.get('id')
            title = category.get('title', '未知分类')
            price = float(category.get('price', 0))
            
            # 筛选1：跳过已购买的分类
            if f"category_{category_id}" in self.user_purchased_items:
                skipped_purchased += 1
                continue
            
            # 筛选1.5：跳过待同步的分类
            from ui.pending_purchase_manager import pending_purchase_manager
            if pending_purchase_manager.is_category_pending(self.user_id, category_id):
                skipped_purchased += 1
                continue
            
            # 筛选2：跳过免费分类
            if price == 0:
                skipped_free += 1
                continue
            
            # 只显示收费且未购买的分类
            display_text = f"💰 {title} (¥{price:.2f})"
            
            # 将完整的分类数据存储在UserRole中
            self.category_combo.addItem(display_text, category)
            available_categories_count += 1
        
        # 🎯 如果没有可购买分类，显示提示
        if available_categories_count == 0:
            if skipped_purchased > 0 and skipped_free > 0:
                self.category_combo.addItem("✅ 该系列所有分类均已购买或免费", None)
            elif skipped_purchased > 0:
                self.category_combo.addItem("✅ 该系列所有分类均已购买", None)
            elif skipped_free > 0:
                self.category_combo.addItem("🆓 该系列所有分类均为免费", None)
            else:
                self.category_combo.addItem("❌ 该系列没有分类", None)
        
        # 分类下拉框填充完成
    
    def get_form_data(self) -> Dict[str, Any]:
        """获取表单数据，用于购买记录创建"""
        series_index = self.series_combo.currentIndex()
        category_index = self.category_combo.currentIndex()
        
        if series_index <= 0:  # 未选择系列
            return {}
        
        selected_series = self.series_combo.itemData(series_index)
        selected_category = self.category_combo.itemData(category_index) if category_index > 0 else None
        
        if not selected_series:
            return {}
        
        if selected_category:
            # 购买具体分类 - 使用规范字段名
            return {
                'purchased_entity_type': 'category',
                'purchased_entity_id': selected_category.get('id'),
                'amount': float(selected_category.get('price', 0)),
                'category_name': selected_category.get('title', '未知分类'),
                'series_name': selected_series.get('title', '未知系列'),
                'purchase_type': '分类'
            }
        else:
            # 🎯 购买整个系列：返回该系列下所有未购买且未待同步分类的购买记录列表
            available_categories = []
            from ui.pending_purchase_manager import pending_purchase_manager
            
            for category in self.categories_data:
                # 🎯 修复：与购买记录存储保持一致，优先使用category_id
                category_id = category.get('category_id') or category.get('original_category_id') or category.get('id')
                
                # 检查未购买且未待同步
                is_purchased = f"category_{category_id}" in self.user_purchased_items
                is_pending = pending_purchase_manager.is_category_pending(self.user_id, category_id)
                
                if not is_purchased and not is_pending:
                    available_categories.append(category)
            
            if not available_categories:
                print("⚠️ 警告：该系列下没有可购买的分类（全部已购买）")
                return {}  # 返回空数据，阻止购买
            
            # 🔧 修复：规范化series_all_categories的数据结构
            series_id = selected_series.get('id')
            series_price = float(selected_series.get('price', 0))
            
            # 🔍 调试：分析系列购买的分类数据
            print(f"[DEBUG] 系列购买分析 - 系列ID: {series_id}")
            print(f"[DEBUG] 可购买分类数量: {len(available_categories)}")
            for i, category in enumerate(available_categories):
                print(f"[DEBUG] 分类{i+1}: {category.get('title')} (ID: {category.get('id')})")
            
            # 为每个分类添加规范的字段，确保前后端数据一致性
            normalized_categories = []
            for i, category in enumerate(available_categories):
                normalized_category = category.copy()
                # 🎯 确保分类数据包含明确的category_id字段
                normalized_category['category_id'] = category.get('id')  # 将id映射为category_id
                
                # 🔧 修复：为每个分类购买生成独立的购买UUID
                import uuid
                purchase_uuid = str(uuid.uuid4())
                normalized_category['purchase_uuid'] = purchase_uuid
                print(f"🎯 生成UUID: purchase -> {purchase_uuid} (分类{i+1}: {category.get('title')})")
                
                normalized_categories.append(normalized_category)
            
            return {
                'purchased_entity_type': 'series_all_categories',
                'purchased_entity_id': series_id,  # 🔧 修复：添加缺失的purchased_entity_id字段
                'amount': series_price,            # 🔧 修复：添加缺失的amount字段
                'series_id': series_id,            # 保持兼容性
                'series_name': selected_series.get('title', '未知系列'),
                'series_price': series_price,      # 保持兼容性
                'categories': normalized_categories  # 🔧 修复：使用规范化的分类数据
            }
    
    def _setup_delete_event_listener(self):
        """设置删除事件监听器 - 🎯 新增功能"""
        try:
            # 导入删除事件通知器
            from ui.async_purchase_operations import purchase_delete_notifier
            
            # 连接删除完成信号
            purchase_delete_notifier.deletion_completed.connect(self._on_purchases_deleted)
            
            print(f"✅ [删除事件监听] 已为用户 {self.user_id} 设置删除事件监听器")
            
        except Exception as e:
            print(f"❌ [删除事件监听] 设置删除事件监听器失败: {e}")
    
    def _on_purchases_deleted(self, deleted_user_id: str, deleted_items: List[Dict]):
        """处理购买记录删除完成事件 - 🎯 新增功能"""
        try:
            # 只处理当前用户的删除事件
            if deleted_user_id != self.user_id:
                return
                
            print(f"🔄 [删除事件处理] 收到用户 {deleted_user_id} 的删除完成事件，{len(deleted_items)} 个项目")
            
            # 提取删除的分类和系列信息
            deleted_categories = set()
            deleted_series = set()
            
            for item in deleted_items:
                category_id = item.get('category_id')
                series_id = item.get('series_id')
                
                if category_id:
                    deleted_categories.add(category_id)
                if series_id:
                    deleted_series.add(series_id)
            
            print(f"📋 [删除事件处理] 识别到删除的分类: {len(deleted_categories)} 个, 系列: {len(deleted_series)} 个")
            
            # 🎯 修复：延时处理，确保缓存清理完成后再刷新数据
            from PyQt6.QtCore import QTimer
            QTimer.singleShot(100, self._refresh_dropdown_data)
            
        except Exception as e:
            print(f"❌ [删除事件处理] 处理删除事件失败: {e}")
    
    def _refresh_dropdown_data(self):
        """刷新下拉框数据 - 🎯 新增功能"""
        try:
            print(f"🔄 [数据刷新] 开始刷新购买记录对话框的下拉框数据...")
            
            # 清空现有数据
            self.series_data = []
            self.categories_data = []
            self.user_purchased_items = {}
            
            # 重新启动异步数据刷新
            self._start_async_cache_refresh()
            
            print(f"✅ [数据刷新] 下拉框数据刷新已启动")
            
        except Exception as e:
            print(f"❌ [数据刷新] 刷新下拉框数据失败: {e}")
    
    def _check_recent_deletions(self):
        """检查最近的删除状态，解决时序问题 - 🎯 修复：主动状态检查"""
        try:
            # 方法1：检查全局数据管理器的缓存状态
            from cache.global_data_manager import global_data_manager
            
            # 如果用户购买记录缓存已被清理，说明可能发生了删除操作
            if global_data_manager.is_data_loaded():
                if self.user_id not in global_data_manager._user_purchases:
                    print(f"🔍 [删除状态检查] 检测到可能的删除操作，触发数据刷新")
                    self._trigger_data_refresh()
                    return
            
            # 方法2：检查待同步管理器的状态
            from ui.pending_purchase_manager import pending_purchase_manager
            
            # 🎯 修复：检查待同步购买记录是否为空
            pending_data = pending_purchase_manager.get_pending_purchases(self.user_id)
            if not pending_data.get('categories') and not pending_data.get('series'):
                if hasattr(global_data_manager, '_user_purchases') and self.user_id in global_data_manager._user_purchases:
                    cached_purchases = global_data_manager._user_purchases[self.user_id]
                    if cached_purchases.get('success') and len(cached_purchases.get('data', [])) == 0:
                        print(f"🔍 [删除状态检查] 检测到购买记录为空，触发数据刷新")
                        self._trigger_data_refresh()
                        return
            
            # 状态正常，无需额外日志
            
        except Exception as e:
            print(f"❌ [删除状态检查] 检查失败: {e}")
            # 降级处理：强制刷新数据
            self._trigger_data_refresh()
    
    def _trigger_data_refresh(self):
        """触发数据刷新 - 🎯 修复：统一的数据刷新触发点"""
        try:
            # 清空现有数据
            self.series_data = []
            self.categories_data = []
            self.user_purchased_items = {}
            
            # 强制重新加载数据
            self._start_async_cache_refresh()
            
        except Exception as e:
            print(f"❌ [数据刷新触发] 失败: {e}")