---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程 

**核心原则**：
- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🛑 强制执行机制

### 阶段门禁条件（函数返回值控制）
```python
class StageGateError(Exception):
    """阶段门禁异常"""
    pass

def validate_stage_gate(stage: str, result: Any) -> bool:
    """验证阶段门禁条件"""
    gates = {
        "stage1_to_stage2": lambda r: (r.file_count >= 5 and 
                                     r.symptoms_identified and 
                                     r.todo_completed),
        "stage2_to_stage3": lambda r: (r.exit_conditions_met and 
                                     r.hypothesis_verification_complete and 
                                     r.todo_verification_recorded),
        "stage3_to_stage4": lambda r: (r.verification_pass_rate == 1.0 and 
                                     r.no_side_effects and 
                                     r.todo_repair_recorded),
        "stage4_complete": lambda r: (r.summary_complete and 
                                    r.optimization_specific and 
                                    r.todo_all_completed)
    }
    
    if stage in gates and not gates[stage](result):
        raise StageGateError(f"阶段门禁未满足: {stage}")
    return True
```

### 违规定义和后果
```python
class ViolationError(Exception):
    """违规异常"""
    def __init__(self, violation_type: str, message: str, severity: str):
        self.violation_type = violation_type
        self.severity = severity
        super().__init__(f"{severity}: {violation_type} - {message}")

def check_violations(execution_context: dict) -> None:
    """检查执行违规"""
    severe_violations = [
        ("skip_loop", "跳过循环机制", lambda ctx: ctx.get('loops_executed', 0) == 0),
        ("imagination_guess", "凭想象猜测", lambda ctx: not ctx.get('real_verification', False)),
        ("early_exit", "提前退出阶段", lambda ctx: not ctx.get('gate_conditions_met', False)),
        ("simplified_impl", "简化实现逃避", lambda ctx: ctx.get('temp_solution', False)),
        ("separate_hyp_verify", "分离假设-验证", lambda ctx: not ctx.get('complete_loop', True))
    ]
    
    for violation_id, description, check_func in severe_violations:
        if check_func(execution_context):
            raise ViolationError(violation_id, description, "SEVERE")
```

## 🚀 主执行函数

```python
def diagnose_and_fix($ARGUMENTS: str) -> DiagnosisResult:
    """
    主诊断函数 - 完整4阶段执行
    
    Args:
        $ARGUMENTS: 用户问题描述和相关信息
        
    Returns:
        DiagnosisResult: 包含修复结果和总结的对象
        
    Raises:
        StageGateError: 阶段门禁条件不满足
        ViolationError: 违反执行规范
    """
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.init_diagnostic_flow()
    
    try:
        # 阶段1：信息收集与真实检查
        todo_tracker.start_stage("阶段1: 信息收集与真实检查")
        stage1_result = information_collection_phase($ARGUMENTS, todo_tracker)
        validate_stage_gate("stage1_to_stage2", stage1_result)
        todo_tracker.complete_stage("阶段1")
        
        # 阶段2：假设-验证循环
        todo_tracker.start_stage("阶段2: 假设-验证循环")
        stage2_result = hypothesis_verification_phase(stage1_result, todo_tracker)
        validate_stage_gate("stage2_to_stage3", stage2_result)
        todo_tracker.complete_stage("阶段2")
        
        # 阶段3：修复实施与验证循环
        todo_tracker.start_stage("阶段3: 修复实施与验证循环")
        stage3_result = repair_implementation_phase(stage2_result, todo_tracker)
        validate_stage_gate("stage3_to_stage4", stage3_result)
        todo_tracker.complete_stage("阶段3")
        
        # 阶段4：总结与发散优化
        todo_tracker.start_stage("阶段4: 总结与发散优化")
        stage4_result = summary_optimization_phase(stage3_result, todo_tracker)
        validate_stage_gate("stage4_complete", stage4_result)
        todo_tracker.complete_stage("阶段4")
        
        return DiagnosisResult(
            success=True,
            stage_results=[stage1_result, stage2_result, stage3_result, stage4_result],
            todo_tracking=todo_tracker.get_final_state()
        )
        
    except (StageGateError, ViolationError) as e:
        print(f"🚨 执行异常: {str(e)}")
        return retry_from_violation(e, todo_tracker)
    except Exception as e:
        return DiagnosisResult(success=False, error=str(e))
```

---

## **🔍 阶段1：信息收集与真实检查**

```python
def information_collection_phase($ARGUMENTS: str, todo_tracker: TodoTracker) -> Stage1Result:
    """
    阶段1：信息收集与真实检查
    
    强制要求：本阶段必须完成多维度信息搜索和真实检查
    成功标准：识别核心症状 + 制定搜索策略 + 完成系统状态检查
    门禁条件：必须满足所有检查要求才能进入阶段2
    """
    
    result = Stage1Result()
    
    # 步骤1.1: 问题信息解析
    todo_tracker.start_step("1.1: 问题信息解析")
    info_analysis = parse_problem_information($ARGUMENTS)
    result.add_step_result("1.1", info_analysis)
    todo_tracker.complete_step("1.1")
    
    # 步骤1.2: 强制真实检查
    todo_tracker.start_step("1.2: 强制真实检查")
    real_check = mandatory_real_verification(info_analysis)
    result.add_step_result("1.2", real_check)
    todo_tracker.complete_step("1.2")
    
    # 质量验证
    if not validate_stage1_quality(result):
        raise StageGateError("阶段1质量不达标，需要补充搜索和验证")
    
    return result

def parse_problem_information($ARGUMENTS: str) -> dict:
    """步骤1.1: 问题信息解析"""
    
    # 1.1.1 问题描述分析
    core_symptoms = extract_core_symptoms($ARGUMENTS)
    modules_involved = identify_modules_and_components($ARGUMENTS)
    trigger_conditions = analyze_trigger_conditions($ARGUMENTS)
    impact_scope = determine_impact_scope($ARGUMENTS)
    
    # 1.1.2 搜索策略制定
    search_strategy = create_multidimensional_search_strategy(
        core_symptoms, modules_involved, trigger_conditions
    )
    
    print(f"📋 问题解析完成:")
    print(f"  核心症状: {core_symptoms}")
    print(f"  涉及模块: {modules_involved}")
    print(f"  触发条件: {trigger_conditions}")
    print(f"  影响范围: {impact_scope}")
    
    return {
        "core_symptoms": core_symptoms,
        "modules_involved": modules_involved,
        "trigger_conditions": trigger_conditions,
        "impact_scope": impact_scope,
        "search_strategy": search_strategy
    }

def create_multidimensional_search_strategy(symptoms: list, modules: list, conditions: list) -> dict:
    """制定多维度搜索策略"""
    
    strategy = {
        "file_patterns": [],
        "content_keywords": [],
        "config_searches": [],
        "log_searches": [],
        "reference_searches": []
    }
    
    # 文件名模式搜索（Glob工具）
    for module in modules:
        strategy["file_patterns"].extend([
            f"**/*{module}*",
            f"**/*{module.lower()}*",
            f"**/*{module}_*"
        ])
    
    # 内容关键词搜索（Grep工具）  
    for symptom in symptoms:
        strategy["content_keywords"].extend([
            symptom,
            symptom.lower(),
            symptom.replace(" ", "_")
        ])
    
    # 错误信息相关关键词
    error_keywords = ["错误", "异常", "失败", "Error", "Exception", "Failed"]
    strategy["content_keywords"].extend(error_keywords)
    
    return strategy

def mandatory_real_verification(info_analysis: dict) -> dict:
    """步骤1.2: 强制真实检查（禁止猜测）"""
    
    verification_result = {
        "search_results": {},
        "search_completeness": {},
        "system_status": {}
    }
    
    # 1.2.1 多工具组合搜索
    search_strategy = info_analysis["search_strategy"]
    
    print("🔎 执行多工具组合搜索...")
    
    # Glob搜索
    glob_results = []
    for pattern in search_strategy["file_patterns"]:
        files = glob_search(pattern)
        glob_results.extend(files)
        print(f"  Glob '{pattern}': {len(files)}个文件")
    
    # Grep搜索
    grep_results = []
    for keyword in search_strategy["content_keywords"]:
        matches = grep_search(keyword, include="*.py,*.js,*.json")
        grep_results.extend(matches)
        print(f"  Grep '{keyword}': {len(matches)}处匹配")
    
    # Task工具复杂搜索
    task_results = complex_cross_reference_search(info_analysis)
    print(f"  Task工具: {len(task_results)}个复杂关联")
    
    verification_result["search_results"] = {
        "glob_files": list(set(glob_results)),
        "grep_matches": list(set(grep_results)),
        "task_findings": task_results
    }
    
    # 1.2.2 搜索完整性验证
    completeness = verify_search_completeness(verification_result["search_results"])
    verification_result["search_completeness"] = completeness
    
    # 1.2.3 系统状态检查
    system_status = comprehensive_system_status_check()
    verification_result["system_status"] = system_status
    
    return verification_result

def comprehensive_system_status_check() -> dict:
    """系统状态检查"""
    
    status = {
        "service_detection": False,
        "service_startup": False,
        "connection_verified": False,
        "environment_check": {}
    }
    
    print("🔧 执行系统状态检查...")
    
    # 服务状态检测与智能启动
    print("  1. 服务状态检测...")
    try:
        response_code = bash_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs')
        if response_code == "200":
            print("    ✅ 服务正常运行")
            status["service_detection"] = True
            status["connection_verified"] = True
        else:
            print(f"    ⚠️ 服务异常 (状态码: {response_code})，尝试启动...")
            startup_result = attempt_service_startup()
            status["service_startup"] = startup_result["success"]
            status["connection_verified"] = startup_result["verified"]
    except Exception as e:
        print(f"    ❌ 服务检测失败: {str(e)}")
        startup_result = attempt_service_startup()
        status["service_startup"] = startup_result["success"]
    
    # 基础环境检查
    print("  2. 基础环境检查...")
    env_checks = {
        "key_files_exist": check_key_files_existence(),
        "config_files_valid": verify_config_files(),
        "log_files_accessible": check_log_files(),
        "database_reachable": test_database_connectivity()
    }
    
    status["environment_check"] = env_checks
    
    for check_name, result in env_checks.items():
        print(f"    {check_name}: {'✅' if result else '❌'}")
    
    return status

def attempt_service_startup() -> dict:
    """尝试启动服务"""
    try:
        # 启动服务
        startup_cmd = "cd mock_server && timeout 10s python src/main.py --port 8000 &"
        bash_command(startup_cmd)
        
        # 等待就绪
        bash_command("sleep 3")
        
        # 验证连接
        response_code = bash_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs')
        
        if response_code == "200":
            print("    ✅ 服务启动成功")
            return {"success": True, "verified": True}
        else:
            print(f"    ⚠️ 服务启动但连接异常，可能需要在Windows环境中启动")
            return {"success": False, "verified": False}
            
    except Exception as e:
        print(f"    ❌ 服务启动失败: {str(e)}")
        print("    💡 建议：请在Windows环境中手动启动mock_server服务")
        return {"success": False, "verified": False}

def validate_stage1_quality(result: Stage1Result) -> bool:
    """验证阶段1质量"""
    
    # 获取搜索结果
    search_results = result.get_step_result("1.2")["search_results"]
    
    # 质量标准检查
    total_files = len(set(
        search_results["glob_files"] + 
        search_results["grep_matches"]
    ))
    
    criteria = {
        "file_count_sufficient": total_files >= 5,
        "tools_used_complete": all([
            len(search_results["glob_files"]) > 0,
            len(search_results["grep_matches"]) > 0,
            len(search_results["task_findings"]) > 0
        ]),
        "symptoms_identified": len(result.get_step_result("1.1")["core_symptoms"]) > 0,
        "search_strategy_complete": result.get_step_result("1.1")["search_strategy"] is not None
    }
    
    print("📊 阶段1质量检查:")
    for criterion, passed in criteria.items():
        print(f"  {criterion}: {'✅' if passed else '❌'}")
    
    return all(criteria.values())
```

---

## **🔬 阶段2：假设-验证循环**

```python
def hypothesis_verification_phase(stage1_result: Stage1Result, todo_tracker: TodoTracker) -> Stage2Result:
    """
    阶段2：假设-验证循环
    
    强制要求：本阶段必须执行完整假设-验证循环机制（2-5轮）
    成功标准：系统性假设建立 + 充分验证迭代 + 动态假设优化 + 基于客观标准退出
    门禁条件：满足科学化循环退出条件才能进入阶段3
    """
    
    result = Stage2Result()
    
    # 强制循环控制参数
    loop_control = {
        "min_rounds": 2,
        "max_rounds": 5,
        "current_round": 0,
        "hypothesis_verified_count": 0,
        "confirmed_issues_count": 0,
        "new_findings_history": []
    }
    
    # 初始化循环追踪
    todo_tracker.init_loop_tracking("阶段2", loop_control["max_rounds"])
    
    # 执行强制循环
    while should_continue_hypothesis_loop(loop_control, result):
        loop_control["current_round"] += 1
        round_num = loop_control["current_round"]
        
        print(f"\n🔄 === 阶段2第{round_num}轮循环 ===")
        todo_tracker.start_loop_round("阶段2", round_num)
        
        # 步骤2.1: 假设建立与调整
        todo_tracker.start_step(f"2.1: 假设建立与调整 (第{round_num}轮)")
        hypotheses = hypothesis_establishment_step(round_num, stage1_result, result)
        todo_tracker.complete_step(f"2.1")
        
        # 步骤2.2: 假设验证执行
        todo_tracker.start_step(f"2.2: 假设验证执行 (第{round_num}轮)")
        verification_results = hypothesis_verification_step(hypotheses)
        todo_tracker.complete_step(f"2.2")
        
        # 步骤2.3: 结果分析与新发现
        todo_tracker.start_step(f"2.3: 结果分析与新发现 (第{round_num}轮)")
        analysis_results = result_analysis_step(verification_results, round_num)
        todo_tracker.complete_step(f"2.3")
        
        # 记录轮次结果
        round_result = {
            "round": round_num,
            "hypotheses": hypotheses,
            "verification_results": verification_results,
            "analysis_results": analysis_results,
            "new_findings": analysis_results["new_findings_count"],
            "confirmed_issues": analysis_results["confirmed_issues_count"]
        }
        
        result.add_round(round_result)
        
        # 更新循环控制参数
        loop_control["hypothesis_verified_count"] += len(verification_results)
        loop_control["confirmed_issues_count"] += analysis_results["confirmed_issues_count"]
        loop_control["new_findings_history"].append(analysis_results["new_findings_count"])
        
        # 记录轮次统计
        print(f"📊 第{round_num}轮统计:")
        print(f"  验证假设: {len(verification_results)}个")
        print(f"  确认问题: {analysis_results['confirmed_issues_count']}个")
        print(f"  新发现: {analysis_results['new_findings_count']}个")
        print(f"  累计确认: {loop_control['confirmed_issues_count']}个问题")
        
        todo_tracker.complete_loop_round("阶段2", round_num, {
            "verified_hypotheses": len(verification_results),
            "confirmed_issues": analysis_results["confirmed_issues_count"],
            "new_findings": analysis_results["new_findings_count"]
        })
        
        # 强制退出条件检查
        exit_reason = check_stage2_exit_conditions(loop_control, result)
        if exit_reason:
            print(f"🔚 循环退出: {exit_reason}")
            break
    
    # 验证阶段2完成质量
    if not validate_stage2_completion(result, loop_control):
        raise StageGateError("阶段2未满足完成条件")
    
    return result

def should_continue_hypothesis_loop(loop_control: dict, result: Stage2Result) -> bool:
    """判断是否继续假设验证循环"""
    
    current_round = loop_control["current_round"]
    max_rounds = loop_control["max_rounds"]
    
    # 检查是否达到最大轮次
    if current_round >= max_rounds:
        return False
    
    # 第1轮强制执行
    if current_round == 0:
        return True
    
    # 检查其他退出条件
    exit_reason = check_stage2_exit_conditions(loop_control, result)
    return exit_reason is None

def hypothesis_establishment_step(round_num: int, stage1_result: Stage1Result, stage2_result: Stage2Result) -> list:
    """步骤2.1: 假设建立与调整"""
    
    if round_num == 1:
        # 第1轮：系统性假设建立
        print("🧠 建立系统性假设体系...")
        hypotheses = create_systematic_hypotheses(stage1_result)
    else:
        # 第2-N轮：动态假设调整
        print(f"🔧 基于第{round_num-1}轮结果调整假设...")
        previous_round = stage2_result.get_latest_round()
        hypotheses = adjust_hypotheses_dynamically(previous_round)
    
    # 假设优先级排序
    prioritized_hypotheses = prioritize_hypotheses(hypotheses)
    
    # 验证方法制定
    for hypothesis in prioritized_hypotheses:
        hypothesis["verification_method"] = assign_verification_method(hypothesis)
    
    print(f"📋 假设建立完成: {len(prioritized_hypotheses)}个")
    for i, hyp in enumerate(prioritized_hypotheses, 1):
        print(f"  {i}. {hyp['description']} (优先级: {hyp['priority']}, 层次: {hyp['layer']})")
    
    return prioritized_hypotheses

def create_systematic_hypotheses(stage1_result: Stage1Result) -> list:
    """建立多层次问题假设体系"""
    
    hypotheses = []
    
    # 基础层假设
    base_hypotheses = [
        {
            "id": "SYNTAX_ERROR",
            "description": "代码语法、拼写错误、导入错误",
            "layer": "BASIC",
            "priority": "HIGH",
            "type": "SYNTAX"
        },
        {
            "id": "CONFIG_ERROR", 
            "description": "配置文件格式、路径错误、参数错误",
            "layer": "BASIC",
            "priority": "HIGH",
            "type": "CONFIG"
        },
        {
            "id": "PERMISSION_ERROR",
            "description": "文件权限、数据库权限、网络权限",
            "layer": "BASIC", 
            "priority": "MEDIUM",
            "type": "PERMISSION"
        }
    ]
    
    # 逻辑层假设
    logic_hypotheses = [
        {
            "id": "BUSINESS_LOGIC_ERROR",
            "description": "算法实现、条件判断、数据处理逻辑",
            "layer": "LOGIC",
            "priority": "HIGH",
            "type": "BUSINESS"
        },
        {
            "id": "STATE_MANAGEMENT_ERROR",
            "description": "对象状态、数据状态、会话状态",
            "layer": "LOGIC",
            "priority": "MEDIUM", 
            "type": "STATE"
        },
        {
            "id": "TIMING_ERROR",
            "description": "操作顺序、异步处理、事件时序",
            "layer": "LOGIC",
            "priority": "MEDIUM",
            "type": "TIMING"
        }
    ]
    
    # 系统层假设
    system_hypotheses = [
        {
            "id": "ARCHITECTURE_PROBLEM",
            "description": "模块耦合、接口设计、数据流设计",
            "layer": "SYSTEM",
            "priority": "MEDIUM",
            "type": "ARCHITECTURE"
        },
        {
            "id": "PERFORMANCE_PROBLEM", 
            "description": "资源消耗、响应时间、并发处理",
            "layer": "SYSTEM",
            "priority": "LOW",
            "type": "PERFORMANCE"
        }
    ]
    
    # 集成层假设
    integration_hypotheses = [
        {
            "id": "MODULE_INTEGRATION_PROBLEM",
            "description": "接口调用、数据传递、错误传播",
            "layer": "INTEGRATION",
            "priority": "HIGH",
            "type": "MODULE_INTEGRATION"
        },
        {
            "id": "EXTERNAL_INTEGRATION_PROBLEM",
            "description": "数据库连接、API调用、文件系统访问",
            "layer": "INTEGRATION", 
            "priority": "HIGH",
            "type": "EXTERNAL_INTEGRATION"
        }
    ]
    
    hypotheses.extend(base_hypotheses)
    hypotheses.extend(logic_hypotheses)
    hypotheses.extend(system_hypotheses)
    hypotheses.extend(integration_hypotheses)
    
    return hypotheses

def hypothesis_verification_step(hypotheses: list) -> list:
    """步骤2.2: 假设验证执行"""
    
    verification_results = []
    
    print("🔍 开始系统性假设验证...")
    
    for i, hypothesis in enumerate(hypotheses, 1):
        print(f"  验证假设{i}: {hypothesis['description']}")
        
        try:
            # 执行验证
            result = execute_hypothesis_verification(hypothesis)
            verification_results.append(result)
            
            status = "✅ 确认" if result["confirmed"] else "❌ 排除"
            print(f"    └─ 结果: {status}")
            
            if result.get("evidence"):
                print(f"    └─ 证据: {result['evidence']}")
                
        except Exception as e:
            print(f"    └─ 验证失败: {str(e)}")
            verification_results.append({
                "hypothesis_id": hypothesis["id"],
                "confirmed": False,
                "error": str(e),
                "verification_method": hypothesis.get("verification_method", "unknown")
            })
    
    # 验证结果统计
    confirmed_count = sum(1 for r in verification_results if r.get("confirmed"))
    total_count = len(verification_results)
    
    print(f"📊 验证统计:")
    print(f"  验证假设: {total_count}个")
    print(f"  确认问题: {confirmed_count}个")
    print(f"  确认率: {confirmed_count/total_count*100:.1f}%")
    
    return verification_results

def execute_hypothesis_verification(hypothesis: dict) -> dict:
    """执行具体的假设验证"""
    
    verification_method = hypothesis.get("verification_method", "generic")
    
    if verification_method == "code_analysis":
        return verify_by_code_analysis(hypothesis)
    elif verification_method == "config_check": 
        return verify_by_config_check(hypothesis)
    elif verification_method == "api_test":
        return verify_by_api_test(hypothesis)
    elif verification_method == "database_query":
        return verify_by_database_query(hypothesis)
    else:
        return verify_by_generic_search(hypothesis)

def verify_by_code_analysis(hypothesis: dict) -> dict:
    """代码层验证"""
    
    try:
        # 1. 语法检查
        syntax_issues = []
        python_files = glob_search("**/*.py")
        
        for file_path in python_files[:10]:  # 限制检查数量
            issues = check_file_syntax(file_path)
            syntax_issues.extend(issues)
        
        # 2. 逻辑验证
        logic_issues = []
        if "BUSINESS" in hypothesis["type"]:
            logic_issues = analyze_business_logic(hypothesis)
        
        # 3. 导入检查
        import_issues = check_import_statements(python_files[:5])
        
        all_issues = syntax_issues + logic_issues + import_issues
        
        return {
            "hypothesis_id": hypothesis["id"],
            "confirmed": len(all_issues) > 0,
            "evidence": f"发现{len(all_issues)}个代码问题" if all_issues else None,
            "details": all_issues,
            "verification_method": "code_analysis"
        }
        
    except Exception as e:
        return {
            "hypothesis_id": hypothesis["id"],
            "confirmed": False,
            "error": str(e),
            "verification_method": "code_analysis"
        }

def verify_by_api_test(hypothesis: dict) -> dict:
    """功能层验证"""
    
    try:
        # 1. 渐进式API功能验证
        
        # Level 1: 基础连通性验证
        basic_connectivity = test_basic_api_connectivity()
        
        # Level 2: 核心功能验证（基于发现的API端点）
        api_endpoints = discover_api_endpoints()
        core_functionality = test_core_api_functions(api_endpoints)
        
        # Level 3: 业务流程验证
        business_flow = test_business_flow_apis(api_endpoints)
        
        issues = []
        if not basic_connectivity["success"]:
            issues.append(f"API连通性问题: {basic_connectivity['error']}")
        
        for func_test in core_functionality:
            if not func_test["success"]:
                issues.append(f"核心功能问题: {func_test['endpoint']} - {func_test['error']}")
        
        return {
            "hypothesis_id": hypothesis["id"],
            "confirmed": len(issues) > 0,
            "evidence": f"发现{len(issues)}个API问题" if issues else None,
            "details": issues,
            "verification_method": "api_test"
        }
        
    except Exception as e:
        return {
            "hypothesis_id": hypothesis["id"],
            "confirmed": False,
            "error": str(e),
            "verification_method": "api_test"
        }

def discover_api_endpoints() -> list:
    """自动发现API端点"""
    try:
        # 从OpenAPI文档自动发现
        openapi_response = bash_command('curl -s http://localhost:8000/openapi.json')
        openapi_data = json.loads(openapi_response)
        
        endpoints = []
        for path, methods in openapi_data.get("paths", {}).items():
            for method, details in methods.items():
                endpoints.append({
                    "path": path,
                    "method": method.upper(),
                    "summary": details.get("summary", ""),
                    "tags": details.get("tags", [])
                })
        
        # 优先级排序：GET > 简单参数 > 常见功能
        endpoints.sort(key=lambda x: (
            0 if x["method"] == "GET" else 1,
            0 if not any(param in x["path"] for param in ["{", "}"]) else 1,
            0 if any(tag in ["health", "status", "info"] for tag in x["tags"]) else 1
        ))
        
        return endpoints[:10]  # 返回前10个最适合测试的端点
        
    except Exception as e:
        print(f"API端点发现失败: {str(e)}")
        return []

def check_stage2_exit_conditions(loop_control: dict, result: Stage2Result) -> str:
    """检查阶段2退出条件"""
    
    current_round = loop_control["current_round"]
    min_rounds = loop_control["min_rounds"]
    max_rounds = loop_control["max_rounds"]
    
    # 基础退出条件
    if current_round >= max_rounds:
        return f"达到最大轮次限制({max_rounds})"
    
    if current_round < min_rounds:
        return None  # 强制执行最少轮次
    
    # 科学化退出条件
    confirmed_issues = loop_control["confirmed_issues_count"]
    new_findings = loop_control["new_findings_history"]
    
    # 退出条件1: 假设验证完成率100% + 连续2轮无新假设产生 + 连续2轮无新问题发现
    if len(new_findings) >= 2 and new_findings[-2:] == [0, 0] and confirmed_issues > 0:
        return "连续2轮无新发现且已确认问题"
    
    # 退出条件2: 未找到任何确认问题（但至少执行2轮后才能以此退出）
    if current_round >= min_rounds and confirmed_issues == 0:
        return "未找到确认问题"
    
    return None  # 继续循环

def validate_stage2_completion(result: Stage2Result, loop_control: dict) -> bool:
    """验证阶段2完成质量"""
    
    criteria = {
        "min_rounds_executed": len(result.rounds) >= loop_control["min_rounds"],
        "hypotheses_sufficient": sum(len(r["hypotheses"]) for r in result.rounds) >= 4,
        "confirmed_issues_found": loop_control["confirmed_issues_count"] >= 1,
        "verification_complete": True  # 所有假设都已验证
    }
    
    print("📊 阶段2完成质量检查:")
    for criterion, passed in criteria.items():
        print(f"  {criterion}: {'✅' if passed else '❌'}")
    
    return all(criteria.values())
```

---

## **🔧 阶段3：修复实施与验证循环**

```python
def repair_implementation_phase(stage2_result: Stage2Result, todo_tracker: TodoTracker) -> Stage3Result:
    """
    阶段3：修复实施与验证循环
    
    强制要求：本阶段必须执行完整修复-验证循环机制（1-5轮）
    成功标准：根本性修复实施 + 充分验证迭代 + 100%验证通过
    门禁条件：验证通过率100%且无副作用才能进入阶段4
    """
    
    result = Stage3Result()
    
    # 强制循环控制参数
    loop_control = {
        "min_rounds": 1,
        "max_rounds": 5,
        "current_round": 0,
        "target_pass_rate": 1.0,
        "total_verification_items": 0,
        "passed_verification_items": 0
    }
    
    # 初始化循环追踪
    todo_tracker.init_loop_tracking("阶段3", loop_control["max_rounds"])
    
    # 收集需要修复的问题
    issues_to_fix = extract_confirmed_issues(stage2_result)
    loop_control["total_verification_items"] = count_verification_items(issues_to_fix)
    
    print(f"🔧 开始修复实施，需要修复 {len(issues_to_fix)} 个问题")
    
    # 执行强制循环
    while should_continue_repair_loop(loop_control, result):
        loop_control["current_round"] += 1
        round_num = loop_control["current_round"]
        
        print(f"\n🔄 === 阶段3第{round_num}轮循环 ===")
        todo_tracker.start_loop_round("阶段3", round_num)
        
        # 步骤3.1: 修复方案设计与调整
        todo_tracker.start_step(f"3.1: 修复方案设计与调整 (第{round_num}轮)")
        fix_plans = repair_plan_design_step(round_num, issues_to_fix, result)
        todo_tracker.complete_step(f"3.1")
        
        # 步骤3.2: 修复方案实施
        todo_tracker.start_step(f"3.2: 修复方案实施 (第{round_num}轮)")
        implementation_results = repair_implementation_step(fix_plans)
        todo_tracker.complete_step(f"3.2")
        
        # 步骤3.3: 修复效果验证
        todo_tracker.start_step(f"3.3: 修复效果验证 (第{round_num}轮)")
        verification_results = repair_verification_step(implementation_results)
        todo_tracker.complete_step(f"3.3")
        
        # 步骤3.4: 验证结果评估
        todo_tracker.start_step(f"3.4: 验证结果评估 (第{round_num}轮)")
        evaluation_results = verification_evaluation_step(verification_results, round_num)
        todo_tracker.complete_step(f"3.4")
        
        # 记录轮次结果
        round_result = {
            "round": round_num,
            "fix_plans": fix_plans,
            "implementation_results": implementation_results,
            "verification_results": verification_results,
            "evaluation_results": evaluation_results
        }
        
        result.add_round(round_result)
        
        # 更新循环控制参数
        current_pass_rate = evaluation_results["verification_pass_rate"]
        loop_control["passed_verification_items"] = evaluation_results["passed_items"]
        
        # 记录轮次统计
        print(f"📊 第{round_num}轮统计:")
        print(f"  修复方案: {len(fix_plans)}个")
        print(f"  实施成功: {implementation_results['success_count']}个")
        print(f"  验证通过率: {current_pass_rate*100:.1f}%")
        print(f"  通过项目: {loop_control['passed_verification_items']}/{loop_control['total_verification_items']}")
        
        todo_tracker.complete_loop_round("阶段3", round_num, {
            "fix_plans": len(fix_plans),
            "implementation_success": implementation_results["success_count"],
            "verification_pass_rate": current_pass_rate
        })
        
        # 强制退出条件检查
        exit_reason = check_stage3_exit_conditions(loop_control, result)
        if exit_reason:
            print(f"🔚 循环退出: {exit_reason}")
            break
    
    # 验证阶段3完成质量
    if not validate_stage3_completion(result, loop_control):
        raise StageGateError("阶段3未满足完成条件")
    
    return result

def repair_plan_design_step(round_num: int, issues_to_fix: list, stage3_result: Stage3Result) -> list:
    """步骤3.1: 修复方案设计与调整"""
    
    if round_num == 1:
        # 第1轮：初始修复方案设计
        print("🛠️ 设计初始修复方案...")
        fix_plans = design_initial_repair_plans(issues_to_fix)
    else:
        # 第2-N轮：修复方案调整
        print(f"🔧 基于第{round_num-1}轮评估调整修复方案...")
        previous_round = stage3_result.get_latest_round()
        fix_plans = adjust_repair_plans(previous_round)
    
    # 验证修复方案的根本性原则
    for plan in fix_plans:
        validate_repair_plan_principles(plan)
    
    print(f"📋 修复方案设计完成: {len(fix_plans)}个")
    for i, plan in enumerate(fix_plans, 1):
        print(f"  {i}. {plan['description']} (策略: {plan['strategy']}, 影响: {plan['impact_assessment']})")
    
    return fix_plans

def design_initial_repair_plans(issues: list) -> list:
    """设计初始修复方案"""
    
    plans = []
    
    for issue in issues:
        # 根本性修复原则
        plan = {
            "issue_id": issue["id"],
            "description": issue["description"],
            "root_cause": issue["root_cause"],
            "strategy": determine_repair_strategy(issue),
            "actions": design_repair_actions(issue),
            "impact_assessment": assess_repair_impact(issue),
            "rollback_plan": create_rollback_plan(issue)
        }
        
        plans.append(plan)
    
    return plans

def determine_repair_strategy(issue: dict) -> str:
    """确定修复策略"""
    
    root_cause = issue.get("root_cause", {})
    issue_type = root_cause.get("type", "unknown")
    
    strategy_map = {
        "SYNTAX": "代码修复",
        "CONFIG": "配置修复", 
        "BUSINESS": "业务逻辑修复",
        "DATA": "数据修复",
        "ARCHITECTURE": "架构重构",
        "INTEGRATION": "集成修复"
    }
    
    return strategy_map.get(issue_type, "通用修复")

def repair_implementation_step(fix_plans: list) -> dict:
    """步骤3.2: 修复方案实施"""
    
    implementation_results = {
        "implementations": [],
        "success_count": 0,
        "failure_count": 0,
        "total_count": len(fix_plans)
    }
    
    print("⚙️ 开始修复实施...")
    
    for i, plan in enumerate(fix_plans, 1):
        print(f"  🔨 实施修复{i}: {plan['description']}")
        
        try:
            # 执行修复操作
            implementation = execute_repair_implementation(plan)
            
            # 原子性检查
            if verify_implementation_atomicity(implementation):
                implementation_results["implementations"].append(implementation)
                implementation_results["success_count"] += 1
                print(f"    ✅ 修复{i}实施成功")
            else:
                print(f"    ❌ 修复{i}原子性检查失败")
                implementation_results["failure_count"] += 1
                
        except Exception as e:
            print(f"    ❌ 修复{i}实施异常: {str(e)}")
            implementation_results["failure_count"] += 1
    
    print(f"📊 实施统计: 成功{implementation_results['success_count']}/{implementation_results['total_count']}")
    
    return implementation_results

def execute_repair_implementation(plan: dict) -> dict:
    """执行具体修复实施"""
    
    implementation = {
        "plan_id": plan["issue_id"],
        "actions_executed": [],
        "files_modified": [],
        "rollback_info": [],
        "success": True
    }
    
    for action in plan["actions"]:
        try:
            if action["type"] == "EDIT_FILE":
                # 文件编辑
                result = execute_file_edit(action)
                implementation["actions_executed"].append(result)
                implementation["files_modified"].append(action["file_path"])
                
            elif action["type"] == "ADD_CODE":
                # 添加代码
                result = execute_code_addition(action)
                implementation["actions_executed"].append(result)
                implementation["files_modified"].append(action["file_path"])
                
            elif action["type"] == "CONFIG_CHANGE":
                # 配置修改
                result = execute_config_change(action)
                implementation["actions_executed"].append(result)
                implementation["files_modified"].append(action["config_path"])
                
            elif action["type"] == "DATA_REPAIR":
                # 数据修复
                result = execute_data_repair(action)
                implementation["actions_executed"].append(result)
                
        except Exception as e:
            implementation["success"] = False
            implementation["error"] = str(e)
            break
    
    return implementation

def repair_verification_step(implementation_results: dict) -> dict:
    """步骤3.3: 修复效果验证"""
    
    verification_results = {
        "system_function_verification": {},
        "wsl_adapted_verification": {},
        "verification_dimensions": {},
        "overall_pass_rate": 0.0
    }
    
    print("✅ 开始修复效果验证...")
    
    # 1. 系统功能验证
    print("  1. 系统功能验证...")
    verification_results["system_function_verification"] = perform_system_function_verification()
    
    # 2. WSL环境适配验证
    print("  2. WSL环境适配验证...")
    verification_results["wsl_adapted_verification"] = perform_wsl_adapted_verification()
    
    # 3. 验证维度覆盖
    print("  3. 验证维度覆盖...")
    verification_results["verification_dimensions"] = perform_multidimensional_verification()
    
    # 计算总体通过率
    all_checks = []
    all_checks.extend(verification_results["system_function_verification"].values())
    all_checks.extend(verification_results["wsl_adapted_verification"].values())
    all_checks.extend(verification_results["verification_dimensions"].values())
    
    if all_checks:
        verification_results["overall_pass_rate"] = sum(all_checks) / len(all_checks)
    
    print(f"📊 验证通过率: {verification_results['overall_pass_rate']*100:.1f}%")
    
    return verification_results

def perform_system_function_verification() -> dict:
    """系统功能验证"""
    
    verifications = {}
    
    # 服务端功能验证
    try:
        # API文档测试
        api_docs_response = bash_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs')
        verifications["api_docs"] = api_docs_response == "200"
        
        # API结构测试
        api_structure = bash_command('curl -s http://localhost:8000/openapi.json')
        verifications["api_structure"] = bool(api_structure and "paths" in api_structure)
        
        # 业务逻辑测试
        business_logic_ok = test_business_logic_endpoints()
        verifications["business_logic"] = business_logic_ok
        
    except Exception as e:
        print(f"    服务端验证异常: {str(e)}")
        verifications.update({"api_docs": False, "api_structure": False, "business_logic": False})
    
    # 管理端功能验证（通过API间接验证）
    try:
        # 界面功能测试（通过API验证数据层）
        interface_data_ok = test_interface_data_integrity()
        verifications["interface_function"] = interface_data_ok
        
        # 数据展示测试（通过API验证）
        data_display_ok = test_data_display_integrity()
        verifications["data_display"] = data_display_ok
        
    except Exception as e:
        print(f"    管理端验证异常: {str(e)}")
        verifications.update({"interface_function": False, "data_display": False})
    
    return verifications

def perform_wsl_adapted_verification() -> dict:
    """WSL环境适配验证方法"""
    
    verifications = {}
    
    # 1. 文件系统层验证
    try:
        # 发现日志文件
        log_files = bash_command('find . -type f -name "*.log" | tail -5')
        verifications["log_files_accessible"] = bool(log_files)
        
        # 发现配置文件
        config_files = bash_command('find . -name "config*" -o -name "settings*" | head -10')
        verifications["config_files_accessible"] = bool(config_files)
        
        # 验证关键文件权限
        key_files_ok = verify_key_file_permissions()
        verifications["file_permissions"] = key_files_ok
        
    except Exception as e:
        print(f"    文件系统验证异常: {str(e)}")
        verifications.update({"log_files_accessible": False, "config_files_accessible": False, "file_permissions": False})
    
    # 2. 服务层验证
    try:
        # 健康检查
        health_check = bash_command('curl -s http://localhost:8000/docs')
        verifications["service_health"] = bool(health_check)
        
        # 服务信息
        service_info = bash_command('curl -s http://localhost:8000/openapi.json | jq .info')
        verifications["service_info"] = bool(service_info)
        
    except Exception as e:
        print(f"    服务层验证异常: {str(e)}")
        verifications.update({"service_health": False, "service_info": False})
    
    return verifications

def perform_multidimensional_verification() -> dict:
    """多维度验证覆盖"""
    
    dimensions = {}
    
    # 1. 功能完整性验证
    dimensions["functional_completeness"] = verify_functional_completeness()
    
    # 2. 数据一致性验证
    dimensions["data_consistency"] = verify_data_consistency()
    
    # 3. 业务逻辑验证
    dimensions["business_logic_integrity"] = verify_business_logic_integrity()
    
    # 4. 性能稳定性验证
    dimensions["performance_stability"] = verify_performance_stability()
    
    # 5. 无副作用验证
    dimensions["no_side_effects"] = verify_no_side_effects()
    
    return dimensions

def check_stage3_exit_conditions(loop_control: dict, result: Stage3Result) -> str:
    """检查阶段3退出条件"""
    
    current_round = loop_control["current_round"]
    max_rounds = loop_control["max_rounds"]
    target_pass_rate = loop_control["target_pass_rate"]
    
    # 基础退出条件
    if current_round >= max_rounds:
        return f"达到最大轮次限制({max_rounds})"
    
    # 获取当前验证通过率
    if result.rounds:
        latest_round = result.rounds[-1]
        current_pass_rate = latest_round["evaluation_results"]["verification_pass_rate"]
        
        # 退出条件1: 验证通过率达到100%
        if current_pass_rate >= target_pass_rate:
            return f"验证通过率达到{target_pass_rate*100:.1f}%"
    
    return None  # 继续循环

def validate_stage3_completion(result: Stage3Result, loop_control: dict) -> bool:
    """验证阶段3完成质量"""
    
    if not result.rounds:
        return False
    
    latest_round = result.rounds[-1]
    
    criteria = {
        "verification_pass_rate_100": latest_round["evaluation_results"]["verification_pass_rate"] == 1.0,
        "no_side_effects_confirmed": latest_round["evaluation_results"]["no_side_effects"],
        "all_fixes_implemented": latest_round["implementation_results"]["success_count"] > 0,
        "verification_complete": all(latest_round["verification_results"].values())
    }
    
    print("📊 阶段3完成质量检查:")
    for criterion, passed in criteria.items():
        print(f"  {criterion}: {'✅' if passed else '❌'}")
    
    return all(criteria.values())
```

---

## **📋 阶段4：总结与发散优化**

```python
def summary_optimization_phase(stage3_result: Stage3Result, todo_tracker: TodoTracker) -> Stage4Result:
    """
    阶段4：总结与发散优化
    
    强制要求：本阶段必须完成工作总结和发散性优化建议
    成功标准：完整总结报告 + 具体优化建议 + 知识沉淀
    门禁条件：所有总结内容完整且有价值才能完成整个流程
    """
    
    result = Stage4Result()
    
    # 步骤4.1: 诊断过程总结
    todo_tracker.start_step("4.1: 诊断过程总结")
    diagnostic_summary = diagnostic_process_summary_step(stage3_result)
    result.add_step_result("4.1", diagnostic_summary)
    todo_tracker.complete_step("4.1")
    
    # 步骤4.2: 解决方案总结
    todo_tracker.start_step("4.2: 解决方案总结")
    solution_summary = solution_summary_step(stage3_result)
    result.add_step_result("4.2", solution_summary)
    todo_tracker.complete_step("4.2")
    
    # 步骤4.3: 经验教训提炼
    todo_tracker.start_step("4.3: 经验教训提炼")
    lessons_learned = lessons_learned_step(stage3_result)
    result.add_step_result("4.3", lessons_learned)
    todo_tracker.complete_step("4.3")
    
    # 步骤4.4: 类似问题预防建议
    todo_tracker.start_step("4.4: 类似问题预防建议")
    prevention_suggestions = prevention_suggestions_step(stage3_result)
    result.add_step_result("4.4", prevention_suggestions)
    todo_tracker.complete_step("4.4")
    
    # 步骤4.5: 系统性优化建议
    todo_tracker.start_step("4.5: 系统性优化建议")
    optimization_suggestions = optimization_suggestions_step(stage3_result)
    result.add_step_result("4.5", optimization_suggestions)
    todo_tracker.complete_step("4.5")
    
    # 验证阶段4完成质量
    if not validate_stage4_completion(result):
        raise StageGateError("阶段4未满足完成条件")
    
    return result
```

---

## 🔄 异常处理与重试机制

```python
class StageGateError(Exception):
    """阶段门禁异常"""
    def __init__(self, message: str, stage: str = None):
        self.stage = stage
        super().__init__(message)

class ViolationError(Exception):
    """违规异常"""
    def __init__(self, violation_type: str, message: str, severity: str):
        self.violation_type = violation_type
        self.severity = severity
        super().__init__(f"{severity}: {violation_type} - {message}")

def retry_from_violation(error: Exception, todo_tracker: TodoTracker) -> DiagnosisResult:
    """从违规异常中重试"""
    
    if isinstance(error, ViolationError) and error.severity == "SEVERE":
        print(f"🚨 严重违规，必须重新执行: {error.violation_type}")
        
        if error.violation_type == "skip_loop":
            return restart_stage_with_forced_loops(error.stage)
        elif error.violation_type == "imagination_guess":
            return restart_with_mandatory_verification()
        else:
            return DiagnosisResult(success=False, error=f"严重违规: {error}")
    
    return DiagnosisResult(success=False, error=str(error))

# 强制自检清单
def mandatory_self_check(stage: str, result: Any) -> bool:
    """强制自检清单（每阶段结束前必须完成）"""
    
    questions = [
        "我是否完成了本阶段的所有核心目标？",
        "我是否有任何猜测或未验证的假设？", 
        "我是否满足了所有阶段切换条件？",
        "我的TodoWrite状态是否准确反映了执行情况？",
        "我是否执行了必需的循环机制（如适用于阶段2、3）？",
        "我是否保持了循环的完整性和自动化程度？"
    ]
    
    print(f"🔍 {stage}阶段强制自检清单:")
    for i, question in enumerate(questions, 1):
        # 这里应该根据实际result检查每个问题
        answer = check_self_question(question, stage, result)
        print(f"  {i}. {question} {'✅' if answer else '❌'}")
        if not answer:
            return False
    
    return True

# 支持类定义
@dataclass
class TodoTracker:
    """TodoWrite状态跟踪器"""
    current_todos: List[dict] = field(default_factory=list)
    
    def init_diagnostic_flow(self):
        # 初始化诊断流程的TodoWrite
        pass
    
    def start_stage(self, stage_name: str):
        # 开始阶段，更新TodoWrite状态为in_progress
        pass
    
    def complete_stage(self, stage_name: str):
        # 完成阶段，更新TodoWrite状态为completed
        pass
```

## 🎯 使用方法

```python
# 直接调用主函数，完整4阶段执行
result = diagnose_and_fix($ARGUMENTS)

if result.success:
    print("✅ 问题已完全解决")
    print("📊 执行统计:")
    for i, stage_result in enumerate(result.stage_results, 1):
        print(f"  阶段{i}: {stage_result.summary}")
else:
    print("❌ 诊断失败")
    print(f"错误: {result.error}")
```

