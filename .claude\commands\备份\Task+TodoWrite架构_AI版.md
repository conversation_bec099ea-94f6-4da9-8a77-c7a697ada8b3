---
description: 专为AI编程优化的Task+TodoWrite多阶段循环架构实施指南
---

# 🤖 Task+TodoWrite架构 - AI实施指南

## 🔧 组件接口规范

### 📋 TodoWriteManager API

```python
class TodoWriteManager:
    TASK_LEVELS = ['stage', 'step', 'verification']
    TASK_STATES = ['pending', 'in_progress', 'completed', 'failed', 'skipped']
    
    def create_stage_tasks(self, stage_config: dict) -> list: pass
    def create_dynamic_tasks(self, task_configs: list) -> list: pass
    def update_task_status(self, task_id: str, status: str) -> bool: pass
    def get_current_todos(self) -> list: pass
    def cleanup_completed_tasks(self, keep_recent: int = 10) -> int: pass
    def get_progress_stats(self) -> dict: pass
```

### 🔄 TaskTodoIntegration API

```python
class TaskTodoIntegration:
    def execute_task_with_sync(self, task_config: dict, todo_id: str) -> dict: pass
    def execute_parallel_tasks(self, task_configs: list) -> dict: pass
    def handle_task_failure(self, task_config: dict, todo_id: str, error: Exception) -> dict: pass
    
    FALLBACK_METHODS = {
        'search': 'fallback_glob_grep_search',
        'verification': 'fallback_read_verification', 
        'analysis': 'fallback_manual_analysis'
    }
```

### 🔁 DynamicTaskManager API

```python
class DynamicTaskManager:
    def manage_loop_execution(self, loop_config: dict) -> dict: pass
    def create_round_tasks(self, round_num: int, context: dict) -> list: pass
    def evaluate_exit_conditions(self, round_result: dict, round_num: int, context: dict) -> dict: pass
    def cleanup_round_tasks(self, round_num: int) -> int: pass
    
    DEFAULT_EXIT_CONDITIONS = {
        'success_rate_threshold': 0.8,
        'quality_score_threshold': 0.7,
        'max_rounds_limit': 5
    }
```

### 🚪 GateConditionManager API

```python
class GateConditionManager:
    def verify_gate_conditions(self, stage: str, context: dict) -> dict: pass
    def create_gate_verification_tasks(self, stage: str) -> list: pass
    def get_gate_configurations(self, stage: str) -> dict: pass
    
    STANDARD_GATE_CONDITIONS = {
        'STAGE1': ['task_completion', 'discovery_threshold', 'quality_score'],
        'STAGE2': ['hypothesis_quality', 'verification_completeness', 'problem_identification'],
        'STAGE3': ['fix_implementation', 'verification_quality', 'problem_resolution']
    }
```

## 📐 核心实现模式

### 🔄 状态同步执行模式

```python
def execute_with_state_sync(task_config: dict, todo_id: str, todo_manager) -> dict:
    """标准状态同步执行模式"""
    
    # 阶段1: 状态准备
    todo_manager.update_task_status(todo_id, 'in_progress')
    execution_start = time.time()
    
    try:
        # 阶段2: 任务执行
        if task_config['type'] == 'task':
            result = Task(
                description=task_config['description'],
                prompt=task_config['prompt']
            )
        else:
            result = execute_traditional_method(task_config)
        
        # 阶段3: 成功处理
        execution_time = time.time() - execution_start
        todo_manager.update_task_status(todo_id, 'completed')
        
        return {
            'success': True,
            'result': result,
            'task_id': todo_id,
            'execution_time': execution_time,
            'fallback_used': False
        }
        
    except Exception as e:
        # 阶段4: 失败处理
        fallback_result = try_fallback_method(task_config, e)
        
        if fallback_result['success']:
            todo_manager.update_task_status(todo_id, 'completed')
        else:
            todo_manager.update_task_status(todo_id, 'failed')
        
        execution_time = time.time() - execution_start
        return {
            'success': fallback_result['success'],
            'result': fallback_result.get('result'),
            'task_id': todo_id,
            'execution_time': execution_time,
            'fallback_used': True,
            'error': str(e)
        }
```

### 🏗️ 主控制器模板

```python
class {{FlowName}}Controller:
    def __init__(self, {{input_params}}):
        self.todo_manager = TodoWriteManager()
        self.task_integration = TaskTodoIntegration(self.todo_manager)
        self.dynamic_manager = DynamicTaskManager(self.todo_manager, self.task_integration)
        self.gate_manager = GateConditionManager(self.todo_manager)
        self.current_stage = 'INIT'
        self.context = {}
    
    def execute_flow(self):
        """执行完整流程"""
        self.initialize_todo_list()
        
        for stage_config in self.flow_config['stages']:
            if self.should_execute_stage(stage_config):
                self.execute_stage(stage_config)
            else:
                break
        
        self.current_stage = 'COMPLETED'
        self.finalize_todo_list()
    
    def execute_stage(self, stage_config: dict):
        """执行单个阶段"""
        stage_tasks = self.todo_manager.create_stage_tasks(stage_config)
        self.todo_manager.add_tasks(stage_tasks)
        
        stage_task_id = f"stage_{stage_config['id']}"
        self.todo_manager.update_task_status(stage_task_id, 'in_progress')
        
        for step_config in stage_config['steps']:
            self.execute_step(step_config, stage_config)
        
        if stage_config.get('has_loop'):
            loop_config = {
                'name': f"{stage_config['name']}循环",
                'max_rounds': stage_config.get('max_rounds', 5),
                'id': stage_config['id']
            }
            self.dynamic_manager.manage_loop_execution(loop_config)
        
        gate_result = self.gate_manager.verify_gate_conditions(stage_config['id'], self.context)
        
        if gate_result['passed']:
            self.todo_manager.update_task_status(stage_task_id, 'completed')
            self.current_stage = stage_config.get('next_stage', 'COMPLETED')
        else:
            self.current_stage = 'FAILED'
```

## ⚙️ 配置参数规范

### 📋 基础配置Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["name", "stages"],
  "properties": {
    "name": {"type": "string"},
    "stages": {
      "type": "array",
      "items": {
        "type": "object",
        "required": ["id", "name", "steps"],
        "properties": {
          "id": {"type": "string"},
          "name": {"type": "string"},
          "steps": {
            "type": "array",
            "items": {
              "type": "object",
              "required": ["id", "name"],
              "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "priority": {"enum": ["high", "medium", "low"], "default": "high"}
              }
            }
          },
          "has_loop": {"type": "boolean", "default": false},
          "max_rounds": {"type": "integer", "minimum": 1, "maximum": 10, "default": 5},
          "next_stage": {"type": "string"}
        }
      }
    }
  }
}
```

### 📊 配置示例

```json
{
  "name": "示例流程",
  "stages": [
    {
      "id": "STAGE1",
      "name": "数据收集",
      "steps": [
        {"id": "discover", "name": "发现数据源", "priority": "high"},
        {"id": "extract", "name": "提取数据", "priority": "high"}
      ],
      "has_loop": false,
      "next_stage": "STAGE2"
    },
    {
      "id": "STAGE2", 
      "name": "数据处理",
      "steps": [
        {"id": "process", "name": "处理数据", "priority": "high"},
        {"id": "validate", "name": "验证结果", "priority": "medium"}
      ],
      "has_loop": true,
      "max_rounds": 3,
      "next_stage": "COMPLETED"
    }
  ]
}
```

## ✅ AI实施验证清单

### 🚀 必需实现检查
- [ ] **组件初始化**: 四大核心组件正确初始化
- [ ] **状态同步**: Task执行与TodoWrite状态实时同步  
- [ ] **错误处理**: Task失败时自动回退到传统方法
- [ ] **循环控制**: 基于退出条件的自动循环管理
- [ ] **门禁验证**: 阶段质量门禁的任务化验证

### 🔧 核心功能验证
- [ ] **三层任务**: 阶段/步骤/验证三层任务结构创建
- [ ] **并行执行**: 多Task并行执行和结果整合
- [ ] **动态管理**: 循环轮次的动态任务创建和清理
- [ ] **质量控制**: 门禁条件检查和质量评估
- [ ] **进度跟踪**: 实时任务状态和进度统计

### 🎯 集成测试点
- [ ] 验证各阶段按序执行
- [ ] 测试循环退出逻辑正确
- [ ] 确认Task失败回退有效
- [ ] 验证状态同步一致性
- [ ] 测试门禁条件检查准确

### 📝 使用模式

1. **继承主控制器模板**
2. **实现四大组件接口**  
3. **配置阶段和步骤定义**
4. **添加业务特定逻辑**
5. **测试执行和验证结果**