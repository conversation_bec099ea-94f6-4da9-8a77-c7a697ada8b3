---
description: 专为AI编程优化的Task+TodoWrite多阶段循环架构实施指南
---

# 🤖 Task+TodoWrite架构 - AI实施指南

## 🔧 组件接口规范

### 📋 TodoWriteManager API

```python
class TodoWriteManager:
    """三层任务管理器 - 标准接口"""
    
    # === 核心数据结构 ===
    TASK_LEVELS = ['stage', 'step', 'verification']
    TASK_STATES = ['pending', 'in_progress', 'completed', 'failed', 'skipped']
    
    # === 必需实现的方法 ===
    def create_stage_tasks(self, stage_config: dict) -> list:
        """
        创建阶段任务
        
        Args:
            stage_config: {
                'id': 'STAGE_ID',
                'name': '阶段名称',
                'steps': [{'id': 'step1', 'name': '步骤1'}],
                'priority': 'high'
            }
        
        Returns:
            list: [stage_task, step_task1, step_task2, ...]
        """
        pass
    
    def create_dynamic_tasks(self, task_configs: list) -> list:
        """
        动态创建任务列表
        
        Args:
            task_configs: [{'description': '', 'type': '', 'priority': ''}]
        
        Returns:
            list: 创建的任务列表
        """
        pass
    
    def update_task_status(self, task_id: str, status: str) -> bool:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态 (pending|in_progress|completed|failed|skipped)
        
        Returns:
            bool: 更新是否成功
        """
        pass
    
    def get_current_todos(self) -> list:
        """获取当前所有任务"""
        pass
    
    def cleanup_completed_tasks(self, keep_recent: int = 10) -> int:
        """清理已完成任务，返回清理数量"""
        pass
    
    # === 状态查询方法 ===
    def get_progress_stats(self) -> dict:
        """
        获取进度统计
        
        Returns:
            {
                'total': int,
                'completed': int, 
                'in_progress': int,
                'failed': int,
                'progress_percentage': float
            }
        """
        pass
```

### 🔄 TaskTodoIntegration API

```python
class TaskTodoIntegration:
    """Task执行与TodoWrite集成 - 标准接口"""
    
    def execute_task_with_sync(self, task_config: dict, todo_id: str) -> dict:
        """
        执行Task并同步状态
        
        Args:
            task_config: {
                'description': 'Task描述',
                'prompt': 'Task提示词',
                'type': 'search|verification|analysis'
            }
            todo_id: 对应的TodoWrite任务ID
        
        Returns:
            {
                'success': bool,
                'result': Any,
                'task_id': str,
                'execution_time': float,
                'fallback_used': bool
            }
        """
        pass
    
    def execute_parallel_tasks(self, task_configs: list) -> dict:
        """
        并行执行多个Task
        
        Args:
            task_configs: Task配置列表
        
        Returns:
            {
                'results': list,
                'success_rate': float,
                'total_count': int,
                'success_count': int,
                'execution_time': float
            }
        """
        pass
    
    def handle_task_failure(self, task_config: dict, todo_id: str, error: Exception) -> dict:
        """Task失败处理 - 必需实现回退逻辑"""
        pass
    
    # === 回退方法映射 ===
    FALLBACK_METHODS = {
        'search': 'fallback_glob_grep_search',
        'verification': 'fallback_read_verification', 
        'analysis': 'fallback_manual_analysis'
    }
```

### 🔁 DynamicTaskManager API

```python
class DynamicTaskManager:
    """动态任务和循环管理 - 标准接口"""
    
    def manage_loop_execution(self, loop_config: dict) -> dict:
        """
        管理循环执行
        
        Args:
            loop_config: {
                'name': '循环名称',
                'max_rounds': int,
                'operation_name': '操作名称',
                'exit_conditions': {...}
            }
        
        Returns:
            {
                'total_rounds': int,
                'exit_reason': str,
                'loop_context': dict,
                'success': bool
            }
        """
        pass
    
    def create_round_tasks(self, round_num: int, context: dict) -> list:
        """
        创建轮次任务
        
        Args:
            round_num: 轮次编号
            context: 执行上下文
        
        Returns:
            list: 轮次任务列表
        """
        pass
    
    def evaluate_exit_conditions(self, round_result: dict, round_num: int, context: dict) -> dict:
        """
        评估退出条件
        
        Returns:
            {
                'should_exit': bool,
                'reason': str,
                'confidence': float
            }
        """
        pass
    
    def cleanup_round_tasks(self, round_num: int) -> int:
        """清理轮次任务"""
        pass
    
    # === 标准退出条件 ===
    DEFAULT_EXIT_CONDITIONS = {
        'success_rate_threshold': 0.8,
        'quality_score_threshold': 0.7,
        'max_rounds_limit': 5
    }
```

### 🚪 GateConditionManager API

```python
class GateConditionManager:
    """门禁条件管理 - 标准接口"""
    
    def verify_gate_conditions(self, stage: str, context: dict) -> dict:
        """
        验证门禁条件
        
        Args:
            stage: 阶段标识
            context: 执行上下文数据
        
        Returns:
            {
                'passed': bool,
                'results': dict,
                'passed_count': int,
                'total_count': int,
                'failed_conditions': list
            }
        """
        pass
    
    def create_gate_verification_tasks(self, stage: str) -> list:
        """创建门禁验证任务"""
        pass
    
    def get_gate_configurations(self, stage: str) -> dict:
        """
        获取门禁配置
        
        Returns:
            {
                'condition_name': {
                    'description': str,
                    'method': str,
                    'threshold': float,
                    'weight': int
                }
            }
        """
        pass
    
    # === 标准门禁条件 ===
    STANDARD_GATE_CONDITIONS = {
        'STAGE1': ['task_completion', 'discovery_threshold', 'quality_score'],
        'STAGE2': ['hypothesis_quality', 'verification_completeness', 'problem_identification'],
        'STAGE3': ['fix_implementation', 'verification_quality', 'problem_resolution']
    }
```

## 📐 实现模式库

### 🏗️ 模式1：三层任务创建模式

```python
def create_three_layer_tasks(stage_config: dict) -> list:
    """
    标准三层任务创建模式
    
    模式结构:
    阶段任务 (1个)
    ├── 步骤任务 (2-5个)
    └── 验证任务 (动态生成)
    """
    tasks = []
    
    # 1. 创建阶段级任务
    stage_task = {
        'content': stage_config['name'],
        'status': 'pending',
        'priority': stage_config.get('priority', 'high'),
        'id': f"stage_{stage_config['id']}",
        'level': 'stage',
        'stage_id': stage_config['id']
    }
    tasks.append(stage_task)
    
    # 2. 创建步骤级任务
    for i, step in enumerate(stage_config.get('steps', [])):
        step_task = {
            'content': f"  - {step['name']}",
            'status': 'pending',
            'priority': step.get('priority', 'high'),
            'id': f"step_{stage_config['id']}_{step['id']}",
            'level': 'step',
            'parent_id': stage_task['id'],
            'step_order': i + 1
        }
        tasks.append(step_task)
    
    # 3. 创建验证级任务 (如果需要)
    if stage_config.get('verifications'):
        for j, verification in enumerate(stage_config['verifications']):
            verify_task = {
                'content': f"    - {verification['description']}",
                'status': 'pending',
                'priority': 'high',
                'id': f"verify_{verification['type']}_{j+1}",
                'level': 'verification',
                'parent_id': step_task['id'],  # 关联到最后一个步骤
                'verification_type': verification['type']
            }
            tasks.append(verify_task)
    
    return tasks

# === 使用示例 ===
stage_config_example = {
    'id': 'DATA_COLLECTION',
    'name': '数据收集与验证阶段',
    'priority': 'high',
    'steps': [
        {'id': 'discover', 'name': '数据源发现'},
        {'id': 'extract', 'name': '数据提取'},
        {'id': 'validate', 'name': '数据验证'}
    ],
    'verifications': [
        {'type': 'completeness', 'description': '完整性检查'},
        {'type': 'quality', 'description': '质量评估'}
    ]
}
```

### 🔄 模式2：状态同步执行模式

```python
def execute_with_state_sync(task_config: dict, todo_id: str, todo_manager) -> dict:
    """
    标准状态同步执行模式
    
    执行流程:
    1. 状态准备 -> in_progress
    2. 任务执行
    3. 成功处理 -> completed
    4. 失败处理 -> failed/回退
    """
    
    # === 阶段1: 状态准备 ===
    todo_manager.update_task_status(todo_id, 'in_progress')
    execution_start = time.time()
    
    try:
        # === 阶段2: 任务执行 ===
        if task_config['type'] == 'task':
            result = Task(
                description=task_config['description'],
                prompt=task_config['prompt']
            )
        else:
            result = execute_traditional_method(task_config)
        
        # === 阶段3: 成功处理 ===
        execution_time = time.time() - execution_start
        todo_manager.update_task_status(todo_id, 'completed')
        
        return {
            'success': True,
            'result': result,
            'task_id': todo_id,
            'execution_time': execution_time,
            'fallback_used': False
        }
        
    except Exception as e:
        # === 阶段4: 失败处理 ===
        print(f"❌ 任务执行失败: {task_config['description']} - {str(e)}")
        
        # 尝试回退方法
        fallback_result = try_fallback_method(task_config, e)
        
        if fallback_result['success']:
            todo_manager.update_task_status(todo_id, 'completed')
            print(f"✅ 回退方法成功")
        else:
            todo_manager.update_task_status(todo_id, 'failed')
            print(f"❌ 回退方法也失败")
        
        execution_time = time.time() - execution_start
        return {
            'success': fallback_result['success'],
            'result': fallback_result.get('result'),
            'task_id': todo_id,
            'execution_time': execution_time,
            'fallback_used': True,
            'error': str(e)
        }

# === 状态同步检查函数 ===
def verify_state_consistency(todo_manager) -> list:
    """验证状态一致性，返回不一致的任务"""
    inconsistencies = []
    
    for todo in todo_manager.current_todos:
        expected_status = get_actual_execution_status(todo['id'])
        if todo['status'] != expected_status:
            inconsistencies.append({
                'task_id': todo['id'],
                'current_status': todo['status'],
                'expected_status': expected_status
            })
    
    return inconsistencies
```

### 🔁 模式3：循环控制管理模式

```python
def manage_standard_loop(loop_config: dict, todo_manager, task_integration) -> dict:
    """
    标准循环控制管理模式
    
    循环结构:
    while 轮次 <= 最大轮次:
        1. 创建轮次任务
        2. 执行轮次操作  
        3. 评估退出条件
        4. 清理轮次任务
    """
    
    current_round = 1
    max_rounds = loop_config.get('max_rounds', 5)
    loop_context = {'results': [], 'metrics': {}}
    
    while current_round <= max_rounds:
        print(f"\n--- 第{current_round}轮{loop_config['name']}循环 ---")
        
        # === 步骤1: 创建轮次任务 ===
        round_tasks = create_standard_round_tasks(current_round, loop_config)
        todo_manager.add_tasks(round_tasks)
        
        # === 步骤2: 执行轮次操作 ===
        round_result = execute_round_operation(
            current_round, loop_config, loop_context, 
            todo_manager, task_integration
        )
        
        # === 步骤3: 评估退出条件 ===
        exit_task_id = f"exit_condition_round_{current_round}"
        todo_manager.update_task_status(exit_task_id, 'in_progress')
        
        exit_evaluation = evaluate_standard_exit_conditions(
            round_result, current_round, loop_context
        )
        
        # 更新退出条件任务状态
        todo_manager.update_task_status(exit_task_id, 'completed')
        
        # === 步骤4: 退出判断 ===
        if exit_evaluation['should_exit']:
            print(f"✅ 循环退出: {exit_evaluation['reason']}")
            break
        else:
            print(f"🔄 继续循环: {exit_evaluation['reason']}")
        
        # === 步骤5: 清理和准备下一轮 ===
        cleanup_count = cleanup_round_tasks(current_round, todo_manager)
        loop_context['results'].append(round_result)
        current_round += 1
    
    return {
        'total_rounds': current_round,
        'exit_reason': exit_evaluation.get('reason', 'max_rounds_reached'),
        'loop_context': loop_context,
        'success': exit_evaluation.get('should_exit', False)
    }

def create_standard_round_tasks(round_num: int, loop_config: dict) -> list:
    """创建标准轮次任务"""
    return [
        {
            'content': f"第{round_num}轮{loop_config['operation_name']}",
            'status': 'pending',
            'priority': 'high',
            'id': f"{loop_config['id']}_operation_round_{round_num}",
            'round': round_num
        },
        {
            'content': f"第{round_num}轮结果验证", 
            'status': 'pending',
            'priority': 'high',
            'id': f"{loop_config['id']}_verify_round_{round_num}",
            'round': round_num
        },
        {
            'content': f"第{round_num}轮退出条件评估",
            'status': 'pending',
            'priority': 'medium',
            'id': f"exit_condition_round_{round_num}",
            'round': round_num
        }
    ]

def evaluate_standard_exit_conditions(round_result: dict, round_num: int, context: dict) -> dict:
    """标准退出条件评估逻辑"""
    
    # 条件1: 成功率检查
    success_rate = round_result.get('success_rate', 0)
    if success_rate >= 0.8:
        return {
            'should_exit': True,
            'reason': f'成功率达标: {success_rate:.2f}',
            'confidence': 0.9
        }
    
    # 条件2: 质量分数检查
    quality_score = round_result.get('quality_score', 0)
    if quality_score >= 0.7:
        return {
            'should_exit': True,
            'reason': f'质量分数达标: {quality_score:.2f}',
            'confidence': 0.8
        }
    
    # 条件3: 最大轮次检查
    if round_num >= 5:
        return {
            'should_exit': True,
            'reason': '达到最大轮次限制',
            'confidence': 1.0
        }
    
    # 条件4: 连续失败检查
    recent_results = context['results'][-3:]  # 最近3轮
    if len(recent_results) >= 3 and all(not r.get('success', True) for r in recent_results):
        return {
            'should_exit': True,
            'reason': '连续3轮失败，终止循环',
            'confidence': 0.7
        }
    
    return {
        'should_exit': False,
        'reason': f'轮次{round_num}条件未满足，继续执行',
        'confidence': 0.6
    }
```

### 🚪 模式4：门禁验证实施模式

```python
def implement_gate_verification(stage: str, context: dict, todo_manager, gate_manager) -> dict:
    """
    标准门禁验证实施模式
    
    验证流程:
    1. 获取门禁配置
    2. 创建验证任务
    3. 执行验证检查
    4. 汇总验证结果
    """
    
    # === 步骤1: 获取门禁配置 ===
    gate_configs = gate_manager.get_gate_configurations(stage)
    
    if not gate_configs:
        return {'passed': True, 'reason': '无门禁条件要求'}
    
    # === 步骤2: 创建验证任务 ===
    gate_tasks = []
    for condition_name, config in gate_configs.items():
        gate_task = {
            'content': f"    - 门禁验证: {config['description']}",
            'status': 'pending',
            'priority': 'high',
            'id': f"gate_{stage.lower()}_{condition_name}",
            'gate_condition': condition_name,
            'verification_method': config['method'],
            'threshold': config.get('threshold', 0.5)
        }
        gate_tasks.append(gate_task)
    
    todo_manager.add_tasks(gate_tasks)
    
    # === 步骤3: 执行验证检查 ===
    gate_results = {}
    
    for gate_task in gate_tasks:
        condition_name = gate_task['gate_condition']
        
        # 更新任务状态
        todo_manager.update_task_status(gate_task['id'], 'in_progress')
        
        try:
            # 执行验证
            result = execute_gate_verification(
                condition_name, 
                gate_task['verification_method'],
                gate_task['threshold'],
                context
            )
            
            gate_results[condition_name] = {
                'passed': result,
                'value': context.get(f'{condition_name}_value', 'N/A'),
                'threshold': gate_task['threshold']
            }
            
            # 更新任务状态
            if result:
                todo_manager.update_task_status(gate_task['id'], 'completed')
                print(f"✅ 门禁条件通过: {gate_configs[condition_name]['description']}")
            else:
                todo_manager.update_task_status(gate_task['id'], 'failed')
                print(f"❌ 门禁条件未满足: {gate_configs[condition_name]['description']}")
                
        except Exception as e:
            print(f"❌ 门禁条件检查异常: {condition_name} - {str(e)}")
            gate_results[condition_name] = {
                'passed': False,
                'error': str(e),
                'threshold': gate_task['threshold']
            }
            todo_manager.update_task_status(gate_task['id'], 'failed')
    
    # === 步骤4: 汇总验证结果 ===
    passed_count = sum(1 for result in gate_results.values() if result.get('passed', False))
    total_count = len(gate_results)
    overall_passed = passed_count == total_count
    
    print(f"📊 {stage}门禁条件检查结果: {passed_count}/{total_count} ({'✅ 通过' if overall_passed else '❌ 未通过'})")
    
    return {
        'passed': overall_passed,
        'results': gate_results,
        'passed_count': passed_count,
        'total_count': total_count,
        'failed_conditions': [name for name, result in gate_results.items() if not result.get('passed', False)]
    }

def execute_gate_verification(condition_name: str, method: str, threshold: float, context: dict) -> bool:
    """执行具体门禁验证"""
    
    verification_methods = {
        'task_completion_rate': lambda ctx: len(ctx.get('completed_tasks', [])) / max(len(ctx.get('total_tasks', [])), 1),
        'discovery_count': lambda ctx: len(ctx.get('discoveries', [])),
        'quality_score': lambda ctx: ctx.get('quality_score', 0),
        'success_rate': lambda ctx: ctx.get('success_rate', 0),
        'error_count': lambda ctx: len(ctx.get('errors', []))
    }
    
    if method in verification_methods:
        value = verification_methods[method](context)
        context[f'{condition_name}_value'] = value
        
        # 根据条件类型判断通过条件
        if method == 'error_count':
            return value <= threshold  # 错误数量应该小于等于阈值
        else:
            return value >= threshold  # 其他指标应该大于等于阈值
    
    print(f"⚠️ 未知的验证方法: {method}")
    return False
```

## 🏗️ 代码生成模板

### 📋 主控制器生成模板

```python
class {{FlowName}}Controller:
    """{{流程描述}} - 基于Task+TodoWrite架构"""
    
    def __init__(self, {{input_params}}):
        # === 流程配置 ===
        self.flow_config = {
            'name': '{{流程名称}}',
            'stages': self.define_stages()
        }
        
        # === 输入数据 ===
        {{#each input_params}}
        self.{{this}} = {{this}}
        {{/each}}
        
        # === 执行状态 ===
        self.current_stage = 'INIT'
        self.context = {}
        
        # === 核心组件初始化 ===
        self.todo_manager = TodoWriteManager()
        self.task_integration = TaskTodoIntegration(self.todo_manager)
        self.dynamic_manager = DynamicTaskManager(self.todo_manager, self.task_integration)
        self.gate_manager = GateConditionManager(self.todo_manager)
    
    def define_stages(self) -> list:
        """定义流程阶段配置"""
        return [
            {{#each stages}}
            {
                'id': '{{this.id}}',
                'name': '{{this.name}}',
                'steps': [
                    {{#each this.steps}}
                    {'id': '{{this.id}}', 'name': '{{this.name}}'},
                    {{/each}}
                ],
                'has_loop': {{this.has_loop}},
                {{#if this.max_rounds}}
                'max_rounds': {{this.max_rounds}},
                {{/if}}
                'next_stage': '{{this.next_stage}}'
            },
            {{/each}}
        ]
    
    def execute_flow(self):
        """执行完整流程"""
        
        print(f"🚀 启动{self.flow_config['name']}流程")
        
        try:
            # 初始化TodoWrite任务列表
            self.initialize_todo_list()
            
            # 执行各阶段
            for stage_config in self.flow_config['stages']:
                if self.should_execute_stage(stage_config):
                    self.execute_stage(stage_config)
                else:
                    break
            
            # 流程完成
            self.current_stage = 'COMPLETED'
            self.finalize_todo_list()
            
            print(f"🏁 {self.flow_config['name']}流程完成")
            
        except Exception as e:
            self.current_stage = 'FAILED'
            print(f"❌ 流程执行失败: {str(e)}")
            self.handle_flow_failure(e)
    
    def execute_stage(self, stage_config: dict):
        """执行单个阶段"""
        
        print(f"\n📍 [STAGE_{stage_config['id']}] {stage_config['name']}")
        
        # 创建阶段任务
        stage_tasks = self.todo_manager.create_stage_tasks(stage_config)
        self.todo_manager.add_tasks(stage_tasks)
        
        # 更新阶段状态
        stage_task_id = f"stage_{stage_config['id']}"
        self.todo_manager.update_task_status(stage_task_id, 'in_progress')
        
        try:
            # 执行阶段步骤
            for step_config in stage_config['steps']:
                self.execute_step(step_config, stage_config)
            
            # 执行循环（如果需要）
            if stage_config.get('has_loop'):
                loop_config = {
                    'name': f"{stage_config['name']}循环",
                    'max_rounds': stage_config.get('max_rounds', 5),
                    'operation_name': '阶段操作',
                    'id': stage_config['id']
                }
                self.dynamic_manager.manage_loop_execution(loop_config)
            
            # 门禁条件检查
            gate_result = self.gate_manager.verify_gate_conditions(
                stage_config['id'], self.context
            )
            
            if gate_result['passed']:
                self.todo_manager.update_task_status(stage_task_id, 'completed')
                self.current_stage = stage_config.get('next_stage', 'COMPLETED')
                print(f"✅ {stage_config['name']}门禁条件满足")
            else:
                print(f"❌ {stage_config['name']}门禁条件不满足")
                self.current_stage = 'FAILED'
                
        except Exception as e:
            print(f"❌ 阶段执行失败: {stage_config['name']} - {str(e)}")
            self.current_stage = 'FAILED'
    
    {{#each custom_methods}}
    def {{this.name}}(self, {{this.params}}):
        """{{this.description}}"""
        # TODO: 实现具体逻辑
        pass
    {{/each}}
    
    # === 标准支持方法 ===
    def initialize_todo_list(self):
        """初始化TodoWrite任务列表"""
        self.todo_manager.sync_with_state_machine('STAGE1')
        self.display_current_todos()
    
    def finalize_todo_list(self):
        """完成TodoWrite任务列表"""
        for todo in self.todo_manager.current_todos:
            if todo['status'] != 'completed':
                self.todo_manager.update_task_status(todo['id'], 'completed')
        self.display_current_todos()
    
    def display_current_todos(self):
        """显示当前TodoWrite任务状态"""
        print(f"\n📋 当前TodoWrite任务状态:")
        stats = self.todo_manager.get_progress_stats()
        print(f"📊 任务统计: 总计{stats['total']}个，已完成{stats['completed']}个，进行中{stats['in_progress']}个")

# === 使用示例 ===
def create_{{flow_name}}_flow({{input_params}}):
    """创建{{流程名称}}流程实例"""
    flow = {{FlowName}}Controller({{input_params}})
    flow.execute_flow()
    return flow.context
```

### 🔄 Task执行模板

```python
class TaskExecutor:
    """Task执行器 - 标准实现模板"""
    
    def __init__(self, todo_manager):
        self.todo_manager = todo_manager
        self.execution_stats = {'total': 0, 'success': 0, 'failed': 0}
    
    def execute_single_task(self, task_config: dict, todo_id: str) -> dict:
        """单Task执行标准模板"""
        
        # 记录执行统计
        self.execution_stats['total'] += 1
        
        # 状态同步执行
        return execute_with_state_sync(task_config, todo_id, self.todo_manager)
    
    def execute_parallel_tasks(self, task_configs: list, max_parallel: int = 6) -> dict:
        """并行Task执行标准模板"""
        
        # 限制并行数量
        limited_configs = task_configs[:max_parallel]
        
        # 创建验证任务
        verification_tasks = []
        for i, config in enumerate(limited_configs):
            todo_id = f"parallel_task_{config['type']}_{i+1}"
            verification_task = {
                'content': f"    - {config['description']}",
                'status': 'pending',
                'priority': 'high',
                'id': todo_id,
                'task_type': config['type']
            }
            verification_tasks.append(verification_task)
        
        self.todo_manager.add_tasks(verification_tasks)
        
        # 并行执行
        results = []
        start_time = time.time()
        
        for i, config in enumerate(limited_configs):
            todo_id = f"parallel_task_{config['type']}_{i+1}"
            result = self.execute_single_task(config, todo_id)
            results.append(result)
        
        execution_time = time.time() - start_time
        
        # 统计结果
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"📊 并行Task执行完成: {success_count}/{total_count} 成功，耗时 {execution_time:.2f}秒")
        
        return {
            'results': results,
            'success_rate': success_count / total_count,
            'total_count': total_count,
            'success_count': success_count,
            'execution_time': execution_time
        }
    
    def get_fallback_method(self, task_type: str):
        """获取回退方法"""
        
        fallback_methods = {
            'search': self.fallback_glob_grep_search,
            'verification': self.fallback_read_verification,
            'analysis': self.fallback_manual_analysis
        }
        
        return fallback_methods.get(task_type, self.default_fallback)
    
    def fallback_glob_grep_search(self, task_config: dict) -> dict:
        """搜索类Task回退方法"""
        try:
            # 使用Glob和Grep工具
            search_results = perform_traditional_search(task_config)
            return {'success': True, 'result': search_results}
        except Exception as e:
            return {'success': False, 'result': None, 'error': str(e)}
    
    def fallback_read_verification(self, task_config: dict) -> dict:
        """验证类Task回退方法"""
        try:
            # 使用Read工具进行验证
            verification_results = perform_manual_verification(task_config)
            return {'success': True, 'result': verification_results}
        except Exception as e:
            return {'success': False, 'result': None, 'error': str(e)}
    
    def default_fallback(self, task_config: dict) -> dict:
        """默认回退方法"""
        return {'success': False, 'result': None, 'error': 'No fallback method available'}
```

## ⚙️ 配置参数规范

### 📋 流程配置JSON Schema

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Task+TodoWrite流程配置",
  "type": "object",
  "required": ["name", "stages"],
  "properties": {
    "name": {
      "type": "string",
      "description": "流程名称"
    },
    "description": {
      "type": "string",
      "description": "流程描述"
    },
    "stages": {
      "type": "array",
      "minItems": 1,
      "items": {
        "$ref": "#/definitions/stage"
      }
    },
    "global_config": {
      "$ref": "#/definitions/globalConfig"
    }
  },
  "definitions": {
    "stage": {
      "type": "object",
      "required": ["id", "name", "steps"],
      "properties": {
        "id": {
          "type": "string",
          "pattern": "^[A-Z][A-Z0-9_]*$",
          "description": "阶段唯一标识"
        },
        "name": {
          "type": "string",
          "description": "阶段显示名称"
        },
        "description": {
          "type": "string",
          "description": "阶段描述"
        },
        "steps": {
          "type": "array",
          "minItems": 1,
          "items": {
            "$ref": "#/definitions/step"
          }
        },
        "has_loop": {
          "type": "boolean",
          "default": false,
          "description": "是否包含循环"
        },
        "max_rounds": {
          "type": "integer",
          "minimum": 1,
          "maximum": 10,
          "default": 5,
          "description": "最大循环轮次"
        },
        "gate_conditions": {
          "type": "object",
          "additionalProperties": {
            "$ref": "#/definitions/gateCondition"
          }
        },
        "next_stage": {
          "type": "string",
          "description": "下一阶段ID"
        }
      }
    },
    "step": {
      "type": "object",
      "required": ["id", "name"],
      "properties": {
        "id": {
          "type": "string",
          "pattern": "^[a-z][a-z0-9_]*$",
          "description": "步骤唯一标识"
        },
        "name": {
          "type": "string",
          "description": "步骤显示名称"
        },
        "description": {
          "type": "string",
          "description": "步骤描述"
        },
        "priority": {
          "type": "string",
          "enum": ["high", "medium", "low"],
          "default": "high",
          "description": "步骤优先级"
        },
        "task_config": {
          "$ref": "#/definitions/taskConfig"
        }
      }
    },
    "taskConfig": {
      "type": "object",
      "properties": {
        "type": {
          "type": "string",
          "enum": ["task", "search", "verification", "analysis"],
          "description": "任务类型"
        },
        "description": {
          "type": "string",
          "description": "Task描述"
        },
        "prompt": {
          "type": "string",
          "description": "Task提示词"
        },
        "parallel": {
          "type": "boolean",
          "default": false,
          "description": "是否并行执行"
        },
        "timeout": {
          "type": "integer",
          "minimum": 30,
          "maximum": 600,
          "default": 300,
          "description": "超时时间(秒)"
        }
      }
    },
    "gateCondition": {
      "type": "object",
      "required": ["description", "method", "threshold"],
      "properties": {
        "description": {
          "type": "string",
          "description": "门禁条件描述"
        },
        "method": {
          "type": "string",
          "enum": ["task_completion_rate", "discovery_count", "quality_score", "success_rate", "error_count"],
          "description": "验证方法"
        },
        "threshold": {
          "type": "number",
          "minimum": 0,
          "maximum": 1,
          "description": "阈值"
        },
        "weight": {
          "type": "integer",
          "minimum": 1,
          "maximum": 100,
          "default": 10,
          "description": "权重"
        }
      }
    },
    "globalConfig": {
      "type": "object",
      "properties": {
        "performance": {
          "$ref": "#/definitions/performanceConfig"
        },
        "error_handling": {
          "$ref": "#/definitions/errorHandlingConfig"
        },
        "logging": {
          "$ref": "#/definitions/loggingConfig"
        }
      }
    },
    "performanceConfig": {
      "type": "object",
      "properties": {
        "max_parallel_tasks": {
          "type": "integer",
          "minimum": 1,
          "maximum": 10,
          "default": 6,
          "description": "最大并行Task数量"
        },
        "cleanup_threshold": {
          "type": "integer",
          "minimum": 10,
          "maximum": 100,
          "default": 50,
          "description": "任务清理阈值"
        },
        "task_timeout": {
          "type": "integer",
          "minimum": 30,
          "maximum": 600,
          "default": 300,
          "description": "Task超时时间(秒)"
        }
      }
    },
    "errorHandlingConfig": {
      "type": "object",
      "properties": {
        "retry_attempts": {
          "type": "integer",
          "minimum": 0,
          "maximum": 5,
          "default": 2,
          "description": "重试次数"
        },
        "fallback_enabled": {
          "type": "boolean",
          "default": true,
          "description": "启用回退机制"
        },
        "fail_fast": {
          "type": "boolean",
          "default": false,
          "description": "快速失败模式"
        }
      }
    },
    "loggingConfig": {
      "type": "object",
      "properties": {
        "level": {
          "type": "string",
          "enum": ["DEBUG", "INFO", "WARNING", "ERROR"],
          "default": "INFO",
          "description": "日志级别"
        },
        "include_task_details": {
          "type": "boolean",
          "default": true,
          "description": "包含Task执行详情"
        }
      }
    }
  }
}
```

### 📊 标准配置模板

```json
{
  "name": "示例流程",
  "description": "基于Task+TodoWrite架构的示例流程",
  "stages": [
    {
      "id": "STAGE1_INIT",
      "name": "初始化阶段",
      "description": "流程初始化和准备工作",
      "steps": [
        {
          "id": "prepare_environment",
          "name": "环境准备",
          "priority": "high",
          "task_config": {
            "type": "verification",
            "description": "验证环境配置",
            "timeout": 60
          }
        },
        {
          "id": "load_data",
          "name": "数据加载",
          "priority": "high",
          "task_config": {
            "type": "task",
            "description": "智能加载和分析数据",
            "prompt": "分析并加载以下数据源...",
            "parallel": false,
            "timeout": 180
          }
        }
      ],
      "has_loop": false,
      "gate_conditions": {
        "environment_ready": {
          "description": "环境就绪检查",
          "method": "task_completion_rate",
          "threshold": 1.0,
          "weight": 30
        },
        "data_loaded": {
          "description": "数据加载完成",
          "method": "success_rate",
          "threshold": 0.9,
          "weight": 20
        }
      },
      "next_stage": "STAGE2_PROCESSING"
    },
    {
      "id": "STAGE2_PROCESSING",
      "name": "处理阶段",
      "description": "数据处理和验证循环",
      "steps": [
        {
          "id": "process_data",
          "name": "数据处理",
          "priority": "high"
        },
        {
          "id": "validate_results",
          "name": "结果验证",
          "priority": "medium"
        }
      ],
      "has_loop": true,
      "max_rounds": 3,
      "gate_conditions": {
        "processing_quality": {
          "description": "处理质量检查",
          "method": "quality_score",
          "threshold": 0.8,
          "weight": 50
        }
      },
      "next_stage": "STAGE3_FINALIZE"
    }
  ],
  "global_config": {
    "performance": {
      "max_parallel_tasks": 4,
      "cleanup_threshold": 30,
      "task_timeout": 240
    },
    "error_handling": {
      "retry_attempts": 2,
      "fallback_enabled": true,
      "fail_fast": false
    },
    "logging": {
      "level": "INFO",
      "include_task_details": true
    }
  }
}
```

## ✅ AI实施检查清单

### 🚀 流程创建检查清单

#### **1. 需求分析阶段**
- [ ] **多阶段需求确认**
  - [ ] 流程是否有3个以上明确阶段？
  - [ ] 每个阶段的职责是否清晰？
  - [ ] 阶段之间的依赖关系是否明确？

- [ ] **循环需求确认**
  - [ ] 是否需要循环验证或迭代优化？
  - [ ] 循环退出条件是否明确？
  - [ ] 最大循环轮次是否合理 (建议1-5轮)？

- [ ] **Task技术需求确认**
  - [ ] 是否需要智能搜索功能？
  - [ ] 是否需要复杂分析能力？
  - [ ] 是否有开放式问题需要解决？

- [ ] **质量控制需求确认**
  - [ ] 是否需要阶段质量门禁？
  - [ ] 质量标准是否量化明确？
  - [ ] 失败处理策略是否明确？

#### **2. 架构设计阶段**
- [ ] **组件选择**
  - [ ] 根据需求评估选择必需组件
  - [ ] 确认组件之间的依赖关系
  - [ ] 验证组件配置的完整性

- [ ] **配置文件准备**
  - [ ] 创建符合JSON Schema的配置文件
  - [ ] 验证所有必需字段已填写
  - [ ] 确认阈值和参数设置合理

- [ ] **接口定义**
  - [ ] 定义输入输出数据结构
  - [ ] 确认方法签名的一致性
  - [ ] 验证错误处理接口完整

#### **3. 代码实现阶段**
- [ ] **主控制器实现**
  - [ ] 继承自标准模板
  - [ ] 实现所有必需的抽象方法
  - [ ] 添加流程特定的业务逻辑

- [ ] **组件集成**
  - [ ] 正确初始化四大核心组件
  - [ ] 验证组件之间的调用关系
  - [ ] 测试组件接口的正确性

- [ ] **错误处理**
  - [ ] 实现Task失败回退机制
  - [ ] 添加状态不一致恢复逻辑
  - [ ] 提供循环异常退出机制

#### **4. 测试验证阶段**
- [ ] **功能测试**
  - [ ] 验证各阶段正常执行
  - [ ] 测试循环控制逻辑
  - [ ] 验证门禁条件检查

- [ ] **异常测试**
  - [ ] 测试Task失败场景
  - [ ] 验证回退机制有效性
  - [ ] 测试状态同步一致性

- [ ] **性能测试**
  - [ ] 验证并行Task执行效率
  - [ ] 测试内存使用和清理
  - [ ] 确认超时处理正确性

### 🔧 组件集成验证清单

#### **TodoWriteManager验证**
- [ ] 三层任务结构创建正确
- [ ] 任务状态更新及时准确
- [ ] 进度统计计算正确
- [ ] 任务清理机制有效

#### **TaskTodoIntegration验证**
- [ ] Task执行状态同步正确
- [ ] 并行Task管理有效
- [ ] 失败回退机制可靠
- [ ] 执行时间统计准确

#### **DynamicTaskManager验证**
- [ ] 循环轮次管理正确
- [ ] 动态任务创建有效
- [ ] 退出条件评估准确
- [ ] 任务清理及时完整

#### **GateConditionManager验证**
- [ ] 门禁条件配置正确
- [ ] 验证任务创建有效
- [ ] 验证逻辑执行准确
- [ ] 结果汇总计算正确

