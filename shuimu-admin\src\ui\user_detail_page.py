#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情页面组件
在主窗口内容区域显示用户详情，支持标签页切换和编辑功能
"""

import sys
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel,
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
    QComboBox, QTextEdit, QGroupBox, QFormLayout, QScrollArea,
    QMessageBox, QProgressBar, QFrame, QSplitter, QHeaderView,
    QDialog, QDialogButtonBox, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon, QColor
from services.user_service import UserService
from database.models import DatabaseManager
from ui.mixins.table_config_mixin import TableConfigMixin
import logging

logger = logging.getLogger(__name__)

class UserDetailLoadThread(QThread):
    """用户详情数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, user_service: UserService, user_id: str):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
    
    def run(self):
        try:
            # 获取用户基本信息
            user_info = self.user_service.get_user_by_id(self.user_id)
            
            # 获取用户购买记录
            purchases = self.user_service.get_user_purchases(self.user_id)
            
            # 获取用户观看进度
            progress = self.user_service.get_user_progress(self.user_id)
            
            # 获取用户收藏
            favorites = self.user_service.get_user_favorites(self.user_id)
            
            # 获取用户设置
            settings = self.user_service.get_user_settings(self.user_id)
            
            # 获取用户缓存
            cache_info = self.user_service.get_user_cache(self.user_id)
            
            result = {
                'success': True,
                'user_info': user_info,
                'purchases': purchases,
                'progress': progress,
                'favorites': favorites,
                'settings': settings,
                'cache_info': cache_info
            }
            
            self.data_loaded.emit(result)
            
        except Exception as e:
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(str(e))

class UserDetailPage(QWidget, TableConfigMixin):
    """用户详情页面 - 只继承QWidget，通过组合使用OptimisticCRUDMixin"""
    
    # 定义乐观更新信号
    operation_started = pyqtSignal(str, str)  # entity_id, operation_type
    operation_completed = pyqtSignal(str, str, bool)  # entity_id, operation_type, success
    operation_failed = pyqtSignal(str, str, str)  # entity_id, operation_type, error_message

    def __init__(self, user_id: str, user_service: UserService, parent=None):
        # 初始化基类
        super().__init__(parent)
        TableConfigMixin.__init__(self)
        
        self.user_id = user_id
        self.user_service = user_service
        self.load_thread = None
        
        # 初始化乐观更新功能（组合模式）
        self._init_optimistic_crud()
        
        self.init_ui()
        self.setup_styles()
        # 初始化表格视觉反馈
        self.init_table_feedback(self.purchases_table)
        self.load_user_detail()
        
    def _init_optimistic_crud(self):
        """初始化乐观更新功能"""
        from ui.mixins.qt_native_optimistic_crud_mixin import QtNativeOptimisticCRUDMixin
        
        # 创建QtNativeOptimisticCRUDMixin实例（组合）
        self._optimistic_crud = QtNativeOptimisticCRUDMixin()
        
        # 连接乐观更新信号
        self._connect_optimistic_signals()
        
    def _connect_optimistic_signals(self):
        """连接乐观更新相关信号"""
        try:
            self.operation_started.connect(self._on_operation_started)
            self.operation_completed.connect(self._on_operation_completed)
            self.operation_failed.connect(self._on_operation_failed)
        except Exception as e:
            print(f"⚠️ 用户详情页：乐观更新信号连接失败: {e}")
    
    def _on_operation_started(self, entity_id: str, operation_type: str):
        """操作开始处理"""
        print(f"🚀 用户详情操作开始: {operation_type} - {entity_id}")
        
    def _on_operation_completed(self, entity_id: str, operation_type: str, success: bool):
        """操作完成处理"""
        if success:
            print(f"✅ 用户详情操作成功: {operation_type} - {entity_id}")
        else:
            print(f"❌ 用户详情操作失败: {operation_type} - {entity_id}")
            
    def _on_operation_failed(self, entity_id: str, operation_type: str, error_message: str):
        """操作失败处理"""
        print(f"💥 用户详情操作错误: {operation_type} - {entity_id}: {error_message}")
    
    # 乐观更新核心方法（代理到组合的实例）
    def optimistic_create(self, entity_type, entity_data, ui_update_func, api_func, rollback_func=None, metadata=None):
        """乐观创建操作"""
        if hasattr(self, '_optimistic_crud'):
            return self._optimistic_crud.optimistic_create(
                entity_type, entity_data, ui_update_func, api_func, rollback_func, metadata
            )
        else:
            print("⚠️ 乐观更新功能未初始化")
            return None
    
    def optimistic_update(self, entity_id, entity_data, ui_update_func, api_func, rollback_func=None, original_data=None):
        """乐观更新操作"""
        if hasattr(self, '_optimistic_crud'):
            return self._optimistic_crud.optimistic_update(
                entity_id, entity_data, ui_update_func, api_func, rollback_func, original_data
            )
        else:
            print("⚠️ 乐观更新功能未初始化")
            return False
    
    def optimistic_delete(self, entity_id, entity_data, ui_update_func, api_func, rollback_func=None):
        """乐观删除操作"""
        if hasattr(self, '_optimistic_crud'):
            return self._optimistic_crud.optimistic_delete(
                entity_id, entity_data, ui_update_func, api_func, rollback_func
            )
        else:
            print("⚠️ 乐观更新功能未初始化")
            return False
    
    def optimistic_toggle(self, entity_id, field_name, new_value, ui_update_func, api_func, rollback_func=None, original_value=None):
        """乐观切换操作"""
        if hasattr(self, '_optimistic_crud'):
            return self._optimistic_crud.optimistic_toggle(
                entity_id, field_name, new_value, ui_update_func, api_func, rollback_func, original_value
            )
        else:
            print("⚠️ 乐观更新功能未初始化")
            return False
    
    def init_table_feedback(self, table_widget):
        """初始化表格视觉反馈"""
        try:
            if hasattr(self, '_optimistic_crud') and self._optimistic_crud:
                self._optimistic_crud.init_table_feedback(table_widget)
            print("✅ 用户详情页：表格视觉反馈初始化完成")
        except Exception as e:
            print(f"⚠️ 用户详情页：表格视觉反馈初始化失败: {e}")

    def show_status_message(self, message: str):
        """实现抽象方法：显示状态消息"""
        print(f"📢 状态消息: {message}")
        # 这里可以添加状态栏显示逻辑
    
    def init_ui(self):
        """初始化UI"""
        # 防止重复设置布局
        if self.layout() is not None:
            print("⚠️ 用户详情页面布局已存在，跳过重复设置")
            return

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题区域
        title_layout = QHBoxLayout()
        self.title_label = QLabel(f"用户详情")
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.load_user_detail)
        title_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(title_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                text-align: center;
                background-color: #f0f0f0;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }
        """)
        layout.addWidget(self.progress_bar)
        
        # 标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: #ffffff;
                border-radius: 4px;
            }
            
            QTabWidget::tab-bar {
                alignment: left;
            }
            
            QTabBar::tab {
                background-color: #f5f5f5;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 100px;
                font-weight: bold;
            }
            
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 1px solid #ffffff;
                color: #007bff;
            }
            
            QTabBar::tab:hover {
                background-color: #e8f4fd;
            }
        """)
        
        # 基本信息标签页
        self.basic_info_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(self.basic_info_tab, "👤 基本信息")
        
        # 购买记录标签页
        self.purchases_tab = self.create_purchases_tab()
        self.tab_widget.addTab(self.purchases_tab, "💰 购买记录")
        
        # 学习进度标签页
        self.progress_tab = self.create_progress_tab()
        self.tab_widget.addTab(self.progress_tab, "📊 学习进度")
        
        # 收藏管理标签页
        self.favorites_tab = self.create_favorites_tab()
        self.tab_widget.addTab(self.favorites_tab, "❤️ 收藏管理")
        
        # 用户设置标签页
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "⚙️ 用户设置")
        
        # 设备缓存标签页
        self.cache_tab = self.create_cache_tab()
        self.tab_widget.addTab(self.cache_tab, "💾 设备缓存")
        
        layout.addWidget(self.tab_widget)

        # 状态栏
        self.status_bar = QLabel("")
        self.status_bar.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 3px;
                padding: 8px 12px;
                color: #28a745;
                font-weight: bold;
                margin-top: 5px;
            }
        """)
        self.status_bar.setVisible(False)
        layout.addWidget(self.status_bar)

        # 状态栏自动隐藏定时器
        self.status_timer = QTimer()
        self.status_timer.setSingleShot(True)
        self.status_timer.timeout.connect(self.hide_status_message)
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                color: #333333;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 12px;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #fafafa;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }
            
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #45a049;
            }
            
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }
            
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
            
            QHeaderView::section {
                background-color: #f8f9fa;
                color: #333333;
                padding: 8px;
                border: 1px solid #e0e0e0;
                font-weight: bold;
            }
            
            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)
    
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 用户头像和基本信息卡片
        info_group = QGroupBox("👤 基本信息")
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)
        info_layout.setContentsMargins(20, 25, 20, 20)
        
        # 创建标签，设置更好的样式
        self.user_id_label = QLabel("-")
        self.user_id_label.setStyleSheet("color: #7f8c8d; font-size: 11px; font-family: monospace;")

        self.username_label = QLabel("-")
        self.username_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")

        self.email_label = QLabel("-")
        self.email_label.setStyleSheet("color: #34495e; font-size: 13px;")

        self.display_name_label = QLabel("-")
        self.display_name_label.setStyleSheet("color: #34495e; font-size: 13px;")

        self.phone_label = QLabel("-")
        self.phone_label.setStyleSheet("color: #34495e; font-size: 13px;")

        self.avatar_label = QLabel("-")
        self.avatar_label.setStyleSheet("color: #34495e; font-size: 13px;")

        self.password_label = QLabel("-")
        self.password_label.setStyleSheet("color: #7f8c8d; font-size: 11px; font-family: monospace;")

        self.status_label = QLabel("-")
        self.admin_label = QLabel("-")
        self.created_label = QLabel("-")
        self.created_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

        self.updated_label = QLabel("-")
        self.updated_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

        self.last_login_label = QLabel("-")
        self.last_login_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

        # 添加字段，使用更友好的标签
        info_layout.addRow("🆔 用户ID:", self.user_id_label)
        info_layout.addRow("👤 用户名:", self.username_label)
        info_layout.addRow("🔐 密码:", self.password_label)
        info_layout.addRow("📧 邮箱:", self.email_label)
        info_layout.addRow("🏷️ 显示名:", self.display_name_label)
        info_layout.addRow("📱 手机号:", self.phone_label)
        info_layout.addRow("🖼️ 头像:", self.avatar_label)
        info_layout.addRow("🔄 状态:", self.status_label)
        info_layout.addRow("🔑 权限:", self.admin_label)
        info_layout.addRow("📅 注册时间:", self.created_label)
        info_layout.addRow("🔄 更新时间:", self.updated_label)
        info_layout.addRow("🕐 最后登录:", self.last_login_label)
        
        layout.addWidget(info_group)
        
        # 操作按钮卡片
        btn_group = QGroupBox("⚙️ 操作")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)
        
        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)
        
        self.edit_info_btn = QPushButton("✏️ 编辑信息")
        self.edit_info_btn.setStyleSheet("QPushButton { background-color: #3498db; }")
        self.edit_info_btn.clicked.connect(self.edit_user_info)

        self.reset_password_btn = QPushButton("🔒 重置密码")
        self.reset_password_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        self.reset_password_btn.clicked.connect(self.reset_user_password)

        self.toggle_status_btn = QPushButton("🔄 切换状态")
        self.toggle_status_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")
        self.toggle_status_btn.clicked.connect(self.toggle_user_status)

        self.toggle_admin_btn = QPushButton("👑 切换权限")
        self.toggle_admin_btn.setStyleSheet("QPushButton { background-color: #9b59b6; }")
        self.toggle_admin_btn.clicked.connect(self.toggle_user_admin)

        btn_layout.addWidget(self.edit_info_btn)
        btn_layout.addWidget(self.reset_password_btn)
        btn_layout.addWidget(self.toggle_status_btn)
        btn_layout.addWidget(self.toggle_admin_btn)
        btn_layout.addStretch()
        
        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)
        
        layout.addStretch()
        
        return widget

    def create_purchases_tab(self) -> QWidget:
        """创建购买记录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💰 购买记录管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.add_purchase_btn = QPushButton("➕ 添加购买记录")
        self.add_purchase_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        self.add_purchase_btn.clicked.connect(self.add_purchase_record)

        self.batch_delete_btn = QPushButton("🗑️ 批量删除")
        self.batch_delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #e74c3c;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #c0392b;
            }
        """)
        self.batch_delete_btn.clicked.connect(self.batch_delete_purchases)

        btn_layout.addWidget(self.add_purchase_btn)
        btn_layout.addWidget(self.batch_delete_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 购买记录表格
        table_group = QGroupBox("📋 购买记录列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(10)

        # 创建表头 - 以分类为主题，后面是所属系列
        header_labels = ["分类名称", "系列名称", "价格", "购买时间", "过期时间", "状态", "订单ID", "项目类型", "项目ID", "操作"]
        self.purchases_table.setHorizontalHeaderLabels(header_labels)

        # 禁用悬停动画，但保留选择功能
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                selection-background-color: #e3f2fd;
                alternate-background-color: #f9f9f9;
                gridline-color: #ddd;
                background-color: white;
                outline: none;
            }
            QTableWidget::item {
                padding: 8px;
                border: none;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: black;
            }
            QTableWidget::item:hover {
                background-color: transparent;
            }
            QTableWidget::item:focus {
                outline: none;
            }
        """)

        # 设置表格选择模式，支持多选
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.purchases_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)  # 支持Ctrl+点击和拖拽多选
        self.purchases_table.horizontalHeader().setStretchLastSection(True)
        self.purchases_table.setAlternatingRowColors(True)

        # 设置固定行高，避免内容变化导致的位置偏移
        self.purchases_table.verticalHeader().setDefaultSectionSize(45)
        self.purchases_table.verticalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Fixed)

        # 🔥 修复：使用统一的列宽管理，避免重复连接信号
        # 设置列宽管理（包含默认列宽设置和保存功能）
        from utils.table_column_manager import setup_table_with_column_management
        default_widths = [120, 120, 80, 130, 130, 80, 80, 80, 80, 120]  # ["分类名称", "系列名称", "价格", "购买时间", "过期时间", "状态", "订单ID", "项目类型", "项目ID", "操作"]
        setup_table_with_column_management(self.purchases_table, "user_detail_purchases", default_widths)
        
        # 移除重复的默认列宽设置和注册配置
        # self.set_default_column_widths()  # 已在setup_table_with_column_management中处理
        # self.register_table_config("user_detail_purchases", self.purchases_table)  # 避免重复连接信号

        table_layout.addWidget(self.purchases_table)
        layout.addWidget(table_group)

        return widget

    def create_progress_tab(self) -> QWidget:
        """创建学习进度标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("📊 学习进度管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.reset_progress_btn = QPushButton("🔄 重置选中进度")
        self.reset_progress_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")
        self.reset_progress_btn.clicked.connect(self.reset_progress_record)

        self.mark_completed_btn = QPushButton("✅ 标记为完成")
        self.mark_completed_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        self.mark_completed_btn.clicked.connect(self.mark_progress_completed)

        btn_layout.addWidget(self.reset_progress_btn)
        btn_layout.addWidget(self.mark_completed_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 学习进度表格
        table_group = QGroupBox("📈 学习进度详情")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.progress_table = QTableWidget()
        self.progress_table.setColumnCount(12)
        self.progress_table.setHorizontalHeaderLabels([
            "视频标题", "分类标题", "系列标题", "进度百分比", "观看次数", "观看位置", "总时长", "最后观看", "创建时间", "更新时间", "视频ID", "ID"
        ])
        self.progress_table.setAlternatingRowColors(True)
        self.progress_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.progress_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)

        # 🔥 优化：使用统一的列宽管理，包含防抖动功能
        from utils.table_column_manager import setup_table_with_column_management
        progress_default_widths = [150, 120, 120, 100, 80, 80, 80, 130, 130, 130, 80, 50]  # ["视频标题", "分类标题", "系列标题", "进度百分比", "观看次数", "观看位置", "总时长", "最后观看", "创建时间", "更新时间", "视频ID", "ID"]
        setup_table_with_column_management(self.progress_table, "user_detail_progress", progress_default_widths)

        table_layout.addWidget(self.progress_table)
        layout.addWidget(table_group)

        return widget

    def create_favorites_tab(self) -> QWidget:
        """创建收藏管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("❤️ 收藏管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        self.delete_favorite_btn = QPushButton("🗑️ 删除选中收藏")
        self.delete_favorite_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        self.delete_favorite_btn.clicked.connect(self.delete_favorite_record)
        btn_layout.addWidget(self.delete_favorite_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 收藏表格
        table_group = QGroupBox("📋 收藏列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.favorites_table = QTableWidget()
        self.favorites_table.setColumnCount(10)
        self.favorites_table.setHorizontalHeaderLabels([
            "视频标题", "分类标题", "系列标题", "收藏时间", "创建时间", "更新时间", "项目类型", "项目ID", "ID", "操作"
        ])
        self.favorites_table.setAlternatingRowColors(True)
        self.favorites_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.favorites_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)

        # 🔥 优化：使用统一的列宽管理，包含防抖动功能
        from utils.table_column_manager import setup_table_with_column_management
        favorites_default_widths = [150, 120, 120, 130, 130, 130, 80, 80, 50, 100]  # ["视频标题", "分类标题", "系列标题", "收藏时间", "创建时间", "更新时间", "项目类型", "项目ID", "ID", "操作"]
        setup_table_with_column_management(self.favorites_table, "user_detail_favorites", favorites_default_widths)

        table_layout.addWidget(self.favorites_table)
        layout.addWidget(table_group)

        return widget

    def create_settings_tab(self) -> QWidget:
        """创建用户设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("⚙️ 设置管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.add_setting_btn = QPushButton("➕ 添加设置")
        self.add_setting_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        self.add_setting_btn.clicked.connect(self.add_user_setting)

        self.edit_setting_btn = QPushButton("✏️ 编辑设置")
        self.edit_setting_btn.setStyleSheet("QPushButton { background-color: #3498db; }")
        self.edit_setting_btn.clicked.connect(self.edit_user_setting)

        self.delete_setting_btn = QPushButton("🗑️ 删除设置")
        self.delete_setting_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        self.delete_setting_btn.clicked.connect(self.delete_user_setting)

        btn_layout.addWidget(self.add_setting_btn)
        btn_layout.addWidget(self.edit_setting_btn)
        btn_layout.addWidget(self.delete_setting_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 设置表格
        table_group = QGroupBox("📋 设置列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.settings_table = QTableWidget()
        self.settings_table.setColumnCount(6)
        self.settings_table.setHorizontalHeaderLabels([
            "ID", "设置键", "设置值", "创建时间", "更新时间", "操作"
        ])
        self.settings_table.setAlternatingRowColors(True)
        self.settings_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.settings_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)

        # 🔥 优化：使用统一的列宽管理，包含防抖动功能
        from utils.table_column_manager import setup_table_with_column_management
        settings_default_widths = [50, 150, 200, 130, 130, 100]  # ["ID", "设置键", "设置值", "创建时间", "更新时间", "操作"]
        setup_table_with_column_management(self.settings_table, "user_detail_settings", settings_default_widths)

        table_layout.addWidget(self.settings_table)
        layout.addWidget(table_group)

        return widget

    def create_cache_tab(self) -> QWidget:
        """创建设备缓存标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💾 缓存管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.clear_cache_btn = QPushButton("🗑️ 清除选中缓存")
        self.clear_cache_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        self.clear_cache_btn.clicked.connect(self.clear_selected_cache)

        self.clear_all_cache_btn = QPushButton("🧹 清除所有缓存")
        self.clear_all_cache_btn.setStyleSheet("QPushButton { background-color: #c0392b; }")
        self.clear_all_cache_btn.clicked.connect(self.clear_all_cache)

        btn_layout.addWidget(self.clear_cache_btn)
        btn_layout.addWidget(self.clear_all_cache_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 缓存表格
        table_group = QGroupBox("📋 缓存列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.cache_table = QTableWidget()
        self.cache_table.setColumnCount(11)
        self.cache_table.setHorizontalHeaderLabels([
            "视频标题", "分类标题", "系列标题", "缓存状态", "本地路径", "文件大小", "缓存时间", "创建时间", "更新时间", "视频ID", "ID"
        ])
        self.cache_table.setAlternatingRowColors(True)
        self.cache_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.cache_table.setSelectionMode(QTableWidget.SelectionMode.MultiSelection)

        # 🔥 优化：使用统一的列宽管理，包含防抖动功能
        from utils.table_column_manager import setup_table_with_column_management
        cache_default_widths = [150, 120, 120, 80, 200, 80, 130, 130, 130, 80, 50]  # ["视频标题", "分类标题", "系列标题", "缓存状态", "本地路径", "文件大小", "缓存时间", "创建时间", "更新时间", "视频ID", "ID"]
        setup_table_with_column_management(self.cache_table, "user_detail_cache", cache_default_widths)

        table_layout.addWidget(self.cache_table)
        layout.addWidget(table_group)

        return widget

    def get_user_accessible_videos(self):
        """获取用户可访问的视频列表（已购买+免费）"""
        try:
            accessible_videos = set()

            # 1. 获取用户购买记录，提取可访问的视频
            if hasattr(self, 'user_purchases') and self.user_purchases:
                purchase_data = self.user_purchases.get('data', [])
                for purchase in purchase_data:
                    if purchase.get('status') == 'is_active':  # 只考虑有效的购买
                        purchased_entity_type = purchase.get('purchased_entity_type', '')
                        purchased_entity_id = purchase.get('purchased_entity_id', '')

                        if purchased_entity_type == 'series':
                            # 购买了系列，获取该系列下的所有视频
                            series_videos = self.get_videos_by_series(purchased_entity_id)
                            accessible_videos.update(series_videos)
                        elif purchased_entity_type == 'category':
                            # 购买了分类，获取该分类下的所有视频
                            category_videos = self.get_videos_by_category(purchased_entity_id)
                            accessible_videos.update(category_videos)

            # 2. 获取所有免费视频
            free_videos = self.get_free_videos()
            accessible_videos.update(free_videos)

            return list(accessible_videos)

        except Exception as e:
            print(f"❌ 获取用户可访问视频列表失败: {e}")
            return []

    def get_videos_by_series(self, series_id):
        """根据系列ID获取视频列表"""
        try:
            # 从内存中获取视频数据
            from services.course_service import CourseService
            course_service = CourseService()

            # 获取该系列下的所有分类
            categories = course_service.get_categories_by_series(series_id)
            video_ids = []

            for category in categories:
                # 获取每个分类下的视频
                videos = course_service.get_videos_by_category(category.get('id'))
                video_ids.extend([v.get('id') for v in videos])

            return video_ids
        except Exception as e:
            print(f"❌ 获取系列视频失败: {e}")
            return []

    def get_videos_by_category(self, category_id):
        """根据分类ID获取视频列表"""
        try:
            from services.course_service import CourseService
            course_service = CourseService()
            videos = course_service.get_videos_by_category(category_id)
            return [v.get('id') for v in videos]
        except Exception as e:
            print(f"❌ 获取分类视频失败: {e}")
            return []

    def get_free_videos(self):
        """获取所有免费视频"""
        try:
            from services.course_service import CourseService
            course_service = CourseService()

            # 获取所有视频，筛选免费的
            all_videos = course_service.get_all_videos()
            free_video_ids = []

            for video in all_videos:
                if video.get('is_free', False) or video.get('price', 0) == 0:
                    free_video_ids.append(video.get('id'))

            return free_video_ids
        except Exception as e:
            print(f"❌ 获取免费视频失败: {e}")
            return []

    def refresh_related_tables(self):
        """刷新与购买记录相关的表格（学习进度、收藏、缓存）"""
        try:
            # 重新从API获取最新数据并填充表格
            print("🔄 开始从API重新获取最新数据...")
            
            # 获取最新的学习进度数据
            progress_data = self.user_service.get_user_progress(self.user_id)
            if progress_data and progress_data.get('success'):
                self.user_progress = progress_data
                self.populate_progress(progress_data)
                print("✅ 学习进度数据已更新")
            
            # 获取最新的收藏数据
            favorites_data = self.user_service.get_user_favorites(self.user_id)
            if favorites_data and favorites_data.get('success'):
                self.user_favorites = favorites_data
                self.populate_favorites(favorites_data)
                print("✅ 收藏数据已更新")
            
            # 获取最新的缓存数据
            cache_data = self.user_service.get_user_cache(self.user_id)
            if cache_data and cache_data.get('success'):
                self.user_cache = cache_data
                self.populate_cache(cache_data)
                print("✅ 缓存数据已更新")

            print("✅ 相关表格已刷新（基于最新的购买记录）")
        except Exception as e:
            print(f"❌ 刷新相关表格失败: {e}")

    def load_user_detail(self):
        """加载用户详情数据"""
        # 优先尝试使用预加载数据
        try:
            from cache.global_data_manager import global_data_manager
            if global_data_manager._data_loaded:
                # 使用预加载数据，即时显示
                result = {
                    'success': True,
                    'user_info': {'success': True, 'data': global_data_manager.get_user_detail(self.user_id)},
                    'purchases': global_data_manager.get_user_purchases(self.user_id),
                    'progress': global_data_manager.get_user_progress(self.user_id),
                    'favorites': global_data_manager.get_user_favorites(self.user_id),
                    'settings': global_data_manager.get_user_settings(self.user_id),
                    'cache_info': global_data_manager.get_user_cache(self.user_id)
                }

                # 直接显示数据，无需加载动画
                self.on_data_loaded(result)
                return
        except Exception as e:
            print(f"使用预加载数据失败: {e}")

        # 如果预加载数据不可用，则使用线程加载
        if self.load_thread and self.load_thread.isRunning():
            return

        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条

        self.load_thread = UserDetailLoadThread(self.user_service, self.user_id)
        self.load_thread.data_loaded.connect(self.on_data_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()

    def on_data_loaded(self, result):
        """数据加载完成"""
        self.progress_bar.setVisible(False)

        if result['success']:
            self.populate_user_info(result['user_info'])
            self.populate_purchases(result['purchases'])
            self.populate_progress(result['progress'])
            self.populate_favorites(result['favorites'])
            self.populate_settings(result['settings'])
            self.populate_cache(result['cache_info'])
        else:
            QMessageBox.warning(self, '错误', result.get('message', '加载用户详情失败'))

    def on_load_error(self, error_msg):
        """数据加载错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '错误', f'加载用户详情失败: {error_msg}')

    def populate_user_info(self, user_info):
        """填充用户基本信息"""
        if not user_info:
            return

        # 处理嵌套的数据结构
        if user_info.get('success') and user_info.get('data'):
            user_data = user_info['data']
        else:
            user_data = user_info

        # 用户ID显示
        user_id = user_data.get('user_id', '') or str(user_data.get('id', ''))
        self.user_id_label.setText(user_id)

        self.username_label.setText(user_data.get('username', '-'))
        self.email_label.setText(user_data.get('email', '-'))
        self.display_name_label.setText(user_data.get('display_name', '-'))
        self.phone_label.setText(user_data.get('phone', '-'))

        # 头像显示 - 兼容服务端返回的avatar和avatar_url字段
        avatar_url = user_data.get('avatar_url', '') or user_data.get('avatar', '')
        if avatar_url:
            self.avatar_label.setText(f'<a href="{avatar_url}">查看头像</a>')
        else:
            self.avatar_label.setText('无')

        # 🎯 修复：密码显示为明文（管理端专用）
        password_hash = user_data.get('password_hash', '')
        if password_hash:
            # 直接显示完整的密码内容（因为管理端只有用户本人使用）
            self.password_label.setText(password_hash)
        else:
            self.password_label.setText('未设置')

        # 状态显示 - 使用图标和颜色
        is_active = user_data.get('is_active', False)
        if is_active:
            status_html = '<span style="color: #27ae60; font-weight: bold; font-size: 14px;">🟢 激活</span>'
        else:
            status_html = '<span style="color: #e74c3c; font-weight: bold; font-size: 14px;">🔴 禁用</span>'
        self.status_label.setText(status_html)

        # 权限显示 - 使用图标和颜色
        is_admin = user_data.get('is_admin', False)
        if is_admin:
            admin_html = '<span style="color: #8e44ad; font-weight: bold; font-size: 14px;">👑 管理员</span>'
        else:
            admin_html = '<span style="color: #34495e; font-weight: bold; font-size: 14px;">👤 普通用户</span>'
        self.admin_label.setText(admin_html)

        # 时间字段格式化显示
        created_at = user_data.get('created_at', '')
        self.created_label.setText(created_at[:19] if created_at else '-')

        updated_at = user_data.get('updated_at', '')
        self.updated_label.setText(updated_at[:19] if updated_at else '-')

        last_login = user_data.get('last_login_at', '')
        self.last_login_label.setText(last_login[:19] if last_login else '从未登录')

        # 更新页面标题
        username = user_data.get('username', self.user_id)
        display_name = user_data.get('display_name', '')
        title = f"用户详情 - {username}"
        if display_name and display_name != username:
            title += f" ({display_name})"
        self.title_label.setText(title)

    def populate_purchases(self, purchases):
        """填充购买记录"""
        # 保存购买记录数据以便后续刷新使用
        self.user_purchases = purchases

        if not purchases or not purchases.get('success'):
            self.purchases_table.setRowCount(0)
            return

        purchase_data = purchases.get('data', [])
        self.purchases_table.setRowCount(len(purchase_data))

        for row, purchase in enumerate(purchase_data):
            # 新的表格结构：["分类名称", "系列名称", "价格", "购买时间", "过期时间", "状态", "订单ID", "项目类型", "项目ID", "操作"]

            # 分类名称
            category_name = purchase.get('category_name', '-')
            self.purchases_table.setItem(row, 0, QTableWidgetItem(category_name))

            # 系列名称
            series_name = purchase.get('series_name', '')
            self.purchases_table.setItem(row, 1, QTableWidgetItem(series_name))

            # 价格
            amount = purchase.get('amount', purchase.get('purchase_price', 0))
            price_text = f"¥{amount:.2f}" if amount > 0 else "免费"
            self.purchases_table.setItem(row, 2, QTableWidgetItem(price_text))

            # 购买时间
            purchase_date = purchase.get('purchase_date', '')
            if purchase_date:
                # 格式化日期显示
                try:
                    dt = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = purchase_date
            else:
                formatted_date = '-'
            self.purchases_table.setItem(row, 3, QTableWidgetItem(formatted_date))

            # 过期时间
            expire_date = purchase.get('expire_date', '')
            if expire_date:
                try:
                    dt = datetime.fromisoformat(expire_date.replace('Z', '+00:00'))
                    formatted_expire = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_expire = expire_date
            else:
                formatted_expire = '-'
            self.purchases_table.setItem(row, 4, QTableWidgetItem(formatted_expire))

            # 状态
            status = purchase.get('status', 'is_active')
            status_text = {'is_active': '有效', 'expired': '已过期'}.get(status, status)
            status_item = QTableWidgetItem(status_text)
            if status == 'is_active':
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            else:
                status_item.setBackground(QColor(255, 200, 200))  # 红色背景
            self.purchases_table.setItem(row, 5, status_item)

            # 订单ID
            order_id = purchase.get('order_id', '')
            self.purchases_table.setItem(row, 6, QTableWidgetItem(str(order_id) if order_id else '-'))

            # 项目类型
            purchased_entity_type = purchase.get('purchased_entity_type', '')
            type_display = {'series': '系列', 'category': '分类'}.get(purchased_entity_type, purchased_entity_type)
            self.purchases_table.setItem(row, 7, QTableWidgetItem(type_display))

            # 项目ID
            purchased_entity_id = purchase.get('purchased_entity_id', '')
            self.purchases_table.setItem(row, 8, QTableWidgetItem(str(purchased_entity_id)))

            # 操作按钮
            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: 1px solid #e74c3c;
                    padding: 6px 12px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    width: 60px;
                    height: 28px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                    border: 1px solid #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                    border: 1px solid #a93226;
                }
            """)
            delete_btn.setFixedSize(60, 28)  # 固定按钮尺寸

            # 使用购买记录ID而不是行号
            purchase_id = str(purchase.get('id', ''))
            delete_btn.clicked.connect(lambda checked=False, pid=purchase_id: self.delete_purchase_by_id(pid))
            self.purchases_table.setCellWidget(row, 9, delete_btn)  # 操作列是第9列
        print(f"✅ 购买记录表格已填充，共 {len(purchase_data)} 条记录")

        # 购买记录变化后，刷新其他相关表格
        self.refresh_related_tables()

    def populate_progress(self, progress):
        """填充学习进度"""
        # 保存学习进度数据以便后续刷新使用
        self.user_progress = progress

        if not progress or not progress.get('success'):
            self.progress_table.setRowCount(0)
            return

        progress_data = progress.get('data', [])

        # 获取用户可访问的视频列表
        accessible_videos = self.get_user_accessible_videos()

        # 只显示用户可访问的视频的学习进度
        filtered_progress = []
        for prog in progress_data:
            video_id = prog.get('video_id')
            if video_id in accessible_videos:
                filtered_progress.append(prog)

        self.progress_table.setRowCount(len(filtered_progress))

        for row, prog in enumerate(filtered_progress):
            # 新的表格结构：["视频标题", "分类标题", "系列标题", "进度百分比", "观看次数", "观看位置", "总时长", "最后观看", "创建时间", "更新时间", "视频ID", "ID"]

            # 视频标题
            self.progress_table.setItem(row, 0, QTableWidgetItem(prog.get('video_title', '')))

            # 分类标题
            self.progress_table.setItem(row, 1, QTableWidgetItem(prog.get('category_title', '')))

            # 系列标题
            self.progress_table.setItem(row, 2, QTableWidgetItem(prog.get('series_title', '')))

            # 进度百分比
            progress_percentage = prog.get('progress_percentage', 0)
            progress_item = QTableWidgetItem(f"{progress_percentage}%")
            # 根据进度设置背景色
            if progress_percentage >= 100:
                progress_item.setBackground(QColor(200, 255, 200))  # 绿色 - 已完成
            elif progress_percentage >= 50:
                progress_item.setBackground(QColor(255, 255, 200))  # 黄色 - 进行中
            elif progress_percentage > 0:
                progress_item.setBackground(QColor(255, 230, 230))  # 浅红色 - 刚开始
            self.progress_table.setItem(row, 3, progress_item)

            # 观看次数
            watch_count = prog.get('watch_count', 0)
            self.progress_table.setItem(row, 4, QTableWidgetItem(str(watch_count)))

            # 观看位置（秒）
            position = prog.get('position', 0)
            self.progress_table.setItem(row, 5, QTableWidgetItem(f"{position}s"))

            # 总时长（秒）
            duration = prog.get('duration', 0)
            self.progress_table.setItem(row, 6, QTableWidgetItem(f"{duration}s"))

            # 最后观看时间
            last_watched = prog.get('last_watched_at', '')
            if last_watched:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_watched.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = last_watched
            else:
                formatted_date = '-'
            self.progress_table.setItem(row, 7, QTableWidgetItem(formatted_date))

            # 创建时间
            created_at = prog.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_created = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_created = created_at
            else:
                formatted_created = '-'
            self.progress_table.setItem(row, 8, QTableWidgetItem(formatted_created))

            # 更新时间
            updated_at = prog.get('updated_at', '')
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    formatted_updated = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_updated = updated_at
            else:
                formatted_updated = '-'
            self.progress_table.setItem(row, 9, QTableWidgetItem(formatted_updated))

            # 视频ID
            self.progress_table.setItem(row, 10, QTableWidgetItem(str(prog.get('video_id', ''))))

            # ID
            self.progress_table.setItem(row, 11, QTableWidgetItem(str(prog.get('id', ''))))

    def populate_favorites(self, favorites):
        """填充收藏记录"""
        # 保存收藏数据以便后续刷新使用
        self.user_favorites = favorites

        if not favorites or not favorites.get('success'):
            self.favorites_table.setRowCount(0)
            return

        favorites_data = favorites.get('data', [])

        # 获取用户可访问的视频列表
        accessible_videos = self.get_user_accessible_videos()

        # 只显示用户可访问的视频的收藏记录
        filtered_favorites = []
        for favorite in favorites_data:
            purchased_entity_type = favorite.get('purchased_entity_type', '')
            purchased_entity_id = favorite.get('purchased_entity_id', '')

            # 如果是视频收藏，检查是否可访问
            if purchased_entity_type == 'video' and purchased_entity_id in accessible_videos:
                filtered_favorites.append(favorite)
            # 如果是系列或分类收藏，暂时都显示（可以根据需要调整）
            elif purchased_entity_type in ['series', 'category']:
                filtered_favorites.append(favorite)

        self.favorites_table.setRowCount(len(filtered_favorites))

        for row, favorite in enumerate(filtered_favorites):
            # 新的表格结构：["视频标题", "分类标题", "系列标题", "收藏时间", "创建时间", "更新时间", "项目类型", "项目ID", "ID", "操作"]

            # 视频标题
            video_title = favorite.get('video_title', '')
            self.favorites_table.setItem(row, 0, QTableWidgetItem(video_title))

            # 分类标题
            category_title = favorite.get('category_title', '')
            self.favorites_table.setItem(row, 1, QTableWidgetItem(category_title))

            # 系列标题
            series_title = favorite.get('series_title', '')
            self.favorites_table.setItem(row, 2, QTableWidgetItem(series_title))

            # 收藏时间
            favorited_at = favorite.get('favorited_at', '')
            if favorited_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(favorited_at.replace('Z', '+00:00'))
                    formatted_favorited = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_favorited = favorited_at
            else:
                formatted_favorited = '-'
            self.favorites_table.setItem(row, 3, QTableWidgetItem(formatted_favorited))

            # 创建时间
            created_at = favorite.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_created = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_created = created_at
            else:
                formatted_created = '-'
            self.favorites_table.setItem(row, 4, QTableWidgetItem(formatted_created))

            # 更新时间
            updated_at = favorite.get('updated_at', '')
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    formatted_updated = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_updated = updated_at
            else:
                formatted_updated = '-'
            self.favorites_table.setItem(row, 5, QTableWidgetItem(formatted_updated))

            # 项目类型
            purchased_entity_type = favorite.get('purchased_entity_type', '')
            type_text = {"video": "视频", "category": "分类", "series": "系列"}.get(purchased_entity_type, purchased_entity_type)
            self.favorites_table.setItem(row, 6, QTableWidgetItem(type_text))

            # 项目ID
            purchased_entity_id = favorite.get('purchased_entity_id', '')
            self.favorites_table.setItem(row, 7, QTableWidgetItem(str(purchased_entity_id)))

            # ID
            self.favorites_table.setItem(row, 8, QTableWidgetItem(str(favorite.get('id', ''))))

            # 操作按钮（暂时显示文本）
            self.favorites_table.setItem(row, 9, QTableWidgetItem("删除"))

    def populate_settings(self, settings):
        """填充用户设置"""
        if not settings or not settings.get('success'):
            self.settings_table.setRowCount(0)
            return

        settings_data = settings.get('data', [])
        self.settings_table.setRowCount(len(settings_data))

        for row, setting in enumerate(settings_data):
            # 新的表格结构：["ID", "设置键", "设置值", "创建时间", "更新时间", "操作"]

            # ID
            self.settings_table.setItem(row, 0, QTableWidgetItem(str(setting.get('id', ''))))

            # 设置键
            setting_key = setting.get('setting_key', '')
            self.settings_table.setItem(row, 1, QTableWidgetItem(setting_key))

            # 设置值
            value = setting.get('setting_value', '')
            # 限制显示长度
            if len(str(value)) > 50:
                display_value = str(value)[:47] + "..."
            else:
                display_value = str(value)
            self.settings_table.setItem(row, 2, QTableWidgetItem(display_value))

            # 创建时间
            created_at = setting.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_created = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_created = created_at
            else:
                formatted_created = '-'
            self.settings_table.setItem(row, 3, QTableWidgetItem(formatted_created))

            # 更新时间
            updated_at = setting.get('updated_at', '')
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    formatted_updated = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_updated = updated_at
            else:
                formatted_updated = '-'
            self.settings_table.setItem(row, 4, QTableWidgetItem(formatted_updated))

            # 操作按钮（暂时显示文本）
            self.settings_table.setItem(row, 5, QTableWidgetItem("编辑"))

    def populate_cache(self, cache_info):
        """填充缓存信息"""
        # 保存缓存数据以便后续刷新使用
        self.user_cache = cache_info

        if not cache_info or not cache_info.get('success'):
            self.cache_table.setRowCount(0)
            return

        cache_data = cache_info.get('data', [])

        # 获取用户可访问的视频列表
        accessible_videos = self.get_user_accessible_videos()

        # 只显示用户可访问的视频的缓存记录
        filtered_cache = []
        for cache in cache_data:
            video_id = cache.get('video_id')
            if video_id in accessible_videos:
                filtered_cache.append(cache)

        self.cache_table.setRowCount(len(filtered_cache))

        for row, cache in enumerate(filtered_cache):
            # 新的表格结构：["视频标题", "分类标题", "系列标题", "缓存状态", "本地路径", "文件大小", "缓存时间", "创建时间", "更新时间", "视频ID", "ID"]

            # 视频标题
            video_title = cache.get('video_title', '')
            self.cache_table.setItem(row, 0, QTableWidgetItem(video_title))

            # 分类标题
            category_title = cache.get('category_title', '')
            self.cache_table.setItem(row, 1, QTableWidgetItem(category_title))

            # 系列标题
            series_title = cache.get('series_title', '')
            self.cache_table.setItem(row, 2, QTableWidgetItem(series_title))

            # 缓存状态
            is_cached = cache.get('is_cached', False)
            status_text = "已缓存" if is_cached else "未缓存"
            status_item = QTableWidgetItem(status_text)
            if is_cached:
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            else:
                status_item.setBackground(QColor(255, 230, 230))  # 浅红色背景
            self.cache_table.setItem(row, 3, status_item)

            # 本地路径
            local_path = cache.get('local_path', '')
            # 限制路径显示长度
            if len(local_path) > 40:
                display_path = "..." + local_path[-37:]
            else:
                display_path = local_path
            self.cache_table.setItem(row, 4, QTableWidgetItem(display_path))

            # 文件大小
            file_size = cache.get('file_size', 0)
            if file_size > 0:
                # 转换为可读的文件大小
                if file_size >= 1024 * 1024 * 1024:  # GB
                    size_text = f"{file_size / (1024 * 1024 * 1024):.2f} GB"
                elif file_size >= 1024 * 1024:  # MB
                    size_text = f"{file_size / (1024 * 1024):.2f} MB"
                elif file_size >= 1024:  # KB
                    size_text = f"{file_size / 1024:.2f} KB"
                else:
                    size_text = f"{file_size} B"
            else:
                size_text = "-"
            self.cache_table.setItem(row, 5, QTableWidgetItem(size_text))

            # 缓存时间
            cached_at = cache.get('cached_at', '')
            if cached_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(cached_at.replace('Z', '+00:00'))
                    formatted_cached = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_cached = cached_at
            else:
                formatted_cached = '-'
            self.cache_table.setItem(row, 6, QTableWidgetItem(formatted_cached))

            # 创建时间
            created_at = cache.get('created_at', '')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_created = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_created = created_at
            else:
                formatted_created = '-'
            self.cache_table.setItem(row, 7, QTableWidgetItem(formatted_created))

            # 更新时间
            updated_at = cache.get('updated_at', '')
            if updated_at:
                try:
                    dt = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                    formatted_updated = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_updated = updated_at
            else:
                formatted_updated = '-'
            self.cache_table.setItem(row, 8, QTableWidgetItem(formatted_updated))

            # 视频ID
            self.cache_table.setItem(row, 9, QTableWidgetItem(str(cache.get('video_id', ''))))

            # ID
            self.cache_table.setItem(row, 10, QTableWidgetItem(str(cache.get('id', ''))))

    # 操作按钮事件处理方法
    def edit_user_info(self):
        """编辑用户信息"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton, QCheckBox, QMessageBox

        # 创建编辑对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("编辑用户信息")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        # 表单布局
        form_layout = QFormLayout()

        # 获取当前用户数据
        current_username = self.username_label.text()
        current_email = self.email_label.text()
        current_display_name = self.display_name_label.text()
        current_phone = self.phone_label.text()

        # 输入字段
        username_edit = QLineEdit(current_username)
        email_edit = QLineEdit(current_email)
        display_name_edit = QLineEdit(current_display_name)
        phone_edit = QLineEdit(current_phone)

        form_layout.addRow("用户名:", username_edit)
        form_layout.addRow("邮箱:", email_edit)
        form_layout.addRow("显示名:", display_name_edit)
        form_layout.addRow("手机号:", phone_edit)

        layout.addLayout(form_layout)

        # 按钮布局
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存")
        cancel_btn = QPushButton("❌ 取消")

        save_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        cancel_btn.setStyleSheet("QPushButton { background-color: #95a5a6; }")

        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        # 事件处理
        def save_changes():
            try:
                # 获取编辑后的数据
                new_data = {
                    'username': username_edit.text().strip(),
                    'email': email_edit.text().strip(),
                    'display_name': display_name_edit.text().strip(),
                    'phone': phone_edit.text().strip()
                }

                # 验证数据
                if not new_data['username']:
                    QMessageBox.warning(dialog, "错误", "用户名不能为空！")
                    return

                if not new_data['email']:
                    QMessageBox.warning(dialog, "错误", "邮箱不能为空！")
                    return

                # 保存原始数据用于回滚
                original_data = {
                    'username': current_username,
                    'email': current_email,
                    'display_name': current_display_name,
                    'phone': current_phone
                }

                # 🎯 修复：使用组合的乐观更新实例
                success = self._optimistic_crud.optimistic_update(
                    entity_id=str(self.user_id),
                    entity_data=new_data,
                    ui_update_func=lambda entity_id, data: self.update_user_info_display(data),
                    api_func=lambda entity_id, data: self.user_service.update_user_info(entity_id, data),
                    rollback_func=lambda entity_id, original_data: self.update_user_info_display(original_data),
                    original_data=original_data
                )

                if success:
                    print(f"🚀 用户信息更新操作已启动: {self.user_id}")
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "错误", "❌ 无法启动用户信息更新操作")

            except Exception as e:
                QMessageBox.critical(dialog, "错误", f"保存失败: {str(e)}")

        def cancel_changes():
            dialog.reject()

        save_btn.clicked.connect(save_changes)
        cancel_btn.clicked.connect(cancel_changes)

        dialog.exec()

    def reset_user_password(self):
        """重置用户密码"""
        from PyQt6.QtWidgets import QInputDialog

        # 确认对话框
        reply = QMessageBox.question(
            self,
            "确认重置密码",
            f"确定要重置用户 {self.username_label.text()} 的密码吗？\n\n重置后的密码将发送到用户邮箱。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 调用重置密码服务
                result = self.user_service.reset_user_password(self.user_id)

                if result.get('success'):
                    self.show_status_message("🔒 密码重置成功！新密码已发送到用户邮箱")
                else:
                    QMessageBox.warning(self, "错误", f"密码重置失败: {result.get('message', '未知错误')}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"密码重置失败: {str(e)}")

    def toggle_user_status(self):
        """切换用户状态 - 使用乐观更新架构"""
        current_status = "激活" in self.status_label.text()
        new_status = not current_status

        # 直接切换，不需要确认，使用乐观更新架构
        try:
            # 🎯 修复：手动发射操作开始信号
            self.operation_started.emit(str(self.user_id), 'toggle')

            # 🎯 修复：使用组合的乐观更新实例
            success = self._optimistic_crud.optimistic_toggle(
                entity_id=str(self.user_id),
                field_name='is_active',
                new_value=new_status,
                ui_update_func=lambda entity_id, field, value: self.update_user_status_display(value),
                api_func=lambda entity_id, field, value: self.user_service.toggle_user_status(entity_id),
                rollback_func=lambda entity_id, field, original_value: self.update_user_status_display(original_value),
                original_value=current_status
            )

            if success:
                print(f"🚀 用户状态切换操作已启动: {self.user_id}")
            else:
                self.show_status_message("❌ 无法启动用户状态切换操作")
                # 🎯 修复：发射操作失败信号
                self.operation_failed.emit(str(self.user_id), 'toggle', '无法启动操作')

        except Exception as e:
            # 🎯 修复：发射操作失败信号
            self.operation_failed.emit(str(self.user_id), 'toggle', str(e))
            QMessageBox.critical(self, "错误", f"状态切换失败: {str(e)}")

    def toggle_user_admin(self):
        """切换用户权限 - 使用乐观更新架构"""
        current_admin = "管理员" in self.admin_label.text()
        new_admin = not current_admin

        # 直接切换，不需要确认，使用乐观更新架构
        try:
            # 🎯 修复：手动发射操作开始信号
            self.operation_started.emit(str(self.user_id), 'toggle')

            # 🎯 修复：使用组合的乐观更新实例
            success = self._optimistic_crud.optimistic_toggle(
                entity_id=str(self.user_id),
                field_name='is_admin',
                new_value=new_admin,
                ui_update_func=lambda entity_id, field, value: self.update_user_admin_display(value),
                api_func=lambda entity_id, field, value: self.user_service.toggle_user_admin(entity_id),
                rollback_func=lambda entity_id, field, original_value: self.update_user_admin_display(original_value),
                original_value=current_admin
            )

            if success:
                print(f"🚀 用户权限切换操作已启动: {self.user_id}")
            else:
                self.show_status_message("❌ 无法启动用户权限切换操作")
                # 🎯 修复：发射操作失败信号
                self.operation_failed.emit(str(self.user_id), 'toggle', '无法启动操作')

        except Exception as e:
            # 🎯 修复：发射操作失败信号
            self.operation_failed.emit(str(self.user_id), 'toggle', str(e))
            QMessageBox.critical(self, "错误", f"权限切换失败: {str(e)}")

    def update_user_status_display(self, is_active):
        """更新用户状态显示"""
        try:
            if is_active:
                status_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
            else:
                status_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
            self.status_label.setText(status_html)
        except Exception as e:
            print(f"更新用户状态显示失败: {e}")

    def update_user_admin_display(self, is_admin):
        """更新用户权限显示"""
        try:
            if is_admin:
                admin_html = '<span style="color: #f39c12; font-weight: bold;">👑 管理员</span>'
            else:
                admin_html = '<span style="color: #95a5a6; font-weight: bold;">👤 普通用户</span>'
            self.admin_label.setText(admin_html)
        except Exception as e:
            print(f"更新用户权限显示失败: {e}")

    def update_user_info_display(self, new_data):
        """更新用户信息显示"""
        self.username_label.setText(new_data.get('username', '-'))
        self.email_label.setText(new_data.get('email', '-'))
        self.display_name_label.setText(new_data.get('display_name', '-'))
        self.phone_label.setText(new_data.get('phone', '-'))

        # 更新页面标题
        username = new_data.get('username', self.user_id)
        display_name = new_data.get('display_name', '')
        title = f"用户详情 - {username}"
        if display_name and display_name != username:
            title += f" ({display_name})"
        self.title_label.setText(title)

    def refresh_user_status(self):
        """刷新用户状态显示"""
        try:
            # 重新获取用户信息
            user_info = self.user_service.get_user_by_id(self.user_id)
            if user_info and user_info.get('success'):
                self.populate_user_info(user_info)
        except Exception as e:
            print(f"刷新用户状态失败: {e}")

    def refresh_purchase_records(self):
        """刷新购买记录显示"""
        try:
            # 重新获取购买记录
            purchases = self.user_service.get_user_purchases(self.user_id)
            if purchases:
                self.populate_purchases(purchases)
        except Exception as e:
            print(f"刷新购买记录失败: {e}")

    def add_purchase_to_table(self, purchase, skip_refresh=False):
        """立即添加购买记录到表格
        
        Args:
            purchase: 购买记录数据
            skip_refresh: 是否跳过刷新相关表格（批量添加时使用）
        """
        try:
            # 在表格顶部插入新行
            self.purchases_table.insertRow(0)

            # 新的表格列定义：["ID", "分类名称", "所属系列", "价格", "购买时间", "状态", "过期时间", "操作"]

            # 第0列：ID
            purchase_id = purchase.get('id', f'temp_{int(time.time())}')
            self.purchases_table.setItem(0, 0, QTableWidgetItem(str(purchase_id)))

            # 第1列：分类名称
            category_name = purchase.get('category_name', purchase.get('item_name', ''))
            self.purchases_table.setItem(0, 1, QTableWidgetItem(category_name))

            # 第2列：所属系列
            series_name = purchase.get('series_name', '未知系列')
            self.purchases_table.setItem(0, 2, QTableWidgetItem(series_name))

            # 第3列：价格
            amount = purchase.get('amount', 0)
            price_text = f"¥{amount:.2f}" if amount > 0 else "免费"
            self.purchases_table.setItem(0, 3, QTableWidgetItem(price_text))

            # 第4列：购买时间
            formatted_date = datetime.now().strftime('%Y-%m-%d %H:%M')
            self.purchases_table.setItem(0, 4, QTableWidgetItem(formatted_date))

            # 第5列：状态
            status_item = QTableWidgetItem("已购买")
            status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            self.purchases_table.setItem(0, 5, status_item)

            # 第6列：过期时间（暂时留空）
            self.purchases_table.setItem(0, 6, QTableWidgetItem("-"))

            # 第7列：操作按钮
            delete_btn = QPushButton("删除")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: 1px solid #e74c3c;
                    padding: 6px 12px;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                    width: 60px;
                    height: 28px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                    border: 1px solid #c0392b;
                }
                QPushButton:pressed {
                    background-color: #a93226;
                    border: 1px solid #a93226;
                }
            """)
            delete_btn.setFixedSize(60, 28)  # 固定按钮尺寸

            # 使用购买记录ID
            purchase_id = str(purchase.get('id', ''))
            delete_btn.clicked.connect(lambda checked=False, pid=purchase_id: self.delete_purchase_by_id(pid))
            self.purchases_table.setCellWidget(0, 7, delete_btn)

            print(f"✅ 乐观更新：已添加购买记录到表格")
            
            # 添加购买记录后，刷新进度、收藏、缓存表格（除非明确跳过）
            if not skip_refresh:
                self.refresh_related_tables()

        except Exception as e:
            print(f"添加购买记录到表格失败: {e}")

    def save_purchase_background(self, purchased_entity_type, purchased_entity_id, item_name, amount):
        """后台保存购买记录 - 使用Qt原生工作线程"""
        # 构建购买记录数据
        purchase_data = {
            'type': purchased_entity_type,
            'purchased_entity_id': purchased_entity_id,
            'item_name': item_name,
            'amount': amount,
            'status': 'is_active'
        }

        print(f"🔄 使用乐观更新保存购买记录: {purchase_data}")

        # 使用乐观更新操作
        def ui_update_func(entity_id, data):
            """UI立即更新函数"""
            self.add_purchase_to_table(data)

        def api_func(data):
            """API调用函数"""
            return self.user_service.add_user_purchase(self.user_id, data)

        def rollback_func(entity_id):
            """回滚函数"""
            print(f"🔄 回滚购买记录添加: {entity_id}")
            # 这里可以添加从表格中移除记录的逻辑

        # 使用乐观更新操作
        self.optimistic_create(
            entity_type='purchase',
            entity_data=purchase_data,
            ui_update_func=ui_update_func,
            api_func=api_func,
            rollback_func=rollback_func
        )

    def delete_purchase_record(self, row):
        """删除购买记录"""
        try:
            # 获取要删除的记录ID
            id_item = self.purchases_table.item(row, 0)
            if not id_item:
                QMessageBox.warning(self, "错误", "无法获取记录ID")
                return

            purchase_id = id_item.text()

            # 获取记录信息用于确认
            category_item = self.purchases_table.item(row, 1)
            series_item = self.purchases_table.item(row, 2)
            category_name = category_item.text() if category_item else "未知分类"
            series_name = series_item.text() if series_item else "未知系列"

            # 确认删除
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除购买记录吗？\n\n分类：{category_name}\n系列：{series_name}",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )

            if reply != QMessageBox.StandardButton.Yes:
                return

            # 调用服务删除记录
            result = self.user_service.delete_user_purchase(self.user_id, purchase_id)

            if result.get('success'):
                # 删除成功，从表格中移除行
                self.purchases_table.removeRow(row)
                self.show_status_message(f"🗑️ 购买记录删除成功：{category_name}")
            else:
                QMessageBox.warning(self, "错误", f"删除失败: {result.get('message', '未知错误')}")

        except Exception as e:
            print(f"删除购买记录异常: {e}")
            QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")

    def delete_purchase_by_id(self, purchase_id):
        """通过ID删除购买记录（乐观删除）"""
        try:
            # 🚀 乐观删除：立即从表格中移除记录
            row_to_delete = None
            category_name = ""

            # 查找要删除的行
            for row in range(self.purchases_table.rowCount()):
                id_item = self.purchases_table.item(row, 0)  # ID在第0列
                if id_item and id_item.text() == purchase_id:
                    row_to_delete = row
                    # 获取分类名称用于提示
                    category_item = self.purchases_table.item(row, 1)  # 分类名称在第1列
                    category_name = category_item.text() if category_item else "未知分类"
                    break

            if row_to_delete is None:
                QMessageBox.warning(self, "错误", "找不到要删除的记录")
                return

            # 立即从表格中删除行
            self.purchases_table.removeRow(row_to_delete)

            # 立即显示删除成功提示
            self.show_status_message(f"🗑️ 购买记录删除成功：{category_name}")

            # 🔄 后台异步删除
            self.delete_purchase_background(purchase_id, category_name)

        except Exception as e:
            print(f"乐观删除异常: {e}")
            QMessageBox.critical(self, "错误", f"删除失败: {str(e)}")

    def delete_purchase_background(self, purchase_id, category_name):
        """后台删除购买记录 - 使用Qt原生工作线程"""
        print(f"🔄 使用乐观更新删除购买记录: {purchase_id}")

        # 使用乐观更新操作
        def ui_update_func(entity_id):
            """UI立即更新函数"""
            # 从表格中移除记录
            for row in range(self.purchases_table.rowCount()):
                if self.purchases_table.item(row, 0) and \
                   self.purchases_table.item(row, 0).text() == str(purchase_id):
                    self.purchases_table.removeRow(row)
                    break

        def api_func(entity_id):
            """API调用函数"""
            return self.user_service.delete_user_purchase(self.user_id, entity_id)

        def rollback_func(entity_id, original_data):
            """回滚函数"""
            print(f"🔄 回滚购买记录删除: {entity_id}")
            # 重新加载购买记录
            self.refresh_purchase_records()

        # 使用乐观更新操作
        self.optimistic_delete(
            entity_id=purchase_id,
            entity_data={'category_name': category_name},
            ui_update_func=ui_update_func,
            api_func=api_func,
            rollback_func=rollback_func
        )

    def batch_delete_purchases(self):
        """批量删除选中的购买记录"""
        try:
            # 获取选中的行
            selected_rows = self.purchases_table.selectionModel().selectedRows()

            if not selected_rows:
                QMessageBox.warning(self, '提示', '请先选择要删除的购买记录')
                return

            selected_purchases = []
            for index in selected_rows:
                row = index.row()
                # 获取购买记录信息
                id_item = self.purchases_table.item(row, 0)  # ID在第0列
                category_item = self.purchases_table.item(row, 1)  # 分类名称在第1列

                if id_item and category_item:
                    selected_purchases.append({
                        'row': row,
                        'id': id_item.text(),
                        'name': category_item.text()
                    })

            if not selected_purchases:
                QMessageBox.warning(self, '提示', '未找到有效的购买记录')
                return

            # 直接删除，不需要确认

            # 🚀 乐观删除：立即从表格中删除所有选中的行
            deleted_names = []

            # 从后往前删除，避免行号变化问题
            for purchase in sorted(selected_purchases, key=lambda x: x['row'], reverse=True):
                self.purchases_table.removeRow(purchase['row'])
                deleted_names.append(purchase['name'])

            # 显示删除成功提示
            self.show_status_message(f"🗑️ 批量删除成功：已删除 {len(selected_purchases)} 条记录")

            # 🔄 后台异步删除
            self.batch_delete_background([p['id'] for p in selected_purchases])

        except Exception as e:
            print(f"批量删除异常: {e}")
            QMessageBox.critical(self, "错误", f"批量删除失败: {str(e)}")

    def batch_delete_background(self, purchase_ids):
        """后台批量删除购买记录 - 使用Qt原生工作线程"""
        print(f"🔄 使用乐观更新批量删除购买记录: {purchase_ids}")

        # 对每个购买记录执行乐观删除
        for purchase_id in purchase_ids:
            def ui_update_func(entity_id):
                """UI立即更新函数"""
                # 从表格中移除记录
                for row in range(self.purchases_table.rowCount()):
                    if self.purchases_table.item(row, 0) and \
                       self.purchases_table.item(row, 0).text() == str(entity_id):
                        self.purchases_table.removeRow(row)
                        break

            def api_func(entity_id):
                """API调用函数"""
                return self.user_service.delete_user_purchase(self.user_id, entity_id)

            def rollback_func(entity_id, original_data):
                """回滚函数"""
                print(f"🔄 回滚购买记录删除: {entity_id}")
                # 重新加载购买记录
                self.refresh_purchase_records()

            # 使用乐观更新操作
            self.optimistic_delete(
                entity_id=purchase_id,
                entity_data={},
                ui_update_func=ui_update_func,
                api_func=api_func,
                rollback_func=rollback_func
            )

    def closeEvent(self, event):
        """窗口关闭事件，保存表格配置"""
        try:
            # 保存所有表格配置
            self.cleanup_table_configs()
            print("✅ 用户详情页面：已保存表格配置")
        except Exception as e:
            print(f"保存表格配置失败: {e}")

        # 调用父类的closeEvent
        super().closeEvent(event)

    def set_default_column_widths(self):
        """设置购买记录表格的默认列宽度"""
        try:
            # 定义每列的默认宽度
            # ["选择", "ID", "分类名称", "所属系列", "价格", "购买时间", "状态", "过期时间", "操作"]
            default_widths = [
                60,   # 选择复选框
                80,   # ID
                180,  # 分类名称
                150,  # 所属系列
                100,  # 价格
                140,  # 购买时间
                80,   # 状态
                120,  # 过期时间
                80    # 操作按钮
            ]

            header = self.purchases_table.horizontalHeader()

            # 设置每列的宽度
            for col, width in enumerate(default_widths):
                if col < self.purchases_table.columnCount():
                    header.resizeSection(col, width)

            # 设置最后一列自动拉伸
            header.setStretchLastSection(True)

            print(f"✅ 已设置购买记录表格默认列宽度: {default_widths}")

        except Exception as e:
            print(f"设置默认列宽度失败: {e}")











    def show_status_message(self, message: str, success: bool = True):
        """显示状态栏消息"""
        if success:
            # 成功消息：绿色
            self.status_bar.setStyleSheet("""
                QLabel {
                    background-color: #d4edda;
                    border: 1px solid #c3e6cb;
                    border-radius: 3px;
                    padding: 8px 12px;
                    color: #155724;
                    font-weight: bold;
                    margin-top: 5px;
                }
            """)
        else:
            # 警告消息：黄色
            self.status_bar.setStyleSheet("""
                QLabel {
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 3px;
                    padding: 8px 12px;
                    color: #856404;
                    font-weight: bold;
                    margin-top: 5px;
                }
            """)

        self.status_bar.setText(message)
        self.status_bar.setVisible(True)

        # 3秒后自动隐藏
        self.status_timer.start(3000)

    def hide_status_message(self):
        """隐藏状态栏消息"""
        self.status_bar.setVisible(False)

    # 购买记录标签页按钮功能
    def add_purchase_record(self):
        """添加购买记录"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton, QComboBox, QDoubleSpinBox, QLabel

        # 创建添加购买记录对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("添加购买记录")
        dialog.setModal(True)
        dialog.resize(500, 400)

        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # 获取系列和分类数据
        try:
            from cache.global_data_manager import global_data_manager
            series_data = global_data_manager._series_data if global_data_manager._data_loaded else []
            category_data = global_data_manager._category_data if global_data_manager._data_loaded else []
        except:
            series_data = []
            category_data = []

        # 系列选择框
        series_combo = QComboBox()
        series_combo.addItem("请选择系列", None)
        for series in series_data:
            series_combo.addItem(f"{series.get('title', '')} (¥{series.get('price', 0):.2f})", series)

        # 分类选择框
        category_combo = QComboBox()
        category_combo.addItem("购买整个系列", None)
        category_combo.setEnabled(False)

        # 价格显示
        price_label = QLabel("¥0.00")
        price_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")

        # 说明标签
        info_label = QLabel("💡 选择系列后可选择具体分类，不选分类则购买整个系列")
        info_label.setStyleSheet("color: #7f8c8d; font-size: 12px;")

        form_layout.addRow("选择系列:", series_combo)
        form_layout.addRow("选择分类:", category_combo)
        form_layout.addRow("价格:", price_label)
        form_layout.addRow("", info_label)

        layout.addLayout(form_layout)

        # 联动逻辑
        def on_series_changed():
            """系列选择改变时的处理"""
            selected_series = series_combo.currentData()
            if selected_series:
                # 启用分类选择并更新分类列表
                category_combo.setEnabled(True)
                update_categories()
                update_price()
            else:
                # 禁用分类选择
                category_combo.setEnabled(False)
                category_combo.clear()
                category_combo.addItem("请先选择系列", None)
                price_label.setText("¥0.00")

        def update_categories():
            """更新分类列表"""
            selected_series = series_combo.currentData()
            if not selected_series:
                return

            category_combo.clear()

            # 获取该系列下的分类
            series_id = selected_series.get('id') or selected_series.get('series_id')
            series_categories = [cat for cat in category_data if cat.get('series_id') == series_id]

            # 获取该系列下已购买的分类名称列表
            try:
                # 从表格中获取该系列下已购买的分类
                purchased_category_names_in_series = set()

                for row in range(self.purchases_table.rowCount()):
                    # 获取分类名称和所属系列
                    category_item = self.purchases_table.item(row, 2)  # 分类名称在第2列
                    series_item = self.purchases_table.item(row, 3)    # 所属系列在第3列

                    if category_item and series_item:
                        category_name = category_item.text()
                        series_name = series_item.text()

                        # 只统计当前选择系列下的已购买分类
                        if series_name == selected_series.get('title', ''):
                            purchased_category_names_in_series.add(category_name)

                print(f"🔍 系列'{selected_series.get('title', '')}'下已购买的分类: {purchased_category_names_in_series}")

            except Exception as e:
                print(f"获取已购买分类失败: {e}")
                purchased_category_names_in_series = set()

            # 检查是否所有分类都已购买（按分类名称筛选）
            unpurchased_categories = [cat for cat in series_categories
                                    if cat.get('title', '') not in purchased_category_names_in_series]

            print(f"🔍 系列下所有分类: {[cat.get('title') for cat in series_categories]}")
            print(f"🔍 未购买的分类: {[cat.get('title') for cat in unpurchased_categories]}")

            if unpurchased_categories:
                # 第一个选项：购买整个系列（仅显示未购买的分类）
                category_combo.addItem(f"购买整个系列 (剩余{len(unpurchased_categories)}个分类)", None)

                # 添加未购买的分类
                for category in unpurchased_categories:
                    category_combo.addItem(f"{category.get('title', '')} (¥{category.get('price', 0):.2f})", category)

                print(f"✅ 分类过滤完成: 显示 {len(unpurchased_categories)} 个未购买分类")
            else:
                # 所有分类都已购买
                category_combo.addItem("该系列所有分类已购买", None)
                category_combo.setEnabled(False)
                print("⚠️ 该系列所有分类都已购买")

        def update_price():
            """更新价格显示"""
            selected_category = category_combo.currentData()
            if selected_category:
                # 选择了具体分类
                price = selected_category.get('price', 0)
                price_label.setText(f"¥{price:.2f}")
            else:
                # 购买整个系列
                selected_series = series_combo.currentData()
                if selected_series:
                    price = selected_series.get('price', 0)
                    price_label.setText(f"¥{price:.2f}")
                else:
                    price_label.setText("¥0.00")

        # 连接信号
        series_combo.currentIndexChanged.connect(on_series_changed)
        category_combo.currentIndexChanged.connect(update_price)

        # 按钮
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存")
        cancel_btn = QPushButton("❌ 取消")

        save_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        cancel_btn.setStyleSheet("QPushButton { background-color: #95a5a6; }")

        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        def save_purchase():
            """保存购买记录"""
            selected_series = series_combo.currentData()
            selected_category = category_combo.currentData()

            if not selected_series:
                QMessageBox.warning(dialog, "错误", "请选择系列！")
                return

            # 确定购买的是系列还是分类
            if selected_category:
                # 购买具体分类
                purchased_entity_type = "category"
                selected_item = selected_category
                item_name = selected_category.get('title', '')
            else:
                # 购买整个系列
                purchased_entity_type = "series"
                selected_item = selected_series
                item_name = selected_series.get('title', '')

            purchased_entity_id = selected_item.get('id') or selected_item.get('series_id')
            amount = selected_item.get('price', 0)

            # 立即显示保存中状态 - 在任何操作之前
            save_btn.setText("💾 保存中...")
            save_btn.setEnabled(False)
            cancel_btn.setEnabled(False)

            # 强制刷新界面
            dialog.repaint()

            try:
                # 先检查是否已经购买过
                try:
                    existing_purchases = self.user_service.get_user_purchases(self.user_id)
                    # 确保existing_purchases是列表格式
                    if isinstance(existing_purchases, dict) and 'data' in existing_purchases:
                        purchase_list = existing_purchases['data']
                    elif isinstance(existing_purchases, list):
                        purchase_list = existing_purchases
                    else:
                        purchase_list = []

                    for purchase in purchase_list:
                        if (purchase.get('type') == purchased_entity_type and
                            purchase.get('purchased_entity_id') == purchased_entity_id):
                            QMessageBox.warning(dialog, "重复购买", f"您已经购买过 {item_name}，无需重复购买！")
                            return
                except Exception as e:
                    print(f"检查重复购买时出错: {e}")
                    # 如果检查失败，继续保存流程

                # 构建购买记录数据
                purchase_data = {
                    'type': purchased_entity_type,
                    'purchased_entity_id': purchased_entity_id,
                    'item_name': item_name,
                    'amount': amount,
                    'status': 'is_active'
                }

                print(f"开始保存购买记录: {purchase_data}")

                # 调用服务保存购买记录
                result = self.user_service.add_user_purchase(self.user_id, purchase_data)

                print(f"保存结果: {result}")

                if result.get('success'):
                    # 保存成功，刷新购买记录显示
                    self.refresh_purchase_records()
                    self.show_status_message(f"➕ 购买记录添加成功：{item_name} (¥{amount:.2f})")
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "错误", f"添加购买记录失败: {result.get('message', '未知错误')}")

            except Exception as e:
                print(f"保存购买记录异常: {e}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(dialog, "错误", f"保存失败: {str(e)}")
            finally:
                # 恢复按钮状态
                save_btn.setText("💾 保存")
                save_btn.setEnabled(True)
                cancel_btn.setEnabled(True)

        def save_purchase():
            """保存购买记录 - 乐观更新版本"""
            selected_series = series_combo.currentData()
            selected_category = category_combo.currentData()

            if not selected_series:
                QMessageBox.warning(dialog, "错误", "请选择系列！")
                return

            # 确定购买的是系列还是分类
            if selected_category:
                purchased_entity_type = "category"
                selected_item = selected_category
                item_name = selected_category.get('title', '')
            else:
                purchased_entity_type = "series"
                selected_item = selected_series
                item_name = selected_series.get('title', '')

            purchased_entity_id = selected_item.get('id') or selected_item.get('series_id')
            amount = selected_item.get('price', 0)

            try:
                # 先检查是否已经购买过
                existing_purchases = self.user_service.get_user_purchases(self.user_id)
                if isinstance(existing_purchases, dict) and 'data' in existing_purchases:
                    purchase_list = existing_purchases['data']
                elif isinstance(existing_purchases, list):
                    purchase_list = existing_purchases
                else:
                    purchase_list = []

                for purchase in purchase_list:
                    if (purchase.get('type') == purchased_entity_type and
                        purchase.get('purchased_entity_id') == purchased_entity_id):
                        QMessageBox.warning(dialog, "重复购买", f"您已经购买过 {item_name}，无需重复购买！")
                        return

                # 🚀 乐观更新：立即关闭对话框并更新界面
                dialog.accept()

                if selected_category:
                    # 购买单个分类
                    optimistic_purchase = {
                        'id': f'temp_{int(time.time())}',
                        'category_name': item_name,
                        'series_name': selected_series.get('title', ''),
                        'amount': amount,
                        'status': '已购买',
                        'purchase_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    # 立即添加到购买记录表格
                    self.add_purchase_to_table(optimistic_purchase)

                    # 显示成功提示
                    self.show_status_message(f"➕ 购买记录添加成功：{item_name} (¥{amount:.2f})")
                else:
                    # 购买整个系列 - 需要展开为多个分类
                    series_id = selected_series.get('id') or selected_series.get('series_id')
                    series_title = selected_series.get('title', '')

                    # 获取该系列下的分类
                    try:
                        from cache.global_data_manager import global_data_manager
                        category_data = global_data_manager._category_data if global_data_manager._data_loaded else []
                        series_categories = [cat for cat in category_data if cat.get('series_id') == series_id]

                        # 为每个分类创建乐观更新记录
                        for i, category in enumerate(series_categories):
                            optimistic_purchase = {
                                'id': f'temp_{int(time.time())}_{i}',
                                'category_name': category.get('title', ''),
                                'series_name': series_title,
                                'amount': category.get('price', amount),
                                'status': '已购买',
                                'purchase_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            }

                            # 立即添加到购买记录表格（批量添加时跳过刷新）
                            self.add_purchase_to_table(optimistic_purchase, skip_refresh=True)

                        # 显示成功提示
                        self.show_status_message(f"➕ 系列购买成功：{series_title} ({len(series_categories)}个分类)")
                        
                        # 批量添加完成后，统一刷新一次相关表格
                        self.refresh_related_tables()

                    except Exception as e:
                        print(f"获取系列分类失败: {e}")
                        # 如果获取分类失败，仍然显示系列购买成功
                        self.show_status_message(f"➕ 购买记录添加成功：{series_title} (¥{amount:.2f})")

                # 🔄 后台异步保存到服务器
                self.save_purchase_background(purchased_entity_type, purchased_entity_id, item_name, amount)

            except Exception as e:
                print(f"检查重复购买时出错: {e}")
                QMessageBox.critical(dialog, "错误", f"操作失败: {str(e)}")



        save_btn.clicked.connect(save_purchase)
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec()



    # 学习进度标签页按钮功能
    def reset_progress_record(self):
        """重置选中的学习进度"""
        # 获取选中的行
        selected_rows = []
        for item in self.progress_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要重置的学习进度", False)
            return

        reply = QMessageBox.question(
            self,
            '确认重置',
            f'确定要重置选中的 {len(selected_rows)} 条学习进度吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加重置学习进度的逻辑
            self.show_status_message("🔄 学习进度重置功能正在开发中")

    def mark_progress_completed(self):
        """标记选中的学习进度为完成"""
        # 获取选中的行
        selected_rows = []
        for item in self.progress_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要标记完成的学习进度", False)
            return

        reply = QMessageBox.question(
            self,
            '确认标记完成',
            f'确定要将选中的 {len(selected_rows)} 条学习进度标记为完成吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加标记完成的逻辑
            self.show_status_message("✅ 学习进度标记完成功能正在开发中")

    # 收藏管理标签页按钮功能
    def delete_favorite_record(self):
        """删除选中的收藏记录"""
        # 获取选中的行
        selected_rows = []
        for item in self.favorites_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要删除的收藏记录", False)
            return

        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除选中的 {len(selected_rows)} 条收藏记录吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加删除收藏记录的逻辑
            self.show_status_message("🗑️ 收藏记录删除功能正在开发中")

    # 设备缓存标签页按钮功能
    def clear_selected_cache(self):
        """清除选中的缓存"""
        # 获取选中的行
        selected_rows = []
        for item in self.cache_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要清除的缓存", False)
            return

        reply = QMessageBox.question(
            self,
            '确认清除',
            f'确定要清除选中的 {len(selected_rows)} 个缓存吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加清除选中缓存的逻辑
            self.show_status_message("🗑️ 缓存清除功能正在开发中")

    def clear_all_cache(self):
        """清除所有缓存"""
        reply = QMessageBox.question(
            self,
            '确认清除所有缓存',
            f'确定要清除用户 {self.user_id} 的所有缓存吗？\n\n⚠️ 此操作将清除所有视频缓存文件！',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加清除所有缓存的逻辑
            self.show_status_message("🧹 清除所有缓存功能正在开发中")

    # 用户设置标签页按钮功能
    def add_user_setting(self):
        """添加用户设置"""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton, QTextEdit

        # 创建添加设置对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("添加用户设置")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # 输入字段
        key_edit = QLineEdit()
        value_edit = QTextEdit()
        value_edit.setMaximumHeight(100)

        form_layout.addRow("设置键:", key_edit)
        form_layout.addRow("设置值:", value_edit)

        layout.addLayout(form_layout)

        # 按钮
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存")
        cancel_btn = QPushButton("❌ 取消")

        save_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        cancel_btn.setStyleSheet("QPushButton { background-color: #95a5a6; }")

        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        def save_setting():
            key = key_edit.text().strip()
            value = value_edit.toPlainText().strip()

            if not key:
                QMessageBox.warning(dialog, "错误", "设置键不能为空！")
                return

            # 这里可以添加保存用户设置的逻辑
            self.show_status_message("➕ 用户设置添加功能正在开发中")
            dialog.accept()

        save_btn.clicked.connect(save_setting)
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec()

    def edit_user_setting(self):
        """编辑用户设置"""
        # 获取选中的行
        selected_rows = []
        for item in self.settings_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要编辑的设置", False)
            return

        if len(selected_rows) > 1:
            self.show_status_message("⚠️ 请只选择一个设置进行编辑", False)
            return

        row = selected_rows[0]
        current_key = self.settings_table.item(row, 0).text()
        current_value = self.settings_table.item(row, 1).text()

        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLineEdit, QPushButton, QTextEdit

        # 创建编辑设置对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("编辑用户设置")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)
        form_layout = QFormLayout()

        # 输入字段
        key_edit = QLineEdit(current_key)
        value_edit = QTextEdit()
        value_edit.setPlainText(current_value)
        value_edit.setMaximumHeight(100)

        form_layout.addRow("设置键:", key_edit)
        form_layout.addRow("设置值:", value_edit)

        layout.addLayout(form_layout)

        # 按钮
        btn_layout = QHBoxLayout()
        save_btn = QPushButton("💾 保存")
        cancel_btn = QPushButton("❌ 取消")

        save_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        cancel_btn.setStyleSheet("QPushButton { background-color: #95a5a6; }")

        btn_layout.addWidget(save_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        def save_setting():
            key = key_edit.text().strip()
            value = value_edit.toPlainText().strip()

            if not key:
                QMessageBox.warning(dialog, "错误", "设置键不能为空！")
                return

            # 这里可以添加保存用户设置的逻辑
            self.show_status_message("✏️ 用户设置编辑功能正在开发中")
            dialog.accept()

        save_btn.clicked.connect(save_setting)
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec()

    def delete_user_setting(self):
        """删除用户设置"""
        # 获取选中的行
        selected_rows = []
        for item in self.settings_table.selectedItems():
            row = item.row()
            if row not in selected_rows:
                selected_rows.append(row)

        if not selected_rows:
            self.show_status_message("⚠️ 请先选择要删除的设置", False)
            return

        reply = QMessageBox.question(
            self,
            '确认删除',
            f'确定要删除选中的 {len(selected_rows)} 个用户设置吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 这里可以添加删除用户设置的逻辑
            self.show_status_message("🗑️ 用户设置删除功能正在开发中")
