---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程

**核心原则**：
- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🛑 强制执行机制

### 阶段门禁条件（决策节点控制）
每个决策节点都有明确的客观判断标准：

```markdown
NODE-1 → NODE-2: 必须完成多维度信息搜索 + 识别核心症状 + TodoWrite标记completed
NODE-2 → NODE-3: 必须满足循环退出条件 + 假设验证充分性确认 + TodoWrite完成验证记录  
NODE-3 → NODE-4: 必须验证通过率100% + 无副作用确认 + TodoWrite记录修复内容
NODE-4完成: 必须总结报告完整 + 优化建议具体 + TodoWrite标记全部completed
```

### 违规定义和后果
```markdown
🚨 严重违规（必须重新执行）：
- 跳过循环机制：NODE-2、3未执行循环→返回重新执行完整节点
- 凭想象猜测：未提供真实验证证据→必须补充验证步骤
- 提前退出节点：未满足门禁条件→必须补充完成所有必需步骤
- 简化实现逃避：临时方案、伪代码→必须提供完整真实修复
- 分离假设-验证：违反完整循环原则→必须重新执行包含假设调整的完整循环

⚠️ 轻微违规（警告并纠正）：
- TodoWrite更新不及时：立即补充更新
- 进度记录不准确：重新统计并记录
- 循环状态记录不完整：补充详细的轮次和步骤状态
```

## 🗺️ 完整任务图

```mermaid
graph TD
    START([开始诊断]) --> N1[NODE-1: 信息收集与真实检查]
    N1 --> D1{信息收集充分?<br/>≥5个文件 + 症状识别 + TodoWrite更新}
    D1 -->|否| R1[扩大搜索范围<br/>增加搜索工具和关键词]
    R1 --> N1
    D1 -->|是| N2[NODE-2: 假设-验证循环]
    
    N2 --> L2{当前轮次检查<br/>round < 2?}
    L2 -->|是| E2[执行假设-验证轮次<br/>3步完整循环]
    E2 --> I2[轮次计数+1]
    I2 --> L2
    L2 -->|否| D2{循环退出条件?<br/>找到根因 + 连续2轮无新发现}
    D2 -->|否且<5轮| E2
    D2 -->|是或≥5轮| N3[NODE-3: 修复实施与验证]
    
    N3 --> L3{当前尝试检查<br/>attempt < 1?}
    L3 -->|是| E3[执行修复尝试<br/>4步完整循环]
    E3 --> I3[尝试计数+1]
    I3 --> L3
    L3 -->|否| D3{修复成功条件?<br/>100%验证通过 + 无副作用}
    D3 -->|否且<5次| E3
    D3 -->|是或≥5次| N4[NODE-4: 全面验证与总结]
    
    N4 --> D4{验证完成条件?<br/>全部测试通过 + 原问题解决}
    D4 -->|否| F4[补充验证测试]
    F4 --> N4
    D4 -->|是| N5[NODE-5: 总结与发散优化]
    N5 --> END([完成])
    
    style N1 fill:#e1f5fe
    style N2 fill:#f3e5f5
    style N3 fill:#fff3e0
    style N4 fill:#e8f5e8
    style N5 fill:#fce4ec
    style D1 fill:#fff9c4
    style D2 fill:#fff9c4
    style D3 fill:#fff9c4
    style D4 fill:#fff9c4
```

## 🎯 任务节点定义

## 🎯 任务节点详细定义

### 📊 **NODE-1: 信息收集与真实检查**

**节点描述：** 对应阶段1，完成多维度信息搜索和核心症状识别

**输入条件：** $ARGUMENTS

**执行内容：**
```python
def execute_node1_information_collection(problem_description: str) -> Node1Result:
    """NODE-1执行内容 - 对应阶段1完整实现"""
    
    print("🔍 执行NODE-1: 信息收集与真实检查")
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_node("NODE-1")
    
    # 任务1.1: 问题信息解析
    print("执行任务1.1: 问题信息解析...")
    problem_analysis = parse_problem_systematically(problem_description)
    print(f"  提取核心症状：{problem_analysis.symptoms_count}个")
    print(f"  识别功能模块：{problem_analysis.modules_count}个")
    print(f"  分析影响范围：{problem_analysis.impact_scope}")
    todo_tracker.complete_task("问题信息解析")
    
    # 任务1.2: 多维度搜索策略制定
    print("执行任务1.2: 多维度搜索策略制定...")
    search_strategy = create_comprehensive_search_strategy(problem_analysis)
    print(f"  文件名模式：{len(search_strategy.file_patterns)}个")
    print(f"  内容关键词：{len(search_strategy.content_keywords)}个")
    print(f"  跨文件引用：{len(search_strategy.reference_patterns)}个")
    todo_tracker.complete_task("搜索策略制定")
    
    # 任务1.3: 强制真实检查 - 多工具组合搜索
    print("执行任务1.3: 强制真实检查...")
    search_results = SearchResults()
    
    # 1.3.1 Glob工具搜索文件名模式
    print("  执行Glob搜索...")
    for i, pattern in enumerate(search_strategy.file_patterns, 1):
        files = glob_search(f"**/*{pattern}*")
        search_results.add_glob_result(f"模式{i}", pattern, files)
        print(f"    模式{i} '{pattern}': {len(files)}个文件")
    
    # 1.3.2 Grep工具搜索内容模式
    print("  执行Grep搜索...")
    for i, keyword in enumerate(search_strategy.content_keywords, 1):
        matches = grep_search(keyword, include="*.py,*.js,*.json")
        search_results.add_grep_result(f"关键词{i}", keyword, matches)
        print(f"    关键词{i} '{keyword}': {len(matches)}处匹配")
    
    # 1.3.3 错误模式搜索
    print("  执行错误模式搜索...")
    error_patterns = ["Error", "Exception", "异常", "错误", "failed", "失败"]
    for pattern in error_patterns:
        error_matches = grep_search(pattern, include="*.py,*.log")
        if error_matches:
            search_results.add_error_result(pattern, error_matches)
            print(f"    错误模式 '{pattern}': {len(error_matches)}处匹配")
    
    # 1.3.4 Task工具复杂搜索（如需要）
    if search_results.total_files < 5:
        print("  启用Task工具进行扩展搜索...")
        task_results = task_search_extended_patterns(problem_analysis)
        search_results.merge_task_results(task_results)
        print(f"    Task扩展搜索: {task_results.additional_files}个文件")
    
    todo_tracker.complete_task("多工具组合搜索")
    
    # 任务1.4: 搜索完整性验证
    print("执行任务1.4: 搜索完整性验证...")
    verification_result = verify_search_completeness(search_results, search_strategy)
    print(f"  交叉验证一致性：{verification_result.consistency_score*100:.1f}%")
    print(f"  边界扩展发现：{verification_result.boundary_findings}个")
    print(f"  遗漏检测结果：{verification_result.coverage_gaps}个缺口")
    todo_tracker.complete_task("搜索完整性验证")
    
    # 任务1.5: 系统状态检查
    print("执行任务1.5: 系统状态检查...")
    service_health = check_service_with_curl()
    print(f"  服务状态检查：{service_health.status_code} ({service_health.description})")
    
    environment_check = verify_wsl_environment()
    print(f"  WSL环境检查：{environment_check.summary}")
    todo_tracker.complete_task("系统状态检查")
    
    # 计算总体结果
    total_files = search_results.total_unique_files
    symptoms_identified = problem_analysis.symptoms_count >= 1
    
    print(f"\n✅ NODE-1执行完成")
    print(f"总计发现文件：{total_files}个")
    print(f"核心症状识别：{'完成' if symptoms_identified else '未完成'}")
    print(f"服务状态：{service_health.description}")
    
    # 完成NODE-1
    todo_tracker.complete_node("NODE-1")
    
    return Node1Result(
        problem_analysis=problem_analysis,
        search_strategy=search_strategy,
        search_results=search_results,
        verification_result=verification_result,
        service_health=service_health,
        environment_check=environment_check,
        total_files_found=total_files,
        symptoms_identified=symptoms_identified,
        todo_tracker=todo_tracker
    )

def parse_problem_systematically(description: str) -> ProblemAnalysis:
    """系统性问题解析"""
    analysis = ProblemAnalysis()
    
    # 提取症状模式
    symptom_indicators = ["显示", "异常", "错误", "失败", "不正常", "无法", "缺失", "空白", "不匹配"]
    for indicator in symptom_indicators:
        if indicator in description:
            analysis.add_symptom(indicator)
    
    # 识别功能模块
    module_keywords = ["用户", "管理", "界面", "数据", "API", "数据库", "购买", "记录", "分类", "登录"]
    for keyword in module_keywords:
        if keyword in description:
            analysis.add_module(keyword)
    
    # 分析影响范围和严重程度
    if any(word in description for word in ["全部", "所有", "整个"]):
        analysis.impact_scope = "GLOBAL"
        analysis.severity = "HIGH"
    elif any(word in description for word in ["部分", "某些", "个别"]):
        analysis.impact_scope = "PARTIAL"
        analysis.severity = "MEDIUM"
    else:
        analysis.impact_scope = "LOCALIZED"
        analysis.severity = "LOW"
    
    # 确定问题触发条件
    trigger_patterns = ["点击", "打开", "加载", "提交", "保存", "查询"]
    for pattern in trigger_patterns:
        if pattern in description:
            analysis.add_trigger_condition(pattern)
    
    return analysis

def create_comprehensive_search_strategy(problem_analysis: ProblemAnalysis) -> SearchStrategy:
    """创建全面的搜索策略"""
    strategy = SearchStrategy()
    
    # 基于症状生成文件名模式
    for symptom in problem_analysis.symptoms:
        if "显示" in symptom or "界面" in symptom:
            strategy.add_file_pattern("view")
            strategy.add_file_pattern("component")
            strategy.add_file_pattern("ui")
        elif "数据" in symptom:
            strategy.add_file_pattern("model")
            strategy.add_file_pattern("data")
            strategy.add_file_pattern("entity")
        elif "API" in symptom or "接口" in symptom:
            strategy.add_file_pattern("api")
            strategy.add_file_pattern("router")
            strategy.add_file_pattern("endpoint")
    
    # 基于模块生成内容关键词
    for module in problem_analysis.modules:
        strategy.add_content_keyword(module)
        # 添加相关的英文术语
        if module == "用户":
            strategy.add_content_keyword("user")
            strategy.add_content_keyword("account")
        elif module == "购买":
            strategy.add_content_keyword("purchase")
            strategy.add_content_keyword("buy")
        elif module == "分类":
            strategy.add_content_keyword("category")
            strategy.add_content_keyword("classification")
    
    # 添加跨文件引用模式
    strategy.add_reference_pattern("import.*from")
    strategy.add_reference_pattern("require\s*\(")
    strategy.add_reference_pattern("@.*route")
    
    # 添加配置文件模式
    strategy.add_file_pattern("config")
    strategy.add_file_pattern("setting")
    strategy.add_file_pattern("env")
    
    return strategy

def check_service_with_curl() -> ServiceHealth:
    """使用curl检查服务状态"""
    try:
        # 执行curl命令检查服务
        result = bash_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs')
        status_code = int(result.stdout.strip())
        
        if status_code == 200:
            return ServiceHealth(status_code, "healthy", "服务正常运行")
        elif status_code == 000:
            return ServiceHealth(status_code, "unavailable", "服务不可访问，需要启动mock_server")
        else:
            return ServiceHealth(status_code, "error", f"服务异常，状态码{status_code}")
    except Exception as e:
        return ServiceHealth(0, "unknown", f"服务检查失败：{str(e)}")
```

**决策条件：**
```python
def decide_node1_to_node2(result: Node1Result) -> bool:
    """判断NODE-1是否可以转换到NODE-2"""
    conditions = {
        "文件数量充分": result.total_files_found >= 5,
        "症状识别完成": result.symptoms_identified,
        "搜索工具完整": len(result.search_results.get_all_tools()) >= 3,
        "服务状态明确": result.service_health.status_code != 0,
        "TodoWrite更新": result.todo_tracker.is_node_completed("NODE-1")
    }
    
    print("🤔 NODE-1→NODE-2决策检查:")
    for condition, passed in conditions.items():
        print(f"  {condition}: {'✅' if passed else '❌'}")
    
    can_proceed = all(conditions.values())
    print(f"决策结果: {'可以继续' if can_proceed else '需要重新执行'}")
    
    return can_proceed
```
### 🔬 **NODE-2: 假设-验证循环**

**节点描述：** 对应阶段2，执行2-5轮假设-验证循环（每轮3步）

**输入条件：** NODE-1结果，包含问题分析和搜索结果

**执行内容：**
```python
def execute_node2_hypothesis_verification(node1_result: Node1Result) -> List[Node2RoundResult]:
    """NODE-2执行内容 - 对应阶段2完整实现（2-5轮循环）"""
    
    print("🔬 执行NODE-2: 假设-验证循环")
    
    round_results = []
    round_num = 1
    max_rounds = 5
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_node("NODE-2")
    
    while round_num <= max_rounds:
        print(f"\n--- 第{round_num}轮假设-验证循环 ---")
        
        # 步骤1: 假设建立
        print(f"执行步骤1: 建立第{round_num}轮假设...")
        if round_num == 1:
            hypotheses = build_initial_hypotheses(node1_result.problem_analysis)
        else:
            previous_round = round_results[-1]
            hypotheses = refine_hypotheses(previous_round, node1_result)
        
        print(f"  建立假设：{len(hypotheses)}个")
        for i, hyp in enumerate(hypotheses, 1):
            print(f"    假设{i}: {hyp.description} (优先级: {hyp.priority})")
        
        # 步骤2: 假设验证（与步骤1不分离，完整循环）
        print(f"执行步骤2: 验证第{round_num}轮假设...")
        verification_results = []
        confirmed_issues = 0
        new_findings = 0
        
        for i, hypothesis in enumerate(hypotheses, 1):
            print(f"  验证假设{i}: {hypothesis.description}")
            
            # 真实验证（禁止凭想象猜测）
            verification = execute_real_hypothesis_verification(hypothesis, node1_result)
            verification_results.append(verification)
            
            if verification.confirmed:
                confirmed_issues += 1
                print(f"    ✅ 确认问题: {verification.evidence[:50]}...")
            else:
                print(f"    ❌ 假设被排除")
            
            # 检查新发现
            if verification.new_information:
                new_findings += 1
                print(f"    🔍 新发现: {verification.new_information[:30]}...")
        
        # 步骤3: 假设调整（在同轮内完成）
        print(f"执行步骤3: 调整第{round_num}轮假设...")
        if confirmed_issues > 0:
            root_cause_analysis = analyze_root_cause(verification_results, node1_result)
            adjustment_made = adjust_hypotheses_in_round(hypotheses, verification_results)
        else:
            root_cause_analysis = None
            adjustment_made = prepare_next_round_strategy(verification_results)
        
        print(f"  根因分析: {root_cause_analysis.summary if root_cause_analysis else '未确定'}")
        print(f"  假设调整: {'已调整' if adjustment_made else '无需调整'}")
        
        # 记录本轮结果
        round_result = Node2RoundResult(
            round_number=round_num,
            hypotheses=hypotheses,
            verification_results=verification_results,
            confirmed_issues=confirmed_issues,
            new_findings=new_findings,
            root_cause_analysis=root_cause_analysis,
            adjustment_made=adjustment_made
        )
        round_results.append(round_result)
        
        # 更新TodoWrite
        todo_tracker.complete_round(f"假设-验证循环第{round_num}轮", confirmed_issues, new_findings)
        
        print(f"  第{round_num}轮统计: 确认问题{confirmed_issues}个，新发现{new_findings}个")
        
        # 强制最少2轮检查
        if round_num < 2:
            print(f"  继续执行（要求最少2轮）")
            round_num += 1
            continue
        
        # 循环退出条件检查（2轮后）
        if should_exit_hypothesis_loop(round_results):
            print(f"  循环退出条件满足，结束假设-验证（共{round_num}轮）")
            break
        
        # 达到最大轮次
        if round_num >= max_rounds:
            print(f"  达到最大轮次限制，结束假设-验证")
            break
        
        round_num += 1
    
    # 完成NODE-2
    todo_tracker.complete_node("NODE-2")
    
    print(f"\n✅ NODE-2执行完成，共执行{len(round_results)}轮")
    total_confirmed = sum(r.confirmed_issues for r in round_results)
    print(f"总确认问题：{total_confirmed}个")
    
    return round_results

def build_initial_hypotheses(problem_analysis: ProblemAnalysis) -> List[Hypothesis]:
    """建立第1轮系统性假设"""
    hypotheses = []
    
    # 基础技术层假设（必备）
    hypotheses.extend([
        Hypothesis(
            id="H001",
            description="代码语法错误或导入问题",
            category="SYNTAX",
            verify_method="syntax_check",
            priority="HIGH"
        ),
        Hypothesis(
            id="H002",
            description="配置文件或环境变量错误",
            category="CONFIG",
            verify_method="config_check",
            priority="HIGH"
        )
    ])
    
    # 基于问题分析添加特定假设
    if any("显示" in symptom or "界面" in symptom for symptom in problem_analysis.symptoms):
        hypotheses.append(
            Hypothesis(
                id="H003",
                description="UI数据绑定或渲染问题",
                category="UI_LOGIC",
                verify_method="ui_data_check",
                priority="HIGH"
            )
        )
    
    if any("数据" in module for module in problem_analysis.modules):
        hypotheses.extend([
            Hypothesis(
                id="H004",
                description="数据库字段映射错误",
                category="DATABASE",
                verify_method="field_mapping_check",
                priority="HIGH"
            ),
            Hypothesis(
                id="H005",
                description="数据关联或外键问题",
                category="DATA_RELATION",
                verify_method="relation_check",
                priority="MEDIUM"
            )
        ])
    
    if problem_analysis.severity == "HIGH":
        hypotheses.append(
            Hypothesis(
                id="H006",
                description="架构设计或集成问题",
                category="ARCHITECTURE",
                verify_method="architecture_check",
                priority="MEDIUM"
            )
        )
    
    return hypotheses

def should_exit_hypothesis_loop(results: List[Node2RoundResult]) -> bool:
    """判断是否应该退出假设-验证循环（退出条件）"""
    
    if len(results) < 2:
        return False  # 强制最少2轮
    
    last_result = results[-1]
    second_last_result = results[-2]
    
    # 退出条件1: 找到根本问题且连续2轮无新发现
    has_root_cause = last_result.root_cause_analysis is not None
    last_no_findings = last_result.new_findings == 0
    second_last_no_findings = second_last_result.new_findings == 0
    
    condition1 = has_root_cause and last_no_findings and second_last_no_findings
    
    # 退出条件2: 确认问题数量稳定（连续2轮确认问题数相同且>0）
    confirmed_stable = (
        last_result.confirmed_issues > 0 and
        last_result.confirmed_issues == second_last_result.confirmed_issues
    )
    
    should_exit = condition1 or confirmed_stable
    
    if should_exit:
        exit_reason = "根本问题确定且连续无新发现" if condition1 else "确认问题数量稳定"
        print(f"    退出条件满足：{exit_reason}")
    
    return should_exit
```

**决策条件：**
```python
def decide_node2_to_node3(results: List[Node2RoundResult]) -> bool:
    """判断NODE-2是否可以转换到NODE-3"""
    
    # 检查循环执行要求
    min_rounds_executed = len(results) >= 2  # 要求：最少2轮
    max_rounds_respected = len(results) <= 5  # 要求：最多5轮
    
    # 检查每轮3步完整性
    all_rounds_complete = all(
        result.hypotheses and  # 步骤1：假设建立
        result.verification_results and  # 步骤2：假设验证
        hasattr(result, 'adjustment_made')  # 步骤3：假设调整
        for result in results
    )
    
    # 检查假设验证充分性
    total_hypotheses = sum(len(r.hypotheses) for r in results)
    verified_hypotheses = sum(len(r.verification_results) for r in results)
    verification_completeness = verified_hypotheses == total_hypotheses
    
    # 检查根本问题确认
    root_cause_identified = any(r.root_cause_analysis for r in results)
    
    # 检查循环退出条件满足
    exit_conditions_met = should_exit_hypothesis_loop(results)
    
    # 检查TodoWrite验证记录
    todo_verification_recorded = True  # 简化检查
    
    conditions = {
        "最少轮次执行": min_rounds_executed,
        "最多轮次遵守": max_rounds_respected,
        "每轮步骤完整": all_rounds_complete,
        "验证充分性确认": verification_completeness,
        "根本问题确认": root_cause_identified,
        "退出条件满足": exit_conditions_met,
        "TodoWrite记录": todo_verification_recorded
    }
    
    print("🤔 NODE-2→NODE-3决策检查:")
    for condition, passed in conditions.items():
        print(f"  {condition}: {'✅' if passed else '❌'}")
        
        # 详细说明关键指标
        if condition == "最少轮次执行":
            print(f"      执行轮次: {len(results)}/2")
        elif condition == "验证充分性确认":
            print(f"      假设总数: {total_hypotheses}, 验证总数: {verified_hypotheses}")
    
    can_proceed = all(conditions.values())
    print(f"决策结果: {'可以继续' if can_proceed else '需要重新执行'}")
    
    return can_proceed
```
```

### 🔧 **NODE-3: 修复实施与验证**

**节点描述：** 对应阶段3，执行1-5轮修复实施与验证循环（每轮4步）

**输入条件：** NODE-2结果，包含根因分析和确认问题

**执行内容：**
```python
def execute_node3_repair_and_verification(node2_results: List[Node2RoundResult]) -> List[Node3AttemptResult]:
    """NODE-3执行内容 - 对应阶段3完整实现（1-5轮循环）"""
    
    print("🔧 执行NODE-3: 修复实施与验证")
    
    attempt_results = []
    attempt_num = 1
    max_attempts = 5
    
    # 提取根因列表
    root_causes = [r.root_cause_analysis for r in node2_results if r.root_cause_analysis]
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_node("NODE-3")
    
    while attempt_num <= max_attempts:
        print(f"\n--- 第{attempt_num}轮修复实施与验证 ---")
        
        # 步骤1: 修复方案设计
        print(f"执行步骤1: 设计第{attempt_num}轮修复方案...")
        if attempt_num == 1:
            fix_plans = design_comprehensive_fix_plans(root_causes)
        else:
            previous_failures = attempt_results[-1].failed_implementations
            fix_plans = redesign_fix_plans(previous_failures, root_causes)
        
        print(f"  设计修复方案：{len(fix_plans)}个")
        for i, plan in enumerate(fix_plans, 1):
            print(f"    方案{i}: {plan.description} (类型: {plan.type})")
        
        # 步骤2: 修复实施（完整真实修复，禁止简化逃避）
        print(f"执行步骤2: 实施第{attempt_num}轮修复...")
        implementations = []
        
        for i, plan in enumerate(fix_plans, 1):
            print(f"  执行修复{i}: {plan.description}")
            
            try:
                implementation = implement_complete_fix(plan)
                implementations.append(implementation)
                
                if implementation.success:
                    print(f"    ✅ 修复成功: {len(implementation.edit_results)}个文件修改")
                else:
                    print(f"    ❌ 修复失败: {implementation.error_message}")
                    
            except Exception as e:
                failed_impl = FailedImplementation(plan, str(e))
                implementations.append(failed_impl)
                print(f"    ❌ 修复异常: {str(e)}")
        
        # 步骤3: 修复验证（每个修复立即验证）
        print(f"执行步骤3: 验证第{attempt_num}轮修复...")
        verification_results = []
        
        for impl in implementations:
            if impl.success:
                print(f"  验证修复: {impl.plan.description}")
                verification = execute_immediate_fix_verification(impl)
                verification_results.append(verification)
                
                if verification.passed:
                    print(f"    ✅ 验证通过: {verification.test_summary}")
                else:
                    print(f"    ❌ 验证失败: {verification.failure_reason}")
        
        # 步骤4: 副作用检查
        print(f"执行步骤4: 检查第{attempt_num}轮副作用...")
        side_effects_check = check_comprehensive_side_effects(implementations)
        
        if side_effects_check.clean:
            print(f"  ✅ 无副作用检测")
        else:
            print(f"  ❌ 发现{side_effects_check.issue_count}个副作用")
            for issue in side_effects_check.issues:
                print(f"    - {issue.type}: {issue.description}")
        
        # 计算本轮成功率
        implementation_success_rate = calculate_implementation_success_rate(implementations)
        verification_pass_rate = calculate_verification_pass_rate(verification_results)
        
        # 记录本轮结果
        attempt_result = Node3AttemptResult(
            attempt_number=attempt_num,
            fix_plans=fix_plans,
            implementations=implementations,
            verification_results=verification_results,
            side_effects_check=side_effects_check,
            implementation_success_rate=implementation_success_rate,
            verification_pass_rate=verification_pass_rate
        )
        attempt_results.append(attempt_result)
        
        # 更新TodoWrite
        todo_tracker.complete_attempt(f"修复实施第{attempt_num}轮", implementation_success_rate, verification_pass_rate)
        
        print(f"  第{attempt_num}轮统计: 实施成功率{implementation_success_rate*100:.1f}%, 验证通过率{verification_pass_rate*100:.1f}%")
        
        # 检查是否完成所有修复
        if (implementation_success_rate == 1.0 and 
            verification_pass_rate == 1.0 and 
            side_effects_check.clean):
            print(f"  所有修复完成且验证通过，结束修复（共{attempt_num}轮）")
            break
        
        # 达到最大尝试次数
        if attempt_num >= max_attempts:
            print(f"  达到最大尝试次数，结束修复")
            break
        
        attempt_num += 1
    
    # 完成NODE-3
    todo_tracker.complete_node("NODE-3")
    
    print(f"\n✅ NODE-3执行完成，共执行{len(attempt_results)}轮")
    latest_result = attempt_results[-1]
    print(f"最终成功率：实施{latest_result.implementation_success_rate*100:.1f}%, 验证{latest_result.verification_pass_rate*100:.1f}%")
    
    return attempt_results

def implement_complete_fix(plan: FixPlan) -> FixImplementation:
    """实施完整的真实修复（禁止简化、临时方案、伪代码）"""
    implementation = FixImplementation(plan)
    
    print(f"    执行修复操作: {plan.type}")
    
    try:
        if plan.type == "FIELD_MAPPING_FIX":
            # 字段映射错误修复
            for action in plan.actions:
                if action.type == "REPLACE_FIELD_USAGE":
                    # 使用Edit工具进行真实文件修改
                    edit_result = edit_file(
                        file_path=action.file_path,
                        old_string=action.old_field_usage,
                        new_string=action.new_field_usage
                    )
                    implementation.add_edit_result(edit_result)
                    print(f"      字段替换: {action.old_field_usage} → {action.new_field_usage}")
        
        elif plan.type == "UI_DATA_BINDING_FIX":
            # UI数据绑定修复
            for action in plan.actions:
                if action.type == "FIX_DATA_ACCESS_PATTERN":
                    edit_result = edit_file(
                        file_path=action.file_path,
                        old_string=action.old_data_access_code,
                        new_string=action.new_data_access_code
                    )
                    implementation.add_edit_result(edit_result)
                    print(f"      数据访问修复: {action.description}")
        
        elif plan.type == "CONFIG_ERROR_FIX":
            # 配置错误修复
            for action in plan.actions:
                if action.type == "UPDATE_CONFIG_VALUE":
                    edit_result = edit_file(
                        file_path=action.config_file_path,
                        old_string=action.old_config_line,
                        new_string=action.new_config_line
                    )
                    implementation.add_edit_result(edit_result)
                    print(f"      配置更新: {action.config_key}")
        
        # 语法验证（确保修改后代码有效）
        syntax_check = verify_syntax_after_modifications(implementation.modified_files)
        implementation.syntax_valid = syntax_check.valid
        
        if not syntax_check.valid:
            implementation.success = False
            implementation.error_message = f"语法错误: {syntax_check.error_details}"
            print(f"      ❌ 语法验证失败: {syntax_check.error_details}")
        else:
            implementation.success = True
            print(f"      ✅ 语法验证通过")
            
    except Exception as e:
        implementation.success = False
        implementation.error_message = str(e)
        print(f"      ❌ 修复异常: {str(e)}")
    
    return implementation
```

**决策条件：**
```python
def decide_node3_to_node4(results: List[Node3AttemptResult]) -> bool:
    """判断NODE-3是否可以转换到NODE-4"""
    
    latest_result = results[-1]
    
    # 检查验证通过率100%（要求）
    verification_pass_rate_100 = latest_result.verification_pass_rate == 1.0
    
    # 检查所有修复完整实施
    all_fixes_complete = latest_result.implementation_success_rate == 1.0
    
    # 检查无副作用确认
    no_side_effects = latest_result.side_effects_check.clean
    
    # 检查循环4步执行完整性
    all_attempts_4_steps = all(
        result.fix_plans and  # 步骤1：修复方案设计
        result.implementations and  # 步骤2：修复实施
        result.verification_results and  # 步骤3：修复验证
        hasattr(result, 'side_effects_check')  # 步骤4：副作用检查
        for result in results
    )
    
    # 检查真实修复验证（不是理论推理）
    real_verification_evidence = all(
        verification.function_test_passed and
        verification.service_health_verified
        for result in results
        for verification in result.verification_results
        if verification.passed
    )
    
    # 检查TodoWrite修复记录
    todo_repair_recorded = True  # 简化检查
    
    conditions = {
        "验证通过率100%": verification_pass_rate_100,
        "所有修复完整实施": all_fixes_complete,
        "无副作用确认": no_side_effects,
        "循环4步执行完整": all_attempts_4_steps,
        "真实验证证据": real_verification_evidence,
        "TodoWrite记录": todo_repair_recorded
    }
    
    print("🤔 NODE-3→NODE-4决策检查:")
    for condition, passed in conditions.items():
        print(f"  {condition}: {'✅' if passed else '❌'}")
        
        # 详细说明关键指标
        if condition == "验证通过率100%":
            print(f"      验证通过率: {latest_result.verification_pass_rate*100:.1f}%")
        elif condition == "所有修复完整实施":
            print(f"      实施成功率: {latest_result.implementation_success_rate*100:.1f}%")
        elif condition == "无副作用确认":
            if not no_side_effects:
                print(f"      副作用数量: {latest_result.side_effects_check.issue_count}")
    
    can_proceed = all(conditions.values())
    print(f"决策结果: {'可以继续' if can_proceed else '需要重新执行'}")
    
    return can_proceed
```

### ✅ **NODE-4: 全面验证与总结**

**节点描述：** 对应阶段4，执行全面验证和生成总结报告

**输入条件：** NODE-3结果，包含所有修复实施结果

**执行内容：**
```python
def execute_node4_comprehensive_verification(node3_results: List[Node3AttemptResult]) -> Node4Result:
    """NODE-4执行内容 - 对应阶段4完整实现"""
    
    print("✅ 执行NODE-4: 全面验证与总结")
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_node("NODE-4")
    
    # 任务4.1: 全面验证测试
    print("执行任务4.1: 全面验证测试...")
    verification_suites = run_comprehensive_verification_suites()
    
    print(f"  服务层测试: {verification_suites['service'].passed_count}/{verification_suites['service'].total_count}")
    print(f"  功能层测试: {verification_suites['functional'].passed_count}/{verification_suites['functional'].total_count}")
    print(f"  数据层测试: {verification_suites['data'].passed_count}/{verification_suites['data'].total_count}")
    print(f"  回归测试: {verification_suites['regression'].passed_count}/{verification_suites['regression'].total_count}")
    print(f"  原问题验证: {verification_suites['original_issue'].passed_count}/{verification_suites['original_issue'].total_count}")
    
    todo_tracker.complete_task("全面验证测试")
    
    # 任务4.2: 执行过程总结
    print("执行任务4.2: 执行过程总结...")
    execution_summary = generate_execution_process_summary({
        "NODE-1": "信息收集结果",
        "NODE-2": node2_results,  # 需要传入前面的结果
        "NODE-3": node3_results
    })
    
    print(f"  总执行时间: {execution_summary.total_time}分钟")
    print(f"  执行效率: {execution_summary.efficiency_score}")
    
    todo_tracker.complete_task("执行过程总结")
    
    # 任务4.3: 技术问题总结
    print("执行任务4.3: 技术问题总结...")
    technical_summary = generate_technical_problem_summary(node3_results)
    
    print(f"  根因类型: {technical_summary.root_cause_types}")
    print(f"  修复方案: {technical_summary.fix_strategies_used}")
    
    todo_tracker.complete_task("技术问题总结")
    
    # 任务4.4: 经验教训总结
    print("执行任务4.4: 经验教训总结...")
    lessons_learned = extract_lessons_learned(node3_results)
    
    print(f"  关键教训: {len(lessons_learned.key_insights)}个")
    print(f"  改进建议: {len(lessons_learned.improvement_suggestions)}个")
    
    todo_tracker.complete_task("经验教训总结")
    
    # 任务4.5: 发散优化建议
    print("执行任务4.5: 发散优化建议...")
    optimization_suggestions = generate_specific_optimization_suggestions({
        "problem_patterns": technical_summary.root_cause_types,
        "fix_effectiveness": verification_suites,
        "execution_efficiency": execution_summary.efficiency_metrics
    })
    
    print(f"  代码质量建议: {len(optimization_suggestions.code_quality)}个")
    print(f"  架构改进建议: {len(optimization_suggestions.architecture)}个")
    print(f"  流程优化建议: {len(optimization_suggestions.process)}个")
    
    todo_tracker.complete_task("发散优化建议")
    
    # 任务4.6: 预防措施建议
    print("执行任务4.6: 预防措施建议...")
    prevention_measures = generate_prevention_measures(technical_summary, lessons_learned)
    
    print(f"  预防检查项: {len(prevention_measures.check_items)}个")
    print(f"  监控建议: {len(prevention_measures.monitoring_suggestions)}个")
    
    todo_tracker.complete_task("预防措施建议")
    
    # 计算总体质量评级
    overall_pass_rate = calculate_overall_verification_pass_rate(verification_suites)
    quality_grade = calculate_quality_grade(execution_summary, verification_suites)
    
    # 完成NODE-4
    todo_tracker.complete_node("NODE-4")
    todo_tracker.mark_all_completed()  # 标记所有TodoWrite完成
    
    print(f"\n✅ NODE-4执行完成")
    print(f"总体验证通过率: {overall_pass_rate*100:.1f}%")
    print(f"质量评级: {quality_grade}")
    
    return Node4Result(
        verification_suites=verification_suites,
        execution_summary=execution_summary,
        technical_summary=technical_summary,
        lessons_learned=lessons_learned,
        optimization_suggestions=optimization_suggestions,
        prevention_measures=prevention_measures,
        overall_pass_rate=overall_pass_rate,
        quality_grade=quality_grade,
        todo_tracker=todo_tracker
    )

def run_comprehensive_verification_suites() -> Dict[str, TestSuite]:
    """运行全面验证测试套件"""
    suites = {}
    
    # 服务层验证
    service_suite = TestSuite("service_layer")
    service_suite.add_test("api_health", test_api_health_complete())
    service_suite.add_test("critical_endpoints", test_all_critical_endpoints())
    service_suite.add_test("database_connection", test_database_stability())
    service_suite.add_test("authentication", test_auth_and_permissions())
    suites["service"] = service_suite
    
    # 功能层验证
    functional_suite = TestSuite("functional_layer")
    functional_suite.add_test("user_management", test_user_management_complete())
    functional_suite.add_test("category_management", test_category_management_complete())
    functional_suite.add_test("purchase_records", test_purchase_records_complete())
    functional_suite.add_test("data_display", test_ui_data_display_complete())
    suites["functional"] = functional_suite
    
    # 数据层验证
    data_suite = TestSuite("data_layer")
    data_suite.add_test("data_integrity", test_data_integrity_complete())
    data_suite.add_test("foreign_keys", test_foreign_key_constraints_complete())
    data_suite.add_test("uuid_consistency", test_uuid_consistency_complete())
    data_suite.add_test("field_mapping", test_field_mapping_correctness_complete())
    suites["data"] = data_suite
    
    # 回归测试
    regression_suite = TestSuite("regression_tests")
    regression_suite.add_test("core_functions", test_core_functions_regression())
    regression_suite.add_test("api_endpoints", test_api_endpoints_regression())
    regression_suite.add_test("ui_components", test_ui_components_regression())
    suites["regression"] = regression_suite
    
    # 原问题验证
    original_issue_suite = TestSuite("original_issue")
    original_issue_suite.add_test("issue_reproduction", test_original_issue_reproduction())
    original_issue_suite.add_test("fix_effectiveness", test_fix_resolves_issue())
    original_issue_suite.add_test("no_recurrence", test_issue_no_recurrence())
    suites["original_issue"] = original_issue_suite
    
    return suites
```

**决策条件：**
```python
def decide_node4_completion(result: Node4Result) -> bool:
    """判断NODE-4是否完成（流程结束条件）"""
    
    # 检查所有验证通过
    all_critical_tests_passed = all(
        suite.pass_rate == 1.0
        for suite_name, suite in result.verification_suites.items()
        if suite_name in ["service", "functional", "data", "original_issue"]
    )
    
    # 检查原问题完全解决
    original_issue_resolved = (
        "original_issue" in result.verification_suites and
        result.verification_suites["original_issue"].pass_rate == 1.0
    )
    
    # 检查总结报告完整
    summary_complete = (
        result.execution_summary.is_complete and
        result.technical_summary.is_complete and
        len(result.lessons_learned.key_insights) >= 3
    )
    
    # 检查优化建议具体
    optimization_specific = (
        len(result.optimization_suggestions.code_quality) >= 2 and
        len(result.optimization_suggestions.process) >= 2
    )
    
    # 检查TodoWrite全部完成
    todo_all_completed = result.todo_tracker.all_tasks_completed()
    
    # 检查质量评级达标
    quality_acceptable = result.quality_grade in ["A", "B", "C"]  # 排除D、F级
    
    conditions = {
        "关键测试100%通过": all_critical_tests_passed,
        "原问题完全解决": original_issue_resolved,
        "总结报告完整": summary_complete,
        "优化建议具体": optimization_specific,
        "TodoWrite全部完成": todo_all_completed,
        "质量评级达标": quality_acceptable
    }
    
    print("🤔 NODE-4完成条件检查:")
    for condition, passed in conditions.items():
        print(f"  {condition}: {'✅' if passed else '❌'}")
        
        # 详细说明关键指标
        if condition == "关键测试100%通过":
            for suite_name in ["service", "functional", "data", "original_issue"]:
                if suite_name in result.verification_suites:
                    rate = result.verification_suites[suite_name].pass_rate
                    print(f"      {suite_name}: {rate*100:.1f}%")
        elif condition == "质量评级达标":
            print(f"      质量评级: {result.quality_grade}")
    
    completion_ready = all(conditions.values())
    print(f"完成状态: {'流程完成' if completion_ready else '需要补充完善'}")
    
    return completion_ready
```

## 🎮 任务图执行控制器

```python
class TaskGraphFlowController:
    """任务图流程控制器"""
    
    def __init__(self):
        self.current_node = "START"
        self.execution_log = []
        self.node_results = {}
        self.decision_history = []
    
    def execute_diagnostic_flow(self, problem_description: str):
        """执行完整的任务图诊断流程"""
        
        print("🚀 启动任务图诊断流程")
        print(f"📝 问题描述: {problem_description}")
        
        # 节点执行器映射
        node_executors = {
            "NODE-1": lambda: execute_node1_information_collection(problem_description),
            "NODE-2": lambda: execute_node2_hypothesis_verification(self.node_results["NODE-1"]),
            "NODE-3": lambda: execute_node3_repair_and_verification(self.node_results["NODE-2"]),
            "NODE-4": lambda: execute_node4_comprehensive_verification(self.node_results["NODE-3"])
        }
        
        # 决策器映射
        decision_makers = {
            "NODE-1": lambda r: decide_node1_to_node2(r),
            "NODE-2": lambda r: decide_node2_to_node3(r),
            "NODE-3": lambda r: decide_node3_to_node4(r),
            "NODE-4": lambda r: decide_node4_completion(r)
        }
        
        # 流程执行
        self.current_node = "NODE-1"
        
        while self.current_node != "END":
            print(f"\n📍 执行节点: {self.current_node}")
            
            # 执行当前节点
            executor = node_executors.get(self.current_node)
            if executor:
                try:
                    result = executor()
                    self.node_results[self.current_node] = result
                    self.execution_log.append(f"{self.current_node}: 执行成功")
                    
                    # 决策下一个节点
                    decision_maker = decision_makers.get(self.current_node)
                    if decision_maker:
                        can_proceed = decision_maker(result)
                        self.decision_history.append(f"{self.current_node}: {'通过' if can_proceed else '阻塞'}")
                        
                        if can_proceed:
                            # 转换到下一个节点
                            next_node = self.get_next_node(self.current_node)
                            self.current_node = next_node
                        else:
                            # 决策失败，重新执行当前节点
                            print(f"⚠️ {self.current_node}决策条件未满足，重新执行")
                            continue
                    else:
                        # 没有决策器，流程结束
                        self.current_node = "END"
                        
                except Exception as e:
                    print(f"❌ {self.current_node}执行异常: {str(e)}")
                    self.execution_log.append(f"{self.current_node}: 执行异常 - {str(e)}")
                    break
            else:
                print(f"❌ 未找到{self.current_node}的执行器")
                break
        
        print("\n🏁 任务图诊断流程完成")
        return self.generate_final_report()
    
    def get_next_node(self, current_node: str) -> str:
        """获取下一个节点"""
        node_sequence = {
            "NODE-1": "NODE-2",
            "NODE-2": "NODE-3",
            "NODE-3": "NODE-4",
            "NODE-4": "END"
        }
        return node_sequence.get(current_node, "END")
```

## 🌟 优势特点

1. **可视化路径**：清晰的流程图表达，易于理解执行顺序和决策逻辑
2. **决策驱动**：每个节点都有明确的客观决策条件，避免主观判断
3. **强制循环**：NODE-2和NODE-3内置强制循环机制，确保充分执行
4. **过程显示**：详细的执行过程显示和统计，实时跟踪进度
5. **门禁控制**：严格的节点转换条件，防止流程跳跃或简化
6. **通用适配**：适用于各种项目和问题类型的诊断修复
7. **完整集成**：融合所有内容，包括4阶段、循环、验证、TodoWrite跟踪

完成任务图架构的重构。现在它包含了完整的流程内容，具备可视化流程图、明确决策节点、强制循环机制和详细的过程工作显示。

### 🧠 **节点3: 建立问题假设**

**执行内容：**
```python
def build_hypotheses(problem_analysis, round_num=1):
    """通用问题假设建立"""
    
    print(f"🧠 第{round_num}轮假设建立...")
    
    hypotheses = []
    problem_type = problem_analysis["problem_type"]
    
    if round_num == 1:
        # 第1轮：基于问题类型的系统性假设
        hypotheses = create_systematic_hypotheses(problem_type)
    else:
        # 后续轮次：基于前轮结果的调整假设
        hypotheses = create_refined_hypotheses(problem_analysis, previous_results)
    
    # 为每个假设分配验证方法
    for i, hypothesis in enumerate(hypotheses, 1):
        hypothesis["verification_method"] = assign_verification_method(hypothesis)
        print(f"  {i}. {hypothesis['description']}")
        print(f"     └─ 验证方法: {hypothesis['verification_method']}")
        print(f"     └─ 优先级: {hypothesis['priority']}")
    
    print(f"📊 假设建立完成: 共{len(hypotheses)}个假设")
    
    return hypotheses

def create_systematic_hypotheses(problem_type):
    """基于问题类型创建系统性假设"""
    
    base_hypotheses = [
        # 基础层假设（适用于所有问题类型）
        {
            "id": "SYNTAX_ERROR",
            "description": "代码语法错误或导入错误",
            "type": "BASIC",
            "priority": "HIGH"
        },
        {
            "id": "CONFIG_ERROR", 
            "description": "配置文件错误或环境变量问题",
            "type": "BASIC",
            "priority": "MEDIUM"
        }
    ]
    
    # 基于问题类型添加特定假设
    type_specific = {
        "UI_DISPLAY": [
            {
                "id": "DATA_BINDING",
                "description": "数据绑定或渲染问题",
                "type": "UI",
                "priority": "HIGH"
            },
            {
                "id": "CSS_STYLE",
                "description": "样式或布局问题",
                "type": "UI", 
                "priority": "MEDIUM"
            }
        ],
        "API_ERROR": [
            {
                "id": "ENDPOINT_ERROR",
                "description": "API端点错误或路由问题",
                "type": "API",
                "priority": "HIGH"
            },
            {
                "id": "REQUEST_HANDLING",
                "description": "请求处理或参数验证问题",
                "type": "API",
                "priority": "HIGH"
            }
        ],
        "DATABASE_ISSUE": [
            {
                "id": "SCHEMA_MISMATCH",
                "description": "数据库结构与代码不匹配",
                "type": "DATABASE",
                "priority": "HIGH"
            },
            {
                "id": "DATA_CONSTRAINT",
                "description": "数据约束或完整性问题",
                "type": "DATABASE",
                "priority": "MEDIUM"
            }
        ]
    }
    
    return base_hypotheses + type_specific.get(problem_type, [])
```

### ✅ **节点4: 验证假设**

**执行内容：**
```python
def verify_hypotheses(hypotheses):
    """通用假设验证"""
    
    print("✅ 开始假设验证...")
    
    verification_results = []
    
    for i, hypothesis in enumerate(hypotheses, 1):
        print(f"🔍 验证假设{i}: {hypothesis['description']}")
        
        # 执行验证
        try:
            result = execute_verification(hypothesis)
            verification_results.append(result)
            
            status = "✅ 确认" if result["confirmed"] else "❌ 排除"
            print(f"  └─ 结果: {status}")
            
            if result["evidence"]:
                print(f"  └─ 证据: {result['evidence']}")
                
        except Exception as e:
            print(f"  └─ 验证异常: {str(e)}")
            verification_results.append({
                "hypothesis_id": hypothesis["id"],
                "confirmed": False,
                "error": str(e)
            })
    
    # 统计验证结果
    confirmed_count = sum(1 for r in verification_results if r.get("confirmed"))
    total_count = len(verification_results)
    
    print(f"📊 验证统计:")
    print(f"  └─ 总假设数: {total_count}")
    print(f"  └─ 确认问题: {confirmed_count}")
    print(f"  └─ 确认率: {confirmed_count/total_count*100:.1f}%")
    
    return verification_results

def execute_verification(hypothesis):
    """执行具体的假设验证"""
    
    verification_method = hypothesis["verification_method"]
    
    if verification_method == "code_analysis":
        return verify_by_code_analysis(hypothesis)
    elif verification_method == "file_check":
        return verify_by_file_check(hypothesis)
    elif verification_method == "api_test":
        return verify_by_api_test(hypothesis)
    elif verification_method == "database_query":
        return verify_by_database_query(hypothesis)
    else:
        return verify_by_generic_search(hypothesis)
```

### 🔧 **节点5: 实施修复**

**执行内容：**
```python
def implement_fix(fix_plan):
    """通用修复实施"""
    
    print("🔧 开始修复实施...")
    print(f"📋 修复计划: {fix_plan['description']}")
    
    implementation_results = []
    
    for step_num, action in enumerate(fix_plan["actions"], 1):
        print(f"🛠️ 执行修复步骤{step_num}: {action['description']}")
        
        try:
            # 执行修复操作
            if action["type"] == "EDIT_FILE":
                result = edit_file_action(action)
            elif action["type"] == "ADD_CODE":
                result = add_code_action(action)
            elif action["type"] == "DELETE_CODE":
                result = delete_code_action(action)
            elif action["type"] == "CONFIG_CHANGE":
                result = config_change_action(action)
            else:
                result = generic_action(action)
            
            implementation_results.append(result)
            
            if result["success"]:
                print(f"  ✅ 步骤{step_num}完成")
            else:
                print(f"  ❌ 步骤{step_num}失败: {result['error']}")
                
        except Exception as e:
            print(f"  ❌ 步骤{step_num}异常: {str(e)}")
            implementation_results.append({
                "step": step_num,
                "success": False,
                "error": str(e)
            })
    
    # 统计修复结果
    success_count = sum(1 for r in implementation_results if r.get("success"))
    total_count = len(implementation_results)
    success_rate = success_count / total_count if total_count > 0 else 0
    
    print(f"📊 修复统计:")
    print(f"  └─ 总步骤数: {total_count}")
    print(f"  └─ 成功步骤: {success_count}")
    print(f"  └─ 成功率: {success_rate*100:.1f}%")
    
    return {
        "plan": fix_plan,
        "results": implementation_results,
        "success_rate": success_rate,
        "overall_success": success_rate == 1.0
    }
```

## 🔄 决策节点逻辑

### **信息充分性决策**
```python
def decide_info_sufficiency(collection_result):
    criteria = {
        "files_found": collection_result["total_findings"] >= 5,
        "keywords_extracted": len(collection_result["keywords"]) >= 3,
        "service_accessible": collection_result["service_status"] == "healthy"
    }
    
    print("🤔 信息充分性决策:")
    for criterion, passed in criteria.items():
        print(f"  └─ {criterion}: {'✅' if passed else '❌'}")
    
    return all(criteria.values())
```

### **根本问题发现决策**
```python
def decide_root_cause_found(verification_results):
    # 检查是否有高置信度的问题确认
    confirmed_issues = [r for r in verification_results if r.get("confirmed")]
    
    high_confidence = any(
        r.get("confidence", 0) >= 0.8 for r in confirmed_issues
    )
    
    sufficient_evidence = any(
        r.get("evidence") and len(r["evidence"]) > 0 for r in confirmed_issues
    )
    
    root_cause_found = high_confidence and sufficient_evidence
    
    print("🤔 根本问题决策:")
    print(f"  └─ 高置信度问题: {'✅' if high_confidence else '❌'}")
    print(f"  └─ 充分证据: {'✅' if sufficient_evidence else '❌'}")
    print(f"  └─ 根因确认: {'✅' if root_cause_found else '❌'}")
    
    return root_cause_found
```

### **修复成功决策**
```python
def decide_fix_success(implementation_result, verification_result):
    implementation_ok = implementation_result["success_rate"] == 1.0
    verification_ok = verification_result["all_tests_passed"]
    no_side_effects = not verification_result.get("side_effects_detected", False)
    
    fix_successful = implementation_ok and verification_ok and no_side_effects
    
    print("🤔 修复成功决策:")
    print(f"  └─ 实施成功: {'✅' if implementation_ok else '❌'}")
    print(f"  └─ 验证通过: {'✅' if verification_ok else '❌'}")  
    print(f"  └─ 无副作用: {'✅' if no_side_effects else '❌'}")
    print(f"  └─ 修复成功: {'✅' if fix_successful else '❌'}")
    
    return fix_successful
```

## 🎯 流程执行控制器

```python
class TaskGraphFlowController:
    """任务图流程控制器"""
    
    def __init__(self):
        self.current_node = "START"
        self.execution_log = []
        self.node_results = {}
    
    def execute_diagnostic_flow(self, problem_description):
        """执行任务图诊断流程"""
        
        print("🚀 启动任务图诊断流程")
        print(f"📝 问题描述: {problem_description}")
        
        # 节点执行映射
        node_executor = {
            "collect_info": self.execute_collect_info,
            "analyze_patterns": self.execute_analyze_patterns,
            "build_hypotheses": self.execute_build_hypotheses,
            "verify_hypotheses": self.execute_verify_hypotheses,
            "implement_fix": self.execute_implement_fix,
            "verify_fix": self.execute_verify_fix
        }
        
        # 流程执行
        self.current_node = "collect_info"
        
        while self.current_node != "END":
            print(f"\n📍 当前节点: {self.current_node}")
            
            # 执行当前节点
            executor = node_executor.get(self.current_node)
            if executor:
                result = executor()
                self.node_results[self.current_node] = result
                
                # 根据结果决定下一个节点
                next_node = self.decide_next_node(self.current_node, result)
                self.current_node = next_node
            else:
                break
        
        print("🏁 诊断流程完成")
        return self.generate_final_report()
```

