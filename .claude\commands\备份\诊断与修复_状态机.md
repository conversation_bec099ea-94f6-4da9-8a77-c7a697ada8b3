---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程 - 状态机架构

## 📋 核心理念

使用有限状态机(FSM)模型，确保诊断流程按照严格的状态转换路径执行，每个状态都有明确的进入条件、执行内容和退出条件。

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：
- 文件读取、编辑、搜索、修改
- 代码分析和静态检查
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证

**不能做的**：
- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 依赖检测规则

**避免误判原则**：
- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：
- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **分离假设-验证** - 禁止将假设建立和验证人为分离，必须保持完整循环
- ❌ **人工循环决策** - 禁止在循环中设置人工决策点，必须基于客观条件自动循环
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖

## 🎛️ 状态机定义

```python
class DiagnosticStateMachine:
    """诊断修复状态机"""
    
    STATES = {
        # 初始化
        'INIT': 'Initial State - 流程启动',
        
        # 阶段1: 信息收集与真实检查
        'STAGE1_COLLECTING': 'Stage 1 - 信息收集与真实检查',
        
        # 阶段2: 假设-验证循环（2-5轮，每轮3步）
        'STAGE2_HYPOTHESIS_LOOP': 'Stage 2 - 假设验证循环',
        
        # 阶段3: 修复实施与验证循环（1-5轮，每轮4步）
        'STAGE3_FIX_LOOP': 'Stage 3 - 修复实施验证循环',
        
        # 阶段4: 总结与发散优化
        'STAGE4_SUMMARY': 'Stage 4 - 总结与发散优化',
        
        # 终结状态
        'COMPLETED': 'Completion - 流程完成',
        'FAILED': 'Failed - 流程失败'
    }
    
    TRANSITIONS = {
        'INIT': ['STAGE1_COLLECTING'],
        'STAGE1_COLLECTING': ['STAGE2_HYPOTHESIS_LOOP', 'FAILED'],
        'STAGE2_HYPOTHESIS_LOOP': ['STAGE3_FIX_LOOP', 'FAILED'],
        'STAGE3_FIX_LOOP': ['STAGE4_SUMMARY', 'FAILED'],
        'STAGE4_SUMMARY': ['COMPLETED', 'FAILED'],
        'COMPLETED': [],
        'FAILED': []
    }
```

## 🏗️ 状态机流程图

```mermaid
stateDiagram-v2
    [*] --> INIT
    INIT --> STAGE1_COLLECTING: 环境检查通过
    
    STAGE1_COLLECTING --> STAGE2_HYPOTHESIS_LOOP: 阶段1门禁条件满足
    STAGE1_COLLECTING --> FAILED: 信息收集失败
    
    STAGE2_HYPOTHESIS_LOOP --> STAGE2_HYPOTHESIS_LOOP: 循环2-5轮，每轮3步
    STAGE2_HYPOTHESIS_LOOP --> STAGE3_FIX_LOOP: 阶段2门禁条件满足
    STAGE2_HYPOTHESIS_LOOP --> FAILED: 假设验证失败
    
    STAGE3_FIX_LOOP --> STAGE3_FIX_LOOP: 循环1-5轮，每轮4步
    STAGE3_FIX_LOOP --> STAGE4_SUMMARY: 阶段3门禁条件满足
    STAGE3_FIX_LOOP --> FAILED: 修复验证失败
    
    STAGE4_SUMMARY --> COMPLETED: 阶段4完成条件满足
    STAGE4_SUMMARY --> FAILED: 总结不完整
    
    COMPLETED --> [*]
    FAILED --> [*]
```

---

## 🔄 状态实现

### **STATE: INIT - 初始化状态**

```python
class InitState:
    """初始状态 - 流程启动和环境检查"""
    
    def __init__(self, $ARGUMENTS):
        self.$ARGUMENTS = $ARGUMENTS
        self.state_name = "INIT"
    
    def can_enter(self, previous_state=None):
        """进入条件检查"""
        return previous_state is None  # 只能从空状态进入
    
    def execute(self):
        """执行状态操作"""
        print(f"🚀 [{self.state_name}] 诊断流程启动")
        print(f"📝 问题描述: {self.$ARGUMENTS}")
        
        # 执行环境检查
        env_check = self.check_environment()
        print(f"🔧 环境检查: {'✅ 通过' if env_check else '❌ 失败'}")
        
        # 初始化TodoWrite
        self.init_todo_tracking()
        print("📋 任务跟踪初始化完成")
        
        return {
            "success": True,
            "environment_ok": env_check,
            "problem_description": self.$ARGUMENTS,
            "next_state": "STAGE1_COLLECTING"
        }
    
    def can_exit_to(self, next_state):
        """退出条件检查"""
        return next_state == "STAGE1_COLLECTING"
    
    def check_environment(self):
        """检查执行环境"""
        print("  正在执行环境检查...")
        
        checks = {
            "working_directory": self.check_working_dir(),
            "basic_tools": self.check_basic_tools(),
            "project_structure": self.check_project_structure(),
            "service_status": self.check_service_status()
        }
        
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"  └─ {check_name}: {status}")
        
        return all(checks.values())
    
    def check_service_status(self):
        """检查后端服务状态并智能启动"""
        try:
            # 步骤1: 服务状态检测
            import subprocess
            check_cmd = 'curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0 and result.stdout.strip() == "200":
                print("    服务已运行 (HTTP 200)")
                return True
            
            print("    服务未运行，尝试启动...")
            
            # 步骤2: 启动服务
            start_cmd = 'cd mock_server && timeout 10s python src/main.py --port 8000 &'
            subprocess.run(start_cmd, shell=True, timeout=15)
            
            # 步骤3: 等待就绪
            import time
            time.sleep(3)
            
            # 步骤4: 验证连接
            verify_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if verify_result.returncode == 0 and verify_result.stdout.strip() == "200":
                print("    服务启动成功")
                return True
            else:
                print("    ❌ WSL环境中启动失败，请在Windows环境中手动启动mock_server")
                print("    命令: cd mock_server && python src/main.py")
                return False
                
        except Exception as e:
            print(f"    ❌ 服务检查异常: {str(e)}")
            print("    请手动在Windows环境中启动mock_server")
            return False
    
    def init_todo_tracking(self):
        """初始化任务跟踪"""
        todos = [
            {"content": f"诊断状态: {self.state_name}", "status": "completed", "priority": "high", "id": "1"},
            {"content": "下一状态: STAGE1_COLLECTING", "status": "pending", "priority": "high", "id": "2"}
        ]
        return todos
```

### **STATE: STAGE1_COLLECTING - 阶段1：信息收集与真实检查**

```python
class Stage1CollectingState:
    """阶段1状态 - 信息收集与真实检查
    
    完整实现阶段1的所有内容：
    - 问题信息解析
    - 强制真实检查（禁止猜测）
    - 多工具组合搜索
    - 搜索完整性验证
    - 系统状态检查
    """
    
    def __init__(self, $ARGUMENTS):
        self.$ARGUMENTS = $ARGUMENTS
        self.state_name = "STAGE1_COLLECTING"
        self.collection_results = {}
    
    def can_enter(self, previous_state):
        """进入条件检查"""
        return previous_state == "INIT"
    
    def execute(self):
        """执行阶段1：信息收集与真实检查"""
        print(f"🔍 [{self.state_name}] 阶段1：信息收集与真实检查")
        print("⚠️ 强制要求：本阶段必须完成多维度信息搜索和真实检查")
        
        # 更新Todo状态
        self.update_todo_status("in_progress")
        
        # 步骤1.1: 问题信息解析
        problem_analysis = self.step_1_1_problem_analysis()
        
        # 步骤1.2: 强制真实检查（禁止猜测）
        search_results = self.step_1_2_real_verification()
        
        # 评估阶段1完成质量
        stage1_quality = self.evaluate_stage1_quality()
        print(f"📊 阶段1质量评分: {stage1_quality:.2f}/1.0")
        
        # 检查阶段1→阶段2门禁条件
        gate_check = self.check_stage1_to_stage2_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE2_HYPOTHESIS_LOOP"
            print("✅ 阶段1门禁条件满足，进入阶段2")
        else:
            next_state = "FAILED"
            print("❌ 阶段1门禁条件不满足，流程失败")
            print(f"失败原因: {gate_check['failure_reasons']}")
        
        self.update_todo_status("completed")
        
        return {
            "success": gate_check["can_proceed"],
            "problem_analysis": problem_analysis,
            "search_results": search_results,
            "stage1_quality": stage1_quality,
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def step_1_1_problem_analysis(self):
        """步骤1.1: 问题信息解析"""
        print("执行步骤1.1: 问题信息解析...")
        
        # 1.1.1 问题描述分析
        core_symptoms = self.extract_core_symptoms()
        functional_modules = self.identify_functional_modules()
        trigger_conditions = self.analyze_trigger_conditions()
        impact_scope = self.determine_impact_scope()
        
        # 1.1.2 搜索策略制定
        search_strategy = self.create_search_strategy(core_symptoms, functional_modules)
        
        print(f"  提取核心症状：{len(core_symptoms)}个")
        print(f"  识别功能模块：{len(functional_modules)}个")
        print(f"  分析影响范围：{impact_scope}")
        print(f"  制定搜索策略：{len(search_strategy['file_patterns'])}个文件模式")
        
        return {
            "core_symptoms": core_symptoms,
            "functional_modules": functional_modules,
            "trigger_conditions": trigger_conditions,
            "impact_scope": impact_scope,
            "search_strategy": search_strategy
        }
    
    def step_1_2_real_verification(self):
        """步骤1.2: 强制真实检查（禁止猜测）"""
        print("执行步骤1.2: 强制真实检查...")
        print("⚠️ 强制要求: 禁止凭想象猜测，必须基于真实验证")
        
        # 1.2.1 多工具组合搜索
        search_results = self.multi_tool_search()
        
        # 1.2.2 搜索完整性验证
        verification_results = self.verify_search_completeness(search_results)
        
        # 1.2.3 系统状态检查
        system_status = self.check_system_status()
        
        print(f"  Glob搜索：{search_results['glob_results']['total_files']}个文件")
        print(f"  Grep搜索：{search_results['grep_results']['total_matches']}处匹配")
        print(f"  系统状态：{system_status['service_status']}")
        
        return {
            "search_results": search_results,
            "verification_results": verification_results,
            "system_status": system_status
        }
    
    def multi_tool_search(self):
        """多工具组合搜索 - 必须使用的搜索组合
        
        必须使用的搜索组合：
        1. Glob: 按文件名模式搜索所有相关文件类型
        2. Grep: 按内容关键词搜索所有匹配内容
        3. Task: 复杂搜索和跨文件引用分析
        4. Read: 逐文件详细检查重要文件
        """
        print("    执行多工具组合搜索...")
        
        # 1. Glob工具 - 文件名模式搜索
        print("      [Glob] 按文件名模式搜索...")
        glob_patterns = [
            "**/*.py",        # Python源码文件
            "**/*.json",      # JSON配置文件
            "**/*.yaml",      # YAML配置文件  
            "**/*.yml",       # YML配置文件
            "**/*.toml",      # TOML配置文件
            "**/*.md",        # 文档文件
            "**/*.txt",       # 文本文件
            "**/*.log",       # 日志文件
            "**/main.py",     # 主程序入口
            "**/app.py",      # 应用入口
            "**/server.py",   # 服务器文件
            "**/config*",     # 配置相关文件
            "**/settings*",   # 设置相关文件
            "**/requirements*", # 依赖文件
            "**/setup.py",    # 安装配置
            "**/Dockerfile",  # Docker配置
            "**/.env*",       # 环境变量
            "**/database*",   # 数据库相关
            "**/router*",     # 路由相关
            "**/api*"         # API相关
        ]
        
        glob_results = {"files_by_pattern": {}, "total_files": 0}
        for pattern in glob_patterns:
            try:
                # 调用Glob工具进行实际搜索
                from tools import Glob
                files = Glob(pattern=pattern).execute()
                glob_results["files_by_pattern"][pattern] = files
                glob_results["total_files"] += len(files)
                print(f"        {pattern}: {len(files)}个文件")
            except Exception as e:
                print(f"        {pattern}: 搜索失败 - {str(e)}")
                glob_results["files_by_pattern"][pattern] = []
        
        # 2. Grep工具 - 内容关键词搜索
        print("      [Grep] 按内容关键词搜索...")
        search_keywords = self.extract_search_keywords()
        
        grep_results = {"matches_by_keyword": {}, "total_matches": 0}
        for keyword in search_keywords:
            try:
                # 调用Grep工具进行实际搜索
                from tools import Grep
                matches = Grep(pattern=keyword, include="*.py").execute()
                grep_results["matches_by_keyword"][keyword] = matches
                grep_results["total_matches"] += len(matches)
                print(f"        '{keyword}': {len(matches)}处匹配")
            except Exception as e:
                print(f"        '{keyword}': 搜索失败 - {str(e)}")
                grep_results["matches_by_keyword"][keyword] = []
        
        # 3. Task工具 - 复杂搜索和跨文件引用分析
        print("      [Task] 复杂搜索和跨文件引用分析...")
        task_results = self.complex_task_search()
        
        # 4. Read工具 - 逐文件详细检查重要文件
        print("      [Read] 详细检查重要文件...")
        read_results = self.detailed_file_inspection(glob_results)
        
        return {
            "glob_results": glob_results,
            "grep_results": grep_results, 
            "task_results": task_results,
            "read_results": read_results,
            "tools_used": 4,  # Glob, Grep, Task, Read
            "search_summary": {
                "total_files_found": glob_results["total_files"],
                "total_content_matches": grep_results["total_matches"],
                "key_files_analyzed": len(read_results.get("analyzed_files", []))
            }
        }
    
    def extract_search_keywords(self):
        """从问题描述中提取搜索关键词
        
        搜索关键词库建立：
        - 错误信息相关的所有关键词
        - 功能模块相关的所有关键词  
        - 可能的变形和相似词汇
        """
        problem_desc = str(self.$ARGUMENTS).lower()
        
        # 基础关键词提取
        base_keywords = []
        
        # 错误类型关键词
        error_keywords = ["error", "exception", "fail", "crash", "bug", "issue", 
                         "错误", "异常", "失败", "崩溃", "问题", "故障"]
        
        # 功能模块关键词
        functional_keywords = ["user", "admin", "login", "auth", "database", "api",
                             "用户", "管理", "登录", "认证", "数据库", "接口"]
        
        # 技术组件关键词
        tech_keywords = ["pyqt", "fastapi", "mysql", "sqlalchemy", "router", "endpoint",
                        "service", "controller", "model", "view"]
        
        # 从问题描述中提取具体词汇
        import re
        words = re.findall(r'\b\w+\b', problem_desc)
        significant_words = [w for w in words if len(w) > 3]
        
        all_keywords = base_keywords + error_keywords + functional_keywords + tech_keywords + significant_words
        
        # 去重并返回
        return list(set(all_keywords))
    
    def complex_task_search(self):
        """复杂搜索和跨文件引用分析
        
        使用Task工具执行复杂的多轮搜索：
        - 项目结构发现
        - API端点自动发现  
        - 跨文件依赖分析
        - 配置文件关联分析
        """
        task_results = {
            "project_structure": {},
            "api_endpoints": {},
            "dependencies": {},
            "configurations": {}
        }
        
        try:
            # 项目结构发现
            print("        执行项目结构发现...")
            structure_search = f"""
            在项目中搜索主要的结构组件：
            1. 使用find命令找到主要的Python文件：find . -name "*.py" | grep -E "(main|app|server|router|api)" | head -5
            2. 使用find命令找到配置文件：find . -name "*.json" -o -name "*.yaml" -o -name "*.toml" | head -10  
            3. 使用grep找到路由和API定义：grep -r "router\\|@app\\|@api" --include="*.py" . | head -10
            """
            
            from tools import Task
            structure_result = Task(
                description="项目结构发现",
                prompt=structure_search
            ).execute()
            task_results["project_structure"] = structure_result
            
        except Exception as e:
            print(f"        Task搜索异常: {str(e)}")
            task_results["project_structure"] = {"error": str(e)}
        
        return task_results
    
    def detailed_file_inspection(self, glob_results):
        """逐文件详细检查重要文件
        
        对关键文件进行详细的Read操作：
        - 主程序文件
        - 配置文件
        - API路由文件
        - 数据库模型文件
        """
        read_results = {"analyzed_files": [], "key_findings": []}
        
        # 确定需要详细检查的重要文件
        important_patterns = ["**/main.py", "**/app.py", "**/server.py", "**/*router*.py", "**/*api*.py"]
        important_files = []
        
        for pattern in important_patterns:
            if pattern in glob_results["files_by_pattern"]:
                important_files.extend(glob_results["files_by_pattern"][pattern][:3])  # 每种类型最多3个
        
        # 逐个详细检查重要文件
        for file_path in important_files[:10]:  # 最多检查10个文件
            try:
                print(f"        检查文件: {file_path}")
                from tools import Read
                file_content = Read(file_path=file_path).execute()
                
                # 分析文件内容，提取关键信息
                key_info = self.analyze_file_content(file_path, file_content)
                read_results["analyzed_files"].append(file_path)
                read_results["key_findings"].extend(key_info)
                
            except Exception as e:
                print(f"        读取文件失败 {file_path}: {str(e)}")
        
        return read_results
    
    def analyze_file_content(self, file_path, content):
        """分析文件内容，提取关键信息"""
        findings = []
        
        # 检查导入语句
        import_lines = [line for line in content.split('\n') if line.strip().startswith('import ') or line.strip().startswith('from ')]
        if import_lines:
            findings.append(f"{file_path}: 发现{len(import_lines)}个导入语句")
        
        # 检查API路由定义
        api_patterns = ['@app.', '@router.', 'def ', 'class ', 'async def']
        for pattern in api_patterns:
            if pattern in content:
                count = content.count(pattern)
                findings.append(f"{file_path}: 发现{count}个{pattern}定义")
        
        return findings
    
    def check_system_status(self):
        """系统状态检查 - 智能服务检测和启动"""
        print("    执行系统状态检查...")
        
        system_status = {
            "service_accessible": False,
            "api_discovery": {},
            "database_connection": False,
            "config_validation": True
        }
        
        try:
            import subprocess
            
            # 服务状态检测与智能启动
            print("      检查服务状态...")
            check_cmd = 'curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0 and result.stdout.strip() == "200":
                system_status["service_accessible"] = True
                print("        ✅ 服务已运行")
                
                # API端点自动发现
                api_discovery = self.discover_api_endpoints()
                system_status["api_discovery"] = api_discovery
                
                # 数据库连接验证
                db_status = self.verify_database_connection(api_discovery)
                system_status["database_connection"] = db_status
                
            else:
                print("        ❌ 服务未运行，需要手动启动")
                
        except Exception as e:
            print(f"        ❌ 系统检查异常: {str(e)}")
        
        return system_status
    
    def discover_api_endpoints(self):
        """API端点自动发现
        
        API端点自动发现策略：
        1. curl -s http://localhost:8000/openapi.json | jq '.paths' | head -20
        2. 自动选择测试端点（优先级：GET > 简单参数 > 常见功能）
        3. 避免硬编码特定功能（如用户管理）
        """
        print("        执行API端点自动发现...")
        
        api_discovery = {
            "openapi_doc": {},
            "available_endpoints": [],
            "test_endpoints": [],
            "discovery_success": False
        }
        
        try:
            import subprocess
            import json
            
            # 1. 获取OpenAPI文档
            openapi_cmd = 'curl -s http://localhost:8000/openapi.json'
            result = subprocess.run(openapi_cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout.strip():
                try:
                    openapi_doc = json.loads(result.stdout)
                    api_discovery["openapi_doc"] = openapi_doc
                    
                    # 2. 提取可用端点
                    if "paths" in openapi_doc:
                        paths = openapi_doc["paths"]
                        api_discovery["available_endpoints"] = list(paths.keys())
                        print(f"          发现{len(paths)}个API端点")
                        
                        # 3. 自动选择测试端点（优先级排序）
                        test_endpoints = self.select_test_endpoints(paths)
                        api_discovery["test_endpoints"] = test_endpoints
                        print(f"          选择{len(test_endpoints)}个测试端点")
                        
                        api_discovery["discovery_success"] = True
                        
                except json.JSONDecodeError as e:
                    print(f"          ❌ OpenAPI文档解析失败: {str(e)}")
                    
            else:
                print(f"          ❌ 无法获取OpenAPI文档: {result.stderr}")
                
        except Exception as e:
            print(f"          ❌ API发现异常: {str(e)}")
        
        return api_discovery
    
    def select_test_endpoints(self, paths):
        """自动选择测试端点
        
        优先级策略：
        1. GET方法优先
        2. 简单参数优先（无复杂请求体）
        3. 常见功能优先（health, docs, status等）
        4. 避免硬编码特定功能
        """
        test_endpoints = []
        
        # 优先级排序规则
        priority_patterns = [
            ("health", "GET"),     # 健康检查
            ("docs", "GET"),       # 文档端点
            ("status", "GET"),     # 状态查询
            ("info", "GET"),       # 信息查询
            ("version", "GET"),    # 版本信息
        ]
        
        # 通用端点模式（避免硬编码特定业务）
        common_patterns = ["list", "get", "query", "search", "find"]
        
        for path, methods in paths.items():
            for method in methods.keys():
                if method.upper() == "GET":
                    # 计算优先级得分
                    score = self.calculate_endpoint_priority(path, method, priority_patterns, common_patterns)
                    
                    test_endpoints.append({
                        "path": path,
                        "method": method.upper(),
                        "priority_score": score,
                        "full_url": f"http://localhost:8000{path}"
                    })
        
        # 按优先级排序并返回前5个
        test_endpoints.sort(key=lambda x: x["priority_score"], reverse=True)
        return test_endpoints[:5]
    
    def calculate_endpoint_priority(self, path, method, priority_patterns, common_patterns):
        """计算端点优先级得分"""
        score = 0
        path_lower = path.lower()
        
        # 优先模式匹配
        for pattern, preferred_method in priority_patterns:
            if pattern in path_lower and method.upper() == preferred_method:
                score += 100
        
        # 通用模式匹配
        for pattern in common_patterns:
            if pattern in path_lower:
                score += 50
        
        # GET方法加分
        if method.upper() == "GET":
            score += 20
        
        # 路径复杂度扣分（参数越多扣分越多）
        param_count = path.count("{")
        score -= param_count * 10
        
        return score
    
    def verify_database_connection(self, api_discovery):
        """数据库连接验证
        
        数据库连接验证策略：
        - 基于发现的API端点进行连接测试
        - 验证响应状态码和数据格式
        - 检查数据库表结构的正确性
        - 确认数据完整性和约束
        """
        print("        执行数据库连接验证...")
        
        if not api_discovery.get("discovery_success"):
            print("          ❌ API发现失败，无法验证数据库连接")
            return False
            
        try:
            import subprocess
            import json
            
            db_verification = {
                "connection_tests": [],
                "response_validation": [],
                "table_structure_check": False,
                "data_integrity_check": False
            }
            
            # 基于发现的API端点进行连接测试
            test_endpoints = api_discovery.get("test_endpoints", [])
            print(f"          使用{len(test_endpoints)}个端点进行数据库验证...")
            
            for endpoint in test_endpoints[:3]:  # 测试前3个端点
                if endpoint["method"] == "GET":
                    test_url = endpoint["full_url"]
                    print(f"            测试端点: {test_url}")
                    
                    # 验证响应状态码
                    test_cmd = f'curl -s -o /dev/null -w "%{{http_code}}" {test_url}'
                    result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True, timeout=5)
                    
                    if result.returncode == 0:
                        status_code = result.stdout.strip()
                        print(f"              状态码: {status_code}")
                        
                        # 验证数据格式
                        data_cmd = f'curl -s {test_url}'
                        data_result = subprocess.run(data_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        
                        if data_result.returncode == 0:
                            try:
                                response_data = json.loads(data_result.stdout)
                                print(f"              数据格式: JSON有效")
                                
                                # 记录验证结果
                                db_verification["connection_tests"].append({
                                    "endpoint": endpoint["path"],
                                    "status_code": status_code,
                                    "data_valid": True,
                                    "response_size": len(data_result.stdout)
                                })
                                
                                db_verification["response_validation"].append(f"端点{endpoint['path']}响应正常")
                                
                            except json.JSONDecodeError:
                                print(f"              数据格式: 非JSON或无效JSON")
                                db_verification["connection_tests"].append({
                                    "endpoint": endpoint["path"],
                                    "status_code": status_code,
                                    "data_valid": False,
                                    "error": "Invalid JSON"
                                })
                        else:
                            print(f"              ❌ 数据获取失败")
                    else:
                        print(f"              ❌ 连接失败: {result.stderr}")
            
            # 检查数据库表结构（通过API响应推断）
            if len(db_verification["connection_tests"]) > 0:
                valid_tests = [t for t in db_verification["connection_tests"] if t.get("data_valid")]
                if len(valid_tests) > 0:
                    db_verification["table_structure_check"] = True
                    print("          ✅ 数据库表结构检查: 通过API响应推断为正常")
                    
                    # 数据完整性检查
                    if len(valid_tests) >= 2:
                        db_verification["data_integrity_check"] = True
                        print("          ✅ 数据完整性检查: 多端点响应一致")
            
            # 评估总体连接状态
            success_rate = len([t for t in db_verification["connection_tests"] if t.get("data_valid")]) / max(len(db_verification["connection_tests"]), 1)
            overall_success = success_rate >= 0.5  # 至少50%成功率
            
            print(f"          数据库连接验证结果: {'✅ 通过' if overall_success else '❌ 失败'} (成功率: {success_rate:.1%})")
            
            return overall_success
            
        except Exception as e:
            print(f"          ❌ 数据库验证异常: {str(e)}")
            return False
    
    def check_stage1_to_stage2_gate(self):
        """检查阶段1→阶段2门禁条件
        
        门禁条件（必须满足才能切换阶段）：
        - 完成多维度信息搜索 
        - 识别核心症状
        - TodoWrite标记completed
        """
        print("    执行阶段1→阶段2门禁条件检查...")
        
        gate_checks = {
            "multi_search_completed": False,
            "core_symptoms_identified": False, 
            "todo_status_updated": False,
            "minimum_files_found": False,
            "service_status_verified": False
        }
        
        failure_reasons = []
        
        # 检查1: 多维度信息搜索完成
        if hasattr(self, 'collection_results'):
            search_results = self.collection_results.get('search_results', {})
            tools_used = search_results.get('tools_used', 0)
            
            if tools_used >= 3:  # 至少使用3种工具
                gate_checks["multi_search_completed"] = True
                print("      ✅ 多维度信息搜索: 完成")
            else:
                failure_reasons.append(f"多维度搜索不足: 仅使用{tools_used}种工具，至少需要3种")
                print(f"      ❌ 多维度信息搜索: 仅使用{tools_used}种工具")
        else:
            failure_reasons.append("未找到搜索结果记录")
            print("      ❌ 多维度信息搜索: 未执行")
        
        # 检查2: 核心症状识别
        if hasattr(self, 'collection_results'):
            problem_analysis = self.collection_results.get('problem_analysis', {})
            core_symptoms = problem_analysis.get('core_symptoms', [])
            
            if len(core_symptoms) > 0:
                gate_checks["core_symptoms_identified"] = True
                print(f"      ✅ 核心症状识别: {len(core_symptoms)}个症状")
            else:
                failure_reasons.append("未识别到核心症状")
                print("      ❌ 核心症状识别: 未识别")
        else:
            failure_reasons.append("未找到问题分析记录")
            print("      ❌ 核心症状识别: 未执行")
        
        # 检查3: 最小文件发现量
        if hasattr(self, 'collection_results'):
            search_results = self.collection_results.get('search_results', {})
            summary = search_results.get('search_summary', {})
            total_files = summary.get('total_files_found', 0)
            
            if total_files >= 5:  # 至少发现5个文件
                gate_checks["minimum_files_found"] = True
                print(f"      ✅ 文件发现量: {total_files}个文件")
            else:
                failure_reasons.append(f"文件发现量不足: 仅{total_files}个，至少需要5个")
                print(f"      ❌ 文件发现量: 仅{total_files}个文件")
        
        # 检查4: 服务状态验证
        if hasattr(self, 'collection_results'):
            system_status = self.collection_results.get('system_status', {})
            service_accessible = system_status.get('service_accessible', False)
            
            if service_accessible:
                gate_checks["service_status_verified"] = True
                print("      ✅ 服务状态验证: 通过")
            else:
                failure_reasons.append("服务状态验证失败")
                print("      ❌ 服务状态验证: 失败")
        
        # 检查5: TodoWrite状态更新
        # 这里简化检查，实际实现中会检查TodoWrite状态
        gate_checks["todo_status_updated"] = True
        print("      ✅ TodoWrite状态: 已更新")
        
        # 综合评估
        passed_checks = sum(gate_checks.values())
        total_checks = len(gate_checks)
        can_proceed = passed_checks >= 4  # 至少通过4项检查
        
        print(f"    门禁条件检查结果: {passed_checks}/{total_checks} ({'✅ 通过' if can_proceed else '❌ 失败'})")
        
        return {
            "can_proceed": can_proceed,
            "gate_checks": gate_checks,
            "failure_reasons": failure_reasons,
            "passed_checks": passed_checks,
            "total_checks": total_checks
        }
```

### **STATE: STAGE2_HYPOTHESIS_LOOP - 阶段2：假设-验证循环**

```python
class Stage2HypothesisLoopState:
    """阶段2状态 - 假设验证循环
    
    完整实现阶段2的所有内容：
    - 强制执行2-5轮假设-验证循环
    - 每轮包含3个步骤：假设建立→假设验证→结果分析
    - 基于客观退出条件自动循环
    """
    
    def __init__(self, stage1_data):
        self.stage1_data = stage1_data
        self.state_name = "STAGE2_HYPOTHESIS_LOOP"
        self.loop_results = []
        self.current_round = 0
        self.max_rounds = 5
        self.min_rounds = 2
    
    def execute(self):
        """执行阶段2：假设-验证循环"""
        print(f"🔬 [{self.state_name}] 阶段2：假设-验证循环")
        print("⚠️ 强制要求：本阶段必须执行完整假设-验证循环机制（2-5轮）")
        
        # 初始化循环
        self.initialize_loop()
        
        # 执行强制循环
        while self.current_round < self.max_rounds:
            self.current_round += 1
            print(f"\n--- 第{self.current_round}轮假设-验证循环 ---")
            
            # 更新TodoWrite状态
            self.update_todo_round_status("in_progress")
            
            # 执行一轮完整的3步循环
            round_result = self.execute_round()
            self.loop_results.append(round_result)
            
            # 更新TodoWrite状态
            self.update_todo_round_status("completed")
            
            # 检查循环退出条件
            if self.should_exit_loop():
                print(f"  循环退出条件满足，结束假设-验证（共{self.current_round}轮）")
                break
            
            # 强制最少2轮检查
            if self.current_round < self.min_rounds:
                print(f"  继续执行（要求最少{self.min_rounds}轮）")
                continue
        
        # 检查阶段2→阶段3门禁条件
        gate_check = self.check_stage2_to_stage3_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE3_FIX_LOOP"
            print("✅ 阶段2门禁条件满足，进入阶段3")
        else:
            next_state = "FAILED"
            print("❌ 阶段2门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_rounds": self.current_round,
            "loop_results": self.loop_results,
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def execute_round(self):
        """执行一轮完整的假设-验证循环（3步）"""
        round_result = {
            "round_number": self.current_round,
            "step_results": {}
        }
        
        # 步骤2.1: 假设建立与调整
        step_2_1_result = self.step_2_1_hypothesis_building()
        round_result["step_results"]["2.1"] = step_2_1_result
        
        # 步骤2.2: 假设验证执行
        step_2_2_result = self.step_2_2_hypothesis_verification(step_2_1_result["hypotheses"])
        round_result["step_results"]["2.2"] = step_2_2_result
        
        # 步骤2.3: 结果分析与新发现
        step_2_3_result = self.step_2_3_result_analysis(step_2_2_result)
        round_result["step_results"]["2.3"] = step_2_3_result
        
        # 统计本轮结果
        round_result["summary"] = {
            "hypotheses_tested": len(step_2_1_result["hypotheses"]),
            "confirmed_issues": step_2_2_result["confirmed_count"],
            "new_findings": step_2_3_result["new_findings_count"],
            "root_cause_found": step_2_3_result.get("root_cause_analysis") is not None
        }
        
        print(f"  第{self.current_round}轮统计: 测试假设{round_result['summary']['hypotheses_tested']}个，确认问题{round_result['summary']['confirmed_issues']}个，新发现{round_result['summary']['new_findings']}个")
        
        return round_result
    
    def step_2_1_hypothesis_building(self):
        """步骤2.1: 假设建立与调整
        
        第1轮：系统性假设建立 - 完整的4层次假设体系
        第2-N轮：动态假设调整 - 基于前轮结果的假设调整
        """
        print(f"执行步骤2.1: 建立第{self.current_round}轮假设...")
        
        if self.current_round == 1:
            # 第1轮：系统性假设建立（强制系统性建立）
            hypotheses = self.build_systematic_hypotheses()
            print(f"  第1轮系统性假设建立: 建立完整的4层次假设体系")
        else:
            # 第2-N轮：动态假设调整（迭代优化）
            previous_round = self.loop_results[-1]
            hypotheses = self.adjust_hypotheses_based_on_results(previous_round)
            print(f"  第{self.current_round}轮动态假设调整: 基于前轮发现调整假设")
        
        # 假设优先级排序
        prioritized_hypotheses = self.prioritize_hypotheses(hypotheses)
        
        print(f"  建立假设总数：{len(prioritized_hypotheses)}个")
        for i, hyp in enumerate(prioritized_hypotheses[:5], 1):  # 显示前5个高优先级
            print(f"    假设{i}: {hyp['description']} (优先级: {hyp['priority']}, 成本: {hyp['verification_cost']})")
        
        if len(prioritized_hypotheses) > 5:
            print(f"    ... 另有{len(prioritized_hypotheses)-5}个假设")
        
        return {"hypotheses": prioritized_hypotheses}
    
    def build_systematic_hypotheses(self):
        """第1轮系统性假设建立 - 多层次问题假设建立
        
        建立完整的4层次假设体系：
        - 基础层假设：语法错误、配置错误、权限错误
        - 逻辑层假设：业务逻辑错误、状态管理错误、时序错误  
        - 系统层假设：架构设计问题、性能问题、兼容性问题
        - 集成层假设：模块集成问题、外部集成问题、环境集成问题
        """
        print("    建立4层次假设体系...")
        problem_desc = str(self.stage1_data.get('problem_analysis', {}).get('core_symptoms', ['未知问题']))
        
        hypotheses = []
        
        # 基础层假设
        print("      构建基础层假设...")
        base_hypotheses = [
            {
                "layer": "基础层",
                "category": "语法错误", 
                "description": "代码语法、拼写错误、导入错误",
                "specific_checks": [
                    "检查Python语法错误和缩进问题",
                    "验证import语句的正确性",
                    "检查函数名和变量名拼写",
                    "确认括号、引号匹配"
                ],
                "verification_method": "Read工具详细检查相关代码文件",
                "priority": "high",
                "verification_cost": "low"
            },
            {
                "layer": "基础层", 
                "category": "配置错误",
                "description": "配置文件格式、路径错误、参数错误",
                "specific_checks": [
                    "验证JSON/YAML格式正确性",
                    "检查配置项完整性",
                    "确认配置值合理性", 
                    "验证路径和URL有效性"
                ],
                "verification_method": "配置文件检查和格式验证",
                "priority": "high",
                "verification_cost": "low"
            },
            {
                "layer": "基础层",
                "category": "权限错误", 
                "description": "文件权限、数据库权限、网络权限",
                "specific_checks": [
                    "检查文件读写权限",
                    "验证数据库连接权限",
                    "确认网络访问权限",
                    "检查端口占用情况"
                ],
                "verification_method": "权限测试和连接验证",
                "priority": "medium",
                "verification_cost": "medium"
            }
        ]
        hypotheses.extend(base_hypotheses)
        
        # 逻辑层假设
        print("      构建逻辑层假设...")
        logic_hypotheses = [
            {
                "layer": "逻辑层",
                "category": "业务逻辑错误",
                "description": "算法实现、条件判断、数据处理逻辑",
                "specific_checks": [
                    "分析业务逻辑正确性",
                    "检查条件判断完整性", 
                    "验证异常处理充分性",
                    "确认数据流向正确性"
                ],
                "verification_method": "业务逻辑分析和数据流追踪",
                "priority": "high",
                "verification_cost": "medium"
            },
            {
                "layer": "逻辑层",
                "category": "状态管理错误",
                "description": "对象状态、数据状态、会话状态",
                "specific_checks": [
                    "检查对象状态一致性",
                    "验证数据状态管理",
                    "确认会话状态处理",
                    "检查状态转换逻辑"
                ],
                "verification_method": "状态管理流程验证",
                "priority": "medium", 
                "verification_cost": "medium"
            },
            {
                "layer": "逻辑层",
                "category": "时序错误",
                "description": "操作顺序、异步处理、事件时序",
                "specific_checks": [
                    "验证操作执行顺序",
                    "检查异步处理机制",
                    "确认事件触发时序",
                    "检查并发控制逻辑"
                ],
                "verification_method": "时序和并发验证测试",
                "priority": "medium",
                "verification_cost": "high"
            }
        ]
        hypotheses.extend(logic_hypotheses)
        
        # 系统层假设
        print("      构建系统层假设...")
        system_hypotheses = [
            {
                "layer": "系统层",
                "category": "架构设计问题",
                "description": "模块耦合、接口设计、数据流设计",
                "specific_checks": [
                    "分析模块耦合度",
                    "检查接口设计合理性",
                    "验证数据流设计",
                    "确认架构一致性"
                ],
                "verification_method": "架构分析和设计审查",
                "priority": "medium",
                "verification_cost": "high"
            },
            {
                "layer": "系统层", 
                "category": "性能问题",
                "description": "资源消耗、响应时间、并发处理",
                "specific_checks": [
                    "监控资源消耗情况",
                    "测量响应时间",
                    "验证并发处理能力",
                    "检查内存和CPU使用"
                ],
                "verification_method": "性能监控和压力测试",
                "priority": "low",
                "verification_cost": "high"
            },
            {
                "layer": "系统层",
                "category": "兼容性问题", 
                "description": "版本兼容、环境差异、平台差异",
                "specific_checks": [
                    "检查版本兼容性",
                    "验证环境差异影响",
                    "确认平台特性支持",
                    "检查依赖版本冲突"
                ],
                "verification_method": "兼容性测试和环境验证",
                "priority": "low",
                "verification_cost": "medium"
            }
        ]
        hypotheses.extend(system_hypotheses)
        
        # 集成层假设
        print("      构建集成层假设...")
        integration_hypotheses = [
            {
                "layer": "集成层",
                "category": "模块集成问题",
                "description": "接口调用、数据传递、错误传播", 
                "specific_checks": [
                    "测试接口调用正确性",
                    "验证数据传递完整性",
                    "检查错误传播机制",
                    "确认模块间通信"
                ],
                "verification_method": "模块集成测试和接口验证",
                "priority": "high",
                "verification_cost": "medium"
            },
            {
                "layer": "集成层",
                "category": "外部集成问题",
                "description": "数据库连接、API调用、文件系统访问",
                "specific_checks": [
                    "验证数据库连接稳定性",
                    "测试API调用响应",
                    "检查文件系统访问",
                    "确认外部服务依赖"
                ],
                "verification_method": "外部集成验证和连通性测试",
                "priority": "high", 
                "verification_cost": "low"
            },
            {
                "layer": "集成层",
                "category": "环境集成问题",
                "description": "开发环境、测试环境、生产环境差异",
                "specific_checks": [
                    "对比环境配置差异",
                    "验证环境变量设置",
                    "检查部署配置一致性",
                    "确认环境依赖完整性"
                ],
                "verification_method": "环境对比和配置验证",
                "priority": "medium",
                "verification_cost": "medium"
            }
        ]
        hypotheses.extend(integration_hypotheses)
        
        print(f"    4层次假设体系构建完成: 基础层{len(base_hypotheses)}个，逻辑层{len(logic_hypotheses)}个，系统层{len(system_hypotheses)}个，集成层{len(integration_hypotheses)}个")
        
        return hypotheses
    
    def prioritize_hypotheses(self, hypotheses):
        """假设优先级排序
        
        基于影响程度、可能性、验证成本进行综合排序：
        - 影响程度评估：高影响（系统不可用）> 中影响（功能异常）> 低影响（体验下降）
        - 可能性评估：高可能性（有明确证据）> 中可能性（逻辑推理）> 低可能性（理论可能）
        - 验证成本评估：低成本（简单检查）> 中成本（需准备）> 高成本（大量资源）
        """
        print("    执行假设优先级排序...")
        
        # 优先级权重计算
        priority_weights = {"high": 3, "medium": 2, "low": 1}
        cost_weights = {"low": 3, "medium": 2, "high": 1}  # 成本低的优先级高
        
        for hypothesis in hypotheses:
            # 计算综合得分
            priority_score = priority_weights.get(hypothesis.get("priority", "medium"), 2)
            cost_score = cost_weights.get(hypothesis.get("verification_cost", "medium"), 2)
            
            # 基于问题描述的相关性得分
            relevance_score = self.calculate_relevance_score(hypothesis)
            
            # 综合得分
            hypothesis["total_score"] = priority_score * 0.4 + cost_score * 0.3 + relevance_score * 0.3
        
        # 按得分排序
        sorted_hypotheses = sorted(hypotheses, key=lambda h: h["total_score"], reverse=True)
        
        print(f"    优先级排序完成: 选择前{min(10, len(sorted_hypotheses))}个高优先级假设进行验证")
        
        return sorted_hypotheses[:10]  # 返回前10个最高优先级假设
    
    def calculate_relevance_score(self, hypothesis):
        """计算假设与问题描述的相关性得分"""
        # 简化实现：基于关键词匹配
        problem_keywords = str(self.stage1_data).lower()
        hypothesis_desc = hypothesis["description"].lower()
        
        # 关键词匹配得分
        common_keywords = ["error", "fail", "exception", "bug", "issue", "错误", "失败", "异常", "问题"]
        matches = sum(1 for keyword in common_keywords if keyword in problem_keywords and keyword in hypothesis_desc)
        
        return min(matches / 2.0, 3.0)  # 最高3分
    
    def step_2_2_hypothesis_verification(self, hypotheses):
        """步骤2.2: 假设验证执行
        
        系统性假设验证方法：
        - 代码层验证：语法检查、逻辑验证
        - 配置层验证：配置文件检查、环境变量验证
        - 数据层验证：项目结构发现、API端点自动发现、数据库连接验证
        - 功能层验证：渐进式API功能验证（Level 1-3）
        """
        print(f"执行步骤2.2: 验证第{self.current_round}轮假设...")
        print("⚠️ 强制要求：禁止凭想象猜测，必须基于真实验证")
        
        verification_results = []
        confirmed_count = 0
        
        for i, hypothesis in enumerate(hypotheses, 1):
            print(f"  验证假设{i}: {hypothesis['description']} (层级: {hypothesis['layer']})")
            
            # 执行真实验证（禁止凭想象猜测）
            verification = self.execute_real_verification(hypothesis)
            verification_results.append(verification)
            
            if verification["confirmed"]:
                confirmed_count += 1
                print(f"    ✅ 确认问题: {verification['evidence'][:80]}...")
                print(f"       证据类型: {verification['evidence_type']}")
            else:
                print(f"    ❌ 假设被排除: {verification.get('rejection_reason', '验证失败')}")
        
        # 量化验证指标
        verification_stats = self.calculate_verification_statistics(verification_results)
        
        print(f"  验证统计: 总假设{len(hypotheses)}个，确认问题{confirmed_count}个，需进一步验证{verification_stats['needs_further_verification']}个")
        
        return {
            "verification_results": verification_results,
            "confirmed_count": confirmed_count,
            "total_tested": len(hypotheses),
            "verification_statistics": verification_stats
        }
    
    def execute_real_verification(self, hypothesis):
        """执行真实验证（禁止凭想象猜测）
        
        根据假设层级和类别选择相应的验证策略：
        - 基础层：代码语法检查、配置文件验证、权限测试
        - 逻辑层：业务逻辑分析、状态管理验证、时序检查
        - 系统层：架构分析、性能测试、兼容性验证
        - 集成层：模块集成测试、外部集成验证、环境对比
        """
        print(f"    执行 {hypothesis['layer']} - {hypothesis['category']} 验证...")
        
        verification_result = {
            "hypothesis_id": hypothesis.get("category", "unknown"),
            "confirmed": False,
            "evidence": "",
            "evidence_type": "",
            "verification_method_used": hypothesis.get("verification_method", ""),
            "rejection_reason": ""
        }
        
        try:
            # 根据假设层级选择验证策略
            if hypothesis["layer"] == "基础层":
                verification_result = self.verify_basic_layer(hypothesis)
            elif hypothesis["layer"] == "逻辑层":
                verification_result = self.verify_logic_layer(hypothesis)
            elif hypothesis["layer"] == "系统层":
                verification_result = self.verify_system_layer(hypothesis)
            elif hypothesis["layer"] == "集成层":
                verification_result = self.verify_integration_layer(hypothesis)
            else:
                verification_result["rejection_reason"] = f"未知假设层级: {hypothesis['layer']}"
                
        except Exception as e:
            verification_result["rejection_reason"] = f"验证过程异常: {str(e)}"
            print(f"      ❌ 验证异常: {str(e)}")
        
        return verification_result
    
    def verify_basic_layer(self, hypothesis):
        """基础层验证：语法错误、配置错误、权限错误"""
        if hypothesis["category"] == "语法错误":
            return self.verify_syntax_errors(hypothesis)
        elif hypothesis["category"] == "配置错误":
            return self.verify_configuration_errors(hypothesis)
        elif hypothesis["category"] == "权限错误":
            return self.verify_permission_errors(hypothesis)
        else:
            return {"confirmed": False, "rejection_reason": f"未知基础层类别: {hypothesis['category']}"}
    
    def verify_syntax_errors(self, hypothesis):
        """代码层验证：语法检查、导入验证"""
        print(f"      执行代码语法检查...")
        
        try:
            # 1. 详细检查相关代码文件
            from tools import Glob, Read
            
            # 搜索Python文件
            python_files = Glob(pattern="**/*.py").execute()[:10]  # 检查前10个Python文件
            
            syntax_issues = []
            import_issues = []
            
            for file_path in python_files:
                try:
                    content = Read(file_path=file_path).execute()
                    
                    # 语法检查
                    syntax_check = self.check_python_syntax(content, file_path)
                    if syntax_check["has_issues"]:
                        syntax_issues.extend(syntax_check["issues"])
                    
                    # 导入语句检查
                    import_check = self.check_import_statements(content, file_path)
                    if import_check["has_issues"]:
                        import_issues.extend(import_check["issues"])
                        
                except Exception as e:
                    print(f"        检查文件失败 {file_path}: {str(e)}")
            
            # 评估验证结果
            total_issues = len(syntax_issues) + len(import_issues)
            
            if total_issues > 0:
                evidence = f"发现{total_issues}个语法/导入问题: " + "; ".join((syntax_issues + import_issues)[:3])
                return {
                    "confirmed": True,
                    "evidence": evidence,
                    "evidence_type": "语法/导入错误",
                    "detailed_issues": {"syntax": syntax_issues, "imports": import_issues}
                }
            else:
                return {
                    "confirmed": False,
                    "rejection_reason": f"检查{len(python_files)}个Python文件，未发现语法或导入错误"
                }
                
        except Exception as e:
            return {"confirmed": False, "rejection_reason": f"语法检查异常: {str(e)}"}
    
    def verify_configuration_errors(self, hypothesis):
        """配置层验证：配置文件格式、路径错误、参数错误"""
        print(f"      执行配置文件检查...")
        
        try:
            from tools import Glob, Read
            import json
            
            # 搜索配置文件
            config_patterns = ["**/*.json", "**/*.yaml", "**/*.yml", "**/*.toml", "**/.env*"]
            config_files = []
            
            for pattern in config_patterns:
                files = Glob(pattern=pattern).execute()
                config_files.extend(files[:5])  # 每种类型最多5个
            
            config_issues = []
            
            for file_path in config_files[:15]:  # 最多检查15个配置文件
                try:
                    content = Read(file_path=file_path).execute()
                    
                    # JSON格式验证
                    if file_path.endswith('.json'):
                        try:
                            json.loads(content)
                        except json.JSONDecodeError as e:
                            config_issues.append(f"{file_path}: JSON格式错误 - {str(e)[:50]}")
                    
                    # 路径和URL验证
                    path_issues = self.check_paths_and_urls(content, file_path)
                    config_issues.extend(path_issues)
                    
                except Exception as e:
                    config_issues.append(f"{file_path}: 读取失败 - {str(e)[:30]}")
            
            if len(config_issues) > 0:
                evidence = f"发现{len(config_issues)}个配置问题: " + "; ".join(config_issues[:3])
                return {
                    "confirmed": True,
                    "evidence": evidence,
                    "evidence_type": "配置文件错误",
                    "detailed_issues": config_issues
                }
            else:
                return {
                    "confirmed": False,
                    "rejection_reason": f"检查{len(config_files)}个配置文件，未发现格式或配置错误"
                }
                
        except Exception as e:
            return {"confirmed": False, "rejection_reason": f"配置检查异常: {str(e)}"}
    
    def verify_integration_layer(self, hypothesis):
        """集成层验证：外部集成问题验证"""
        if hypothesis["category"] == "外部集成问题":
            return self.verify_external_integration(hypothesis)
        elif hypothesis["category"] == "模块集成问题":
            return self.verify_module_integration(hypothesis)
        elif hypothesis["category"] == "环境集成问题":
            return self.verify_environment_integration(hypothesis)
        else:
            return {"confirmed": False, "rejection_reason": f"未知集成层类别: {hypothesis['category']}"}
    
    def verify_external_integration(self, hypothesis):
        """外部集成验证：数据库连接、API调用、文件系统访问
        
        3级渐进式API功能验证：
        - Level 1: 基础连通性验证（docs端点、openapi.json）
        - Level 2: 核心功能验证（基于发现的API端点）
        - Level 3: 业务流程验证（基于项目特定逻辑）
        """
        print(f"      执行外部集成验证...")
        
        try:
            import subprocess
            import json
            
            integration_results = {
                "level1_connectivity": False,
                "level2_core_functions": False,
                "level3_business_processes": False
            }
            
            issues = []
            
            # Level 1: 基础连通性验证
            print(f"        Level 1: 基础连通性验证...")
            level1_result = self.verify_basic_connectivity()
            integration_results["level1_connectivity"] = level1_result["success"]
            
            if not level1_result["success"]:
                issues.append(f"Level 1失败: {level1_result['error']}")
            else:
                print(f"          ✅ 基础连通性验证通过")
                
                # Level 2: 核心功能验证
                print(f"        Level 2: 核心功能验证...")
                level2_result = self.verify_core_functionality()
                integration_results["level2_core_functions"] = level2_result["success"]
                
                if not level2_result["success"]:
                    issues.append(f"Level 2失败: {level2_result['error']}")
                else:
                    print(f"          ✅ 核心功能验证通过")
                    
                    # Level 3: 业务流程验证
                    print(f"        Level 3: 业务流程验证...")
                    level3_result = self.verify_business_processes()
                    integration_results["level3_business_processes"] = level3_result["success"]
                    
                    if not level3_result["success"]:
                        issues.append(f"Level 3失败: {level3_result['error']}")
                    else:
                        print(f"          ✅ 业务流程验证通过")
            
            # 评估总体集成状态
            failed_levels = len(issues)
            
            if failed_levels > 0:
                evidence = f"外部集成问题: {failed_levels}个级别失败 - " + "; ".join(issues[:2])
                return {
                    "confirmed": True,
                    "evidence": evidence,
                    "evidence_type": "外部集成错误",
                    "integration_results": integration_results,
                    "failed_levels": failed_levels
                }
            else:
                return {
                    "confirmed": False,
                    "rejection_reason": "3级渐进式API功能验证全部通过，外部集成正常"
                }
                
        except Exception as e:
            return {"confirmed": False, "rejection_reason": f"外部集成验证异常: {str(e)}"}
    
    def verify_basic_connectivity(self):
        """Level 1: 基础连通性验证（docs端点、openapi.json）"""
        try:
            import subprocess
            
            # 验证docs端点
            docs_cmd = 'curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs'
            docs_result = subprocess.run(docs_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if docs_result.returncode != 0 or docs_result.stdout.strip() != "200":
                return {"success": False, "error": f"docs端点不可访问 (状态码: {docs_result.stdout.strip()})"}
            
            # 验证openapi.json
            api_cmd = 'curl -s http://localhost:8000/openapi.json'
            api_result = subprocess.run(api_cmd, shell=True, capture_output=True, text=True, timeout=5)
            
            if api_result.returncode != 0:
                return {"success": False, "error": "openapi.json获取失败"}
                
            try:
                import json
                json.loads(api_result.stdout)
                return {"success": True}
            except json.JSONDecodeError:
                return {"success": False, "error": "openapi.json格式无效"}
                
        except Exception as e:
            return {"success": False, "error": f"连通性验证异常: {str(e)}"}
    
    def verify_core_functionality(self):
        """Level 2: 核心功能验证（基于发现的API端点）"""
        try:
            # 自动发现API端点并测试
            from tools import Task
            
            discovery_prompt = """
            使用curl命令自动发现和测试API端点：
            1. 获取API文档：curl -s http://localhost:8000/openapi.json
            2. 提取可用端点并测试前3个GET端点
            3. 验证响应状态码和数据格式
            请返回测试结果统计
            """
            
            discovery_result = Task(
                description="API端点功能验证",
                prompt=discovery_prompt
            ).execute()
            
            # 简化评估：如果Task工具成功执行且没有异常，认为核心功能正常
            if "error" in str(discovery_result).lower() or "fail" in str(discovery_result).lower():
                return {"success": False, "error": "API端点测试发现问题"}
            else:
                return {"success": True}
                
        except Exception as e:
            return {"success": False, "error": f"核心功能验证异常: {str(e)}"}
    
    def verify_business_processes(self):
        """Level 3: 业务流程验证（基于项目特定逻辑）"""
        # 简化实现：基于项目结构推断业务流程状态
        try:
            from tools import Grep
            
            # 搜索业务逻辑相关的关键词
            business_keywords = ["user", "admin", "login", "auth", "database", "api"]
            business_indicators = 0
            
            for keyword in business_keywords:
                try:
                    matches = Grep(pattern=keyword, include="*.py").execute()
                    if len(matches) > 0:
                        business_indicators += 1
                except:
                    pass
            
            if business_indicators >= 3:  # 至少3个业务指标
                return {"success": True}
            else:
                return {"success": False, "error": f"业务流程指标不足: 仅{business_indicators}个"}
                
        except Exception as e:
            return {"success": False, "error": f"业务流程验证异常: {str(e)}"}
    
    def calculate_verification_statistics(self, verification_results):
        """计算量化验证指标"""
        stats = {
            "total_verified": len(verification_results),
            "confirmed_count": len([r for r in verification_results if r.get("confirmed")]),
            "rejected_count": len([r for r in verification_results if not r.get("confirmed")]),
            "needs_further_verification": 0,
            "evidence_types": {}
        }
        
        # 统计证据类型
        for result in verification_results:
            if result.get("confirmed") and result.get("evidence_type"):
                evidence_type = result["evidence_type"]
                stats["evidence_types"][evidence_type] = stats["evidence_types"].get(evidence_type, 0) + 1
        
        return stats
    
    def check_python_syntax(self, content, file_path):
        """检查Python语法错误"""
        issues = []
        
        # 简化的语法检查
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line:
                # 检查常见语法错误
                if line.count('(') != line.count(')'):
                    issues.append(f"{file_path}:{i} 括号不匹配")
                if line.count('[') != line.count(']'):
                    issues.append(f"{file_path}:{i} 方括号不匹配")
                if line.count('{') != line.count('}'):
                    issues.append(f"{file_path}:{i} 花括号不匹配")
        
        return {"has_issues": len(issues) > 0, "issues": issues}
    
    def check_import_statements(self, content, file_path):
        """检查导入语句错误"""
        issues = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line.startswith('import ') or line.startswith('from '):
                # 检查导入语句语法
                if line.startswith('from ') and ' import ' not in line:
                    issues.append(f"{file_path}:{i} from语句缺少import")
                if line.endswith(','):
                    issues.append(f"{file_path}:{i} 导入语句末尾不应有逗号")
        
        return {"has_issues": len(issues) > 0, "issues": issues}
    
    def check_paths_and_urls(self, content, file_path):
        """检查配置文件中的路径和URL"""
        issues = []
        
        lines = content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            # 检查URL格式
            if 'http://' in line or 'https://' in line:
                if 'localhost:' not in line and '127.0.0.1:' not in line:
                    # 简化检查：只检查本地URL
                    continue
            
            # 检查文件路径
            if any(path_indicator in line for path_indicator in ['path', 'file', 'dir', '/']):
                if '..' in line:
                    issues.append(f"{file_path}:{i} 存在相对路径'..'")
        
        return issues
    
    def verify_logic_layer(self, hypothesis):
        """逻辑层验证：业务逻辑错误、状态管理错误、时序错误"""
        # 简化实现
        return {"confirmed": False, "rejection_reason": f"逻辑层验证暂未实现: {hypothesis['category']}"}
    
    def verify_system_layer(self, hypothesis):
        """系统层验证：架构设计问题、性能问题、兼容性问题"""
        # 简化实现
        return {"confirmed": False, "rejection_reason": f"系统层验证暂未实现: {hypothesis['category']}"}
    
    def verify_module_integration(self, hypothesis):
        """模块集成验证"""
        # 简化实现
        return {"confirmed": False, "rejection_reason": "模块集成验证暂未实现"}
    
    def verify_environment_integration(self, hypothesis):
        """环境集成验证"""
        # 简化实现
        return {"confirmed": False, "rejection_reason": "环境集成验证暂未实现"}
    
    def step_2_3_result_analysis(self, verification_results):
        """步骤2.3: 结果分析与新发现"""
        print(f"执行步骤2.3: 分析第{self.current_round}轮结果...")
        
        # 分析验证结果
        confirmed_issues = [r for r in verification_results["verification_results"] if r["confirmed"]]
        new_findings = self.identify_new_findings(verification_results)
        
        # 根因分析
        root_cause_analysis = None
        if len(confirmed_issues) > 0:
            root_cause_analysis = self.analyze_root_cause(confirmed_issues)
        
        print(f"  确认问题数: {len(confirmed_issues)}")
        print(f"  新发现数: {len(new_findings)}")
        print(f"  根因分析: {'已完成' if root_cause_analysis else '待进一步分析'}")
        
        return {
            "confirmed_issues": confirmed_issues,
            "new_findings": new_findings,
            "new_findings_count": len(new_findings),
            "root_cause_analysis": root_cause_analysis
        }
    
    def should_exit_loop(self):
        """检查循环退出条件"""
        # 强制最少2轮
        if self.current_round < self.min_rounds:
            return False
        
        # 退出条件：假设验证完成率100% + 连续2轮无新假设产生 + 连续2轮无新问题发现
        if len(self.loop_results) >= 2:
            last_result = self.loop_results[-1]
            second_last_result = self.loop_results[-2]
            
            # 检查是否找到根本问题且连续2轮无新发现
            has_root_cause = last_result["summary"]["root_cause_found"]
            last_no_findings = last_result["summary"]["new_findings"] == 0
            second_last_no_findings = second_last_result["summary"]["new_findings"] == 0
            
            return has_root_cause and last_no_findings and second_last_no_findings
        
        return False
    
    def check_stage2_to_stage3_gate(self):
        """检查阶段2→阶段3门禁条件"""
        # 门禁条件：满足循环退出条件 + 假设验证充分性确认 + TodoWrite完成验证记录
        
        conditions = {
            "最少轮次执行": self.current_round >= self.min_rounds,
            "最多轮次遵守": self.current_round <= self.max_rounds,
            "假设验证充分": sum(r["summary"]["hypotheses_tested"] for r in self.loop_results) >= 5,
            "根本问题确认": any(r["summary"]["root_cause_found"] for r in self.loop_results),
            "退出条件满足": self.should_exit_loop() or self.current_round >= self.max_rounds,
            "TodoWrite记录": True  # 简化检查
        }
        
        print("🤔 阶段2→阶段3门禁检查:")
        for condition, passed in conditions.items():
            print(f"  {condition}: {'✅' if passed else '❌'}")
        
        can_proceed = all(conditions.values())
        failure_reasons = [cond for cond, passed in conditions.items() if not passed]
        
        return {
            "can_proceed": can_proceed,
            "conditions": conditions,
            "failure_reasons": failure_reasons
        }
```

### **STATE: STAGE3_FIX_LOOP - 阶段3：修复实施与验证循环**

```python
class Stage3FixLoopState:
    """阶段3状态 - 修复实施验证循环
    
    完整实现阶段3的所有内容：
    - 强制执行1-5轮修复-验证循环
    - 每轮包含4个步骤：方案设计→方案实施→效果验证→结果评估
    - 目标：验证通过率100%且无副作用
    """
    
    def __init__(self, stage2_data):
        self.stage2_data = stage2_data
        self.state_name = "STAGE3_FIX_LOOP"
        self.loop_results = []
        self.current_attempt = 0
        self.max_attempts = 5
        self.min_attempts = 1
    
    def execute(self):
        """执行阶段3：修复实施与验证循环"""
        print(f"🔧 [{self.state_name}] 阶段3：修复实施与验证循环")
        print("⚠️ 强制要求：本阶段必须执行完整修复-验证循环机制（1-5轮）")
        
        # 初始化循环
        self.initialize_fix_loop()
        
        # 执行强制循环
        while self.current_attempt < self.max_attempts:
            self.current_attempt += 1
            print(f"\n--- 第{self.current_attempt}轮修复实施与验证 ---")
            
            # 更新TodoWrite状态
            self.update_todo_attempt_status("in_progress")
            
            # 执行一轮完整的4步循环
            attempt_result = self.execute_attempt()
            self.loop_results.append(attempt_result)
            
            # 更新TodoWrite状态
            self.update_todo_attempt_status("completed")
            
            # 检查是否达到100%验证通过
            if self.is_fully_verified():
                print(f"  所有修复完成且验证通过，结束修复（共{self.current_attempt}轮）")
                break
        
        # 检查阶段3→阶段4门禁条件
        gate_check = self.check_stage3_to_stage4_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE4_SUMMARY"
            print("✅ 阶段3门禁条件满足，进入阶段4")
        else:
            next_state = "FAILED"
            print("❌ 阶段3门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_attempts": self.current_attempt,
            "loop_results": self.loop_results,
            "final_verification_rate": self.calculate_final_verification_rate(),
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def execute_attempt(self):
        """执行一轮完整的修复-验证循环（4步）"""
        attempt_result = {
            "attempt_number": self.current_attempt,
            "step_results": {}
        }
        
        # 步骤3.1: 修复方案设计与调整
        step_3_1_result = self.step_3_1_fix_design()
        attempt_result["step_results"]["3.1"] = step_3_1_result
        
        # 步骤3.2: 修复方案实施
        step_3_2_result = self.step_3_2_fix_implementation(step_3_1_result["fix_plans"])
        attempt_result["step_results"]["3.2"] = step_3_2_result
        
        # 步骤3.3: 修复效果验证
        step_3_3_result = self.step_3_3_fix_verification(step_3_2_result)
        attempt_result["step_results"]["3.3"] = step_3_3_result
        
        # 步骤3.4: 验证结果评估
        step_3_4_result = self.step_3_4_result_evaluation(step_3_3_result)
        attempt_result["step_results"]["3.4"] = step_3_4_result
        
        # 统计本轮结果
        attempt_result["summary"] = {
            "implementation_success_rate": step_3_2_result["success_rate"],
            "verification_pass_rate": step_3_3_result["pass_rate"],
            "side_effects_detected": step_3_4_result["side_effects_count"] > 0,
            "overall_success": step_3_3_result["pass_rate"] == 1.0 and step_3_4_result["side_effects_count"] == 0
        }
        
        print(f"  第{self.current_attempt}轮统计: 实施成功率{attempt_result['summary']['implementation_success_rate']*100:.1f}%, 验证通过率{attempt_result['summary']['verification_pass_rate']*100:.1f}%")
        
        return attempt_result
    
    def step_3_1_fix_design(self):
        """步骤3.1: 修复方案设计与调整"""
        print(f"执行步骤3.1: 设计第{self.current_attempt}轮修复方案...")
        
        if self.current_attempt == 1:
            # 第1轮：初始修复方案设计
            fix_plans = self.design_initial_fix_plans()
        else:
            # 第2-N轮：基于前轮评估的方案调整
            previous_attempt = self.loop_results[-1]
            fix_plans = self.adjust_fix_plans(previous_attempt)
        
        print(f"  设计修复方案：{len(fix_plans)}个")
        for i, plan in enumerate(fix_plans, 1):
            print(f"    方案{i}: {plan['description']} (类型: {plan['type']})")
        
        return {"fix_plans": fix_plans}
    
    def step_3_2_fix_implementation(self, fix_plans):
        """步骤3.2: 修复方案实施"""
        print(f"执行步骤3.2: 实施第{self.current_attempt}轮修复...")
        
        implementations = []
        successful_count = 0
        
        for i, plan in enumerate(fix_plans, 1):
            print(f"  执行修复{i}: {plan['description']}")
            
            try:
                # 实施完整的真实修复（禁止简化、临时方案、伪代码）
                implementation = self.implement_complete_fix(plan)
                implementations.append(implementation)
                
                if implementation["success"]:
                    successful_count += 1
                    print(f"    ✅ 修复成功: {len(implementation['edit_results'])}个文件修改")
                else:
                    print(f"    ❌ 修复失败: {implementation['error_message']}")
                    
            except Exception as e:
                print(f"    ❌ 修复异常: {str(e)}")
                implementations.append({"success": False, "error": str(e)})
        
        success_rate = successful_count / len(fix_plans) if fix_plans else 0
        print(f"  实施统计: 成功率{success_rate*100:.1f}%")
        
        return {
            "implementations": implementations,
            "successful_count": successful_count,
            "total_plans": len(fix_plans),
            "success_rate": success_rate
        }
    
    def step_3_3_fix_verification(self, implementation_result):
        """步骤3.3: 修复效果验证"""
        print(f"执行步骤3.3: 验证第{self.current_attempt}轮修复...")
        
        # 系统功能验证
        verification_results = self.perform_comprehensive_verification()
        
        # 计算验证通过率
        pass_rate = self.calculate_verification_pass_rate(verification_results)
        
        print(f"  验证统计: 通过率{pass_rate*100:.1f}%")
        
        return {
            "verification_results": verification_results,
            "pass_rate": pass_rate,
            "details": verification_results
        }
    
    def step_3_4_result_evaluation(self, verification_result):
        """步骤3.4: 验证结果评估"""
        print(f"执行步骤3.4: 评估第{self.current_attempt}轮结果...")
        
        # 副作用检测
        side_effects = self.detect_side_effects()
        
        # 修复质量评估
        quality_assessment = self.assess_fix_quality(verification_result)
        
        print(f"  副作用检测: {len(side_effects)}个问题")
        print(f"  质量评估: {quality_assessment['score']:.2f}/1.0")
        
        return {
            "side_effects": side_effects,
            "side_effects_count": len(side_effects),
            "quality_assessment": quality_assessment
        }
    
    def check_stage3_to_stage4_gate(self):
        """检查阶段3→阶段4门禁条件"""
        # 门禁条件：验证通过率100% + 无副作用确认 + TodoWrite记录修复内容
        
        final_result = self.loop_results[-1] if self.loop_results else {}
        
        conditions = {
            "验证通过率100%": final_result.get("summary", {}).get("verification_pass_rate", 0) == 1.0,
            "无副作用确认": not final_result.get("summary", {}).get("side_effects_detected", True),
            "修复完整实施": final_result.get("summary", {}).get("implementation_success_rate", 0) == 1.0,
            "循环4步完整": all(f"3.{i}" in final_result.get("step_results", {}) for i in range(1, 5)),
            "TodoWrite记录": True  # 简化检查
        }
        
        print("🤔 阶段3→阶段4门禁检查:")
        for condition, passed in conditions.items():
            print(f"  {condition}: {'✅' if passed else '❌'}")
        
        can_proceed = all(conditions.values())
        failure_reasons = [cond for cond, passed in conditions.items() if not passed]
        
        return {
            "can_proceed": can_proceed,
            "conditions": conditions,
            "failure_reasons": failure_reasons
        }
```

### **STATE: STAGE4_SUMMARY - 阶段4：总结与发散优化**

```python
class Stage4SummaryState:
    """阶段4状态 - 总结与发散优化
    
    完整实现阶段4的所有内容：
    - 诊断过程总结
    - 解决方案总结  
    - 类似问题预防建议
    - 系统性优化建议
    - 发散性改进建议
    """
    
    def __init__(self, stage3_data):
        self.stage3_data = stage3_data
        self.state_name = "STAGE4_SUMMARY"
        self.summary_results = {}
    
    def execute(self):
        """执行阶段4：总结与发散优化"""
        print(f"📋 [{self.state_name}] 阶段4：总结与发散优化")
        print("⚠️ 强制要求：本阶段必须完成工作总结和发散性优化建议")
        
        # 更新Todo状态
        self.update_todo_status("in_progress")
        
        # 步骤4.1: 诊断过程总结
        step_4_1_result = self.step_4_1_diagnostic_summary()
        
        # 步骤4.2: 解决方案总结
        step_4_2_result = self.step_4_2_solution_summary()
        
        # 步骤4.3: 类似问题预防建议
        step_4_3_result = self.step_4_3_prevention_suggestions()
        
        # 步骤4.4: 系统性优化建议
        step_4_4_result = self.step_4_4_optimization_suggestions()
        
        # 步骤4.5: 发散性改进建议
        step_4_5_result = self.step_4_5_improvement_suggestions()
        
        # 整合所有总结结果
        self.summary_results = {
            "diagnostic_summary": step_4_1_result,
            "solution_summary": step_4_2_result,
            "prevention_suggestions": step_4_3_result,
            "optimization_suggestions": step_4_4_result,
            "improvement_suggestions": step_4_5_result
        }
        
        # 检查阶段4完成条件
        completion_check = self.check_stage4_completion()
        
        if completion_check["can_complete"]:
            next_state = "COMPLETED"
            print("✅ 阶段4完成条件满足，流程成功完成")
        else:
            next_state = "FAILED"
            print("❌ 阶段4完成条件不满足，总结不完整")
        
        self.update_todo_status("completed")
        
        return {
            "success": completion_check["can_complete"],
            "summary_results": self.summary_results,
            "completion_check": completion_check,
            "next_state": next_state
        }
    
    def step_4_1_diagnostic_summary(self):
        """步骤4.1: 诊断过程总结"""
        print("执行步骤4.1: 诊断过程总结...")
        
        # 4.1.1 诊断流程回顾
        flow_review = self.review_diagnostic_flow()
        
        # 4.1.2 关键发现总结
        key_findings = self.summarize_key_findings()
        
        # 4.1.3 方法论评估
        methodology_assessment = self.assess_methodology()
        
        return {
            "flow_review": flow_review,
            "key_findings": key_findings,
            "methodology_assessment": methodology_assessment
        }
    
    def step_4_2_solution_summary(self):
        """步骤4.2: 解决方案总结"""
        print("执行步骤4.2: 解决方案总结...")
        
        # 4.2.1 修复方案回顾
        fix_review = self.review_fix_solutions()
        
        # 4.2.2 修复效果评估
        effect_assessment = self.assess_fix_effects()
        
        # 4.2.3 替代方案分析
        alternative_analysis = self.analyze_alternatives()
        
        return {
            "fix_review": fix_review,
            "effect_assessment": effect_assessment,
            "alternative_analysis": alternative_analysis
        }
    
    def check_stage4_completion(self):
        """检查阶段4完成条件"""
        # 门禁条件：总结报告完整 + 优化建议具体 + TodoWrite标记全部completed
        
        conditions = {
            "总结报告完整": all(key in self.summary_results for key in [
                "diagnostic_summary", "solution_summary", "optimization_suggestions"
            ]),
            "优化建议具体": len(self.summary_results.get("optimization_suggestions", {}).get("suggestions", [])) >= 3,
            "预防措施完备": len(self.summary_results.get("prevention_suggestions", {}).get("measures", [])) >= 2,
            "发散性改进充分": len(self.summary_results.get("improvement_suggestions", {}).get("improvements", [])) >= 2,
            "TodoWrite全部完成": True  # 简化检查
        }
        
        print("🤔 阶段4完成条件检查:")
        for condition, passed in conditions.items():
            print(f"  {condition}: {'✅' if passed else '❌'}")
        
        can_complete = all(conditions.values())
        failure_reasons = [cond for cond, passed in conditions.items() if not passed]
        
        return {
            "can_complete": can_complete,
            "conditions": conditions,
            "failure_reasons": failure_reasons
        }
```

