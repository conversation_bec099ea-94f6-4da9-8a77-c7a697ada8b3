#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库管理器
替换原有的JSON文件存储
"""

import mysql.connector
from mysql.connector import pooling
import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

class MySQLManager:
    def __init__(self):
        self.config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER', 'shuimu_server'),
            'password': os.getenv('DB_PASSWORD', 'dyj217'),
            'database': os.getenv('DB_NAME', 'shuimu_course_server'),
            'charset': 'utf8mb4',
            'autocommit': False  # 修复：关闭自动提交，使用显式事务管理
        }
        
        # 创建连接池
        self.pool = mysql.connector.pooling.MySQLConnectionPool(
            pool_name='shuimu_pool',
            pool_size=15,                    # 优化：从5增加到15
            pool_reset_session=True,
            **self.config
        )
    
    def get_connection(self):
        """获取数据库连接"""
        return self.pool.get_connection()
    
    def execute_query(self, query: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor(dictionary=True)
            cursor.execute(query, params or ())
            result = cursor.fetchall()
            cursor.close()
            return result
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute(query, params or ())
            affected_rows = cursor.rowcount
            conn.commit()  # 显式提交事务
            return affected_rows
        except Exception as e:
            if conn:
                conn.rollback()  # 发生错误时回滚
            print(f"❌ 数据库更新失败: {e}")
            print(f"SQL: {query}")
            print(f"参数: {params}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    # ========== 用户数据相关方法 ==========
    
    def get_user_progress(self, user_id: str, video_id: str) -> Optional[Dict]:
        """获取用户观看进度"""
        result = self.execute_query("""
            SELECT * FROM user_progress 
            WHERE user_id = %s AND video_id = %s
        """, (user_id, video_id))
        
        if result:
            progress = result[0]
            return {
                'video_id': progress['video_id'],
                'position': progress['position'],
                'duration': progress['duration'],
                'watchCount': progress['watch_count'],
                'progress': float(progress['progress_percentage']),
                'lastWatchedAt': progress['last_watched_at'].isoformat() if progress['last_watched_at'] else None
            }
        
        return {
            'video_id': video_id,
            'position': 0,
            'duration': 0,
            'watchCount': 0,
            'progress': 0.0,
            'lastWatchedAt': None
        }
    
    def update_user_progress(self, user_id: str, video_id: str, position: int, duration: int, watch_count: int = None) -> bool:
        """更新用户观看进度"""
        progress_percentage = (position / duration * 100) if duration > 0 else 0
        
        # 如果没有提供观看次数，先查询当前值
        if watch_count is None:
            current = self.get_user_progress(user_id, video_id)
            watch_count = current.get('watchCount', 0)
            if position > current.get('position', 0):  # 只有进度前进时才增加观看次数
                watch_count += 1
        
        affected_rows = self.execute_update("""
            INSERT INTO user_progress (user_id, video_id, position, duration, watch_count, progress_percentage, last_watched_at)
            VALUES (%s, %s, %s, %s, %s, %s, NOW())
            ON DUPLICATE KEY UPDATE
            position = VALUES(position),
            duration = VALUES(duration),
            watch_count = VALUES(watch_count),
            progress_percentage = VALUES(progress_percentage),
            last_watched_at = VALUES(last_watched_at),
            updated_at = NOW()
        """, (user_id, video_id, position, duration, watch_count, progress_percentage))
        
        return affected_rows > 0
    
    def get_user_all_progress(self, user_id: str) -> List[Dict]:
        """获取用户所有观看进度"""
        result = self.execute_query("""
            SELECT * FROM user_progress WHERE user_id = %s
        """, (user_id,))

        progress_list = []
        for progress in result:
            progress_list.append({
                'video_id': progress['video_id'],
                'position': progress['position'],
                'duration': progress['duration'],
                'watchCount': progress['watch_count'],
                'progress': float(progress['progress_percentage']),
                'lastWatchedAt': progress['last_watched_at'].isoformat() if progress['last_watched_at'] else None
            })

        return progress_list

    def get_user_complete_progress(self, user_id: str) -> List[Dict]:
        """获取用户完整观看进度 - 包含所有已购买和免费视频"""
        # 获取用户已购买的系列和分类
        purchased_items = self.execute_query("""
            SELECT DISTINCT purchased_entity_type, purchased_entity_id FROM user_purchases
            WHERE user_id = %s AND status IN ('is_active', 'completed')
        """, (user_id,))

        # 构建已购买项目的条件
        purchased_series = []
        purchased_categories = []
        for item in purchased_items:
            if item['purchased_entity_type'] == 'series':
                purchased_series.append(item['purchased_entity_id'])
            elif item['purchased_entity_type'] == 'category':
                purchased_categories.append(item['purchased_entity_id'])

        # 构建查询条件：免费视频 + 已购买系列的视频 + 已购买分类的视频
        where_conditions = ["s.is_free = 1"]  # 免费视频
        params = [user_id]

        if purchased_series:
            placeholders = ','.join(['%s'] * len(purchased_series))
            where_conditions.append(f"s.id IN ({placeholders})")
            params.extend(purchased_series)

        if purchased_categories:
            placeholders = ','.join(['%s'] * len(purchased_categories))
            where_conditions.append(f"c.id IN ({placeholders})")
            params.extend(purchased_categories)

        where_clause = " OR ".join(where_conditions)

        # 查询所有可访问的视频及其进度 - 使用COLLATE解决字符集冲突
        result = self.execute_query(f"""
            SELECT
                v.id as video_id,
                v.title as video_title,
                v.duration as video_duration,
                c.id as category_id,
                c.title as category_title,
                s.id as series_id,
                s.title as series_title,
                COALESCE(up.position, 0) as position,
                COALESCE(up.watch_count, 0) as watch_count,
                COALESCE(up.progress_percentage, 0) as progress_percentage,
                up.last_watched_at
            FROM videos v
            JOIN categories c ON v.category_id COLLATE utf8mb4_unicode_ci = c.id COLLATE utf8mb4_unicode_ci
            JOIN series s ON c.series_id COLLATE utf8mb4_unicode_ci = s.id COLLATE utf8mb4_unicode_ci
            LEFT JOIN user_progress up ON v.id COLLATE utf8mb4_unicode_ci = up.video_id COLLATE utf8mb4_unicode_ci AND up.user_id = %s
            WHERE ({where_clause}) AND s.is_published = 1
            ORDER BY s.created_at DESC, c.order_index, v.order_index
        """, params)

        progress_list = []
        for row in result:
            progress_list.append({
                'video_id': row['video_id'],
                'videoTitle': row['video_title'],
                'videoDuration': row['video_duration'],
                'category_id': row['category_id'],
                'categoryTitle': row['category_title'],
                'series_id': row['series_id'],
                'seriesTitle': row['series_title'],
                'position': row['position'],
                'watchCount': row['watch_count'],
                'progressPercentage': int(row['progress_percentage']),  # 整数百分比
                'lastWatchedAt': row['last_watched_at'].isoformat() if row['last_watched_at'] else None
            })

        return progress_list
    
    def get_user_cache_status(self, user_id: str, video_id: str) -> Dict:
        """获取用户视频缓存状态"""
        result = self.execute_query("""
            SELECT * FROM user_cache 
            WHERE user_id = %s AND video_id = %s
        """, (user_id, video_id))
        
        if result:
            cache = result[0]
            return {
                'video_id': cache['video_id'],
                'isCached': bool(cache['is_cached']),
                'localPath': cache['local_path'],
                'size': cache['file_size'],
                'updated_at': cache['cached_at'].isoformat() if cache['cached_at'] else None
            }
        
        return {
            'video_id': video_id,
            'isCached': False,
            'localPath': None,
            'size': 0,
            'updated_at': None
        }
    
    def update_user_cache_status(self, user_id: str, video_id: str, is_cached: bool, local_path: str = None, file_size: int = 0) -> bool:
        """更新用户视频缓存状态"""
        if is_cached:
            # 先获取视频详细信息
            video_info = self.execute_query("""
                SELECT v.title as video_title, c.title as category_title, s.title as series_title
                FROM videos v
                JOIN categories c ON v.category_id = c.id
                JOIN series s ON c.series_id = s.id
                WHERE v.id = %s
            """, (video_id,))

            if video_info:
                video_data = video_info[0]
                affected_rows = self.execute_update("""
                    INSERT INTO user_cache (user_id, video_id, is_cached, local_path, file_size,
                                          video_title, series_title, category_title, cached_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, NOW())
                    ON DUPLICATE KEY UPDATE
                    is_cached = VALUES(is_cached),
                    local_path = VALUES(local_path),
                    file_size = VALUES(file_size),
                    video_title = VALUES(video_title),
                    series_title = VALUES(series_title),
                    category_title = VALUES(category_title),
                    cached_at = VALUES(cached_at),
                    updated_at = NOW()
                """, (user_id, video_id, is_cached, local_path, file_size,
                      video_data['video_title'], video_data['series_title'], video_data['category_title']))
            else:
                return False
        else:
            # 删除缓存记录
            affected_rows = self.execute_update("""
                DELETE FROM user_cache WHERE user_id = %s AND video_id = %s
            """, (user_id, video_id))

        return affected_rows > 0
    
    def get_user_cache_list(self, user_id: str) -> List[Dict]:
        """获取用户所有缓存列表"""
        result = self.execute_query("""
            SELECT * FROM user_cache WHERE user_id = %s AND is_cached = TRUE
        """, (user_id,))

        cache_list = []
        for cache in result:
            cache_list.append({
                'video_id': cache['video_id'],
                'videoTitle': cache.get('video_title', ''),
                'seriesTitle': cache.get('series_title', ''),
                'categoryTitle': cache.get('category_title', ''),
                'isCached': bool(cache['is_cached']),
                'localPath': cache['local_path'],
                'fileSize': cache['file_size'],
                'cachedAt': cache['cached_at'].isoformat() if cache['cached_at'] else None
            })

        return cache_list

    def get_user_complete_cache_status(self, user_id: str) -> List[Dict]:
        """获取用户所有视频的缓存状态 - 包含所有已购买和免费视频"""
        # 获取用户已购买的系列和分类
        purchased_items = self.execute_query("""
            SELECT DISTINCT purchased_entity_type, purchased_entity_id FROM user_purchases
            WHERE user_id = %s AND status IN ('is_active', 'completed')
        """, (user_id,))

        # 构建已购买项目的条件
        purchased_series = []
        purchased_categories = []
        for item in purchased_items:
            if item['purchased_entity_type'] == 'series':
                purchased_series.append(item['purchased_entity_id'])
            elif item['purchased_entity_type'] == 'category':
                purchased_categories.append(item['purchased_entity_id'])

        # 构建查询条件：免费视频 + 已购买系列的视频 + 已购买分类的视频
        where_conditions = ["s.is_free = 1"]  # 免费视频
        params = [user_id]

        if purchased_series:
            placeholders = ','.join(['%s'] * len(purchased_series))
            where_conditions.append(f"s.id IN ({placeholders})")
            params.extend(purchased_series)

        if purchased_categories:
            placeholders = ','.join(['%s'] * len(purchased_categories))
            where_conditions.append(f"c.id IN ({placeholders})")
            params.extend(purchased_categories)

        where_clause = " OR ".join(where_conditions)

        # 查询所有可访问的视频及其缓存状态
        result = self.execute_query(f"""
            SELECT
                v.id as video_id,
                v.title as video_title,
                c.id as category_id,
                c.title as category_title,
                s.id as series_id,
                s.title as series_title,
                CASE WHEN uc.is_cached = 1 THEN 1 ELSE 0 END as is_cached,
                uc.local_path,
                uc.file_size,
                uc.cached_at
            FROM videos v
            JOIN categories c ON v.category_id COLLATE utf8mb4_unicode_ci = c.id COLLATE utf8mb4_unicode_ci
            JOIN series s ON c.series_id COLLATE utf8mb4_unicode_ci = s.id COLLATE utf8mb4_unicode_ci
            LEFT JOIN user_cache uc ON v.id COLLATE utf8mb4_unicode_ci = uc.video_id COLLATE utf8mb4_unicode_ci AND uc.user_id = %s
            WHERE ({where_clause}) AND s.is_published = 1
            ORDER BY s.created_at DESC, c.order_index, v.order_index
        """, params)

        cache_list = []
        for row in result:
            cache_list.append({
                'video_id': row['video_id'],
                'videoTitle': row['video_title'],
                'category_id': row['category_id'],
                'categoryTitle': row['category_title'],
                'series_id': row['series_id'],
                'seriesTitle': row['series_title'],
                'isCached': bool(row['is_cached']),
                'localPath': row['local_path'] or '',
                'fileSize': row['file_size'] or 0,
                'cachedAt': row['cached_at'].isoformat() if row['cached_at'] else None
            })

        return cache_list
    
    def get_user_favorites(self, user_id: str) -> Dict:
        """获取用户收藏列表"""
        result = self.execute_query("""
            SELECT * FROM user_favorites WHERE user_id = %s ORDER BY favorited_at DESC
        """, (user_id,))
        
        favorites = {'videos': [], 'categories': [], 'series': []}
        for fav in result:
            # 🔧 修复字段映射：数据库使用favorited_entity_type/favorited_entity_id，代码期望favorited_entity_type/favorited_entity_id
            entity_type = fav.get('favorited_entity_type', fav.get('favorited_entity_type', 'video'))
            entity_id = fav.get('favorited_entity_id', fav.get('favorited_entity_id', ''))
            
            favorites[f"{entity_type}s"].append({
                'id': entity_id,
                'type': entity_type,
                'favoritedAt': fav['favorited_at'].isoformat() if fav.get('favorited_at') else None
            })
        
        return favorites
    
    def update_user_favorite(self, user_id: str, favorited_entity_type: str, favorited_entity_id: str, action: str) -> bool:
        """
        更新用户收藏 - 严格遵循字段命名规范
        
        Args:
            user_id: 用户UUID
            favorited_entity_type: 收藏实体类型（符合见名知意原则）
            favorited_entity_id: 收藏实体UUID（符合见名知意原则）
            action: 操作类型（add/remove）
        """
        if action == 'add':
            affected_rows = self.execute_update("""
                INSERT IGNORE INTO user_favorites (user_id, item_type, item_id)
                VALUES (%s, %s, %s)
            """, (user_id, favorited_entity_type, favorited_entity_id))
        elif action == 'remove':
            affected_rows = self.execute_update("""
                DELETE FROM user_favorites 
                WHERE user_id = %s AND item_type = %s AND item_id = %s
            """, (user_id, favorited_entity_type, favorited_entity_id))
        else:
            return False
        
        return affected_rows > 0

    def get_user_video_favorites_status(self, user_id: str) -> List[Dict]:
        """获取用户所有视频的收藏状态"""
        # 获取用户已购买的系列和分类
        purchased_items = self.execute_query("""
            SELECT DISTINCT purchased_entity_type, purchased_entity_id FROM user_purchases
            WHERE user_id = %s AND status IN ('is_active', 'completed')
        """, (user_id,))

        # 构建已购买项目的条件
        purchased_series = []
        purchased_categories = []
        for item in purchased_items:
            if item['purchased_entity_type'] == 'series':
                purchased_series.append(item['purchased_entity_id'])
            elif item['purchased_entity_type'] == 'category':
                purchased_categories.append(item['purchased_entity_id'])

        # 构建查询条件：免费视频 + 已购买系列的视频 + 已购买分类的视频
        where_conditions = ["s.is_free = 1"]  # 免费视频
        params = [user_id]

        if purchased_series:
            placeholders = ','.join(['%s'] * len(purchased_series))
            where_conditions.append(f"s.id IN ({placeholders})")
            params.extend(purchased_series)

        if purchased_categories:
            placeholders = ','.join(['%s'] * len(purchased_categories))
            where_conditions.append(f"c.id IN ({placeholders})")
            params.extend(purchased_categories)

        where_clause = " OR ".join(where_conditions)

        # 查询所有可访问的视频及其收藏状态 - 使用COLLATE解决字符集冲突
        result = self.execute_query(f"""
            SELECT
                v.id as video_id,
                v.title as video_title,
                c.id as category_id,
                c.title as category_title,
                s.id as series_id,
                s.title as series_title,
                CASE WHEN uf.id IS NOT NULL THEN 1 ELSE 0 END as is_favorited,
                uf.favorited_at
            FROM videos v
            JOIN categories c ON v.category_id COLLATE utf8mb4_unicode_ci = c.id COLLATE utf8mb4_unicode_ci
            JOIN series s ON c.series_id COLLATE utf8mb4_unicode_ci = s.id COLLATE utf8mb4_unicode_ci
            LEFT JOIN user_favorites uf ON v.id COLLATE utf8mb4_unicode_ci = uf.item_id COLLATE utf8mb4_unicode_ci AND uf.user_id = %s AND uf.item_type = 'video'
            WHERE ({where_clause}) AND s.is_published = 1
            ORDER BY s.created_at DESC, c.order_index, v.order_index
        """, params)

        favorites_list = []
        for row in result:
            favorites_list.append({
                'video_id': row['video_id'],
                'videoTitle': row['video_title'],
                'category_id': row['category_id'],
                'categoryTitle': row['category_title'],
                'series_id': row['series_id'],
                'seriesTitle': row['series_title'],
                'isFavorited': bool(row['is_favorited']),
                'favoritedAt': row['favorited_at'].isoformat() if row['favorited_at'] else None
            })

        return favorites_list

    def toggle_user_video_favorite(self, user_id: str, video_id: str) -> Dict[str, Any]:
        """切换用户视频收藏状态"""
        # 检查当前收藏状态
        current_favorite = self.execute_query("""
            SELECT id FROM user_favorites
            WHERE user_id = %s AND item_id = %s AND item_type = 'video'
        """, (user_id, video_id))

        if current_favorite:
            # 已收藏，取消收藏
            affected_rows = self.execute_update("""
                DELETE FROM user_favorites
                WHERE user_id = %s AND item_id = %s AND item_type = 'video'
            """, (user_id, video_id))
            action = 'removed'
            is_favorited = False
        else:
            # 未收藏，添加收藏
            # 先获取视频详细信息
            video_info = self.execute_query("""
                SELECT v.title as video_title, c.title as category_title, s.title as series_title
                FROM videos v
                JOIN categories c ON v.category_id = c.id
                JOIN series s ON c.series_id = s.id
                WHERE v.id = %s
            """, (video_id,))

            if video_info:
                video_data = video_info[0]
                affected_rows = self.execute_update("""
                    INSERT INTO user_favorites (user_id, item_type, item_id, video_title, series_title, category_title)
                    VALUES (%s, 'video', %s, %s, %s, %s)
                """, (user_id, video_id, video_data['video_title'], video_data['series_title'], video_data['category_title']))
                action = 'added'
                is_favorited = True
            else:
                return {'success': False, 'message': '视频不存在'}

        return {
            'success': affected_rows > 0,
            'action': action,
            'isFavorited': is_favorited,
            'video_id': video_id
        }

    def get_user_settings(self, user_id: str) -> Dict:
        """获取用户设置"""
        result = self.execute_query("""
            SELECT setting_key, setting_value FROM user_settings WHERE user_id = %s
        """, (user_id,))
        
        settings = {}
        for setting in result:
            key = setting['setting_key']
            value = setting['setting_value']
            
            # 尝试解析JSON值
            try:
                if value.startswith('{') or value.startswith('['):
                    value = json.loads(value)
                elif value.lower() in ['true', 'false']:
                    value = value.lower() == 'true'
                elif value.isdigit():
                    value = int(value)
                elif '.' in value and value.replace('.', '').isdigit():
                    value = float(value)
            except:
                pass  # 保持原始字符串值
            
            settings[key] = value
        
        return settings
    
    def update_user_settings(self, user_id: str, settings: Dict) -> bool:
        """更新用户设置"""
        success = True
        for key, value in settings.items():
            # 转换值为字符串存储
            if isinstance(value, (dict, list)):
                value_str = json.dumps(value, ensure_ascii=False)
            elif isinstance(value, bool):
                value_str = str(value).lower()
            else:
                value_str = str(value)
            
            affected_rows = self.execute_update("""
                INSERT INTO user_settings (user_id, setting_key, setting_value)
                VALUES (%s, %s, %s)
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                updated_at = NOW()
            """, (user_id, key, value_str))
            
            if affected_rows == 0:
                success = False
        
        return success
    
    def get_user_purchases(self, user_id: str) -> Dict:
        """获取用户购买记录 - 🎯 增强版本：自动包含免费分类"""
        try:
            # 使用增强版查询方法
            from .mysql_manager_enhanced import get_user_purchases_with_free_categories
            return get_user_purchases_with_free_categories(self, user_id)
            
        except Exception as e:
            print(f"❌ 获取用户购买记录失败: {e}")
            return {'success': False, 'data': [], 'message': f'查询购买记录失败: {str(e)}'}
    
    def add_user_purchase(self, user_id: str, purchased_entity_type: str, purchased_entity_id: str, purchase_price: float) -> bool:
        """
        添加用户购买记录 - 严格遵循字段命名规范
        
        Args:
            user_id: 用户UUID
            purchased_entity_type: 购买实体类型（符合见名知意原则）
            purchased_entity_id: 购买实体UUID（符合见名知意原则）
            purchase_price: 购买价格
        """
        affected_rows = self.execute_update("""
            INSERT INTO user_purchases (user_id, purchased_entity_type, purchased_entity_id, purchase_price, status)
            VALUES (%s, %s, %s, %s, 'is_active')
            ON DUPLICATE KEY UPDATE
            purchase_price = VALUES(purchase_price),
            status = VALUES(status)
        """, (user_id, purchased_entity_type, purchased_entity_id, purchase_price))

        return affected_rows > 0

    # ========== 原有方法保持不变 ==========
    
    # 系列相关方法
    def get_all_series(self) -> List[Dict]:
        """获取所有系列"""
        series_data = self.execute_query("""
            SELECT s.*, 
                   COUNT(DISTINCT c.id) as category_count,
                   COUNT(DISTINCT v.id) as video_count
            FROM series s
            LEFT JOIN categories c ON s.id = c.series_id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE s.is_published = 1
            GROUP BY s.id
            ORDER BY s.created_at DESC
        """)
        
        # 为每个系列添加分类ID列表
        for series in series_data:
            categories = self.execute_query("""
                SELECT id FROM categories WHERE series_id = %s ORDER BY order_index
            """, (series['id'],))
            series['category_ids'] = [cat['id'] for cat in categories]
            
            # 转换数据类型以匹配原有API
            series['price'] = float(series['price']) if series['price'] else 0.0
            # 🔥 修复：使用数据库中的is_free字段，不再根据价格判断
            series['is_free'] = bool(series.get('is_free', False))
            # 🔥 修复：使用数据库中的isPackage字段
            series['is_package'] = bool(series.get('is_package', False))
            series['defaultExpanded'] = False  # 默认值
        
        return series_data
    
    def get_series_by_id(self, series_id: str) -> Optional[Dict]:
        """根据ID获取系列"""
        result = self.execute_query("""
            SELECT * FROM series WHERE id = %s
        """, (series_id,))
        
        if result:
            series = result[0]
            # 添加分类ID列表
            categories = self.execute_query("""
                SELECT id FROM categories WHERE series_id = %s ORDER BY order_index
            """, (series_id,))
            series['category_ids'] = [cat['id'] for cat in categories]
            return series
        
        return None
    
    def update_series(self, series_id: str, data: Dict) -> bool:
        """更新系列"""
        affected_rows = self.execute_update("""
            UPDATE series 
            SET title = %s, description = %s, price = %s, is_free = %s, is_published = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('price', 0),
            data.get('is_free', False),
            data.get('is_published', True),
            series_id
        ))
        
        return affected_rows > 0
    
    # 分类相关方法
    def get_all_categories(self) -> List[Dict]:
        """获取所有分类"""
        categories_data = self.execute_query("""
            SELECT c.*,
                   s.title as series_title,
                   COUNT(v.id) as video_count
            FROM categories c
            LEFT JOIN series s ON c.series_id = s.id
            LEFT JOIN videos v ON c.id = v.category_id
            WHERE s.is_published = 1
            GROUP BY c.id
            ORDER BY c.series_id, c.order_index
        """)
        
        # 为每个分类添加视频列表
        for category in categories_data:
            videos = self.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category['id'],))
            
            # 转换视频数据格式
            category['videos'] = []
            for video in videos:
                video_dict = {
                    'id': video['id'],
                    'title': video['title'],
                    'description': video['description'],
                    'duration': video['duration'],
                    'cloudUrl': video['video_url'],
                    'localPath': '',  # 暂时为空
                    'playCount': 0,   # 暂时为0
                    'category_id': video['category_id']
                }
                category['videos'].append(video_dict)
            
            # 转换分类数据格式
            category['price'] = float(category['price']) if category['price'] else 0.0
            category['is_free'] = category['price'] == 0
            category['isPurchased'] = False  # 默认值
            category['defaultExpanded'] = False  # 默认值
            category['totalVideos'] = category['video_count']
            category['progress'] = 0.0  # 默认值
            category['watchCount'] = 0  # 默认值
        
        return categories_data
    
    def get_category_by_id(self, category_id: str) -> Optional[Dict]:
        """根据ID获取分类"""
        result = self.execute_query("""
            SELECT * FROM categories WHERE id = %s
        """, (category_id,))
        
        if result:
            category = result[0]
            # 添加视频列表
            videos = self.execute_query("""
                SELECT * FROM videos WHERE category_id = %s ORDER BY order_index
            """, (category_id,))
            category['videos'] = videos
            return category
        
        return None
    
    def update_category(self, category_id: str, data: Dict) -> bool:
        """更新分类"""
        print(f"🔄 MySQL更新分类: {category_id}")
        print(f"📊 更新数据: {data}")

        # 🎯 关键修复：添加series_id和order_index字段的更新
        affected_rows = self.execute_update("""
            UPDATE categories
            SET title = %s, description = %s, price = %s, series_id = %s, order_index = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('price', 0),
            data.get('series_id'),  # 🎯 添加series_id更新
            data.get('order_index', 0),  # 🎯 添加order_index更新
            category_id
        ))

        print(f"✅ MySQL分类更新完成: 影响行数={affected_rows}")
        return affected_rows > 0
    
    def delete_category(self, category_id: str) -> bool:
        """删除分类（仅删除分类本身，不删除视频）"""
        affected_rows = self.execute_update("""
            DELETE FROM categories WHERE id = %s
        """, (category_id,))
        
        return affected_rows > 0
    
    def cascade_delete_category(self, category_id: str) -> Dict[str, Any]:
        """级联删除分类及其所有相关视频"""
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 获取分类信息
            cursor.execute("SELECT * FROM categories WHERE id = %s", (category_id,))
            category = cursor.fetchone()
            
            if not category:
                conn.rollback()
                return {
                    'success': False,
                    'message': '分类不存在',
                    'data': None
                }
            
            # 2. 获取该分类下的所有视频
            cursor.execute("SELECT id FROM videos WHERE category_id = %s", (category_id,))
            videos = cursor.fetchall()
            video_ids = [video['id'] for video in videos]
            deleted_videos_count = len(video_ids)
            
            # 3. 删除视频相关的依赖数据
            if video_ids:
                video_ids_placeholders = ','.join(['%s'] * len(video_ids))
                
                # 删除用户观看进度
                cursor.execute(f"DELETE FROM user_progress WHERE video_id IN ({video_ids_placeholders})", video_ids)
                
                # 删除用户收藏记录
                cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'video' AND item_id IN ({video_ids_placeholders})", video_ids)
                
                # 删除用户缓存记录
                cursor.execute(f"DELETE FROM user_cache WHERE video_id IN ({video_ids_placeholders})", video_ids)
                
                # 删除视频本身
                cursor.execute(f"DELETE FROM videos WHERE id IN ({video_ids_placeholders})", video_ids)
            
            # 4. 删除分类
            cursor.execute("DELETE FROM categories WHERE id = %s", (category_id,))
            
            # 5. 更新系列统计信息（视频数量）
            series_id = category['series_id']
            cursor.execute("""
                UPDATE series 
                SET updated_at = NOW()
                WHERE id = %s
            """, (series_id,))
            
            # 提交事务
            conn.commit()
            
            return {
                'success': True,
                'message': '分类及所有相关视频删除成功',
                'data': {
                    'deleted_category_id': category_id,
                    'deleted_videos_count': deleted_videos_count,
                    'updated_series_id': series_id
                }
            }
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 级联删除分类失败: {str(e)}")
            return {
                'success': False,
                'message': f'级联删除失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    # 视频相关方法
    def get_all_videos(self) -> List[Dict]:
        """获取所有视频"""
        videos_data = self.execute_query("""
            SELECT v.*,
                   c.title as category_title,
                   c.series_id,
                   s.title as series_title
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE s.is_published = 1
            ORDER BY s.created_at DESC, c.order_index, v.order_index
        """)
        
        # 转换数据格式以匹配原有API
        for video in videos_data:
            video['cloudUrl'] = video['video_url']
            video['localPath'] = ''  # 暂时为空
            video['playCount'] = 0   # 暂时为0
            video['category_id'] = video['category_id']
            # 🎯 添加系列和分类信息
            video['series_id'] = video.get('series_id', '')
            video['seriesTitle'] = video.get('series_title', '')
            video['categoryTitle'] = video.get('category_title', '')
        
        return videos_data
    
    def get_video_by_id(self, video_id: str) -> Optional[Dict]:
        """根据ID获取视频（包含系列和分类信息）"""
        result = self.execute_query("""
            SELECT
                v.*,
                c.title as category_title,
                c.series_id,
                s.title as series_title
            FROM videos v
            LEFT JOIN categories c ON v.category_id = c.id
            LEFT JOIN series s ON c.series_id = s.id
            WHERE v.id = %s
        """, (video_id,))

        if result:
            video = result[0]
            # 🎯 添加系列和分类信息
            video['series_id'] = video.get('series_id', '')
            video['seriesTitle'] = video.get('series_title', '')
            video['categoryTitle'] = video.get('category_title', '')
            return video

        return None
    
    def update_video(self, video_id: str, data: Dict) -> bool:
        """更新视频"""
        affected_rows = self.execute_update("""
            UPDATE videos 
            SET title = %s, description = %s, video_url = %s, duration = %s, updated_at = NOW()
            WHERE id = %s
        """, (
            data.get('title'),
            data.get('description'),
            data.get('video_url'),
            data.get('duration', 0),
            video_id
        ))
        
        return affected_rows > 0
    
    # ========== 批量删除方法 - 四层同步乐观更新规则性能优化 ==========
    
    def batch_delete_series(self, series_ids: List[str], cascade: bool = False) -> Dict[str, Any]:
        """批量删除系列"""
        if not series_ids:
            return {
                'success': False,
                'message': '没有提供要删除的系列ID',
                'data': None
            }
        
        # 限制批量大小
        if len(series_ids) > 100:
            return {
                'success': False,
                'message': '批量删除系列数量不能超过100个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证系列存在性并获取统计信息
            series_ids_placeholders = ','.join(['%s'] * len(series_ids))
            cursor.execute(f"""
                SELECT id, title FROM series WHERE id IN ({series_ids_placeholders})
            """, series_ids)
            existing_series = cursor.fetchall()
            
            existing_ids = [series['id'] for series in existing_series]
            failed_ids = [sid for sid in series_ids if sid not in existing_ids]
            
            if not existing_ids:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有找到有效的系列ID',
                    'data': {
                        'total_requested': len(series_ids),
                        'successfully_deleted': 0,
                        'failed_deletions': len(series_ids),
                        'deleted_ids': [],
                        'failed_ids': series_ids
                    }
                }
            
            deleted_categories_count = 0
            deleted_videos_count = 0
            
            if cascade:
                # 2. 级联删除：获取所有要删除的分类和视频
                existing_ids_placeholders = ','.join(['%s'] * len(existing_ids))
                
                # 获取所有相关分类
                cursor.execute(f"""
                    SELECT id FROM categories WHERE series_id IN ({existing_ids_placeholders})
                """, existing_ids)
                categories = cursor.fetchall()
                category_ids = [cat['id'] for cat in categories]
                
                if category_ids:
                    category_ids_placeholders = ','.join(['%s'] * len(category_ids))
                    
                    # 获取所有相关视频
                    cursor.execute(f"""
                        SELECT id FROM videos WHERE category_id IN ({category_ids_placeholders})
                    """, category_ids)
                    videos = cursor.fetchall()
                    video_ids = [video['id'] for video in videos]
                    
                    if video_ids:
                        video_ids_placeholders = ','.join(['%s'] * len(video_ids))
                        
                        # 删除视频相关的依赖数据
                        cursor.execute(f"DELETE FROM user_progress WHERE video_id IN ({video_ids_placeholders})", video_ids)
                        cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'video' AND item_id IN ({video_ids_placeholders})", video_ids)
                        cursor.execute(f"DELETE FROM user_cache WHERE video_id IN ({video_ids_placeholders})", video_ids)
                        
                        # 删除视频
                        cursor.execute(f"DELETE FROM videos WHERE id IN ({video_ids_placeholders})", video_ids)
                        deleted_videos_count = cursor.rowcount
                    
                    # 删除分类相关的依赖数据
                    cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'category' AND item_id IN ({category_ids_placeholders})", category_ids)
                    
                    # 删除分类
                    cursor.execute(f"DELETE FROM categories WHERE id IN ({category_ids_placeholders})", category_ids)
                    deleted_categories_count = cursor.rowcount
            
            # 3. 删除系列相关的依赖数据
            cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'series' AND item_id IN ({existing_ids_placeholders})", existing_ids)
            cursor.execute(f"DELETE FROM user_purchases WHERE purchased_entity_type = 'series' AND purchased_entity_id IN ({existing_ids_placeholders})", existing_ids)
            
            # 4. 删除系列本身
            cursor.execute(f"DELETE FROM series WHERE id IN ({existing_ids_placeholders})", existing_ids)
            series_deleted_count = cursor.rowcount
            
            # 提交事务
            conn.commit()
            
            return {
                'success': True,
                'message': '批量删除操作完成',
                'data': {
                    'total_requested': len(series_ids),
                    'successfully_deleted': series_deleted_count,
                    'failed_deletions': len(failed_ids),
                    'deleted_ids': existing_ids,
                    'failed_ids': failed_ids,
                    'cascade_stats': {
                        'deleted_categories': deleted_categories_count,
                        'deleted_videos': deleted_videos_count
                    } if cascade else None
                }
            }
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量删除系列失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量删除失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def batch_delete_categories(self, category_ids: List[str], cascade: bool = False) -> Dict[str, Any]:
        """批量删除分类"""
        if not category_ids:
            return {
                'success': False,
                'message': '没有提供要删除的分类ID',
                'data': None
            }
        
        # 限制批量大小
        if len(category_ids) > 200:
            return {
                'success': False,
                'message': '批量删除分类数量不能超过200个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证分类存在性
            category_ids_placeholders = ','.join(['%s'] * len(category_ids))
            cursor.execute(f"""
                SELECT id, title FROM categories WHERE id IN ({category_ids_placeholders})
            """, category_ids)
            existing_categories = cursor.fetchall()
            
            existing_ids = [cat['id'] for cat in existing_categories]
            failed_ids = [cid for cid in category_ids if cid not in existing_ids]
            
            if not existing_ids:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有找到有效的分类ID',
                    'data': {
                        'total_requested': len(category_ids),
                        'successfully_deleted': 0,
                        'failed_deletions': len(category_ids),
                        'deleted_ids': [],
                        'failed_ids': category_ids
                    }
                }
            
            deleted_videos_count = 0
            
            if cascade:
                # 2. 级联删除：获取所有要删除的视频
                existing_ids_placeholders = ','.join(['%s'] * len(existing_ids))
                
                cursor.execute(f"""
                    SELECT id FROM videos WHERE category_id IN ({existing_ids_placeholders})
                """, existing_ids)
                videos = cursor.fetchall()
                video_ids = [video['id'] for video in videos]
                
                if video_ids:
                    video_ids_placeholders = ','.join(['%s'] * len(video_ids))
                    
                    # 删除视频相关的依赖数据
                    cursor.execute(f"DELETE FROM user_progress WHERE video_id IN ({video_ids_placeholders})", video_ids)
                    cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'video' AND item_id IN ({video_ids_placeholders})", video_ids)
                    cursor.execute(f"DELETE FROM user_cache WHERE video_id IN ({video_ids_placeholders})", video_ids)
                    
                    # 删除视频
                    cursor.execute(f"DELETE FROM videos WHERE id IN ({video_ids_placeholders})", video_ids)
                    deleted_videos_count = cursor.rowcount
            
            # 3. 删除分类相关的依赖数据
            existing_ids_placeholders = ','.join(['%s'] * len(existing_ids))
            cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'category' AND item_id IN ({existing_ids_placeholders})", existing_ids)
            cursor.execute(f"DELETE FROM user_purchases WHERE purchased_entity_type = 'category' AND purchased_entity_id IN ({existing_ids_placeholders})", existing_ids)
            
            # 4. 删除分类本身
            cursor.execute(f"DELETE FROM categories WHERE id IN ({existing_ids_placeholders})", existing_ids)
            categories_deleted_count = cursor.rowcount
            
            # 提交事务
            conn.commit()
            
            return {
                'success': True,
                'message': '批量删除操作完成',
                'data': {
                    'total_requested': len(category_ids),
                    'successfully_deleted': categories_deleted_count,
                    'failed_deletions': len(failed_ids),
                    'deleted_ids': existing_ids,
                    'failed_ids': failed_ids,
                    'cascade_stats': {
                        'deleted_categories': 0,
                        'deleted_videos': deleted_videos_count
                    } if cascade else None
                }
            }
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量删除分类失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量删除失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def batch_delete_videos(self, video_ids: List[str]) -> Dict[str, Any]:
        """批量删除视频"""
        if not video_ids:
            return {
                'success': False,
                'message': '没有提供要删除的视频ID',
                'data': None
            }
        
        # 限制批量大小
        if len(video_ids) > 500:
            return {
                'success': False,
                'message': '批量删除视频数量不能超过500个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证视频存在性
            video_ids_placeholders = ','.join(['%s'] * len(video_ids))
            cursor.execute(f"""
                SELECT id, title FROM videos WHERE id IN ({video_ids_placeholders})
            """, video_ids)
            existing_videos = cursor.fetchall()
            
            existing_ids = [video['id'] for video in existing_videos]
            failed_ids = [vid for vid in video_ids if vid not in existing_ids]
            
            if not existing_ids:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有找到有效的视频ID',
                    'data': {
                        'total_requested': len(video_ids),
                        'successfully_deleted': 0,
                        'failed_deletions': len(video_ids),
                        'deleted_ids': [],
                        'failed_ids': video_ids
                    }
                }
            
            # 2. 删除视频相关的依赖数据
            existing_ids_placeholders = ','.join(['%s'] * len(existing_ids))
            cursor.execute(f"DELETE FROM user_progress WHERE video_id IN ({existing_ids_placeholders})", existing_ids)
            cursor.execute(f"DELETE FROM user_favorites WHERE item_type = 'video' AND item_id IN ({existing_ids_placeholders})", existing_ids)
            cursor.execute(f"DELETE FROM user_cache WHERE video_id IN ({existing_ids_placeholders})", existing_ids)
            
            # 3. 删除视频本身
            cursor.execute(f"DELETE FROM videos WHERE id IN ({existing_ids_placeholders})", existing_ids)
            videos_deleted_count = cursor.rowcount
            
            # 提交事务
            conn.commit()
            
            return {
                'success': True,
                'message': '批量删除操作完成',
                'data': {
                    'total_requested': len(video_ids),
                    'successfully_deleted': videos_deleted_count,
                    'failed_deletions': len(failed_ids),
                    'deleted_ids': existing_ids,
                    'failed_ids': failed_ids,
                    'cascade_stats': {
                        'deleted_categories': 0,
                        'deleted_videos': videos_deleted_count
                    }
                }
            }
            
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量删除视频失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量删除失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    # ========== 批量创建方法 - 四层同步乐观更新规则核心功能 ==========
    
    def batch_create_series(self, series_list: List[Dict]) -> Dict[str, Any]:
        """批量创建系列"""
        if not series_list:
            return {
                'success': False,
                'message': '没有提供要创建的系列数据',
                'data': None
            }
        
        # 限制批量大小
        if len(series_list) > 100:
            return {
                'success': False,
                'message': '批量创建系列数量不能超过100个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证UUID格式和唯一性
            from ..utils.uuid_validator import is_valid_uuid
            
            valid_items = []
            validation_errors = []
            
            for i, item in enumerate(series_list):
                # 检查必填字段
                if not item.get('id'):
                    validation_errors.append(f"第{i+1}个系列缺少ID字段")
                    continue
                
                if not item.get('title'):
                    validation_errors.append(f"第{i+1}个系列缺少标题字段")
                    continue
                
                # 验证UUID格式
                if not is_valid_uuid(item['id']):
                    validation_errors.append(f"第{i+1}个系列ID格式无效: {item['id']}")
                    continue
                
                # 检查ID是否已存在
                cursor.execute("SELECT id FROM series WHERE id = %s", (item['id'],))
                if cursor.fetchone():
                    validation_errors.append(f"第{i+1}个系列ID已存在: {item['id']}")
                    continue
                
                valid_items.append(item)
            
            if validation_errors:
                conn.rollback()
                return {
                    'success': False,
                    'message': '数据验证失败',
                    'data': {
                        'total_requested': len(series_list),
                        'successfully_created': 0,
                        'failed_creations': len(validation_errors),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
            
            # 2. 批量插入数据
            if valid_items:
                insert_sql = """
                    INSERT INTO series (id, title, description, price, is_free, is_published, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                insert_data = []
                for item in valid_items:
                    insert_data.append((
                        item['id'],
                        item['title'],
                        item.get('description', ''),
                        item.get('price', 0.0),
                        item.get('is_free', False),
                        item.get('is_published', True)
                    ))
                
                cursor.executemany(insert_sql, insert_data)
                created_count = cursor.rowcount
                
                # 提交事务
                conn.commit()
                
                return {
                    'success': True,
                    'message': '批量创建操作完成',
                    'data': {
                        'total_requested': len(series_list),
                        'successfully_created': created_count,
                        'failed_creations': 0,
                        'created_ids': [item['id'] for item in valid_items],
                        'failed_items': [],
                        'validation_errors': []
                    }
                }
            else:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有有效的数据可创建',
                    'data': {
                        'total_requested': len(series_list),
                        'successfully_created': 0,
                        'failed_creations': len(series_list),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
                
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量创建系列失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量创建失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def batch_create_categories(self, categories_list: List[Dict]) -> Dict[str, Any]:
        """批量创建分类"""
        if not categories_list:
            return {
                'success': False,
                'message': '没有提供要创建的分类数据',
                'data': None
            }
        
        # 限制批量大小
        if len(categories_list) > 200:
            return {
                'success': False,
                'message': '批量创建分类数量不能超过200个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证UUID格式和唯一性
            from ..utils.uuid_validator import is_valid_uuid
            
            valid_items = []
            validation_errors = []
            
            for i, item in enumerate(categories_list):
                # 检查必填字段
                if not item.get('id'):
                    validation_errors.append(f"第{i+1}个分类缺少ID字段")
                    continue
                
                if not item.get('title'):
                    validation_errors.append(f"第{i+1}个分类缺少标题字段")
                    continue
                
                if not item.get('series_id'):
                    validation_errors.append(f"第{i+1}个分类缺少系列ID字段")
                    continue
                
                # 验证UUID格式
                if not is_valid_uuid(item['id']):
                    validation_errors.append(f"第{i+1}个分类ID格式无效: {item['id']}")
                    continue
                
                if not is_valid_uuid(item['series_id']):
                    validation_errors.append(f"第{i+1}个分类系列ID格式无效: {item['series_id']}")
                    continue
                
                # 检查ID是否已存在
                cursor.execute("SELECT id FROM categories WHERE id = %s", (item['id'],))
                if cursor.fetchone():
                    validation_errors.append(f"第{i+1}个分类ID已存在: {item['id']}")
                    continue
                
                # 检查系列是否存在
                cursor.execute("SELECT id FROM series WHERE id = %s", (item['series_id'],))
                if not cursor.fetchone():
                    validation_errors.append(f"第{i+1}个分类的系列ID不存在: {item['series_id']}")
                    continue
                
                valid_items.append(item)
            
            if validation_errors:
                conn.rollback()
                return {
                    'success': False,
                    'message': '数据验证失败',
                    'data': {
                        'total_requested': len(categories_list),
                        'successfully_created': 0,
                        'failed_creations': len(validation_errors),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
            
            # 2. 批量插入数据
            if valid_items:
                insert_sql = """
                    INSERT INTO categories (id, title, description, series_id, price, order_index, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                insert_data = []
                for item in valid_items:
                    insert_data.append((
                        item['id'],
                        item['title'],
                        item.get('description', ''),
                        item['series_id'],
                        item.get('price', 0.0),
                        item.get('order_index', 0)
                    ))
                
                cursor.executemany(insert_sql, insert_data)
                created_count = cursor.rowcount
                
                # 提交事务
                conn.commit()
                
                return {
                    'success': True,
                    'message': '批量创建操作完成',
                    'data': {
                        'total_requested': len(categories_list),
                        'successfully_created': created_count,
                        'failed_creations': 0,
                        'created_ids': [item['id'] for item in valid_items],
                        'failed_items': [],
                        'validation_errors': []
                    }
                }
            else:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有有效的数据可创建',
                    'data': {
                        'total_requested': len(categories_list),
                        'successfully_created': 0,
                        'failed_creations': len(categories_list),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
                
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量创建分类失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量创建失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def batch_create_videos(self, videos_list: List[Dict]) -> Dict[str, Any]:
        """批量创建视频"""
        if not videos_list:
            return {
                'success': False,
                'message': '没有提供要创建的视频数据',
                'data': None
            }
        
        # 限制批量大小
        if len(videos_list) > 500:
            return {
                'success': False,
                'message': '批量创建视频数量不能超过500个',
                'data': None
            }
        
        conn = None
        cursor = None
        try:
            conn = self.get_connection()
            cursor = conn.cursor(dictionary=True)
            
            # 开始事务
            conn.start_transaction()
            
            # 1. 验证UUID格式和唯一性
            from ..utils.uuid_validator import is_valid_uuid
            
            valid_items = []
            validation_errors = []
            
            for i, item in enumerate(videos_list):
                # 检查必填字段
                if not item.get('id'):
                    validation_errors.append(f"第{i+1}个视频缺少ID字段")
                    continue
                
                if not item.get('title'):
                    validation_errors.append(f"第{i+1}个视频缺少标题字段")
                    continue
                
                if not item.get('category_id'):
                    validation_errors.append(f"第{i+1}个视频缺少分类ID字段")
                    continue
                
                if not item.get('video_url'):
                    validation_errors.append(f"第{i+1}个视频缺少视频URL字段")
                    continue
                
                # 验证UUID格式
                if not is_valid_uuid(item['id']):
                    validation_errors.append(f"第{i+1}个视频ID格式无效: {item['id']}")
                    continue
                
                if not is_valid_uuid(item['category_id']):
                    validation_errors.append(f"第{i+1}个视频分类ID格式无效: {item['category_id']}")
                    continue
                
                # 检查ID是否已存在
                cursor.execute("SELECT id FROM videos WHERE id = %s", (item['id'],))
                if cursor.fetchone():
                    validation_errors.append(f"第{i+1}个视频ID已存在: {item['id']}")
                    continue
                
                # 检查分类是否存在
                cursor.execute("SELECT id FROM categories WHERE id = %s", (item['category_id'],))
                if not cursor.fetchone():
                    validation_errors.append(f"第{i+1}个视频的分类ID不存在: {item['category_id']}")
                    continue
                
                valid_items.append(item)
            
            if validation_errors:
                conn.rollback()
                return {
                    'success': False,
                    'message': '数据验证失败',
                    'data': {
                        'total_requested': len(videos_list),
                        'successfully_created': 0,
                        'failed_creations': len(validation_errors),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
            
            # 2. 批量插入数据
            if valid_items:
                insert_sql = """
                    INSERT INTO videos (id, title, description, category_id, video_url, duration, order_index, created_at, updated_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
                """
                
                insert_data = []
                for item in valid_items:
                    insert_data.append((
                        item['id'],
                        item['title'],
                        item.get('description', ''),
                        item['category_id'],
                        item['video_url'],
                        item.get('duration', 0),
                        item.get('order_index', 0)
                    ))
                
                cursor.executemany(insert_sql, insert_data)
                created_count = cursor.rowcount
                
                # 🎯 关键修复：批量视频创建后更新分类和系列统计信息
                if created_count > 0:
                    # 获取所有涉及的分类ID
                    affected_category_ids = list(set(item['category_id'] for item in valid_items))
                    print(f"📊 批量视频创建影响了 {len(affected_category_ids)} 个分类")
                    
                    # 更新所有涉及的分类统计
                    for category_id in affected_category_ids:
                        cursor.execute("""
                            UPDATE categories SET updated_at = NOW() WHERE id = %s
                        """, (category_id,))
                        print(f"✅ 已更新分类统计: {category_id}")
                        
                        # 获取分类所属的系列ID并更新系列统计
                        cursor.execute("""
                            SELECT series_id FROM categories WHERE id = %s
                        """, (category_id,))
                        result = cursor.fetchone()
                        if result and result['series_id']:
                            series_id = result['series_id']
                            cursor.execute("""
                                UPDATE series SET updated_at = NOW() WHERE id = %s
                            """, (series_id,))
                            print(f"✅ 已更新系列统计: {series_id}")
                
                # 提交事务
                conn.commit()
                
                return {
                    'success': True,
                    'message': '批量创建操作完成',
                    'data': {
                        'total_requested': len(videos_list),
                        'successfully_created': created_count,
                        'failed_creations': 0,
                        'created_ids': [item['id'] for item in valid_items],
                        'failed_items': [],
                        'validation_errors': []
                    }
                }
            else:
                conn.rollback()
                return {
                    'success': False,
                    'message': '没有有效的数据可创建',
                    'data': {
                        'total_requested': len(videos_list),
                        'successfully_created': 0,
                        'failed_creations': len(videos_list),
                        'created_ids': [],
                        'failed_items': validation_errors,
                        'validation_errors': validation_errors
                    }
                }
                
        except Exception as e:
            if conn:
                conn.rollback()
            print(f"❌ 批量创建视频失败: {str(e)}")
            return {
                'success': False,
                'message': f'批量创建失败: {str(e)}',
                'data': None
            }
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def create_purchase_table_indexes(self) -> bool:
        """创建购买记录表性能优化索引"""
        try:
            print("🔧 创建购买记录表索引...")
            
            # 索引创建SQL列表
            index_sqls = [
                "CREATE INDEX IF NOT EXISTS idx_user_purchases_user_id ON user_purchases(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_user_purchases_entity_type_id ON user_purchases(purchased_entity_type, purchased_entity_id)", 
                "CREATE INDEX IF NOT EXISTS idx_user_purchases_status ON user_purchases(status)",
                "CREATE INDEX IF NOT EXISTS idx_user_purchases_purchase_date ON user_purchases(purchase_date)",
                "CREATE INDEX IF NOT EXISTS idx_user_purchases_composite ON user_purchases(user_id, status, purchase_date)",
                "CREATE INDEX IF NOT EXISTS idx_categories_series_id ON categories(series_id)",
                "CREATE INDEX IF NOT EXISTS idx_videos_category_id ON videos(category_id)"
            ]
            
            success_count = 0
            for sql in index_sqls:
                try:
                    self.execute_update(sql)
                    success_count += 1
                    print(f"✅ 索引创建成功")
                except Exception as e:
                    if "Duplicate key name" in str(e):
                        success_count += 1
                        print(f"⚠️ 索引已存在")
                    else:
                        print(f"❌ 索引创建失败: {e}")
            
            print(f"📊 索引创建结果: {success_count}/{len(index_sqls)} 个")
            return success_count == len(index_sqls)
            
        except Exception as e:
            print(f"❌ 创建购买记录表索引失败: {e}")
            return False

    # 用户相关方法
    def get_user_by_id(self, user_id: str) -> Optional[Dict]:
        """根据ID获取用户"""
        result = self.execute_query("""
            SELECT * FROM users WHERE id = %s
        """, (user_id,))
        
        return result[0] if result else None
    
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户"""
        result = self.execute_query("""
            SELECT * FROM users WHERE username = %s
        """, (username,))
        
        return result[0] if result else None
    
    def get_user_by_email(self, email: str) -> Optional[Dict]:
        """根据邮箱获取用户"""
        result = self.execute_query("""
            SELECT * FROM users WHERE email = %s
        """, (email,))
        
        return result[0] if result else None
    
    def update_user_last_login(self, user_id: str) -> bool:
        """更新用户最后登录时间"""
        affected_rows = self.execute_update("""
            UPDATE users SET last_login_at = NOW(), updated_at = NOW() WHERE id = %s
        """, (user_id,))
        
        return affected_rows > 0


# 创建全局实例
mysql_manager = MySQLManager()
