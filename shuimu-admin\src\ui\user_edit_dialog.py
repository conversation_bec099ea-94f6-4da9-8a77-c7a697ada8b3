#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户编辑对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QCheckBox, QFormLayout,
                            QMessageBox, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Dict, Any, Optional
import threading
import re


class UserEditDialog(QDialog):
    """用户编辑对话框"""
    
    def __init__(self, parent, user_data: Dict[str, Any], api_client):
        super().__init__(parent)
        self.user_data = user_data.copy()
        self.api_client = api_client
        self.result = False
        
        # 设置对话框
        username = self.user_data.get('username', '未知用户')
        self.setWindowTitle(f"编辑用户 - {username}")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 创建界面
        self.create_widgets()
        
        # 填充数据
        self.populate_data()
        
    def create_widgets(self):
        """创建界面控件"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel(f"编辑用户 - {self.user_data.get('username', '未知用户')}")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 表单
        form_layout = QFormLayout()
        layout.addLayout(form_layout)
        
        # 用户名（只读）
        self.username_entry = QLineEdit()
        self.username_entry.setReadOnly(True)
        form_layout.addRow("用户名:", self.username_entry)
        
        # 邮箱
        self.email_entry = QLineEdit()
        form_layout.addRow("邮箱*:", self.email_entry)
        
        # 显示名
        self.display_name_entry = QLineEdit()
        form_layout.addRow("显示名:", self.display_name_entry)
        
        # 手机号
        self.phone_entry = QLineEdit()
        form_layout.addRow("手机号:", self.phone_entry)
        
        # 状态选项
        options_frame = QFrame()
        options_layout = QVBoxLayout(options_frame)
        
        self.is_active_checkbox = QCheckBox("激活用户")
        options_layout.addWidget(self.is_active_checkbox)
        
        self.is_admin_checkbox = QCheckBox("管理员权限")
        options_layout.addWidget(self.is_admin_checkbox)
        
        form_layout.addRow("选项:", options_frame)
        
        # 密码重置区域
        password_frame = QFrame()
        password_layout = QVBoxLayout(password_frame)
        
        password_label = QLabel("重置密码（留空则不修改）:")
        password_layout.addWidget(password_label)
        
        self.new_password_entry = QLineEdit()
        self.new_password_entry.setEchoMode(QLineEdit.EchoMode.Password)
        self.new_password_entry.setPlaceholderText("新密码")
        password_layout.addWidget(self.new_password_entry)
        
        self.confirm_password_entry = QLineEdit()
        self.confirm_password_entry.setEchoMode(QLineEdit.EchoMode.Password)
        self.confirm_password_entry.setPlaceholderText("确认新密码")
        password_layout.addWidget(self.confirm_password_entry)
        
        form_layout.addRow("", password_frame)
        
        # 按钮
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        save_btn = QPushButton("保存更改")
        save_btn.clicked.connect(self.save_changes)
        save_btn.setDefault(True)
        button_layout.addWidget(save_btn)
        
    def populate_data(self):
        """填充用户数据"""
        self.username_entry.setText(self.user_data.get('username', ''))
        self.email_entry.setText(self.user_data.get('email', ''))
        self.display_name_entry.setText(self.user_data.get('display_name', ''))
        self.phone_entry.setText(self.user_data.get('phone', ''))
        self.is_active_checkbox.setChecked(self.user_data.get('is_active', False))
        self.is_admin_checkbox.setChecked(self.user_data.get('is_admin', False))
        
    def validate_input(self) -> bool:
        """验证输入"""
        # 邮箱
        email = self.email_entry.text().strip()
        if not email:
            QMessageBox.warning(self, "验证错误", "邮箱不能为空")
            return False
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            QMessageBox.warning(self, "验证错误", "请输入有效的邮箱地址")
            return False
        
        # 手机号验证（如果提供）
        phone = self.phone_entry.text().strip()
        if phone:
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, phone):
                QMessageBox.warning(self, "验证错误", "请输入有效的手机号")
                return False
        
        # 密码验证（如果提供）
        new_password = self.new_password_entry.text()
        confirm_password = self.confirm_password_entry.text()
        
        if new_password or confirm_password:
            if len(new_password) < 6:
                QMessageBox.warning(self, "验证错误", "密码至少需要6个字符")
                return False
            
            if new_password != confirm_password:
                QMessageBox.warning(self, "验证错误", "两次输入的密码不一致")
                return False
        
        return True
    
    def save_changes(self):
        """保存更改"""
        if not self.validate_input():
            return
        
        def save_in_thread():
            try:
                # 构建更新数据
                update_data = {
                    'email': self.email_entry.text().strip(),
                    'display_name': self.display_name_entry.text().strip() or None,
                    'phone': self.phone_entry.text().strip() or None,
                    'is_active': self.is_active_checkbox.isChecked(),
                    'is_admin': self.is_admin_checkbox.isChecked()
                }
                
                # 如果提供了新密码，则包含密码
                new_password = self.new_password_entry.text()
                if new_password:
                    update_data['password'] = new_password
                
                # 调用API
                user_id = self.user_data.get('id')
                response = self.api_client.update_user(user_id, update_data)
                
                if response.get('success'):
                    self.result = True
                    QMessageBox.information(self, "成功", "用户信息更新成功！")
                    self.accept()
                else:
                    error_msg = response.get('message', '更新用户失败')
                    QMessageBox.critical(self, "错误", error_msg)
                    
            except Exception as e:
                error_msg = f"更新用户失败: {str(e)}"
                QMessageBox.critical(self, "错误", error_msg)
        
        threading.Thread(target=save_in_thread, daemon=True).start()
    
    def show(self) -> bool:
        """显示对话框并返回是否保存了更改"""
        result = self.exec()
        return self.result and result == QDialog.DialogCode.Accepted