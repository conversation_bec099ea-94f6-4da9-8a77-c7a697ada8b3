---
description: 基于 `$ARGUMENTS` 按照 问题诊断与修复流程 执行
---

# 🔍 问题诊断与修复流程

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：

- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖

## 🏗️ 环境理解

### WSL环境快速适配

**可以做的**：
- 文件读取、编辑、搜索、修改
- 代码分析和静态检查
- 服务验证（通过curl快速测试）
- 日志分析和文本处理

**不能做的**：
- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

## 🚀 快速诊断与修复流程

### **D1: Diagnose (快速诊断)**

#### **1. 现象提取：What happened?**
```bash
# 快速信息收集
echo "问题描述：$ARGUMENTS"
echo "当前时间：$(date)"
echo "工作目录：$(pwd)"
```

#### **2. 环境上下文：Where & When?**
```bash
# 项目类型快速识别
find . -name "*.py" | head -3 | xargs grep -l "FastAPI\|Flask\|Django\|PyQt" 2>/dev/null

# 最近修改文件
find . -name "*.py" -mtime -1 | head -5

# 服务状态快速检查
curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs 2>/dev/null || echo "服务未启动"
```

### **D2: Drill-down (深入分析 )**

#### **3. 数据流回溯：How data flows?**
```bash
# 快速搜索关键错误信息
grep -r "错误关键词" --include="*.py" . | head -5

# 相关文件快速定位
find . -name "*.py" -exec grep -l "相关函数名\|类名" {} \; | head -3

# 最近日志快速检查
find . -name "*.log" -mtime -1 -exec tail -n 10 {} \; | head -20
```

#### **4. 状态一致性检查：Which state is wrong?**
```bash
# 配置文件快速检查
find . -name "*.json" -o -name "*.yaml" -o -name "*.toml" | head -5 | xargs ls -la

# API端点快速发现和测试
if curl -s http://localhost:8000/openapi.json >/dev/null 2>&1; then
    curl -s http://localhost:8000/openapi.json | jq '.paths | keys[0:3]' 2>/dev/null || echo "API文档可用"
    # 快速测试第一个GET端点
    FIRST_GET=$(curl -s http://localhost:8000/openapi.json | jq -r '.paths | to_entries[] | select(.value.get) | .key' | head -1)
    [ -n "$FIRST_GET" ] && curl -s "http://localhost:8000$FIRST_GET" | head -3
fi
```

### **A1: Analyze (根因分析 )**

#### **5. 影响域映射：What components involved?**
```bash
# 快速依赖关系分析
grep -r "import\|from" --include="*.py" . | grep "相关模块" | head -5

# 相关文件关联度分析
find . -name "*.py" -exec grep -l "问题相关类名\|函数名" {} \; | head -3
```

#### **6. 根因假设：Why it happens?**
```bash
# 常见问题模式快速匹配
grep -r "Exception\|Error\|Failed\|None" --include="*.py" . | head -5

# 快速语法检查（静态分析）
find . -name "*.py" -exec python3 -m py_compile {} \; 2>&1 | head -5
```

### **R1: Repair (修复实施 )**

#### **7. 最小化修复：How to fix minimally?**
```bash
# 基于发现的根因进行最小化修复
# 使用Edit工具进行精确修复
# 优先修复明显的语法错误和逻辑错误
```

#### **8. 快速验证：How to ensure quality?**
```bash
# 修复后快速验证
python3 -m py_compile 修复的文件.py

# 服务快速验证
if curl -s http://localhost:8000/docs >/dev/null 2>&1; then
    echo "服务验证通过"
    # 快速API测试
    curl -s "http://localhost:8000/api/健康检查端点" | head -3
fi

# 快速功能验证
grep -r "修复相关的关键词" --include="*.py" . | head -3
```
