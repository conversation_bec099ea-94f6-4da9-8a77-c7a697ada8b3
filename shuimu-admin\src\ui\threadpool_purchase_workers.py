#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
符合PyQt6原生线程+线程池模式的购买记录异步任务
严格遵循CLAUDE.md中的线程处理规范
"""

from typing import Dict, Any
from PyQt6.QtCore import QRunnable, QThreadPool, pyqtSignal, QObject
from datetime import datetime

class TaskSignals(QObject):
    """任务信号类 - 用于QRunnable与主线程通信"""
    finished = pyqtSignal(object)  # 任务完成
    error = pyqtSignal(str)        # 任务错误

class PurchaseApiRunnable(QRunnable):
    """购买记录API调用任务 - 第2层API异步处理"""
    
    def __init__(self, user_service, user_id: str, purchase_data: Dict[str, Any]):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
        self.purchase_data = purchase_data
        self.signals = TaskSignals()
        
    def run(self):
        """执行API调用任务"""
        try:
            result = self.user_service.add_user_purchase(self.user_id, self.purchase_data)
            
            if result and result.get('success'):
                self.signals.finished.emit({
                    'success': True,
                    'data': result,
                    'purchase_data': self.purchase_data
                })
            else:
                error_msg = result.get('message', '添加失败') if result else '添加失败'
                self.signals.finished.emit({
                    'success': False,
                    'error': error_msg,
                    'purchase_data': self.purchase_data
                })
                
        except Exception as e:
            self.signals.error.emit(str(e))

class CacheUpdateRunnable(QRunnable):
    """缓存更新任务 - 第3层缓存异步处理"""
    
    def __init__(self, user_id: str, purchase_data: Dict[str, Any]):
        super().__init__()
        self.user_id = user_id
        self.purchase_data = purchase_data
        self.signals = TaskSignals()
        
    def run(self):
        """执行缓存更新任务"""
        try:
            from cache.global_data_manager import global_data_manager
            
            success = global_data_manager.add_user_purchase_to_cache(self.user_id, self.purchase_data)
            
            self.signals.finished.emit({
                'success': success,
                'user_id': self.user_id,
                'purchase_data': self.purchase_data
            })
                
        except Exception as e:
            self.signals.error.emit(str(e))

class CacheRefreshRunnable(QRunnable):
    """缓存刷新任务 - 对话框数据刷新"""
    
    def __init__(self, user_id: str):
        super().__init__()
        self.user_id = user_id
        self.signals = TaskSignals()
        
    def run(self):
        """执行缓存刷新任务"""
        try:
            from cache.global_data_manager import global_data_manager
            
            success = global_data_manager.refresh_user_purchases(self.user_id)
            
            self.signals.finished.emit({
                'success': success,
                'user_id': self.user_id
            })
                
        except Exception as e:
            self.signals.error.emit(str(e))

class ThreadPoolPurchaseManager(QObject):
    """线程池购买记录管理器 - 符合PyQt6原生线程+线程池模式"""
    
    # 信号定义
    purchase_completed = pyqtSignal(bool, str)  # 购买完成 (成功/失败, 消息)
    cache_refresh_completed = pyqtSignal(bool)  # 缓存刷新完成
    
    def __init__(self, user_service, user_id: str):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
        self.threadpool = QThreadPool.globalInstance()
        
        # 当前任务状态
        self.current_purchase_data = None
        self.api_completed = False
        self.cache_completed = False
        
    def add_purchase_async(self, purchase_data: Dict[str, Any]):
        """异步添加购买记录 - 使用线程池"""
        self.current_purchase_data = purchase_data
        self.api_completed = False
        self.cache_completed = False
        
        # 第2层：API调用任务
        api_runnable = PurchaseApiRunnable(self.user_service, self.user_id, purchase_data)
        api_runnable.signals.finished.connect(self._on_api_completed)
        api_runnable.signals.error.connect(self._on_api_error)
        
        self.threadpool.start(api_runnable)
        
    def refresh_cache_async(self):
        """异步刷新缓存 - 使用线程池"""
        cache_runnable = CacheRefreshRunnable(self.user_id)
        cache_runnable.signals.finished.connect(self._on_cache_refresh_completed)
        cache_runnable.signals.error.connect(self._on_cache_refresh_error)
        
        self.threadpool.start(cache_runnable)
        
    def _on_api_completed(self, result):
        """API完成回调"""
        self.api_completed = True
        
        if result['success']:
            # 第3层：缓存更新任务
            cache_runnable = CacheUpdateRunnable(self.user_id, result['purchase_data'])
            cache_runnable.signals.finished.connect(self._on_cache_update_completed)
            cache_runnable.signals.error.connect(self._on_cache_update_error)
            
            self.threadpool.start(cache_runnable)
        else:
            # API失败，直接完成
            self.purchase_completed.emit(False, result['error'])
            
    def _on_api_error(self, error_msg):
        """API错误回调"""
        self.purchase_completed.emit(False, f"API调用异常: {error_msg}")
        
    def _on_cache_update_completed(self, result):
        """缓存更新完成回调"""
        self.cache_completed = True
        
        if result['success']:
            print(f"🔔 [调试] ThreadPoolPurchaseManager: 准备发送purchase_completed信号(True)")
            self.purchase_completed.emit(True, "购买记录添加成功")
            print(f"🔔 [调试] ThreadPoolPurchaseManager: purchase_completed信号已发送")
        else:
            print(f"🔔 [调试] ThreadPoolPurchaseManager: 准备发送purchase_completed信号(True, 缓存失败)")
            self.purchase_completed.emit(True, "购买记录添加成功（缓存更新失败）")
            
    def _on_cache_update_error(self, error_msg):
        """缓存更新错误回调"""
        # 缓存失败不影响主要功能
        self.purchase_completed.emit(True, "购买记录添加成功（缓存操作失败）")
        
    def _on_cache_refresh_completed(self, result):
        """缓存刷新完成回调"""
        self.cache_refresh_completed.emit(result['success'])
        
    def _on_cache_refresh_error(self, error_msg):
        """缓存刷新错误回调"""
        self.cache_refresh_completed.emit(False)
        
    def get_active_thread_count(self) -> int:
        """获取活动线程数量 - 用于调试"""
        return self.threadpool.activeThreadCount()
        
    def get_max_thread_count(self) -> int:
        """获取最大线程数量 - 用于调试"""
        return self.threadpool.maxThreadCount()
        
    def wait_for_done(self, timeout_ms: int = 30000) -> bool:
        """等待所有任务完成 - 用于测试"""
        return self.threadpool.waitForDone(timeout_ms)