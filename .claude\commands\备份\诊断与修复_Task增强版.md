---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程

## 📋 核心理念

使用有限状态机(FSM)模型，确保诊断流程按照严格的状态转换路径执行，每个状态都有明确的进入条件、执行内容和退出条件。

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    ├── 智能代理工具 (Task)
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：
- 文件读取、编辑、搜索、修改
- 代码分析和静态检查
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证
- 智能搜索和关联分析

**不能做的**：
- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 依赖检测规则

**避免误判原则**：
- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：
- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **分离假设-验证** - 禁止将假设建立和验证人为分离，必须保持完整循环
- ❌ **人工循环决策** - 禁止在循环中设置人工决策点，必须基于客观条件自动循环
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖

## 🎛️ 状态机定义

```python
class DiagnosticStateMachine:
    """诊断修复状态机"""
    
    STATES = {
        # 初始化
        'INIT': 'Initial State - 流程启动',
        
        # 阶段1: 信息收集与真实检查
        'STAGE1_COLLECTING': 'Stage 1 - 信息收集与真实检查',
        
        # 阶段2: 假设-验证循环（2-5轮，每轮3步）
        'STAGE2_HYPOTHESIS_LOOP': 'Stage 2 - 假设验证循环',
        
        # 阶段3: 修复实施与验证循环（1-5轮，每轮4步）
        'STAGE3_FIX_LOOP': 'Stage 3 - 修复实施验证循环',
        
        # 阶段4: 总结与发散优化
        'STAGE4_SUMMARY': 'Stage 4 - 总结与发散优化',
        
        # 终结状态
        'COMPLETED': 'Completion - 流程完成',
        'FAILED': 'Failed - 流程失败'
    }
    
    TRANSITIONS = {
        'INIT': ['STAGE1_COLLECTING'],
        'STAGE1_COLLECTING': ['STAGE2_HYPOTHESIS_LOOP', 'FAILED'],
        'STAGE2_HYPOTHESIS_LOOP': ['STAGE3_FIX_LOOP', 'FAILED'],
        'STAGE3_FIX_LOOP': ['STAGE4_SUMMARY', 'FAILED'],
        'STAGE4_SUMMARY': ['COMPLETED', 'FAILED'],
        'COMPLETED': [],
        'FAILED': []
    }
```

---

## 🔄 状态实现

### **STATE: STAGE1_COLLECTING - 阶段1：信息收集与真实检查**

```python
class Stage1CollectingState:
    """阶段1状态 - 信息收集与真实检查
    
    完整实现阶段1的所有内容：
    - 问题信息解析
    - 强制真实检查（禁止猜测）
    - 多工具组合搜索
    - 搜索完整性验证
    - 系统状态检查
    """
    
    def __init__(self, $ARGUMENTS):
        self.$ARGUMENTS = $ARGUMENTS
        self.state_name = "STAGE1_COLLECTING"
        self.collection_results = {}
    
    def execute(self):
        """执行阶段1：信息收集与真实检查"""
        print(f"🔍 [{self.state_name}] 阶段1：信息收集与真实检查")
        print("⚠️ 强制要求：本阶段必须完成多维度信息搜索和真实检查")
        
        # 更新Todo状态
        self.update_todo_status("in_progress")
        
        # 步骤1.1: 问题信息解析（保留原逻辑）
        problem_analysis = self.step_1_1_problem_analysis()
        
        # 步骤1.2: 强制真实检查（禁止猜测）
        search_results = self.step_1_2_real_verification()
        
        # 评估阶段1完成质量
        stage1_quality = self.evaluate_stage1_quality()
        print(f"📊 阶段1质量评分: {stage1_quality:.2f}/1.0")
        
        # 检查阶段1→阶段2门禁条件
        gate_check = self.check_stage1_to_stage2_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE2_HYPOTHESIS_LOOP"
            print("✅ 阶段1门禁条件满足，进入阶段2")
        else:
            next_state = "FAILED"
            print("❌ 阶段1门禁条件不满足，流程失败")
        
        self.update_todo_status("completed")
        
        return {
            "success": gate_check["can_proceed"],
            "problem_analysis": problem_analysis,
            "search_results": search_results,
            "stage1_quality": stage1_quality,
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def step_1_1_problem_analysis(self):
        """步骤1.1: 问题信息解析"""
        print("执行步骤1.1: 问题信息解析...")
        
        # 1.1.1 问题描述分析
        core_symptoms = self.extract_core_symptoms()
        functional_modules = self.identify_functional_modules()
        trigger_conditions = self.analyze_trigger_conditions()
        impact_scope = self.determine_impact_scope()
        
        # 1.1.2 搜索策略制定
        search_strategy = self.create_search_strategy(core_symptoms, functional_modules)
        
        print(f"  提取核心症状：{len(core_symptoms)}个")
        print(f"  识别功能模块：{len(functional_modules)}个")
        print(f"  分析影响范围：{impact_scope}")
        print(f"  制定搜索策略：{len(search_strategy['search_dimensions'])}个搜索维度")
        
        return {
            "core_symptoms": core_symptoms,
            "functional_modules": functional_modules,
            "trigger_conditions": trigger_conditions,
            "impact_scope": impact_scope,
            "search_strategy": search_strategy
        }
    
    def create_task_search_strategy(self, core_symptoms, functional_modules):
        """为Task制定智能搜索策略"""
        task_strategy = {
            "parallel_tasks": [],
            "task_priorities": {},
            "expected_outcomes": {}
        }
        
        # Task 1: 项目架构发现
        architecture_task = {
            "name": "项目架构发现",
            "description": "项目架构分析",
            "prompt": f"""
            分析项目的完整架构，重点关注与问题 '{self.$ARGUMENTS}' 相关的组件：
            
            搜索目标：
            1. 主要Python模块和包结构（特别是 {', '.join(functional_modules[:3])} 相关）
            2. FastAPI路由和端点定义
            3. 数据库模型和表结构
            4. 关键配置文件和环境变量
            5. 项目依赖和版本信息
            
            核心症状背景：{', '.join(core_symptoms[:3])}
            
            返回要求：
            - 文件路径和具体行号
            - 关键函数和类的定义位置
            - 模块间的依赖关系
            - 可能的架构问题点
            """
        }
        task_strategy["parallel_tasks"].append(architecture_task)
        
        # Task 2: 问题相关代码搜索
        problem_search_task = {
            "name": "问题相关搜索",
            "description": "问题相关代码搜索",
            "prompt": f"""
            深度搜索与问题 '{self.$ARGUMENTS}' 直接相关的所有代码：
            
            搜索范围：
            1. 错误处理代码（try/except、error logs、异常抛出）
            2. 与症状 {', '.join(core_symptoms[:2])} 相关的业务逻辑
            3. API端点和数据验证逻辑
            4. 权限检查和认证相关代码
            5. 数据库操作和事务处理
            
            功能模块重点：{', '.join(functional_modules[:3])}
            
            返回要求：
            - 具体的代码片段和位置
            - 错误处理的完整链路
            - 可能的bug模式
            - 相关的单元测试（如果有）
            """
        }
        task_strategy["parallel_tasks"].append(problem_search_task)
        
        # Task 3: 系统状态和服务检查
        system_status_task = {
            "name": "系统状态检查", 
            "description": "系统状态检查",
            "prompt": f"""
            全面检查当前系统运行状态和环境配置：
            
            检查项目：
            1. mock_server服务状态和API可用性
            2. 数据库连接配置和表结构验证
            3. 关键配置文件的有效性和一致性
            4. 项目依赖的版本和兼容性
            5. 环境变量和路径配置
            
            问题上下文：{self.$ARGUMENTS}
            
            返回要求：
            - 服务运行状态和响应时间
            - 配置问题的具体位置
            - 依赖冲突的详细信息
            - 环境差异的影响分析
            """
        }
        task_strategy["parallel_tasks"].append(system_status_task)
        
        # Task 4: 依赖关系和集成分析
        dependency_analysis_task = {
            "name": "依赖关系分析",
            "description": "依赖关系分析", 
            "prompt": f"""
            分析项目的依赖关系和集成问题：
            
            分析维度：
            1. Python包依赖和版本冲突
            2. 模块间的调用关系和数据流
            3. 外部API和服务的集成点
            4. 数据库表之间的关联关系
            5. 前后端接口的匹配性
            
            重点关注模块：{', '.join(functional_modules)}
            问题症状：{', '.join(core_symptoms)}
            
            返回要求：
            - 依赖图和调用链路
            - 版本冲突的具体包名
            - 集成断点和错误传播路径
            - 数据一致性问题
            """
        }
        task_strategy["parallel_tasks"].append(dependency_analysis_task)
        
        # 设置Task优先级
        task_strategy["task_priorities"] = {
            "问题相关搜索": "high",
            "项目架构发现": "high", 
            "系统状态检查": "medium",
            "依赖关系分析": "medium"
        }
        
        return task_strategy
    
    def step_1_2_task_driven_verification(self):
        """步骤1.2: 强制真实检查（禁止猜测）"""
        print("执行步骤1.2: 强制真实检查（禁止猜测）...")
        print("⚠️ 强制要求：禁止凭想象猜测，必须基于真实验证")
        
        # 获取Task搜索策略
        problem_analysis = self.collection_results.get('problem_analysis', {})
        task_strategy = problem_analysis.get('task_search_strategy', {})
        parallel_tasks = task_strategy.get('parallel_tasks', [])
        
        if not parallel_tasks:
            print("❌ 未找到Task搜索策略，回退到传统搜索方法")
            return self.fallback_to_traditional_search()
        
        # 🚀 并行启动所有Task
        print(f"  并行启动{len(parallel_tasks)}个专门化Task...")
        task_results = {}
        
        for task_config in parallel_tasks:
            print(f"    启动Task: {task_config['name']}")
            try:
                # 调用Task工具
                task_result = Task(
                    description=task_config["description"],
                    prompt=task_config["prompt"]
                )
                task_results[task_config["name"]] = {
                    "status": "completed",
                    "result": task_result,
                    "task_type": task_config["name"]
                }
                print(f"      ✅ {task_config['name']} 完成")
                
            except Exception as e:
                print(f"      ❌ {task_config['name']} 失败: {str(e)}")
                task_results[task_config["name"]] = {
                    "status": "failed",
                    "error": str(e),
                    "task_type": task_config["name"]
                }
        
        # 整合Task结果
        integrated_results = self.integrate_task_results(task_results)
        
        # 执行补充验证（基于Task结果）
        supplementary_verification = self.execute_supplementary_verification(integrated_results)
        
        print(f"  Task搜索总结: {len(task_results)}个Task，"
              f"{len([r for r in task_results.values() if r['status'] == 'completed'])}个成功")
        
        return {
            "task_results": task_results,
            "integrated_results": integrated_results,
            "supplementary_verification": supplementary_verification,
            "search_quality": self.assess_task_search_quality(task_results)
        }
    
    def integrate_task_results(self, task_results):
        """整合所有Task的搜索结果"""
        print("    整合Task搜索结果...")
        
        integrated = {
            "architecture_info": {},
            "problem_code_locations": [],
            "system_issues": [],
            "dependency_problems": [],
            "evidence_chain": [],
            "discovery_summary": {}
        }
        
        # 处理每个Task的结果
        for task_name, task_data in task_results.items():
            if task_data["status"] != "completed":
                continue
                
            task_result = task_data["result"]
            
            if "架构" in task_name:
                integrated["architecture_info"] = self.extract_architecture_info(task_result)
            elif "搜索" in task_name:
                integrated["problem_code_locations"] = self.extract_code_locations(task_result)
            elif "状态" in task_name:
                integrated["system_issues"] = self.extract_system_issues(task_result)
            elif "依赖" in task_name:
                integrated["dependency_problems"] = self.extract_dependency_problems(task_result)
        
        # 构建证据链
        integrated["evidence_chain"] = self.build_evidence_chain_from_tasks(integrated)
        
        # 生成发现总结
        integrated["discovery_summary"] = {
            "total_files_discovered": len(integrated.get("architecture_info", {}).get("files", [])),
            "problem_locations_found": len(integrated["problem_code_locations"]),
            "system_issues_identified": len(integrated["system_issues"]),
            "dependency_conflicts": len(integrated["dependency_problems"]),
            "evidence_strength": len(integrated["evidence_chain"])
        }
        
        print(f"      整合完成: 发现{integrated['discovery_summary']['total_files_discovered']}个文件，"
              f"{integrated['discovery_summary']['problem_locations_found']}个问题位置")
        
        return integrated
    
    def extract_architecture_info(self, task_result):
        """从架构Task结果中提取结构信息"""
        # 简化实现：解析Task返回的架构信息
        return {
            "files": [],  # 从task_result中解析文件列表
            "modules": [],  # 从task_result中解析模块信息
            "apis": [],  # 从task_result中解析API端点
            "configs": []  # 从task_result中解析配置文件
        }
    
    def extract_code_locations(self, task_result):
        """从问题搜索Task结果中提取代码位置"""
        # 简化实现：解析Task返回的代码位置
        return []  # 包含文件路径、行号、问题描述的列表
    
    def extract_system_issues(self, task_result):
        """从系统状态Task结果中提取系统问题"""
        # 简化实现：解析Task返回的系统状态
        return []  # 包含服务状态、配置问题、环境问题的列表
    
    def extract_dependency_problems(self, task_result):
        """从依赖分析Task结果中提取依赖问题"""
        # 简化实现：解析Task返回的依赖问题
        return []  # 包含版本冲突、调用链问题的列表
    
    def build_evidence_chain_from_tasks(self, integrated_results):
        """基于Task结果构建证据链"""
        evidence_chain = []
        
        # 基于问题代码位置构建证据
        for location in integrated_results["problem_code_locations"]:
            evidence_chain.append({
                "type": "code_evidence",
                "location": location,
                "confidence": "high"
            })
        
        # 基于系统问题构建证据
        for issue in integrated_results["system_issues"]:
            evidence_chain.append({
                "type": "system_evidence", 
                "issue": issue,
                "confidence": "medium"
            })
        
        return evidence_chain
    
    def execute_supplementary_verification(self, integrated_results):
        """基于Task结果执行补充验证"""
        print("    执行补充验证...")
        
        verification_results = {
            "file_existence_check": True,
            "api_connectivity_check": True,
            "config_validity_check": True
        }
        
        # 验证Task发现的文件是否真实存在
        discovered_files = integrated_results.get("architecture_info", {}).get("files", [])
        if discovered_files:
            verification_results["file_existence_check"] = self.verify_file_existence(discovered_files[:5])
        
        # 验证Task发现的API端点是否可访问
        discovered_apis = integrated_results.get("architecture_info", {}).get("apis", [])
        if discovered_apis:
            verification_results["api_connectivity_check"] = self.verify_api_connectivity(discovered_apis[:3])
        
        return verification_results
    
    def verify_file_existence(self, file_list):
        """验证文件是否存在"""
        try:
            for file_path in file_list:
                # 使用Read工具验证文件存在性
                Read(file_path=file_path)
            return True
        except Exception:
            return False
    
    def verify_api_connectivity(self, api_list):
        """验证API连接性"""
        try:
            # 使用Bash工具测试API连接
            Bash(
                command='curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs',
                description="测试API连接"
            )
            return True
        except Exception:
            return False
    
    def assess_task_search_quality(self, task_results):
        """评估Task搜索质量"""
        total_tasks = len(task_results)
        successful_tasks = len([r for r in task_results.values() if r["status"] == "completed"])
        
        if total_tasks == 0:
            return 0.0
        
        success_rate = successful_tasks / total_tasks
        return min(success_rate * 1.2, 1.0)  # 轻微加权，最高1.0
    
    def fallback_to_traditional_search(self):
        """回退到传统搜索方法"""
        print("  回退到传统Glob/Grep搜索方法...")
        
        # 使用原有的multi_tool_search逻辑
        return {
            "fallback_used": True,
            "search_method": "traditional_glob_grep",
            "task_results": {},
            "integrated_results": {"fallback": True}
        }
    
    def evaluate_stage1_quality_with_tasks(self):
        """评估阶段1质量（Task增强版）"""
        quality_metrics = {
            "task_success_rate": 0.0,
            "discovery_coverage": 0.0,
            "evidence_strength": 0.0,
            "verification_completeness": 0.0
        }
        
        # 获取Task搜索结果
        task_search_results = self.collection_results.get("task_search_results", {})
        
        # Task成功率
        task_results = task_search_results.get("task_results", {})
        if task_results:
            successful_tasks = len([r for r in task_results.values() if r["status"] == "completed"])
            quality_metrics["task_success_rate"] = successful_tasks / len(task_results)
        
        # 发现覆盖率
        integrated_results = task_search_results.get("integrated_results", {})
        discovery_summary = integrated_results.get("discovery_summary", {})
        
        if discovery_summary:
            # 基于发现的文件数量、问题位置等评估覆盖率
            files_found = discovery_summary.get("total_files_discovered", 0)
            problems_found = discovery_summary.get("problem_locations_found", 0)
            quality_metrics["discovery_coverage"] = min((files_found + problems_found * 2) / 10, 1.0)
        
        # 证据强度
        evidence_chain = integrated_results.get("evidence_chain", [])
        quality_metrics["evidence_strength"] = min(len(evidence_chain) / 5, 1.0)
        
        # 验证完整性
        supplementary_verification = task_search_results.get("supplementary_verification", {})
        if supplementary_verification:
            verified_checks = sum(supplementary_verification.values())
            total_checks = len(supplementary_verification)
            quality_metrics["verification_completeness"] = verified_checks / total_checks if total_checks > 0 else 0
        
        # 综合质量评分
        weights = {"task_success_rate": 0.3, "discovery_coverage": 0.3, "evidence_strength": 0.2, "verification_completeness": 0.2}
        overall_quality = sum(quality_metrics[k] * weights[k] for k in quality_metrics)
        
        return overall_quality
    
    def check_stage1_to_stage2_gate(self):
        """检查阶段1→阶段2门禁条件（Task增强版）"""
        print("    执行阶段1→阶段2门禁条件检查（Task增强版）...")
        
        gate_checks = {
            "task_search_completed": False,
            "core_symptoms_identified": False,
            "minimum_discoveries_made": False,
            "task_quality_threshold": False,
            "verification_passed": False
        }
        
        failure_reasons = []
        
        # 检查1: Task搜索完成
        task_search_results = self.collection_results.get("task_search_results", {})
        task_results = task_search_results.get("task_results", {})
        
        if task_results:
            successful_tasks = len([r for r in task_results.values() if r["status"] == "completed"])
            total_tasks = len(task_results)
            
            if successful_tasks >= 2:  # 至少2个Task成功
                gate_checks["task_search_completed"] = True
                print(f"      ✅ Task搜索完成: {successful_tasks}/{total_tasks}个成功")
            else:
                failure_reasons.append(f"Task搜索不足: 仅{successful_tasks}/{total_tasks}个成功")
                print(f"      ❌ Task搜索不足: {successful_tasks}/{total_tasks}")
        
        # 检查2: 核心症状识别（保留原逻辑）
        problem_analysis = self.collection_results.get("problem_analysis", {})
        core_symptoms = problem_analysis.get("core_symptoms", [])
        
        if len(core_symptoms) > 0:
            gate_checks["core_symptoms_identified"] = True
            print(f"      ✅ 核心症状识别: {len(core_symptoms)}个症状")
        else:
            failure_reasons.append("未识别到核心症状")
            print("      ❌ 核心症状识别: 未识别")
        
        # 检查3: 最小发现量（基于Task结果）
        integrated_results = task_search_results.get("integrated_results", {})
        discovery_summary = integrated_results.get("discovery_summary", {})
        
        total_discoveries = (
            discovery_summary.get("total_files_discovered", 0) +
            discovery_summary.get("problem_locations_found", 0) * 2 +
            discovery_summary.get("system_issues_identified", 0)
        )
        
        if total_discoveries >= 3:  # 至少3个有效发现
            gate_checks["minimum_discoveries_made"] = True
            print(f"      ✅ 发现量达标: {total_discoveries}个有效发现")
        else:
            failure_reasons.append(f"发现量不足: 仅{total_discoveries}个，至少需要3个")
            print(f"      ❌ 发现量不足: {total_discoveries}")
        
        # 检查4: Task质量阈值
        search_quality = task_search_results.get("search_quality", 0.0)
        
        if search_quality >= 0.6:  # 质量阈值60%
            gate_checks["task_quality_threshold"] = True
            print(f"      ✅ Task质量达标: {search_quality:.2f}")
        else:
            failure_reasons.append(f"Task质量不足: {search_quality:.2f}，要求≥0.6")
            print(f"      ❌ Task质量不足: {search_quality:.2f}")
        
        # 检查5: 补充验证通过
        supplementary_verification = task_search_results.get("supplementary_verification", {})
        if supplementary_verification:
            verified_checks = sum(supplementary_verification.values())
            total_checks = len(supplementary_verification)
            
            if verified_checks >= total_checks * 0.7:  # 70%验证通过
                gate_checks["verification_passed"] = True
                print(f"      ✅ 补充验证通过: {verified_checks}/{total_checks}")
            else:
                failure_reasons.append(f"补充验证不足: {verified_checks}/{total_checks}")
                print(f"      ❌ 补充验证不足: {verified_checks}/{total_checks}")
        else:
            gate_checks["verification_passed"] = True  # 无验证项时默认通过
            print("      ✅ 补充验证: 无需验证")
        
        # 综合评估
        passed_checks = sum(gate_checks.values())
        total_checks = len(gate_checks)
        can_proceed = passed_checks >= 4  # 至少通过4项检查
        
        print(f"    阶段1→阶段2门禁条件检查结果: {passed_checks}/{total_checks} ({'✅ 通过' if can_proceed else '❌ 失败'})")
        
        return {
            "can_proceed": can_proceed,
            "gate_checks": gate_checks,
            "failure_reasons": failure_reasons,
            "passed_checks": passed_checks,
            "total_checks": total_checks,
            "process_type": "智能诊断流程"
        }
```

### **STATE: STAGE2_TASK_HYPOTHESIS_LOOP - 阶段2：Task增强假设验证循环**

```python
class Stage2TaskHypothesisLoopState:
    """阶段2状态 - 假设验证循环
    
    完整实现阶段2的所有内容：
    - 强制执行2-5轮假设-验证循环
    - 每轮包含3个步骤：假设建立→假设验证→结果分析
    - 基于客观退出条件自动循环
    """
    
    def __init__(self, stage1_data):
        self.stage1_data = stage1_data
        self.state_name = "STAGE2_TASK_HYPOTHESIS_LOOP"
        self.loop_results = []
        self.current_round = 0
        self.max_rounds = 5
        self.min_rounds = 2
    
    def execute(self):
        """执行阶段2：Task增强假设验证循环"""
        print(f"🔬 [{self.state_name}] 阶段2：Task增强假设验证循环")
        print("⚠️ 强制要求：使用智能代理进行深度假设验证")
        
        # 初始化循环
        self.initialize_task_loop()
        
        # 执行强制循环（保留原逻辑）
        while self.current_round < self.max_rounds:
            self.current_round += 1
            print(f"\n--- 第{self.current_round}轮假设验证循环 ---")
            
            # 更新TodoWrite状态
            self.update_todo_round_status("in_progress")
            
            # 执行一轮完整的3步循环
            round_result = self.execute_task_enhanced_round()
            self.loop_results.append(round_result)
            
            # 更新TodoWrite状态
            self.update_todo_round_status("completed")
            
            # 检查循环退出条件（保留原逻辑）
            if self.should_exit_loop():
                print(f"  循环退出条件满足，结束假设验证（共{self.current_round}轮）")
                break
            
            if self.current_round < self.min_rounds:
                print(f"  继续执行（要求最少{self.min_rounds}轮）")
                continue
        
        # 检查阶段2→阶段3门禁条件
        gate_check = self.check_stage2_to_stage3_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE3_TASK_FIX_LOOP"
            print("✅ 阶段2门禁条件满足，进入阶段3")
        else:
            next_state = "FAILED"
            print("❌ 阶段2门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_rounds": self.current_round,
            "loop_results": self.loop_results,
            "gate_check": gate_check,
            "next_state": next_state,
            "process_type": "智能诊断流程"
        }
    
    def execute_task_enhanced_round(self):
        """执行一轮假设验证循环"""
        round_result = {
            "round_number": self.current_round,
            "step_results": {},
            "task_usage": {}
        }
        
        # 步骤2.1: 假设建立与调整（保留原逻辑）
        step_2_1_result = self.step_2_1_hypothesis_building()
        round_result["step_results"]["2.1"] = step_2_1_result
        
        # 步骤2.2: 智能假设验证
        step_2_2_result = self.step_2_2_task_hypothesis_verification(step_2_1_result["hypotheses"])
        round_result["step_results"]["2.2"] = step_2_2_result
        round_result["task_usage"]["verification_tasks"] = step_2_2_result.get("task_count", 0)
        
        # 步骤2.3: 结果分析与新发现（保留原逻辑）
        step_2_3_result = self.step_2_3_result_analysis(step_2_2_result)
        round_result["step_results"]["2.3"] = step_2_3_result
        
        # 统计本轮结果
        round_result["summary"] = {
            "hypotheses_tested": len(step_2_1_result["hypotheses"]),
            "task_verified_hypotheses": step_2_2_result.get("task_verified_count", 0),
            "confirmed_issues": step_2_2_result["confirmed_count"],
            "new_findings": step_2_3_result["new_findings_count"],
            "root_cause_found": step_2_3_result.get("root_cause_analysis") is not None,
            "task_enhancement_quality": step_2_2_result.get("task_quality_score", 0.0)
        }
        
        print(f"  第{self.current_round}轮验证统计: "
              f"测试假设{round_result['summary']['hypotheses_tested']}个，"
              f"Task验证{round_result['summary']['task_verified_hypotheses']}个，"
              f"确认问题{round_result['summary']['confirmed_issues']}个")
        
        return round_result
    
    def step_2_2_task_hypothesis_verification(self, hypotheses):
        """步骤2.2: 智能假设验证"""
        print(f"执行步骤2.2: Task驱动假设验证...")
        print("⚠️ 为每个假设启动专门的验证代理...")
        
        verification_results = []
        confirmed_count = 0
        task_verified_count = 0
        task_results = {}
        
        # 为高优先级假设创建验证Task
        high_priority_hypotheses = [h for h in hypotheses if h.get("priority") == "high"][:6]  # 最多6个高优先级
        
        print(f"  选择{len(high_priority_hypotheses)}个高优先级假设进行Task验证...")
        
        for i, hypothesis in enumerate(high_priority_hypotheses, 1):
            print(f"  Task验证假设{i}: {hypothesis['description'][:50]}...")
            
            try:
                # 为假设创建专门的验证代理
                verification_task = self.create_hypothesis_verification_task(hypothesis, i)
                
                # 执行Task验证
                task_result = Task(
                    description=verification_task["description"],
                    prompt=verification_task["prompt"]
                )
                
                # 解析Task验证结果
                task_verification = self.parse_task_verification_result(task_result, hypothesis)
                task_results[f"hypothesis_{i}"] = task_verification
                task_verified_count += 1
                
                print(f"    ✅ Task验证完成: {task_verification['conclusion']}")
                
                # 整合到验证结果中
                verification_results.append({
                    "hypothesis_id": hypothesis.get("category", "unknown"),
                    "verified_by_task": True,
                    "task_conclusion": task_verification["conclusion"],
                    "confirmed": task_verification["confirmed"],
                    "evidence": task_verification["evidence"],
                    "evidence_type": "Task验证证据",
                    "verification_method_used": f"Task代理验证 - {hypothesis.get('verification_method', '')}",
                    "confidence_level": task_verification.get("confidence", "medium")
                })
                
                if task_verification["confirmed"]:
                    confirmed_count += 1
                    
            except Exception as e:
                print(f"    ❌ Task验证失败，回退到传统验证: {str(e)}")
                
                # 回退到传统验证方法
                traditional_verification = self.execute_traditional_verification(hypothesis)
                verification_results.append(traditional_verification)
                
                if traditional_verification["confirmed"]:
                    confirmed_count += 1
        
        # 对剩余假设使用传统验证
        remaining_hypotheses = hypotheses[len(high_priority_hypotheses):]
        for hypothesis in remaining_hypotheses[:4]:  # 最多4个传统验证
            traditional_verification = self.execute_traditional_verification(hypothesis)
            verification_results.append(traditional_verification)
            
            if traditional_verification["confirmed"]:
                confirmed_count += 1
        
        # 计算Task增强质量
        task_quality_score = self.calculate_task_verification_quality(task_results, task_verified_count)
        
        print(f"  Task验证总结: {task_verified_count}个Task验证，{confirmed_count}个问题确认")
        
        return {
            "verification_results": verification_results,
            "confirmed_count": confirmed_count,
            "total_tested": len(verification_results),
            "task_verified_count": task_verified_count,
            "task_results": task_results,
            "task_quality_score": task_quality_score,
            "verification_type": "智能验证"
        }
    
    def create_hypothesis_verification_task(self, hypothesis, index):
        """为假设创建专门的验证Task"""
        task_config = {
            "description": f"验证假设{index}-{hypothesis['category']}",
            "prompt": f"""
            深度验证假设：{hypothesis['description']}
            
            假设详情：
            - 层级：{hypothesis.get('layer', '未知')}
            - 类别：{hypothesis.get('category', '未知')}
            - 优先级：{hypothesis.get('priority', 'medium')}
            
            验证要求：
            1. 执行具体检查项目：
            {self.format_specific_checks(hypothesis.get('specific_checks', []))}
            
            2. 搜索相关证据：
            - 查找相关的代码文件和具体行号
            - 搜索错误日志和异常处理
            - 检查配置文件和环境设置
            - 验证API端点和数据库操作
            
            3. 分析验证结果：
            - 提供具体的证据（文件路径:行号 + 代码片段）
            - 判断假设是否成立（确认/反驳/不确定）
            - 评估证据的可靠性（高/中/低）
            - 识别需要进一步调查的问题点
            
            问题背景：{self.stage1_data.get('problem_analysis', {}).get('core_symptoms', ['未知问题'])}
            
            返回格式：
            **验证结论**: [确认/反驳/不确定]
            **核心证据**: [具体的文件位置和代码片段]
            **可靠性**: [高/中/低]
            **进一步调查点**: [需要深入的问题]
            """
        }
        
        return task_config
    
    def format_specific_checks(self, checks):
        """格式化具体检查项目"""
        if not checks:
            return "- 无具体检查项目"
        
        formatted = []
        for i, check in enumerate(checks[:5], 1):  # 最多5个检查项
            formatted.append(f"   {i}. {check}")
        
        return "\n".join(formatted)
    
    def parse_task_verification_result(self, task_result, hypothesis):
        """解析Task验证结果"""
        # 简化实现：解析Task返回的验证结论
        # 实际实现中需要解析Task返回的具体内容
        
        verification = {
            "conclusion": "Task验证完成",
            "confirmed": False,
            "evidence": "Task返回的证据信息",
            "confidence": "medium",
            "further_investigation": []
        }
        
        # 这里应该包含解析task_result的逻辑
        # 例如：查找关键词、提取文件位置、判断结论等
        
        return verification
    
    def execute_traditional_verification(self, hypothesis):
        """执行传统验证方法（作为Task的回退）"""
        # 使用原有的execute_real_verification逻辑
        return {
            "hypothesis_id": hypothesis.get("category", "unknown"),
            "verified_by_task": False,
            "confirmed": False,
            "evidence": "传统验证方法的证据",
            "evidence_type": "传统验证证据",
            "verification_method_used": hypothesis.get("verification_method", "传统验证")
        }
    
    def calculate_task_verification_quality(self, task_results, task_count):
        """计算Task验证质量评分"""
        if task_count == 0:
            return 0.0
        
        # 基于Task成功率和验证深度计算质量
        successful_tasks = len([r for r in task_results.values() if r.get("confidence") in ["high", "medium"]])
        success_rate = successful_tasks / task_count
        
        return min(success_rate * 1.1, 1.0)  # 轻微加权，最高1.0
```

### **STATE: STAGE3_TASK_FIX_LOOP - 阶段3：Task验证修复循环**

```python
class Stage3TaskFixLoopState:
    """阶段3状态 - Task验证修复循环
    
    完整实现阶段3的所有内容：
    - 强制执行1-5轮修复实施验证循环
    - 每轮包含4个步骤：修复实施→修复验证→回归测试→质量评估
    - 基于客观退出条件自动循环
    """
    
    def __init__(self, stage2_data):
        self.stage2_data = stage2_data
        self.state_name = "STAGE3_TASK_FIX_LOOP"
        self.fix_results = []
        self.current_round = 0
        self.max_rounds = 5
        self.min_rounds = 1
    
    def execute(self):
        """执行阶段3：Task验证修复循环"""
        print(f"🔧 [{self.state_name}] 阶段3：Task验证修复循环")
        print("⚠️ 强制要求：使用智能代理进行全面修复验证")
        
        # 基于阶段2结果制定修复计划
        fix_plan = self.create_fix_plan_from_stage2()
        
        # 执行修复循环
        while self.current_round < self.max_rounds:
            self.current_round += 1
            print(f"\n--- 第{self.current_round}轮修复验证循环 ---")
            
            # 更新TodoWrite状态
            self.update_todo_fix_status("in_progress")
            
            # 执行一轮完整的4步修复循环
            round_result = self.execute_task_enhanced_fix_round(fix_plan)
            self.fix_results.append(round_result)
            
            # 更新TodoWrite状态
            self.update_todo_fix_status("completed")
            
            # 检查修复循环退出条件
            if self.should_exit_fix_loop():
                print(f"  修复循环退出条件满足，结束修复验证（共{self.current_round}轮）")
                break
            
            if self.current_round < self.min_rounds:
                print(f"  继续执行（要求最少{self.min_rounds}轮）")
                continue
        
        # 检查阶段3→阶段4门禁条件
        gate_check = self.check_stage3_to_stage4_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE4_SUMMARY"
            print("✅ 阶段3门禁条件满足，进入阶段4")
        else:
            next_state = "FAILED"
            print("❌ 阶段3门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_rounds": self.current_round,
            "fix_results": self.fix_results,
            "gate_check": gate_check,
            "next_state": next_state,
            "process_type": "智能诊断流程"
        }
    
    def create_fix_plan_from_stage2(self):
        """基于阶段2结果制定修复计划"""
        print("  基于阶段2假设验证结果制定修复计划...")
        
        # 收集已确认的问题
        confirmed_issues = []
        for round_result in self.stage2_data.get("loop_results", []):
            verification_results = round_result.get("step_results", {}).get("2.2", {}).get("verification_results", [])
            for verification in verification_results:
                if verification.get("confirmed"):
                    confirmed_issues.append(verification)
        
        fix_plan = {
            "target_issues": confirmed_issues,
            "fix_priorities": self.prioritize_fixes(confirmed_issues),
            "verification_strategy": self.design_task_verification_strategy(confirmed_issues),
            "success_criteria": self.define_success_criteria(confirmed_issues)
        }
        
        print(f"    修复计划: {len(confirmed_issues)}个确认问题，{len(fix_plan['fix_priorities'])}个优先修复项")
        
        return fix_plan
    
    def prioritize_fixes(self, issues):
        """对修复项进行优先级排序"""
        priorities = []
        
        # 按影响程度和修复难度排序
        for issue in issues:
            priority_score = 0
            
            # 基于证据类型评分
            if "语法" in issue.get("evidence_type", ""):
                priority_score += 3  # 语法错误优先级高
            elif "配置" in issue.get("evidence_type", ""):
                priority_score += 2  # 配置错误中等优先级
            else:
                priority_score += 1  # 其他问题低优先级
            
            # 基于验证可信度评分
            if issue.get("verified_by_task"):
                priority_score += 1  # Task验证的问题加分
            
            priorities.append({
                "issue": issue,
                "priority_score": priority_score,
                "fix_complexity": self.estimate_fix_complexity(issue)
            })
        
        # 按优先级排序
        return sorted(priorities, key=lambda x: x["priority_score"], reverse=True)
    
    def estimate_fix_complexity(self, issue):
        """估算修复复杂度"""
        evidence_type = issue.get("evidence_type", "")
        
        if "语法" in evidence_type:
            return "low"
        elif "配置" in evidence_type:
            return "low"
        elif "逻辑" in evidence_type:
            return "medium"
        elif "架构" in evidence_type:
            return "high"
        else:
            return "medium"
    
    def design_task_verification_strategy(self, issues):
        """设计Task验证策略"""
        return {
            "comprehensive_verification": True,
            "verification_dimensions": [
                "代码修复质量检查",
                "级联影响评估",
                "API功能完整性测试",
                "错误处理边界验证",
                "性能影响评估"
            ],
            "task_timeout": 300,  # 5分钟超时
            "fallback_enabled": True
        }
    
    def define_success_criteria(self, issues):
        """定义成功标准"""
        return {
            "minimum_fixes_required": max(1, len(issues) // 2),  # 至少修复一半问题
            "verification_pass_rate": 0.8,  # 80%验证通过率
            "no_new_issues_introduced": True,  # 不引入新问题
            "critical_functions_working": True  # 核心功能正常
        }
    
    def execute_task_enhanced_fix_round(self, fix_plan):
        """执行一轮修复循环"""
        round_result = {
            "round_number": self.current_round,
            "step_results": {},
            "task_usage": {}
        }
        
        # 步骤3.1: 修复实施（保留原逻辑）
        step_3_1_result = self.step_3_1_fix_implementation(fix_plan)
        round_result["step_results"]["3.1"] = step_3_1_result
        
        # 步骤3.2: 智能修复验证
        step_3_2_result = self.step_3_2_task_fix_verification(step_3_1_result["fix_actions"])
        round_result["step_results"]["3.2"] = step_3_2_result
        round_result["task_usage"]["verification_task"] = step_3_2_result.get("task_used", False)
        
        # 步骤3.3: 回归测试（保留原逻辑）
        step_3_3_result = self.step_3_3_regression_testing(step_3_1_result, step_3_2_result)
        round_result["step_results"]["3.3"] = step_3_3_result
        
        # 步骤3.4: 质量评估（保留原逻辑）
        step_3_4_result = self.step_3_4_quality_assessment(step_3_1_result, step_3_2_result, step_3_3_result)
        round_result["step_results"]["3.4"] = step_3_4_result
        
        # 统计本轮结果
        round_result["summary"] = {
            "fixes_attempted": len(step_3_1_result.get("fix_actions", [])),
            "task_verification_quality": step_3_2_result.get("verification_quality", 0.0),
            "regression_tests_passed": step_3_3_result.get("tests_passed", 0),
            "overall_quality_score": step_3_4_result.get("quality_score", 0.0),
            "new_issues_found": step_3_3_result.get("new_issues_count", 0)
        }
        
        print(f"  第{self.current_round}轮修复统计: "
              f"尝试修复{round_result['summary']['fixes_attempted']}项，"
              f"Task验证质量{round_result['summary']['task_verification_quality']:.2f}，"
              f"整体质量{round_result['summary']['overall_quality_score']:.2f}")
        
        return round_result
    
    def step_3_2_task_fix_verification(self, fix_actions):
        """步骤3.2: 智能修复验证"""
        print(f"执行步骤3.2: Task驱动修复验证...")
        print("⚠️ 启动全面修复验证代理...")
        
        if not fix_actions:
            print("  无修复行动，跳过Task验证")
            return {"task_used": False, "verification_quality": 0.0}
        
        try:
            # 创建全面修复验证代理
            verification_task = self.create_comprehensive_fix_verification_task(fix_actions)
            
            # 执行Task验证
            task_result = Task(
                description=verification_task["description"],
                prompt=verification_task["prompt"]
            )
            
            # 解析Task验证结果
            verification_results = self.parse_fix_verification_result(task_result, fix_actions)
            
            print(f"    ✅ Task修复验证完成: {verification_results['overall_conclusion']}")
            
            return {
                "task_used": True,
                "task_result": task_result,
                "verification_results": verification_results,
                "verification_quality": verification_results.get("quality_score", 0.0),
                "detailed_findings": verification_results.get("detailed_findings", []),
                "recommendations": verification_results.get("recommendations", [])
            }
            
        except Exception as e:
            print(f"    ❌ Task验证失败，回退到传统验证: {str(e)}")
            
            # 回退到传统验证方法
            traditional_verification = self.execute_traditional_fix_verification(fix_actions)
            
            return {
                "task_used": False,
                "fallback_used": True,
                "verification_results": traditional_verification,
                "verification_quality": traditional_verification.get("quality_score", 0.0)
            }
    
    def create_comprehensive_fix_verification_task(self, fix_actions):
        """创建全面的修复验证Task"""
        task_config = {
            "description": "全面修复验证",
            "prompt": f"""
            全面验证修复效果和质量：
            
            修复行动列表：
            {self.format_fix_actions(fix_actions)}
            
            验证维度：
            
            1. **代码修复质量检查**：
            - 检查修复代码的语法正确性和逻辑完整性
            - 验证修复是否真正解决了原始问题
            - 评估代码质量和最佳实践遵循情况
            - 检查是否有遗漏的边界情况
            
            2. **级联影响评估**：
            - 分析修复对其他模块的潜在影响
            - 检查API接口的兼容性
            - 验证数据库操作的一致性
            - 评估配置变更的系统影响
            
            3. **API功能完整性测试**：
            - 验证相关API端点的响应正确性
            - 检查数据传输的完整性和格式
            - 测试错误处理和异常情况
            - 确认权限和认证机制正常
            
            4. **错误处理边界验证**：
            - 测试各种异常输入和边界条件
            - 验证错误信息的准确性和有用性
            - 检查异常传播和日志记录
            - 确认系统的容错能力
            
            5. **性能影响评估**：
            - 评估修复对系统性能的影响
            - 检查是否引入了性能瓶颈
            - 验证资源使用的合理性
            - 分析并发处理能力
            
            原始问题背景：{self.stage2_data.get('problem_description', '未知问题')}
            
            返回格式：
            **整体结论**: [修复成功/部分成功/修复失败]
            **质量评分**: [0.0-1.0]
            **详细发现**:
            - 代码质量: [评估结果]
            - 级联影响: [影响分析]
            - API功能: [测试结果]
            - 错误处理: [验证结果]
            - 性能影响: [评估结果]
            **建议**:
            - [具体的改进建议]
            **新发现的问题**:
            - [如果有新问题，请详细说明]
            """
        }
        
        return task_config
    
    def format_fix_actions(self, fix_actions):
        """格式化修复行动列表"""
        if not fix_actions:
            return "- 无修复行动"
        
        formatted = []
        for i, action in enumerate(fix_actions[:10], 1):  # 最多显示10个行动
            formatted.append(f"   {i}. {action}")
        
        return "\n".join(formatted)
    
    def parse_fix_verification_result(self, task_result, fix_actions):
        """解析修复验证Task结果"""
        # 简化实现：解析Task返回的验证结论
        # 实际实现中需要解析Task返回的具体内容
        
        verification = {
            "overall_conclusion": "修复验证完成",
            "quality_score": 0.8,  # 从task_result中解析
            "detailed_findings": {
                "code_quality": "良好",
                "cascade_impact": "轻微影响",
                "api_functionality": "正常",
                "error_handling": "充分",
                "performance_impact": "无显著影响"
            },
            "recommendations": [
                "建议增加单元测试",
                "建议完善错误日志"
            ],
            "new_issues": []
        }
        
        # 这里应该包含解析task_result的逻辑
        # 例如：提取质量评分、识别问题、解析建议等
        
        return verification
    
    def execute_traditional_fix_verification(self, fix_actions):
        """执行传统修复验证方法（作为Task的回退）"""
        return {
            "overall_conclusion": "传统验证完成",
            "quality_score": 0.6,
            "method": "传统验证",
            "limitations": "验证深度有限"
        }
```

### **STATE: STAGE4_SUMMARY - 阶段4：总结与发散优化**

```python
class Stage4SummaryState:
    """阶段4状态 - 总结与发散优化（保持原实现）"""
    
    def __init__(self, stage3_data):
        self.stage3_data = stage3_data
        self.state_name = "STAGE4_SUMMARY"
    
    def execute(self):
        """执行阶段4：总结与发散优化"""
        print(f"📋 [{self.state_name}] 阶段4：总结与发散优化")
        print("📊 Task增强版诊断修复流程总结")
        
        # 生成Task增强版总结报告
        summary_report = self.generate_task_enhanced_summary()
        
        # 发散优化建议
        optimization_suggestions = self.generate_optimization_suggestions()
        
        # 流程质量评估
        process_quality = self.evaluate_overall_process_quality()
        
        return {
            "success": True,
            "summary_report": summary_report,
            "optimization_suggestions": optimization_suggestions,
            "process_quality": process_quality,
            "next_state": "COMPLETED",
            "process_type": "智能诊断流程"
        }
    
    def generate_task_enhanced_summary(self):
        """生成Task增强版总结报告"""
        return {
            "task_usage_statistics": self.collect_task_usage_stats(),
            "performance_improvements": self.calculate_performance_improvements(),
            "quality_metrics": self.collect_quality_metrics(),
            "lessons_learned": self.extract_lessons_learned()
        }
    
    def collect_task_usage_stats(self):
        """收集Task使用统计"""
        return {
            "total_tasks_used": 0,  # 从各阶段收集
            "task_success_rate": 0.0,
            "average_task_quality": 0.0,
            "fallback_usage": 0
        }
```

---

## 🔄 完整状态机控制器

```python
class DiagnosticController:
    """诊断流程控制器"""
    
    def __init__(self, problem_description):
        self.problem_description = problem_description
        self.current_state = "INIT"
        self.state_history = []
        self.global_context = {}
        
    def execute_full_diagnostic_flow(self):
        """执行完整的诊断流程"""
        print("🚀 启动问题诊断与修复流程")
        print(f"📝 问题描述: {self.problem_description}")
        
        # 初始化TodoWrite
        self.initialize_todo_tracking()
        
        # 状态机执行循环
        while self.current_state not in ["COMPLETED", "FAILED"]:
            print(f"\n{'='*60}")
            print(f"当前状态: {self.current_state}")
            
            # 执行当前状态
            state_result = self.execute_current_state()
            
            # 记录状态历史
            self.state_history.append({
                "state": self.current_state,
                "result": state_result,
                "timestamp": self.get_timestamp()
            })
            
            # 状态转换
            if state_result.get("success", False):
                next_state = state_result.get("next_state", "FAILED")
                print(f"✅ 状态转换: {self.current_state} → {next_state}")
                self.current_state = next_state
            else:
                print(f"❌ 状态执行失败: {self.current_state} → FAILED")
                self.current_state = "FAILED"
                break
        
        # 生成最终报告
        final_report = self.generate_final_report()
        
        print(f"\n{'='*60}")
        print(f"🏁 诊断流程完成: {self.current_state}")
        
        return final_report
    
    def execute_current_state(self):
        """执行当前状态"""
        if self.current_state == "INIT":
            return self.execute_init_state()
        elif self.current_state == "STAGE1_COLLECTING":
            return self.execute_stage1_collecting()
        elif self.current_state == "STAGE2_HYPOTHESIS_LOOP":
            return self.execute_stage2_hypothesis_loop()
        elif self.current_state == "STAGE3_FIX_LOOP":
            return self.execute_stage3_fix_loop()
        elif self.current_state == "STAGE4_SUMMARY":
            return self.execute_stage4_summary()
        else:
            return {"success": False, "error": f"未知状态: {self.current_state}"}
    
    def execute_stage1_collecting(self):
        """执行阶段1：信息收集"""
        stage1 = Stage1CollectingState(self.problem_description)
        return stage1.execute()
    
    def execute_stage2_hypothesis_loop(self):
        """执行阶段2：假设验证循环"""
        stage1_data = self.get_latest_state_data("STAGE1_COLLECTING")
        stage2 = Stage2HypothesisLoopState(stage1_data)
        return stage2.execute()
    
    def execute_stage3_fix_loop(self):
        """执行阶段3：修复循环"""
        stage2_data = self.get_latest_state_data("STAGE2_HYPOTHESIS_LOOP")
        stage3 = Stage3FixLoopState(stage2_data)
        return stage3.execute()
```

---

## 📊 流程特性总结

### **核心特性**

1. **严格的状态机控制**：确保诊断流程按照预定路径执行，每个状态都有明确的进入和退出条件
2. **智能搜索与验证**：使用Task工具进行多维度并行搜索和假设验证
3. **强制真实检查**：禁止凭想象猜测，所有假设都必须通过真实验证
4. **完整的容错机制**：Task失败时自动回退到传统方法，确保流程的可靠性
5. **质量门禁控制**：每个阶段都有严格的质量评估和门禁条件

### **技术架构**

1. **四层同步乐观更新**：UI层、API层、持久层、补偿层的协调工作
2. **多工具组合使用**：Glob、Grep、Read、Task等工具的有机结合
3. **并行处理能力**：多个Task同时执行，提升整体效率
4. **状态跟踪机制**：通过TodoWrite实时跟踪流程进度和状态

### **使用指南**

1. **适用场景**：复杂系统问题的系统性诊断和修复
2. **执行方式**：通过状态机控制器自动执行完整流程
3. **监控机制**：实时状态跟踪和质量评估
4. **扩展性**：可根据具体需求调整搜索策略和验证方法