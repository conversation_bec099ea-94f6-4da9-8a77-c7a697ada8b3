"""
管理端用户API接口
提供用户的CRUD操作 - 使用MySQL数据库
"""

import sys
import os
import locale

# 强制设置UTF-8编码
os.environ['PYTHONIOENCODING'] = 'utf-8'
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
if hasattr(sys.stderr, 'reconfigure'):
    sys.stderr.reconfigure(encoding='utf-8')

import uuid
from datetime import datetime
from fastapi import APIRouter, HTTPException, Header, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from ..database.mysql_manager import mysql_manager
from ..utils.log_manager import global_log_manager

router = APIRouter()

# 数据模型
class UserCreate(BaseModel):
    id: str  # UUID架构：客户端必须提供ID
    username: str
    email: str
    password: str
    phone: Optional[str] = ""
    avatar_url: Optional[str] = ""
    is_active: Optional[bool] = True

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None

@router.get("/users")
def get_users_admin(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None)
):
    """获取用户列表（管理端）"""
    try:
        # 构建查询条件
        where_conditions = ["1=1"]
        params = []

        if search:
            where_conditions.append("(username LIKE %s OR email LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        if is_active is not None:
            where_conditions.append("is_active = %s")
            params.append(is_active)

        where_clause = " AND ".join(where_conditions)

        # 获取总数
        count_sql = f"""
            SELECT COUNT(*) as total 
            FROM users 
            WHERE {where_clause}
        """
        total_result = mysql_manager.execute_query(count_sql, params)
        total = total_result[0]['total'] if total_result else 0

        # 获取数据
        offset = (page - 1) * page_size
        data_sql = f"""
            SELECT id, username, email, display_name, phone, avatar_url,
                   is_active, is_admin, last_login_at, created_at, updated_at
            FROM users
            WHERE {where_clause}
            ORDER BY created_at DESC
            LIMIT %s OFFSET %s
        """
        params.extend([page_size, offset])
        users = mysql_manager.execute_query(data_sql, params)

        return {
            "success": True,
            "data": users,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")

@router.post("/users")
def create_user_admin(user: UserCreate):
    """创建用户（管理端）"""
    try:
        # UUID架构：必须使用客户端提供的ID
        if not user.id:
            raise HTTPException(status_code=400, detail="UUID架构要求：必须提供ID")

        new_id = user.id  # 强制使用客户端提供的UUID

        # 检查用户名和邮箱是否已存在
        existing_user = mysql_manager.execute_query("""
            SELECT id FROM users WHERE username = %s OR email = %s
        """, (user.username, user.email))

        if existing_user:
            raise HTTPException(status_code=400, detail="用户名或邮箱已存在")

        # 执行插入SQL
        affected_rows = mysql_manager.execute_update("""
            INSERT INTO users (id, username, email, password_hash, display_name, phone, avatar_url, is_active, is_admin, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), NOW())
        """, (
            new_id,
            user.username,
            user.email,
            user.password,  # 注意：这里应该是哈希后的密码
            user.username,  # 默认显示名为用户名
            user.phone,
            user.avatar_url,
            user.is_active,
            False  # 默认不是管理员
        ))

        if affected_rows > 0:
            # 获取创建的用户
            created_user = mysql_manager.execute_query("""
                SELECT id, username, email, display_name, phone, avatar_url, is_active, is_admin, created_at, updated_at
                FROM users WHERE id = %s
            """, (new_id,))

            return {
                "success": True,
                "message": "用户创建成功",
                "data": created_user[0] if created_user else None
            }
        else:
            raise HTTPException(status_code=500, detail="创建用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建用户失败: {str(e)}")

@router.put("/users/{user_id}")
def update_user_admin(user_id: str, user_data: dict):
    """更新用户（管理端）"""
    try:
        # [DEBUG] 服务器调试：记录完整请求信息
        print(f"[DEBUG] Admin API收到PUT请求: /api/admin/v1/users/{user_id}")
        print(f"[DEBUG] Admin API请求数据: {user_data}")
        print(f"[DEBUG] Admin API请求数据类型: {type(user_data)}")
        print(f"[DEBUG] Admin API密码字段检查: password = {user_data.get('password', 'NOT_FOUND')}")
        
        # 新增：详细分析密码字段
        if 'password' in user_data:
            password_value = user_data['password']
            print(f"[DEBUG] Admin API密码字段存在: '{password_value}'")
            print(f"[DEBUG] Admin API密码字段类型: {type(password_value)}")
            print(f"[DEBUG] Admin API密码字段长度: {len(password_value) if password_value else 'N/A'}")
        else:
            print(f"Admin API密码字段不存在!")
        
        # 新增：打印所有接收到的字段
        print(f"[DEBUG] Admin API接收到的所有字段:")
        for field_name in ['username', 'email', 'password', 'display_name', 'phone', 'is_active', 'is_admin']:
            field_value = user_data.get(field_name, 'NOT_FOUND')
            print(f"    {field_name}: {field_value}")

        # 检查用户是否存在
        check_sql = "SELECT id, username, email, display_name, phone FROM users WHERE id = %s"
        existing_user = mysql_manager.execute_query(check_sql, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 修复：构建动态更新字段和参数
        update_fields = []
        params = []

        if 'username' in user_data:
            update_fields.append("username = %s")
            params.append(user_data['username'])

        if 'email' in user_data:
            update_fields.append("email = %s")
            params.append(user_data['email'])

        if 'display_name' in user_data:
            update_fields.append("display_name = %s")
            params.append(user_data['display_name'])

        if 'phone' in user_data:
            update_fields.append("phone = %s")
            params.append(user_data['phone'])

        # 关键修复：添加密码字段支持
        if 'password' in user_data:
            print(f"[DEBUG] Admin API调试：检测到密码更新 - 新密码 = '{user_data['password']}'")
            update_fields.append("password_hash = %s")
            params.append(user_data['password'])  # 保存明文密码
            print(f"[DEBUG] Admin API调试：密码已添加到更新字段，当前params = {params}")

        if 'is_active' in user_data:
            update_fields.append("is_active = %s")
            params.append(user_data['is_active'])

        if 'is_admin' in user_data:
            update_fields.append("is_admin = %s")
            params.append(user_data['is_admin'])

        if not update_fields:
            raise HTTPException(status_code=400, detail="没有提供要更新的字段")

        # 添加更新时间和WHERE条件参数
        update_fields.append("updated_at = NOW()")
        params.append(user_id)

        # 修复：构建动态SQL
        update_sql = f"""
            UPDATE users
            SET {', '.join(update_fields)}
            WHERE id = %s
        """

        print(f"[DEBUG] Admin API调试：准备执行SQL更新")
        print(f"[DEBUG] Admin API调试：update_fields = {update_fields}")
        print(f"[DEBUG] Admin API调试：final_params = {tuple(params)}")
        print(f"[DEBUG] Admin API调试：update_sql = {update_sql}")

        affected_rows = mysql_manager.execute_update(update_sql, tuple(params))
        print(f"[DEBUG] Admin API调试：SQL执行完成，影响行数 = {affected_rows}")

        if affected_rows == 0:
            raise HTTPException(status_code=500, detail="更新失败")

        # 修复：获取更新后的完整用户信息（包含密码）
        updated_user = mysql_manager.execute_query("""
            SELECT id, username, email, display_name, phone, 
                   password_hash, is_active, is_admin, updated_at
            FROM users WHERE id = %s
        """, (user_id,))
        user_info = updated_user[0] if updated_user else None

        return {
            "success": True,
            "message": f"用户 {user_id} 信息更新成功",
            "data": {
                "id": user_info["id"],
                "username": user_info["username"],
                "email": user_info["email"],
                "display_name": user_info["display_name"],
                "phone": user_info["phone"],
                "password_hash": user_info["password_hash"],  # 添加密码字段到响应
                "is_active": user_info["is_active"],
                "is_admin": user_info["is_admin"],
                "updated_at": user_info["updated_at"].isoformat() if user_info["updated_at"] else None
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新用户信息失败: {str(e)}")

@router.delete("/users/{user_id}")
def delete_user_admin(user_id: str):
    """删除用户（管理端）"""
    try:
        # 检查用户是否存在
        existing_user = mysql_manager.execute_query("""
            SELECT * FROM users WHERE id = %s
        """, (user_id,))

        if not existing_user:
            raise HTTPException(status_code=404, detail="用户不存在")

        # 删除用户
        affected_rows = mysql_manager.execute_update("""
            DELETE FROM users WHERE id = %s
        """, (user_id,))

        if affected_rows > 0:
            return {
                "success": True,
                "message": "用户删除成功"
            }
        else:
            raise HTTPException(status_code=500, detail="删除用户失败")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除用户失败: {str(e)}")

@router.get("/users/{user_id}")
def get_user_admin(user_id: str):
    """获取单个用户详情（管理端）"""
    try:
        user = mysql_manager.execute_query("""
            SELECT id, username, email, display_name, avatar_url, phone, password_hash, is_active, is_admin, last_login_at, created_at, updated_at
            FROM users WHERE id = %s
        """, (user_id,))

        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "data": user[0]
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户详情失败: {str(e)}")

@router.get("/users/{user_id}/purchases")
def get_user_purchases_admin(user_id: str):
    """获取用户购买记录（管理端）- 优化版本，支持视频数量和购买类型"""
    try:
        # 使用优化后的mysql_manager，支持视频数量计算和购买类型判断
        result = mysql_manager.get_user_purchases(user_id)
        
        # 验证数据格式，确保包含新字段
        if result.get('success') and result.get('data'):
            for purchase in result['data']:
                # 确保所有必需字段都存在
                purchase.setdefault('purchase_type', '分类')
                purchase.setdefault('video_count', 0)
                purchase.setdefault('status', 'paid')
        
        # 检查查询是否成功
        if not result.get('success', False):
            return {
                "success": False,
                "data": [],
                "message": result.get('message', f"获取用户 {user_id} 购买记录失败")
            }
        
        return {
            "success": True,
            "data": result.get('data', []),
            "message": f"获取用户 {user_id} 购买记录成功（包含视频数量和购买类型）"
        }

        # 转换为管理端需要的格式
        purchase_list = []

        # 处理系列购买记录
        for series_purchase in purchases.get('series', []):
            purchase_list.append({
                'id': series_purchase['id'],
                'type': 'series',
                'item_name': f"系列 {series_purchase['id']}",  # 后续可以关联实际名称
                'amount': series_purchase['amount'],
                'purchase_date': series_purchase['purchaseDate'],
                'payment_method': series_purchase.get('paymentMethod', ''),
                'transaction_id': series_purchase.get('transactionId', ''),
                'status': 'completed'
            })

        # 处理分类购买记录
        for category_purchase in purchases.get('categories', []):
            purchase_list.append({
                'id': category_purchase['id'],
                'type': 'category',
                'item_name': f"分类 {category_purchase['id']}",  # 后续可以关联实际名称
                'amount': category_purchase['amount'],
                'purchase_date': category_purchase['purchaseDate'],
                'payment_method': category_purchase.get('paymentMethod', ''),
                'transaction_id': category_purchase.get('transactionId', ''),
                'status': 'completed'
            })

        return {
            "success": True,
            "data": purchase_list,
            "message": f"获取用户 {user_id} 购买记录成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户购买记录失败: {str(e)}")

@router.get("/users/{user_id}/progress")
def get_user_progress_admin(user_id: str):
    """获取用户观看进度（管理端）- 基于购买记录的动态查询"""
    try:
        global_log_manager.write_log(f"🔍进度API：获取用户 {user_id} 基于购买记录的观看进度", "DEBUG")
        
        from src.database.mysql_manager import mysql_manager
        
        try:
            # 调用基于购买记录的完整进度查询方法
            progress_list = mysql_manager.get_user_complete_progress(user_id)
            
            # 格式化数据为管理端需要的格式
            formatted_progress = []
            for progress in progress_list:
                progress_percentage = progress.get('progressPercentage', 0)
                formatted_progress.append({
                    'video_id': progress['video_id'],
                    'video_title': progress.get('videoTitle', f"视频 {progress['video_id']}"),
                    'category_title': progress.get('categoryTitle', '未知分类'),
                    'series_title': progress.get('seriesTitle', '未知系列'),
                    'category_id': progress.get('category_id'),
                    'series_id': progress.get('series_id'), 
                    'progress_seconds': progress.get('position', 0),
                    'total_duration': progress.get('videoDuration', 300),
                    'progress_percentage': progress_percentage,
                    'progress_display': f"{progress_percentage}%",  # 百分比格式显示（无小数点）
                    'watch_count': progress.get('watchCount', 0),
                    'completed': progress_percentage >= 95,
                    'last_watched': progress.get('lastWatchedAt'),
                    'has_progress': progress.get('position', 0) > 0
                })
            
            global_log_manager.write_log(f"进度API成功：返回 {len(formatted_progress)} 条记录（基于购买记录）", "INFO")
            return {
                "success": True,
                "data": formatted_progress,
                "message": f"获取用户 {user_id} 观看进度成功（包含所有可访问视频）"
            }
            
        except Exception as db_error:
            global_log_manager.write_log(f"基于购买记录的进度查询失败：{db_error}", "ERROR")
            # 如果查询失败，返回空数据而不是500错误
            return {
                "success": True,
                "data": [],
                "message": f"暂无用户 {user_id} 的观看进度记录"
            }

    except Exception as e:
        global_log_manager.write_log(f"进度API异常：{str(e)}", "ERROR")
        return {
            "success": False,
            "data": [],
            "message": f"获取观看进度失败: {str(e)}"
        }

@router.get("/users/{user_id}/favorites")
def get_user_favorites_admin(user_id: str):
    """获取用户收藏（管理端）- 基于购买记录的动态查询"""
    try:
        global_log_manager.write_log(f"🔍收藏API：获取用户 {user_id} 基于购买记录的收藏状态", "DEBUG")
        
        from src.database.mysql_manager import mysql_manager
        
        try:
            # 调用基于购买记录的视频收藏状态查询方法
            favorites_list = mysql_manager.get_user_video_favorites_status(user_id)
            
            # 格式化数据为管理端需要的格式
            formatted_favorites = []
            for favorite in favorites_list:
                formatted_favorites.append({
                    'video_id': favorite['video_id'],
                    'video_title': favorite.get('videoTitle', f"视频 {favorite['video_id']}"),
                    'category_title': favorite.get('categoryTitle', '未知分类'),
                    'series_title': favorite.get('seriesTitle', '未知系列'),
                    'category_id': favorite.get('category_id'),
                    'series_id': favorite.get('series_id'),
                    'thumbnail_url': "",
                    'is_favorited': favorite.get('isFavorited', False),
                    'favorited_at': favorite.get('favoritedAt'),
                    'created_at': favorite.get('favoritedAt'),
                    'updated_at': favorite.get('favoritedAt'),
                    'favorited_entity_type': 'video',
                    'favorited_entity_id': favorite['video_id']
                })
            
            global_log_manager.write_log(f"收藏API成功：返回 {len(formatted_favorites)} 条记录（基于购买记录）", "INFO")
            return {
                "success": True,
                "data": formatted_favorites,
                "message": f"获取用户 {user_id} 收藏记录成功（包含所有可访问视频）"
            }
            
        except Exception as db_error:
            global_log_manager.write_log(f"基于购买记录的收藏查询失败：{db_error}", "ERROR")
            # 如果查询失败，返回空数据而不是500错误
            return {
                "success": True,
                "data": [],
                "message": f"暂无用户 {user_id} 的收藏记录"
            }

    except Exception as e:
        global_log_manager.write_log(f"收藏API异常：{str(e)}", "ERROR")
        return {
            "success": False,
            "data": [],
            "message": f"获取收藏记录失败: {str(e)}"
        }

@router.get("/users/{user_id}/cache")
def get_user_cache_admin(user_id: str):
    """获取用户缓存信息（管理端）- 基于购买记录的动态查询"""
    try:
        global_log_manager.write_log(f"🔍缓存API：获取用户 {user_id} 基于购买记录的缓存状态", "DEBUG")
        
        from src.database.mysql_manager import mysql_manager
        
        try:
            # 调用基于购买记录的完整缓存状态查询方法
            cache_list = mysql_manager.get_user_complete_cache_status(user_id)
            
            # 格式化数据为管理端需要的格式
            formatted_cache = []
            for cache in cache_list:
                file_size = cache.get('fileSize', 0) or 0
                file_size_mb = round(file_size / 1024 / 1024, 2) if file_size > 0 else 0
                
                formatted_cache.append({
                    'video_id': cache['video_id'],
                    'video_title': cache.get('videoTitle', f"视频 {cache['video_id']}"),
                    'category_title': cache.get('categoryTitle', '未知分类'),
                    'series_title': cache.get('seriesTitle', '未知系列'),
                    'category_id': cache.get('category_id'),
                    'series_id': cache.get('series_id'),
                    'video_url': "",
                    'cache_status': cache.get('isCached', False),
                    'is_cached': cache.get('isCached', False),
                    'local_path': cache.get('localPath', ''),
                    'file_size': file_size,
                    'file_size_mb': file_size_mb,
                    'cached_at': cache.get('cachedAt'),
                    'has_cache_record': cache.get('isCached', False)
                })
            
            global_log_manager.write_log(f"缓存API成功：返回 {len(formatted_cache)} 条记录（基于购买记录）", "INFO")
            return {
                "success": True,
                "data": formatted_cache,
                "message": f"获取用户 {user_id} 缓存记录成功（包含所有可访问视频）"
            }
            
        except Exception as db_error:
            global_log_manager.write_log(f"基于购买记录的缓存查询失败：{db_error}", "ERROR")
            # 如果查询失败，返回空数据而不是500错误
            return {
                "success": True,
                "data": [],
                "message": f"暂无用户 {user_id} 的缓存记录"
            }

    except Exception as e:
        global_log_manager.write_log(f"缓存API异常：{str(e)}", "ERROR")
        return {
            "success": False,
            "data": [],
            "message": f"获取缓存记录失败: {str(e)}"
        }



@router.post("/users/{user_id}/reset-password")
def reset_user_password(user_id: str):
    """重置用户密码（管理端）- 修复：固定密码ultrathink，使用mysql_manager"""
    try:
        # 修复：使用固定密码 "ultrathink"
        new_password = "ultrathink"

        # 修复：使用mysql_manager以保持与其他API的一致性
        # 调试：先查询用户当前的密码
        existing_user = mysql_manager.execute_query("""
            SELECT id, password_hash FROM users WHERE id = %s
        """, (user_id,))
        print(f"重置密码API：查询用户 '{user_id}' 结果: {existing_user}")

        if existing_user:
            current_password = existing_user[0].get('password_hash', '')
            print(f"重置密码API：当前密码 = '{current_password}', 新密码 = '{new_password}'")

            if current_password == new_password:
                print(f"重置密码API：密码相同，无需更新")
                return {
                    "success": True,
                    "message": f"用户 {user_id} 密码重置成功（密码未变化）",
                    "new_password": new_password
                }

        affected_rows = mysql_manager.execute_update("""
            UPDATE users SET password_hash = %s WHERE id = %s
        """, (new_password, user_id))
        print(f"重置密码API：UPDATE影响行数: {affected_rows}")

        if affected_rows == 0:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "message": f"用户 {user_id} 密码重置成功",
            "new_password": new_password  # 管理端显示明文密码
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置用户密码失败: {str(e)}")

@router.post("/users/{user_id}/toggle-status")
def toggle_user_status(user_id: str):
    """切换用户状态（管理端）"""
    try:
        import pymysql

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 获取当前状态
            select_sql = "SELECT is_active FROM users WHERE id = %s"
            cursor.execute(select_sql, (user_id,))
            result = cursor.fetchone()

            if not result:
                raise HTTPException(status_code=404, detail="用户不存在")

            current_status = bool(result[0])
            new_status = not current_status

            # 更新状态
            update_sql = "UPDATE users SET is_active = %s WHERE id = %s"
            cursor.execute(update_sql, (new_status, user_id))
            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": f"用户 {user_id} 状态切换成功",
            "new_status": new_status
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"切换用户状态失败: {str(e)}")

@router.post("/users/{user_id}/toggle-admin")
def toggle_user_admin(user_id: str):
    """切换用户管理员权限（管理端）"""
    try:
        import pymysql

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 获取当前权限
            select_sql = "SELECT is_admin FROM users WHERE id = %s"
            cursor.execute(select_sql, (user_id,))
            result = cursor.fetchone()

            if not result:
                raise HTTPException(status_code=404, detail="用户不存在")

            current_admin = bool(result[0])
            new_admin = not current_admin

            # 更新权限
            update_sql = "UPDATE users SET is_admin = %s WHERE id = %s"
            cursor.execute(update_sql, (new_admin, user_id))
            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": f"用户 {user_id} 权限切换成功",
            "new_admin_status": new_admin
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"切换用户权限失败: {str(e)}")

@router.post("/users/{user_id}/purchases")
def add_user_purchase(
    user_id: str,
    purchase_data: dict,
    current_user_id: str = Header("admin", alias="X-User-Id"),
    is_admin: str = Header("true", alias="X-Is-Admin")
):
    """添加用户购买记录（管理端）"""
    try:
        # 验证管理员权限
        if is_admin != "true":
            raise HTTPException(status_code=403, detail="需要管理员权限")

        import pymysql
        from datetime import datetime

        # 创建数据库连接
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 检查用户是否存在
            check_user_sql = "SELECT id FROM users WHERE id = %s"
            cursor.execute(check_user_sql, (user_id,))
            if not cursor.fetchone():
                connection.close()
                raise HTTPException(status_code=404, detail="用户不存在")

            purchase_type = purchase_data.get('purchased_entity_type')
            purchased_entity_id = purchase_data.get('purchased_entity_id')
            amount = purchase_data.get('amount')

            global_log_manager.write_log(f"[DEBUG] 服务端调试：收到购买请求 - type={purchase_type}, purchased_entity_id={purchased_entity_id}, amount={amount}", "DEBUG")

            if purchase_type == 'series' or purchase_type == 'series_all_categories':
                # 购买系列：展开为该系列下的所有分类
                global_log_manager.write_log(f"服务端调试：开始处理系列购买 - series_id={purchased_entity_id}", "DEBUG")
                
                # 修复：处理series_all_categories类型的特殊情况
                if purchase_type == 'series_all_categories':
                    # 客户端已经提供了系列ID和分类数据
                    series_id = purchase_data.get('series_id') or purchased_entity_id
                    categories_data = purchase_data.get('categories', [])
                    series_name = purchase_data.get('series_name', '未知系列')
                    
                    global_log_manager.write_log(f"服务端调试：series_all_categories模式 - 系列ID={series_id}, 分类数={len(categories_data)}", "DEBUG")
                    
                    if categories_data:
                        # UUID架构：直接使用客户端提供的分类数据，包含UUID主键
                        insert_sql = """
                            INSERT INTO user_purchases
                            (id, user_id, purchased_entity_type, purchased_entity_id, series_name, purchase_price, status)
                            VALUES (%s, %s, %s, %s, %s, %s, %s)
                        """
                        
                        purchase_ids = []
                        for i, category in enumerate(categories_data):
                            print(f"🔍 [DETAILED_DEBUG] 处理分类{i+1}数据:")
                            print(f"    分类标题: {category.get('title', 'Unknown')}")
                            print(f"    完整分类数据结构: {category}")
                            
                            # UUID架构：严格验证管理端提供的购买记录UUID
                            purchase_uuid = (category.get('purchase_uuid') or 
                                           category.get('purchase_id'))
                            
                            print(f"    🔑 提取的purchase_uuid: {purchase_uuid}")
                            print(f"    📄 category.get('id'): {category.get('id')}")
                            print(f"    ⚠️  两者是否相同: {purchase_uuid == category.get('id')}")
                            
                            # 🔍 NEW: 详细分析所有可能的ID字段
                            print(f"[FIELD_ANALYSIS] 分析分类数据所有ID字段:")
                            id_fields = ['category_id', 'purchased_entity_id', 'original_category_id', 'id', 'purchase_uuid']
                            for field in id_fields:
                                value = category.get(field, 'NOT_FOUND')
                                print(f"    🏷️  {field}: {value}")
                            
                            if not purchase_uuid:
                                # 🚫 修复：服务端不允许生成UUID，必须由管理端提供
                                raise HTTPException(status_code=400, detail=f"UUID架构错误：管理端必须为每个分类购买提供purchase_uuid - {category.get('title', 'Unknown')}")
                            
                            # UUID架构：规范化分类ID获取逻辑，支持多种字段格式  
                            category_id_raw = category.get('category_id')
                            purchased_entity_id_raw = category.get('purchased_entity_id')
                            original_category_id_raw = category.get('original_category_id')
                            id_raw = category.get('id')
                            
                            print(f"[FALLBACK_ANALYSIS] ID字段优先级检查:")
                            print(f"    1️⃣ category_id: {category_id_raw}")
                            print(f"    2️⃣ purchased_entity_id: {purchased_entity_id_raw}")
                            print(f"    3️⃣ original_category_id: {original_category_id_raw}")
                            print(f"    4️⃣ id (FALLBACK): {id_raw}")
                            
                            category_id = (category_id_raw or 
                                         purchased_entity_id_raw or 
                                         original_category_id_raw or 
                                         id_raw)
                            
                            print(f"[FINAL_RESULT] 最终选择的category_id: {category_id}")
                            print(f"[FINAL_RESULT] 购买UUID: {purchase_uuid}")
                            print(f"[CRITICAL_CHECK] 🚨 UUID混用检查 - 相同: {category_id == purchase_uuid}")
                            
                            # 🚨 Critical validation: Prevent UUID mixing
                            if category_id == purchase_uuid:
                                print(f"[CRITICAL_ERROR] 🚨 检测到UUID架构违反！分类ID和购买UUID相同: {category_id}")
                                raise HTTPException(status_code=400, detail=f"UUID架构违反：分类ID不能与购买UUID相同 - {category.get('title', 'Unknown')}")
                            
                            if not category_id:
                                raise HTTPException(status_code=400, detail=f"UUID架构：分类数据缺少ID字段 - {category.get('title', 'Unknown')}") 
                            category_title = category.get('title', '未知分类')
                            category_price = float(category.get('price', 0))
                            
                            print(f"🔑 UUID架构：管理端生成购买记录UUID: {purchase_uuid}")
                            print(f"[RETRY] UUID架构：插入分类购买记录 - {category_title} (购买UUID: {purchase_uuid}, 分类ID: {category_id}, 价格: ¥{category_price})")
                            
                            try:
                                cursor.execute(insert_sql, (
                                    purchase_uuid,  # UUID架构：使用管理端生成的UUID作为主键
                                    user_id,
                                    'category',  # 统一存储为分类类型
                                    category_id,
                                    series_name,  # 存储所属系列名称
                                    category_price,
                                    'is_active'
                                ))
                            except Exception as db_error:
                                connection.close()
                                print(f"UUID架构：数据库插入失败 - {db_error}")
                                print(f"   SQL: {insert_sql}")
                                print(f"   参数: UUID={purchase_uuid}, user={user_id}, category={category_id}")
                                raise HTTPException(status_code=500, detail=f"UUID架构：数据库插入失败 - {str(db_error)}")
                            purchase_ids.append(purchase_uuid)  # UUID架构：返回管理端生成的UUID
                        
                        connection.commit()
                        global_log_manager.write_log(f"服务端调试：系列购买完成，成功添加 {len(purchase_ids)} 个分类购买记录", "DEBUG")
                        
                        return {
                            "success": True,
                            "message": f"系列购买成功，已添加{len(categories_data)}个分类的购买记录",
                            "data": {
                                "series_name": series_name,
                                "categories_count": len(categories_data),
                                "purchase_ids": purchase_ids
                            }
                        }
                    else:
                        # 没有分类数据，按原逻辑处理
                        purchased_entity_id = series_id
                
                # 1. 获取系列信息
                series_sql = "SELECT title FROM series WHERE id = %s"
                cursor.execute(series_sql, (purchased_entity_id,))
                series_result = cursor.fetchone()
                if not series_result:
                    connection.close()
                    raise HTTPException(status_code=404, detail="系列不存在")

                series_name = series_result[0]

                # 2. 获取该系列下的所有分类
                categories_sql = "SELECT id, title, price FROM categories WHERE series_id = %s"
                cursor.execute(categories_sql, (purchased_entity_id,))
                categories = cursor.fetchall()

                if not categories:
                    connection.close()
                    raise HTTPException(status_code=404, detail="该系列下没有分类")

                # UUID架构：为每个分类创建购买记录，使用UUID主键
                insert_sql = """
                    INSERT INTO user_purchases
                    (id, user_id, purchased_entity_type, purchased_entity_id, series_name, purchase_price, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """

                purchase_ids = []
                for category in categories:
                    category_id, category_title, category_price = category
                    # UUID架构：管理端应该为每个分类购买提供UUID
                    # 需要修改前端购买逻辑，在分类数据中包含purchase_uuid
                    # 目前从category数据中获取，如果没有则抛出错误
                    purchase_uuid = category.get('purchase_uuid') or category.get('purchase_id')
                    if not purchase_uuid:
                        connection.close()
                        raise HTTPException(status_code=400, detail="UUID架构：管理端必须为每个购买记录提供UUID")
                    
                    print(f"UUID架构：使用管理端提供的购买UUID: {purchase_uuid}")
                    
                    cursor.execute(insert_sql, (
                        purchase_uuid,  # UUID架构：使用管理端提供的UUID
                        user_id,
                        'category',  # 统一存储为分类类型
                        category_id,
                        series_name,  # 存储所属系列名称
                        category_price or amount,  # 使用分类价格，如果没有则使用系列价格
                        'is_active'
                    ))
                    purchase_ids.append(purchase_uuid)  # UUID架构：返回UUID而非lastrowid

                connection.commit()

                return {
                    "success": True,
                    "message": f"系列购买成功，已添加{len(categories)}个分类的购买记录",
                    "data": {
                        "series_name": series_name,
                        "categories_count": len(categories),
                        "purchase_ids": purchase_ids
                    }
                }

            else:
                # 购买单个分类
                # 1. 获取分类信息和所属系列
                category_sql = """
                    SELECT c.title, s.title as series_title
                    FROM categories c
                    LEFT JOIN series s ON c.series_id = s.id
                    WHERE c.id = %s
                """
                cursor.execute(category_sql, (purchased_entity_id,))
                category_result = cursor.fetchone()
                if not category_result:
                    connection.close()
                    raise HTTPException(status_code=404, detail="分类不存在")

                category_title, series_title = category_result

                # UUID架构：插入单个分类购买记录，使用UUID主键
                insert_sql = """
                    INSERT INTO user_purchases
                    (id, user_id, purchased_entity_type, purchased_entity_id, series_name, purchase_price, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """

                # UUID架构：管理端必须提供购买记录的UUID
                purchase_uuid = purchase_data.get('purchase_uuid') or purchase_data.get('id')
                if not purchase_uuid:
                    connection.close()
                    raise HTTPException(status_code=400, detail="UUID架构：管理端必须提供购买记录UUID")
                
                print(f"UUID架构：使用管理端提供的购买UUID: {purchase_uuid}")
                
                cursor.execute(insert_sql, (
                    purchase_uuid,  # UUID架构：使用管理端提供的UUID
                    user_id,
                    'category',  # 统一存储为分类类型
                    purchased_entity_id,
                    series_title or '未知系列',  # 存储所属系列名称
                    amount,
                    'is_active'
                ))

                purchase_id = purchase_uuid  # UUID架构：返回UUID而非lastrowid
                connection.commit()

                return {
                    "success": True,
                    "message": f"分类购买成功",
                    "data": {
                        "id": purchase_id,
                        "category_name": category_title,
                        "series_name": series_title,
                        "amount": amount
                    }
                }

        connection.close()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加购买记录失败: {str(e)}")

@router.delete("/users/{user_id}/purchases/{purchase_id}")
def delete_user_purchase_admin(user_id: str, purchase_id: str):
    """删除用户购买记录（管理端）- UUID架构：使用UUID主键删除"""
    try:
        import pymysql

        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='shuimu_server',
            password='dyj217',
            database='shuimu_course_server',
            charset='utf8mb4'
        )

        with connection.cursor() as cursor:
            # 检查购买记录是否存在
            check_sql = "SELECT id, purchased_entity_id, series_name FROM user_purchases WHERE id = %s AND user_id = %s"
            cursor.execute(check_sql, (purchase_id, user_id))
            purchase_record = cursor.fetchone()

            if not purchase_record:
                connection.close()
                raise HTTPException(status_code=404, detail="购买记录不存在")

            # 删除购买记录
            delete_sql = "DELETE FROM user_purchases WHERE id = %s AND user_id = %s"
            cursor.execute(delete_sql, (purchase_id, user_id))

            if cursor.rowcount == 0:
                connection.close()
                raise HTTPException(status_code=404, detail="删除失败，记录不存在")

            connection.commit()

        connection.close()

        return {
            "success": True,
            "message": "购买记录删除成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        global_log_manager.write_log(f"删除购买记录异常: {e}", "ERROR")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")

@router.get("/users/{user_id}/settings")
def get_user_settings_admin(user_id: str):
    """获取用户设置（管理端）"""
    try:
        from ..utils.user_data_mysql import get_user_settings

        settings = get_user_settings(user_id)

        # 转换为管理端需要的格式
        formatted_settings = []
        for key, value in settings.items():
            formatted_settings.append({
                'key': key,
                'value': str(value),
                'type': type(value).__name__
            })

        return {
            "success": True,
            "data": formatted_settings,
            "message": f"获取用户 {user_id} 设置成功"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户设置失败: {str(e)}")
