#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTabWidget, QWidget,
                            QScrollArea, QFrame, QGridLayout, QTreeWidget,
                            QTreeWidgetItem, QComboBox, QMessageBox, QGroupBox)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from typing import Dict, Any
import threading
from datetime import datetime

class UserDetailDialog(QDialog):
    """用户详情对话框"""
    
    def __init__(self, parent, user_data: Dict[str, Any], api_client):
        super().__init__(parent)
        self.user_data = user_data
        self.api_client = api_client
        self.user_progress = []
        self.user_stats = {}
        
        # 设置对话框
        username = self.user_data.get('username', '未知用户')
        self.setWindowTitle(f"用户详情 - {username}")
        self.resize(800, 600)
        self.setModal(True)
        
        # 创建界面
        self.create_widgets()
        
        # 加载用户详细数据
        self.load_user_details()
    
    def create_widgets(self):
        """创建界面组件"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 基本信息标签页
        self.create_basic_info_tab(tab_widget)
        
        # 学习统计标签页
        self.create_stats_tab(tab_widget)
        
        # 学习进度标签页
        self.create_progress_tab(tab_widget)
        
        # 操作历史标签页
        self.create_history_tab(tab_widget)
        
        # 按钮栏
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)
        
        edit_btn = QPushButton("编辑用户")
        edit_btn.clicked.connect(self.edit_user)
        button_layout.addWidget(edit_btn)
        
        reset_btn = QPushButton("重置密码")
        reset_btn.clicked.connect(self.reset_password)
        button_layout.addWidget(reset_btn)
        
        refresh_btn = QPushButton("刷新数据")
        refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(refresh_btn)
        
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
    
    def create_basic_info_tab(self, tab_widget):
        """创建基本信息标签页"""
        widget = QWidget()
        tab_widget.addTab(widget, "基本信息")
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        # 内容框架
        content_widget = QWidget()
        layout = QGridLayout(content_widget)
        
        # 用户信息字段
        info_fields = [
            ("用户ID", "id"),
            ("用户名", "username"),
            ("邮箱", "email"),
            ("显示名", "display_name"),
            ("手机号", "phone"),
            ("头像URL", "avatar_url"),
            ("账户状态", "is_active"),
            ("用户角色", "is_admin"),
            ("最后登录", "last_login_at"),
            ("创建时间", "created_at"),
            ("更新时间", "updated_at")
        ]
        
        self.info_entries = {}
        
        for i, (label_text, key) in enumerate(info_fields):
            # 标签
            label = QLabel(f"{label_text}:")
            label_font = QFont("Arial", 10)
            label_font.setBold(True)
            label.setFont(label_font)
            layout.addWidget(label, i, 0)
            
            # 值
            value = self.user_data.get(key, "")
            if key == "is_active":
                value = "活跃" if value else "禁用"
            elif key == "is_admin":
                value = "管理员" if value else "普通用户"
            elif key in ["last_login_at", "created_at", "updated_at"]:
                value = self.format_datetime(value)
            
            entry = QLineEdit(str(value))
            entry.setReadOnly(True)
            layout.addWidget(entry, i, 1)
            self.info_entries[key] = entry
        
        # 设置列权重
        layout.setColumnStretch(1, 1)
        
        scroll_area.setWidget(content_widget)
        
        # 标签页布局
        tab_layout = QVBoxLayout(widget)
        tab_layout.addWidget(scroll_area)
    
    def create_stats_tab(self, tab_widget):
        """创建学习统计标签页"""
        widget = QWidget()
        tab_widget.addTab(widget, "学习统计")
        
        layout = QVBoxLayout(widget)
        
        # 统计信息组
        stats_group = QGroupBox("学习统计")
        layout.addWidget(stats_group)
        stats_layout = QGridLayout(stats_group)
        
        # 统计字段
        self.stats_labels = {}
        stats_fields = [
            ("观看视频数", "watched_videos"),
            ("购买系列数", "purchased_series"),
            ("总观看次数", "total_watch_count"),
            ("总观看时长", "total_watch_time"),
            ("完成率", "completion_rate")
        ]
        
        for i, (label_text, key) in enumerate(stats_fields):
            label = QLabel(f"{label_text}:")
            label_font = QFont("Arial", 10)
            label_font.setBold(True)
            label.setFont(label_font)
            stats_layout.addWidget(label, i, 0)
            
            value_label = QLabel("加载中...")
            stats_layout.addWidget(value_label, i, 1)
            self.stats_labels[key] = value_label
        
        # 最近活动组
        activity_group = QGroupBox("最近活动")
        layout.addWidget(activity_group)
        activity_layout = QVBoxLayout(activity_group)
        
        # 最近活动树
        self.activity_tree = QTreeWidget()
        headers = ["时间", "活动", "详情"]
        self.activity_tree.setHeaderLabels(headers)
        for i, width in enumerate([150, 150, 150]):
            self.activity_tree.setColumnWidth(i, width)
        
        activity_layout.addWidget(self.activity_tree)
    
    def create_progress_tab(self, tab_widget):
        """创建学习进度标签页"""
        widget = QWidget()
        tab_widget.addTab(widget, "学习进度")
        
        layout = QVBoxLayout(widget)
        
        # 工具栏
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)
        
        toolbar_layout.addWidget(QLabel("筛选:"))
        self.progress_filter_combo = QComboBox()
        self.progress_filter_combo.addItems(["全部", "已完成", "进行中", "未开始"])
        self.progress_filter_combo.currentTextChanged.connect(self.filter_progress)
        toolbar_layout.addWidget(self.progress_filter_combo)
        
        toolbar_layout.addStretch()
        
        refresh_progress_btn = QPushButton("刷新进度")
        refresh_progress_btn.clicked.connect(self.load_user_progress)
        toolbar_layout.addWidget(refresh_progress_btn)
        
        # 进度列表
        self.progress_tree = QTreeWidget()
        headers = ["视频", "系列", "分类", "进度", "观看次数", "最后观看", "状态"]
        self.progress_tree.setHeaderLabels(headers)
        
        column_widths = [200, 150, 150, 80, 80, 150, 80]
        for i, width in enumerate(column_widths):
            self.progress_tree.setColumnWidth(i, width)
        
        layout.addWidget(self.progress_tree)
    
    def create_history_tab(self, tab_widget):
        """创建操作历史标签页"""
        widget = QWidget()
        tab_widget.addTab(widget, "操作历史")
        
        layout = QVBoxLayout(widget)
        
        # 历史记录列表
        self.history_tree = QTreeWidget()
        headers = ["时间", "操作", "IP地址", "用户代理", "结果"]
        self.history_tree.setHeaderLabels(headers)
        
        for i, width in enumerate([150, 150, 150, 150, 150]):
            self.history_tree.setColumnWidth(i, width)
        
        layout.addWidget(self.history_tree)
        
        # 加载历史记录的占位符
        placeholder_item = QTreeWidgetItem(["暂无数据", "", "", "", ""])
        self.history_tree.addTopLevelItem(placeholder_item)
    
    def load_user_details(self):
        """加载用户详细数据"""
        self.load_user_stats()
        self.load_user_progress()
    
    def load_user_stats(self):
        """加载用户统计数据"""
        def load_in_thread():
            try:
                user_id = self.user_data.get('id')
                if not user_id:
                    return
                
                # 获取用户资料（包含统计信息）
                response = self.api_client.get_user_profile(user_id)
                
                if response.get('success'):
                    user_profile = response.get('data', {})
                    stats = user_profile.get('stats', {})
                    
                    # 更新统计信息
                    QTimer.singleShot(0, lambda: self.update_stats_display(stats))
                else:
                    QTimer.singleShot(0, self.update_stats_error)
                    
            except Exception as e:
                QTimer.singleShot(0, self.update_stats_error)
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def load_user_progress(self):
        """加载用户学习进度"""
        def load_in_thread():
            try:
                user_id = self.user_data.get('id')
                if not user_id:
                    return
                
                # 获取用户所有进度
                response = self.api_client.get_user_progress(user_id)
                
                if response.get('success'):
                    self.user_progress = response.get('data', [])
                    QTimer.singleShot(0, self.update_progress_display)
                else:
                    QTimer.singleShot(0, self.update_progress_error)
                    
            except Exception as e:
                QTimer.singleShot(0, self.update_progress_error)
        
        threading.Thread(target=load_in_thread, daemon=True).start()
    
    def update_stats_display(self, stats):
        """更新统计信息显示"""
        self.stats_labels["watched_videos"].setText(str(stats.get("watched_videos", 0)))
        self.stats_labels["purchased_series"].setText(str(stats.get("purchased_series", 0)))
        self.stats_labels["total_watch_count"].setText(str(stats.get("total_watch_count", 0)))
        
        # 计算总观看时长（假设每次观看平均30分钟）
        total_minutes = stats.get("total_watch_count", 0) * 30
        hours = total_minutes // 60
        minutes = total_minutes % 60
        self.stats_labels["total_watch_time"].setText(f"{hours}小时{minutes}分钟")
        
        # 计算完成率
        watched = stats.get("watched_videos", 0)
        if watched > 0:
            # 这里需要获取总视频数来计算完成率，暂时使用假数据
            completion_rate = min(100, (watched / 100) * 100)
            self.stats_labels["completion_rate"].setText(f"{completion_rate:.1f}%")
        else:
            self.stats_labels["completion_rate"].setText("0%")
    
    def update_stats_error(self):
        """更新统计信息错误状态"""
        for label in self.stats_labels.values():
            label.setText("加载失败")
    
    def update_progress_display(self):
        """更新进度显示"""
        # 清空现有数据
        self.progress_tree.clear()
        
        # 添加进度数据
        for progress in self.user_progress:
            progress_percent = f"{progress.get('progress', 0) * 100:.1f}%"
            status = "已完成" if progress.get('is_completed') else "进行中"
            
            values = [
                progress.get('video_title', '未知视频'),
                progress.get('series_title', '未知系列'),
                progress.get('category_title', '未知分类'),
                progress_percent,
                str(progress.get('watch_count', 0)),
                self.format_datetime(progress.get('last_watched_at')),
                status
            ]
            
            item = QTreeWidgetItem(values)
            self.progress_tree.addTopLevelItem(item)
    
    def update_progress_error(self):
        """更新进度错误状态"""
        self.progress_tree.clear()
        error_item = QTreeWidgetItem(["加载失败", "", "", "", "", "", ""])
        self.progress_tree.addTopLevelItem(error_item)
    
    def filter_progress(self):
        """筛选进度"""
        filter_value = self.progress_filter_combo.currentText()
        
        # 清空现有显示
        self.progress_tree.clear()
        
        # 根据筛选条件显示数据
        for progress in self.user_progress:
            show_item = True
            
            if filter_value == "已完成" and not progress.get('is_completed'):
                show_item = False
            elif filter_value == "进行中" and (progress.get('is_completed') or progress.get('progress', 0) == 0):
                show_item = False
            elif filter_value == "未开始" and progress.get('progress', 0) > 0:
                show_item = False
            
            if show_item:
                progress_percent = f"{progress.get('progress', 0) * 100:.1f}%"
                status = "已完成" if progress.get('is_completed') else "进行中"
                
                values = [
                    progress.get('video_title', '未知视频'),
                    progress.get('series_title', '未知系列'),
                    progress.get('category_title', '未知分类'),
                    progress_percent,
                    str(progress.get('watch_count', 0)),
                    self.format_datetime(progress.get('last_watched_at')),
                    status
                ]
                
                item = QTreeWidgetItem(values)
                self.progress_tree.addTopLevelItem(item)
    
    def format_datetime(self, dt_str):
        """格式化日期时间"""
        if not dt_str:
            return "从未"
        try:
            if 'T' in dt_str:
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M")
            return dt_str
        except:
            return dt_str
    
    def edit_user(self):
        """编辑用户"""
        from .user_edit_dialog import UserEditDialog
        dialog = UserEditDialog(self.dialog, self.user_data, self.api_client)
        if dialog.show():
            # 刷新用户数据
            self.refresh_data()
    
    def reset_password(self):
        """重置密码"""
        reply = QMessageBox.question(self, "确认", f"确定要重置用户 '{self.user_data.get('username')}' 的密码吗？")
        if reply == QMessageBox.StandardButton.Yes:
            # TODO: 实现密码重置功能
            QMessageBox.information(self, "提示", "密码重置功能待实现")
    
    def refresh_data(self):
        """刷新数据"""
        self.load_user_details()
    
    def show(self):
        """显示对话框 - 兼容原接口"""
        self.exec()
