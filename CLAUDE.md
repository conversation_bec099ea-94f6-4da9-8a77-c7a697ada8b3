# 水幕课程管理系统 - Claude Code 项目指导

## 🎯 项目规范
- **语言**: 中文交流，英文代码
- **原则**: DRY，高内聚低耦合，禁止伪造、简化实现
- **日志**: 错误、警告、结果、状态
- **文档**: 不创建过程文件，整合到现有文档

## 📁 项目结构
- **`shuimu-admin/`** - PyQt6桌面管理端（主要开发）
- **`mock_server/`** - FastAPI后端服务
- **`app/`** - Android移动端

## 🔧 技术栈
- **主要**: PyQt6 + SQLAlchemy + MySQL + FastAPI
- **线程**: PyQt6原生线程+线程池模式
- **架构**: 四层同步乐观更新、UUID、版本化API

## 📋 快速开始指令

### 🚀 常用命令
```bash
# 管理端开发
cd shuimu-admin && python src/main.py

# 后端服务
cd mock_server && python src/main.py
```

## 🔧 项目特定技术约定

### 🏗️ 核心架构：四层同步乐观更新
```
| 层级      | 名称   | 职责                               | 执行时机                                 |
| --------- | ------ | ---------------------------------- | ---------------------------------------- |
| **第1层** | UI层   | 立即更新界面，包括完整级联更新     | 用户操作时立即执行，ui立即更新，启动后台线程不阻塞UI |
| **第2层** | API层  | 异步提交到服务器，支持级联并行调用 | UI更新后异步执行不阻塞主程序             |
| **第3层** | 持久层 | 同步本地MySQL、内存缓存、统计信息  | API成功后并行执行                        |
| **第4层** | 补偿层 | 失败回滚、错误提示、级联重试机制   | API失败时立即执行                        |
```

### 🎯 重要原则
- **UUID架构**：客户端生成UUID，整个项目唯一的uuid来源，一旦生成就是正式的，没有临时id一说；每个业务实体只能有一个主UUID，关联时使用existing UUID，不重新生成。
- **数据库操作**：通过现有FastAPI端点操作数据库；操作前先检查服务状态，无服务时启动mock_server，必须用8000端口

### 🔗 API规范
- **版本化**: `/api/v1/` 服务端、app端，`/api/admin/v1/` 管理端
- **常量类**: 使用`APIEndpoints`，禁止硬编码路径
- **权限头**: `X-User-Id` 和 `X-Is-Admin`

### 🗄️ 数据库规范
- **主键**: `id VARCHAR(50)`
- **外键**: `{实体名}_id` + 约束
- **前后缀**: `is_`布尔，`_at`时间
- **禁用**: `item_id`、`data`、`type`、`value`等模糊命名

### 🔤 命名规范
- **原则：**见名知意，禁止使用类似于item_type/item_id这样的命名，推荐：类似于：favorited_entity_type/favorited_entity_id
- **全项目**: `snake_case` (数据库、API、Python代码)  
- **Android端**: `camelCase` + HTTP客户端自动转换

#### ⚠️ 其他注意事项

- 执行任务过程中不要生成过程文件或总结文件，除非经过用户同意或用户要求
- 禁止兼容旧的规范内容，必须100%符合此规范文档内容