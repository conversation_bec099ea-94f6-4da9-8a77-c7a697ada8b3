---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行，集成Task技术和TodoWrite任务管理架构
---

# 🔍 问题诊断与修复流程 - TodoWrite架构版

## 📋 核心理念

使用有限状态机(FSM)模型结合TodoWrite任务管理和Task智能代理技术，确保诊断流程按照严格的状态转换路径执行，每个状态都有明确的进入条件、执行内容和退出条件。

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理
- ✅ 100%任务跟踪：所有步骤通过TodoWrite明确管理

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    ├── 智能代理工具 (Task)
    ├── 任务管理工具 (TodoWrite)
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：
- 文件读取、编辑、搜索、修改
- 代码分析和静态检查
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证
- 智能搜索和关联分析
- 任务状态跟踪和管理

**不能做的**：
- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 依赖检测规则

**避免误判原则**：
- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：
- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **分离假设-验证** - 禁止将假设建立和验证人为分离，必须保持完整循环
- ❌ **人工循环决策** - 禁止在循环中设置人工决策点，必须基于客观条件自动循环
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖
- ❌ **任务状态不同步** - TodoWrite状态必须与实际执行状态保持一致

## 🎛️ 状态机定义

```python
class DiagnosticStateMachine:
    """诊断修复状态机"""
    
    STATES = {
        # 初始化
        'INIT': 'Initial State - 流程启动',
        
        # 阶段1: 信息收集与真实检查
        'STAGE1_COLLECTING': 'Stage 1 - 信息收集与真实检查',
        
        # 阶段2: 假设-验证循环（2-5轮，每轮3步）
        'STAGE2_HYPOTHESIS_LOOP': 'Stage 2 - 假设验证循环',
        
        # 阶段3: 修复实施与验证循环（1-5轮，每轮4步）
        'STAGE3_FIX_LOOP': 'Stage 3 - 修复实施验证循环',
        
        # 阶段4: 总结与发散优化
        'STAGE4_SUMMARY': 'Stage 4 - 总结与发散优化',
        
        # 终结状态
        'COMPLETED': 'Completion - 流程完成',
        'FAILED': 'Failed - 流程失败'
    }
    
    TRANSITIONS = {
        'INIT': ['STAGE1_COLLECTING'],
        'STAGE1_COLLECTING': ['STAGE2_HYPOTHESIS_LOOP', 'FAILED'],
        'STAGE2_HYPOTHESIS_LOOP': ['STAGE3_FIX_LOOP', 'FAILED'],
        'STAGE3_FIX_LOOP': ['STAGE4_SUMMARY', 'FAILED'],
        'STAGE4_SUMMARY': ['COMPLETED', 'FAILED'],
        'COMPLETED': [],
        'FAILED': []
    }
```

## 📋 TodoWrite任务管理架构

### 🏗️ 三层任务结构设计

```python
class TodoWriteManager:
    """TodoWrite任务管理器 - 三层任务架构"""
    
    def __init__(self):
        self.current_todos = []
        self.task_hierarchy = {
            'stage': {},      # 阶段级任务
            'step': {},       # 步骤级任务  
            'verification': {}# 验证级任务
        }
    
    # 阶段级任务模板
    STAGE_TASK_TEMPLATES = {
        'STAGE1': {
            'content': '阶段1：信息收集与真实检查',
            'priority': 'high',
            'substeps': ['步骤1.1：问题信息解析', '步骤1.2：Task驱动并行搜索验证', '阶段1门禁条件检查']
        },
        'STAGE2': {
            'content': '阶段2：假设验证循环',
            'priority': 'high', 
            'substeps': ['假设循环轮次管理', '假设建立与验证', '循环退出条件检查']
        },
        'STAGE3': {
            'content': '阶段3：修复实施验证循环',
            'priority': 'high',
            'substeps': ['修复循环轮次管理', '修复实施与验证', '循环退出条件检查']
        },
        'STAGE4': {
            'content': '阶段4：总结与发散优化',
            'priority': 'medium',
            'substeps': ['问题解决总结', '发散优化建议', '流程完成确认']
        }
    }
    
    def create_stage_todos(self, stage_name):
        """为指定阶段创建所有相关任务"""
        template = self.STAGE_TASK_TEMPLATES.get(stage_name)
        if not template:
            return
            
        # 创建阶段级任务
        stage_todo = {
            'content': template['content'],
            'status': 'pending',
            'priority': template['priority'],
            'id': f'stage_{stage_name.lower()}'
        }
        
        # 创建步骤级任务
        step_todos = []
        for i, substep in enumerate(template['substeps']):
            step_todo = {
                'content': f'  - {substep}',
                'status': 'pending',
                'priority': template['priority'],
                'id': f'step_{stage_name.lower()}_{i+1}'
            }
            step_todos.append(step_todo)
        
        return [stage_todo] + step_todos
    
    def create_dynamic_verification_todos(self, verification_tasks):
        """动态创建验证级任务"""
        verification_todos = []
        for i, task in enumerate(verification_tasks):
            verification_todo = {
                'content': f'    - {task["description"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'verify_{task["type"]}_{i+1}'
            }
            verification_todos.append(verification_todo)
        return verification_todos
    
    def update_task_status(self, task_id, status):
        """更新任务状态"""
        for todo in self.current_todos:
            if todo['id'] == task_id:
                todo['status'] = status
                break
    
    def get_current_todos(self):
        """获取当前任务列表"""
        return self.current_todos
    
    def sync_with_state_machine(self, current_state, context=None):
        """与状态机状态同步"""
        # 根据当前状态创建或更新任务
        if current_state == 'STAGE1_COLLECTING':
            new_todos = self.create_stage_todos('STAGE1')
            self.current_todos.extend(new_todos)
        elif current_state == 'STAGE2_HYPOTHESIS_LOOP':
            new_todos = self.create_stage_todos('STAGE2')
            self.current_todos.extend(new_todos)
        # ... 其他状态的处理
```

### 🔄 Task执行与TodoWrite状态同步机制

```python
class TaskTodoIntegration:
    """Task执行与TodoWrite状态同步集成"""
    
    def __init__(self, todo_manager):
        self.todo_manager = todo_manager
        self.active_tasks = {}
    
    def execute_task_with_todo_sync(self, task_config, todo_id):
        """执行Task并同步TodoWrite状态"""
        
        # 1. 任务开始前更新状态
        self.todo_manager.update_task_status(todo_id, 'in_progress')
        print(f"🚀 启动Task: {task_config['description']}")
        
        try:
            # 2. 执行Task
            result = Task(
                description=task_config['description'],
                prompt=task_config['prompt']
            )
            
            # 3. 任务成功完成
            self.todo_manager.update_task_status(todo_id, 'completed')
            print(f"✅ Task完成: {task_config['description']}")
            
            return {
                'success': True,
                'result': result,
                'task_id': todo_id
            }
            
        except Exception as e:
            # 4. Task失败处理
            print(f"❌ Task失败: {task_config['description']} - {str(e)}")
            print(f"🔄 启动回退机制...")
            
            # 保持in_progress状态，启动回退
            fallback_result = self.execute_fallback_method(task_config)
            
            if fallback_result['success']:
                self.todo_manager.update_task_status(todo_id, 'completed')
                print(f"✅ 回退方法成功: {task_config['description']}")
            else:
                # 标记为失败，但不阻塞流程
                print(f"⚠️ 回退方法也失败，任务标记为需要人工干预")
            
            return {
                'success': fallback_result['success'],
                'result': fallback_result['result'],
                'task_id': todo_id,
                'fallback_used': True
            }
    
    def execute_parallel_tasks_with_todos(self, task_configs):
        """并行执行多个Task并同步TodoWrite状态"""
        
        # 1. 创建验证级任务
        verification_todos = self.todo_manager.create_dynamic_verification_todos(task_configs)
        self.todo_manager.current_todos.extend(verification_todos)
        
        # 2. 并行启动Task
        task_results = []
        for i, task_config in enumerate(task_configs):
            todo_id = f'verify_{task_config["type"]}_{i+1}'
            result = self.execute_task_with_todo_sync(task_config, todo_id)
            task_results.append(result)
        
        # 3. 整合结果
        success_count = sum(1 for r in task_results if r['success'])
        total_count = len(task_results)
        
        print(f"📊 并行Task执行完成: {success_count}/{total_count} 成功")
        
        return {
            'results': task_results,
            'success_rate': success_count / total_count,
            'total_count': total_count,
            'success_count': success_count
        }
    
    def execute_fallback_method(self, task_config):
        """Task失败时的回退方法"""
        try:
            # 根据Task类型选择合适的回退方法
            if task_config['type'] == 'search':
                return self.fallback_glob_grep_search(task_config)
            elif task_config['type'] == 'verification':
                return self.fallback_manual_verification(task_config)
            elif task_config['type'] == 'analysis':
                return self.fallback_read_analysis(task_config)
            else:
                return {'success': False, 'result': None}
        except Exception as e:
            return {'success': False, 'result': None}
    
    def fallback_glob_grep_search(self, task_config):
        """搜索类Task的回退方法"""
        # 使用传统的Glob和Grep工具
        # 实现具体的回退逻辑
        return {'success': True, 'result': 'fallback_search_result'}
    
    def fallback_manual_verification(self, task_config):
        """验证类Task的回退方法"""
        # 使用Read工具进行手动验证
        # 实现具体的回退逻辑
        return {'success': True, 'result': 'fallback_verification_result'}
    
    def fallback_read_analysis(self, task_config):
        """分析类Task的回退方法"""
        # 使用Read工具进行文件分析
        # 实现具体的回退逻辑
        return {'success': True, 'result': 'fallback_analysis_result'}
```

### 🔗 动态任务管理和循环控制

```python
class DynamicTaskManager:
    """动态任务管理器 - 循环控制增强"""
    
    def __init__(self, todo_manager, task_integration):
        self.todo_manager = todo_manager
        self.task_integration = task_integration
        self.loop_context = {}
    
    def manage_hypothesis_loop_todos(self, max_rounds=5):
        """管理假设验证循环的动态任务"""
        
        current_round = 1
        
        while current_round <= max_rounds:
            print(f"\n--- 第{current_round}轮假设验证循环 ---")
            
            # 1. 为当前轮次创建动态任务
            round_todos = self.create_hypothesis_round_todos(current_round)
            self.todo_manager.current_todos.extend(round_todos)
            
            # 2. 执行假设建立任务
            hypothesis_task_id = f'hypothesis_build_round_{current_round}'
            self.todo_manager.update_task_status(hypothesis_task_id, 'in_progress')
            
            hypotheses = self.build_hypotheses_for_round(current_round)
            
            self.todo_manager.update_task_status(hypothesis_task_id, 'completed')
            
            # 3. 为高优先级假设创建验证任务
            verification_tasks = self.create_hypothesis_verification_tasks(hypotheses)
            verification_todos = self.todo_manager.create_dynamic_verification_todos(verification_tasks)
            self.todo_manager.current_todos.extend(verification_todos)
            
            # 4. 执行假设验证
            verification_results = self.task_integration.execute_parallel_tasks_with_todos(verification_tasks)
            
            # 5. 评估循环退出条件
            exit_condition_task_id = f'exit_condition_round_{current_round}'
            self.todo_manager.update_task_status(exit_condition_task_id, 'in_progress')
            
            should_exit = self.evaluate_hypothesis_exit_conditions(verification_results, current_round)
            
            self.todo_manager.update_task_status(exit_condition_task_id, 'completed')
            
            # 6. 循环控制决策
            if should_exit:
                print(f"✅ 假设验证循环退出条件满足，结束循环（共{current_round}轮）")
                break
            
            current_round += 1
            
            # 7. 清理已完成的轮次任务，准备下一轮
            self.cleanup_completed_round_todos(current_round - 1)
        
        return {
            'total_rounds': current_round,
            'exit_reason': 'conditions_met' if should_exit else 'max_rounds_reached'
        }
    
    def create_hypothesis_round_todos(self, round_num):
        """为假设验证轮次创建任务"""
        return [
            {
                'content': f'第{round_num}轮假设建立',
                'status': 'pending',
                'priority': 'high',
                'id': f'hypothesis_build_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮假设验证',
                'status': 'pending',
                'priority': 'high',
                'id': f'hypothesis_verify_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮退出条件评估',
                'status': 'pending',
                'priority': 'high',
                'id': f'exit_condition_round_{round_num}'
            }
        ]
    
    def manage_fix_loop_todos(self, max_rounds=5):
        """管理修复实施循环的动态任务"""
        
        current_round = 1
        
        while current_round <= max_rounds:
            print(f"\n--- 第{current_round}轮修复实施循环 ---")
            
            # 1. 为当前轮次创建动态任务
            round_todos = self.create_fix_round_todos(current_round)
            self.todo_manager.current_todos.extend(round_todos)
            
            # 2. 执行修复计划制定
            plan_task_id = f'fix_plan_round_{current_round}'
            self.todo_manager.update_task_status(plan_task_id, 'in_progress')
            
            fix_plan = self.create_fix_plan_for_round(current_round)
            
            self.todo_manager.update_task_status(plan_task_id, 'completed')
            
            # 3. 执行修复实施
            impl_task_id = f'fix_impl_round_{current_round}'
            self.todo_manager.update_task_status(impl_task_id, 'in_progress')
            
            impl_results = self.implement_fixes(fix_plan)
            
            self.todo_manager.update_task_status(impl_task_id, 'completed')
            
            # 4. 执行修复验证
            verify_task_id = f'fix_verify_round_{current_round}'
            
            verification_config = {
                'description': f'第{current_round}轮修复效果验证',
                'prompt': f'验证修复效果和质量评估',
                'type': 'verification'
            }
            
            verification_result = self.task_integration.execute_task_with_todo_sync(
                verification_config, verify_task_id
            )
            
            # 5. 评估循环退出条件
            exit_condition_task_id = f'fix_exit_condition_round_{current_round}'
            self.todo_manager.update_task_status(exit_condition_task_id, 'in_progress')
            
            should_exit = self.evaluate_fix_exit_conditions(verification_result, current_round)
            
            self.todo_manager.update_task_status(exit_condition_task_id, 'completed')
            
            # 6. 循环控制决策
            if should_exit:
                print(f"✅ 修复验证循环退出条件满足，结束循环（共{current_round}轮）")
                break
            
            current_round += 1
            
            # 7. 清理已完成的轮次任务
            self.cleanup_completed_round_todos(current_round - 1)
        
        return {
            'total_rounds': current_round,
            'exit_reason': 'conditions_met' if should_exit else 'max_rounds_reached'
        }
    
    def create_fix_round_todos(self, round_num):
        """为修复实施轮次创建任务"""
        return [
            {
                'content': f'第{round_num}轮修复计划制定',
                'status': 'pending',
                'priority': 'high',
                'id': f'fix_plan_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮修复实施',
                'status': 'pending',
                'priority': 'high',
                'id': f'fix_impl_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮修复验证',
                'status': 'pending',
                'priority': 'high',
                'id': f'fix_verify_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮退出条件评估',
                'status': 'pending',
                'priority': 'high',
                'id': f'fix_exit_condition_round_{round_num}'
            }
        ]
    
    def cleanup_completed_round_todos(self, round_num):
        """清理已完成轮次的任务"""
        # 移除已完成的轮次任务，保持任务列表简洁
        self.todo_manager.current_todos = [
            todo for todo in self.todo_manager.current_todos
            if not (todo['id'].endswith(f'_round_{round_num}') and todo['status'] == 'completed')
        ]
```

### 🚪 门禁条件与TodoWrite验证联动

```python
class GateConditionManager:
    """门禁条件管理器 - TodoWrite验证联动"""
    
    def __init__(self, todo_manager):
        self.todo_manager = todo_manager
        self.gate_conditions = {}
    
    def check_stage_gate_conditions_with_todos(self, stage, context):
        """检查阶段门禁条件并创建验证任务"""
        
        gate_configs = self.get_gate_condition_configs(stage)
        
        # 为每个门禁条件创建验证任务
        gate_todos = []
        for condition_name, config in gate_configs.items():
            gate_todo = {
                'content': f'    - 门禁验证: {config["description"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'gate_{stage.lower()}_{condition_name}'
            }
            gate_todos.append(gate_todo)
        
        self.todo_manager.current_todos.extend(gate_todos)
        
        # 执行门禁条件检查
        gate_results = {}
        for condition_name, config in gate_configs.items():
            task_id = f'gate_{stage.lower()}_{condition_name}'
            
            self.todo_manager.update_task_status(task_id, 'in_progress')
            
            try:
                result = self.verify_gate_condition(condition_name, config, context)
                gate_results[condition_name] = result
                
                if result:
                    self.todo_manager.update_task_status(task_id, 'completed')
                    print(f"✅ 门禁条件通过: {config['description']}")
                else:
                    print(f"❌ 门禁条件未满足: {config['description']}")
                    # 保持in_progress状态，等待重试或修复
                    
            except Exception as e:
                print(f"❌ 门禁条件检查异常: {config['description']} - {str(e)}")
                gate_results[condition_name] = False
        
        # 评估整体门禁通过情况
        passed_count = sum(1 for result in gate_results.values() if result)
        total_count = len(gate_results)
        
        gate_passed = passed_count == total_count
        
        print(f"📊 {stage}门禁条件检查结果: {passed_count}/{total_count} ({'✅ 通过' if gate_passed else '❌ 未通过'})")
        
        return {
            'passed': gate_passed,
            'results': gate_results,
            'passed_count': passed_count,
            'total_count': total_count
        }
    
    def get_gate_condition_configs(self, stage):
        """获取阶段门禁条件配置"""
        
        gate_configs = {
            'STAGE1': {
                'task_search_completed': {
                    'description': 'Task搜索完成',
                    'check_method': 'verify_task_completion'
                },
                'core_symptoms_identified': {
                    'description': '核心症状识别',
                    'check_method': 'verify_symptom_identification'
                },
                'discovery_threshold': {
                    'description': '发现量达标',
                    'check_method': 'verify_discovery_threshold'
                },
                'task_quality_threshold': {
                    'description': 'Task质量达标',
                    'check_method': 'verify_task_quality'
                },
                'supplementary_verification': {
                    'description': '补充验证通过',
                    'check_method': 'verify_supplementary_checks'
                }
            },
            'STAGE2': {
                'hypothesis_generation': {
                    'description': '假设生成质量',
                    'check_method': 'verify_hypothesis_quality'
                },
                'verification_completion': {
                    'description': '验证执行完整性',
                    'check_method': 'verify_verification_completeness'
                },
                'problem_identification': {
                    'description': '问题识别准确性',
                    'check_method': 'verify_problem_identification'
                },
                'solution_readiness': {
                    'description': '解决方案就绪度',
                    'check_method': 'verify_solution_readiness'
                }
            },
            'STAGE3': {
                'fix_implementation': {
                    'description': '修复实施完整性',
                    'check_method': 'verify_fix_implementation'
                },
                'verification_quality': {
                    'description': '验证质量达标',
                    'check_method': 'verify_verification_quality'
                },
                'problem_resolution': {
                    'description': '问题解决确认',
                    'check_method': 'verify_problem_resolution'
                },
                'stability_verification': {
                    'description': '稳定性验证',
                    'check_method': 'verify_stability'
                }
            }
        }
        
        return gate_configs.get(stage, {})
    
    def verify_gate_condition(self, condition_name, config, context):
        """验证具体的门禁条件"""
        
        check_method = getattr(self, config['check_method'], None)
        if not check_method:
            print(f"⚠️ 未找到检查方法: {config['check_method']}")
            return False
        
        try:
            return check_method(context)
        except Exception as e:
            print(f"❌ 门禁条件检查异常: {condition_name} - {str(e)}")
            return False
    
    def verify_task_completion(self, context):
        """验证Task搜索完成"""
        task_results = context.get('task_results', {})
        success_count = task_results.get('success_count', 0)
        total_count = task_results.get('total_count', 0)
        
        return success_count >= total_count * 0.75  # 至少75%成功率
    
    def verify_symptom_identification(self, context):
        """验证核心症状识别"""
        symptoms = context.get('identified_symptoms', [])
        return len(symptoms) >= 2  # 至少识别2个核心症状
    
    def verify_discovery_threshold(self, context):
        """验证发现量达标"""
        discoveries = context.get('discoveries', [])
        return len(discoveries) >= 5  # 至少5个有效发现
    
    def verify_task_quality(self, context):
        """验证Task质量达标"""
        quality_score = context.get('task_quality_score', 0)
        return quality_score >= 0.6  # 质量分数至少0.6
    
    def verify_supplementary_checks(self, context):
        """验证补充验证通过"""
        supplementary_results = context.get('supplementary_results', [])
        passed_count = sum(1 for result in supplementary_results if result.get('passed', False))
        return passed_count >= len(supplementary_results) * 0.8  # 至少80%通过
    
    # 其他验证方法的实现...
    def verify_hypothesis_quality(self, context):
        """验证假设生成质量"""
        hypotheses = context.get('hypotheses', [])
        return len(hypotheses) >= 8  # 至少8个假设
    
    def verify_verification_completeness(self, context):
        """验证验证执行完整性"""
        verification_results = context.get('verification_results', [])
        return len(verification_results) >= 4  # 至少4个验证结果
    
    def verify_problem_identification(self, context):
        """验证问题识别准确性"""
        identified_problems = context.get('identified_problems', [])
        return len(identified_problems) >= 2  # 至少识别2个问题
    
    def verify_solution_readiness(self, context):
        """验证解决方案就绪度"""
        solutions = context.get('solutions', [])
        return len(solutions) >= 1  # 至少1个可行解决方案
    
    def verify_fix_implementation(self, context):
        """验证修复实施完整性"""
        implemented_fixes = context.get('implemented_fixes', [])
        return len(implemented_fixes) >= 1  # 至少实施1个修复
    
    def verify_verification_quality(self, context):
        """验证验证质量达标"""
        verification_quality = context.get('verification_quality', 0)
        return verification_quality >= 0.7  # 验证质量至少0.7
    
    def verify_problem_resolution(self, context):
        """验证问题解决确认"""
        resolution_status = context.get('problem_resolved', False)
        return resolution_status == True
    
    def verify_stability(self, context):
        """验证稳定性验证"""
        stability_checks = context.get('stability_checks', [])
        passed_checks = sum(1 for check in stability_checks if check.get('passed', False))
        return passed_checks >= len(stability_checks) * 0.9  # 至少90%稳定性检查通过
```

## 🚀 完整流程实现

### 📍 主流程控制器

```python
class TodoWriteDiagnosticFlow:
    """TodoWrite架构诊断修复流程主控制器"""
    
    def __init__(self, problem_description):
        self.problem_description = problem_description
        self.current_state = 'INIT'
        self.context = {}
        
        # 初始化管理组件
        self.todo_manager = TodoWriteManager()
        self.task_integration = TaskTodoIntegration(self.todo_manager)
        self.dynamic_manager = DynamicTaskManager(self.todo_manager, self.task_integration)
        self.gate_manager = GateConditionManager(self.todo_manager)
        
        # 状态机
        self.state_machine = DiagnosticStateMachine()
    
    def execute_diagnostic_flow(self):
        """执行完整的诊断修复流程"""
        
        print(f"🚀 启动TodoWrite架构诊断修复流程")
        print(f"📋 问题描述: {self.problem_description}")
        
        # 创建初始TodoWrite任务列表
        self.initialize_todo_list()
        
        try:
            # 阶段1: 信息收集与真实检查
            if self.current_state == 'INIT':
                self.execute_stage1_collecting()
            
            # 阶段2: 假设验证循环
            if self.current_state == 'STAGE1_COLLECTING':
                self.execute_stage2_hypothesis_loop()
            
            # 阶段3: 修复实施验证循环
            if self.current_state == 'STAGE2_HYPOTHESIS_LOOP':
                self.execute_stage3_fix_loop()
            
            # 阶段4: 总结与发散优化
            if self.current_state == 'STAGE3_FIX_LOOP':
                self.execute_stage4_summary()
            
            # 流程完成
            self.current_state = 'COMPLETED'
            self.finalize_todo_list()
            
            print(f"🏁 TodoWrite架构诊断流程完成: {self.current_state}")
            
        except Exception as e:
            self.current_state = 'FAILED'
            print(f"❌ 诊断流程执行失败: {str(e)}")
            self.handle_flow_failure()
    
    def initialize_todo_list(self):
        """初始化TodoWrite任务列表"""
        
        # 同步状态机并创建初始任务
        self.todo_manager.sync_with_state_machine('STAGE1_COLLECTING')
        
        # 输出当前任务列表
        self.display_current_todos()
    
    def execute_stage1_collecting(self):
        """执行阶段1：信息收集与真实检查"""
        
        print(f"\n🔍 [STAGE1_COLLECTING] 阶段1：信息收集与真实检查")
        
        # 更新阶段任务状态
        self.todo_manager.update_task_status('stage_stage1', 'in_progress')
        
        # 步骤1.1: 问题信息解析
        step_1_1_result = self.execute_step_1_1_problem_analysis()
        
        # 步骤1.2: Task驱动并行搜索验证
        step_1_2_result = self.execute_step_1_2_task_driven_search()
        
        # 门禁条件检查
        gate_result = self.gate_manager.check_stage_gate_conditions_with_todos('STAGE1', self.context)
        
        if gate_result['passed']:
            self.todo_manager.update_task_status('stage_stage1', 'completed')
            self.current_state = 'STAGE2_HYPOTHESIS_LOOP'
            print(f"✅ 阶段1门禁条件满足，进入阶段2")
        else:
            print(f"❌ 阶段1门禁条件不满足，流程终止")
            self.current_state = 'FAILED'
    
    def execute_step_1_1_problem_analysis(self):
        """执行步骤1.1：问题信息解析"""
        
        self.todo_manager.update_task_status('step_stage1_1', 'in_progress')
        
        print(f"执行步骤1.1: 问题信息解析...")
        
        # 解析问题描述，提取核心信息
        core_symptoms = self.extract_core_symptoms(self.problem_description)
        functional_modules = self.identify_functional_modules(self.problem_description)
        search_strategy = self.create_search_strategy(core_symptoms, functional_modules)
        
        # 更新上下文
        self.context.update({
            'core_symptoms': core_symptoms,
            'functional_modules': functional_modules,
            'search_strategy': search_strategy,
            'identified_symptoms': core_symptoms
        })
        
        print(f"  提取核心症状：{len(core_symptoms)}个")
        print(f"  识别功能模块：{len(functional_modules)}个")
        print(f"  制定搜索策略：{len(search_strategy.get('parallel_tasks', []))}个并行Task")
        
        self.todo_manager.update_task_status('step_stage1_1', 'completed')
        
        return {
            'core_symptoms': core_symptoms,
            'functional_modules': functional_modules,
            'search_strategy': search_strategy
        }
    
    def execute_step_1_2_task_driven_search(self):
        """执行步骤1.2：Task驱动并行搜索验证"""
        
        self.todo_manager.update_task_status('step_stage1_2', 'in_progress')
        
        print(f"执行步骤1.2: Task驱动并行搜索验证...")
        
        # 获取搜索策略
        search_strategy = self.context.get('search_strategy', {})
        parallel_tasks = search_strategy.get('parallel_tasks', [])
        
        # 执行并行Task搜索
        search_results = self.task_integration.execute_parallel_tasks_with_todos(parallel_tasks)
        
        # 整合搜索结果
        discoveries = self.integrate_search_results(search_results)
        supplementary_results = self.execute_supplementary_verification(discoveries)
        
        # 计算质量评分
        quality_score = self.calculate_search_quality(search_results, discoveries)
        
        # 更新上下文
        self.context.update({
            'task_results': search_results,
            'discoveries': discoveries,
            'supplementary_results': supplementary_results,
            'task_quality_score': quality_score
        })
        
        print(f"📊 阶段1质量评分: {quality_score:.2f}/1.0")
        
        self.todo_manager.update_task_status('step_stage1_2', 'completed')
        
        return {
            'task_results': search_results,
            'discoveries': discoveries,
            'quality_score': quality_score
        }
    
    def execute_stage2_hypothesis_loop(self):
        """执行阶段2：假设验证循环"""
        
        print(f"\n🔬 [STAGE2_HYPOTHESIS_LOOP] 阶段2：假设验证循环")
        
        # 创建阶段2任务
        stage2_todos = self.todo_manager.create_stage_todos('STAGE2')
        self.todo_manager.current_todos.extend(stage2_todos)
        
        # 更新阶段任务状态
        self.todo_manager.update_task_status('stage_stage2', 'in_progress')
        
        # 执行假设验证循环
        loop_result = self.dynamic_manager.manage_hypothesis_loop_todos(max_rounds=5)
        
        # 门禁条件检查
        gate_result = self.gate_manager.check_stage_gate_conditions_with_todos('STAGE2', self.context)
        
        if gate_result['passed']:
            self.todo_manager.update_task_status('stage_stage2', 'completed')
            self.current_state = 'STAGE3_FIX_LOOP'
            print(f"✅ 阶段2门禁条件满足，进入阶段3")
        else:
            print(f"❌ 阶段2门禁条件不满足，流程终止")
            self.current_state = 'FAILED'
    
    def execute_stage3_fix_loop(self):
        """执行阶段3：修复实施验证循环"""
        
        print(f"\n🔧 [STAGE3_FIX_LOOP] 阶段3：修复实施验证循环")
        
        # 创建阶段3任务
        stage3_todos = self.todo_manager.create_stage_todos('STAGE3')
        self.todo_manager.current_todos.extend(stage3_todos)
        
        # 更新阶段任务状态
        self.todo_manager.update_task_status('stage_stage3', 'in_progress')
        
        # 执行修复实施循环
        loop_result = self.dynamic_manager.manage_fix_loop_todos(max_rounds=5)
        
        # 门禁条件检查
        gate_result = self.gate_manager.check_stage_gate_conditions_with_todos('STAGE3', self.context)
        
        if gate_result['passed']:
            self.todo_manager.update_task_status('stage_stage3', 'completed')
            self.current_state = 'STAGE4_SUMMARY'
            print(f"✅ 阶段3门禁条件满足，进入阶段4")
        else:
            print(f"❌ 阶段3门禁条件不满足，流程终止")
            self.current_state = 'FAILED'
    
    def execute_stage4_summary(self):
        """执行阶段4：总结与发散优化"""
        
        print(f"\n📋 [STAGE4_SUMMARY] 阶段4：总结与发散优化")
        
        # 创建阶段4任务
        stage4_todos = self.todo_manager.create_stage_todos('STAGE4')
        self.todo_manager.current_todos.extend(stage4_todos)
        
        # 更新阶段任务状态
        self.todo_manager.update_task_status('stage_stage4', 'in_progress')
        
        # 执行总结任务
        summary_result = self.create_diagnostic_summary()
        optimization_suggestions = self.generate_optimization_suggestions()
        
        # 更新上下文
        self.context.update({
            'summary': summary_result,
            'optimizations': optimization_suggestions
        })
        
        self.todo_manager.update_task_status('stage_stage4', 'completed')
        
        return {
            'summary': summary_result,
            'optimizations': optimization_suggestions
        }
    
    def display_current_todos(self):
        """显示当前TodoWrite任务状态"""
        
        print(f"\n📋 当前TodoWrite任务状态:")
        
        current_todos = self.todo_manager.get_current_todos()
        
        for todo in current_todos:
            status_icon = {
                'pending': '⏳',
                'in_progress': '🔄', 
                'completed': '✅'
            }.get(todo['status'], '❓')
            
            print(f"  {status_icon} {todo['content']} ({todo['priority']})")
        
        # 统计信息
        total = len(current_todos)
        completed = sum(1 for todo in current_todos if todo['status'] == 'completed')
        in_progress = sum(1 for todo in current_todos if todo['status'] == 'in_progress')
        pending = sum(1 for todo in current_todos if todo['status'] == 'pending')
        
        print(f"\n📊 任务统计: 总计{total}个，已完成{completed}个，进行中{in_progress}个，待处理{pending}个")
    
    def finalize_todo_list(self):
        """完成TodoWrite任务列表"""
        
        # 标记所有剩余任务为完成
        for todo in self.todo_manager.current_todos:
            if todo['status'] != 'completed':
                self.todo_manager.update_task_status(todo['id'], 'completed')
        
        # 显示最终状态
        self.display_current_todos()
    
    # 辅助方法实现
    def extract_core_symptoms(self, problem_description):
        """提取核心症状"""
        # 实现症状提取逻辑
        return ['症状1', '症状2', '症状3']
    
    def identify_functional_modules(self, problem_description):
        """识别功能模块"""
        # 实现模块识别逻辑
        return ['模块1', '模块2']
    
    def create_search_strategy(self, symptoms, modules):
        """创建搜索策略"""
        # 实现搜索策略制定逻辑
        return {
            'parallel_tasks': [
                {'description': '项目架构发现', 'type': 'search'},
                {'description': '问题相关搜索', 'type': 'search'},
                {'description': '系统状态检查', 'type': 'verification'},
                {'description': '依赖关系分析', 'type': 'analysis'}
            ]
        }
    
    def integrate_search_results(self, search_results):
        """整合搜索结果"""
        # 实现结果整合逻辑
        return ['发现1', '发现2', '发现3', '发现4', '发现5']
    
    def execute_supplementary_verification(self, discoveries):
        """执行补充验证"""
        # 实现补充验证逻辑
        return [{'passed': True}, {'passed': True}, {'passed': True}]
    
    def calculate_search_quality(self, search_results, discoveries):
        """计算搜索质量"""
        # 实现质量计算逻辑
        success_rate = search_results.get('success_rate', 0)
        discovery_count = len(discoveries)
        return min(1.0, success_rate * 0.7 + (discovery_count / 10) * 0.3)
    
    def build_hypotheses_for_round(self, round_num):
        """为指定轮次建立假设"""
        # 实现假设建立逻辑
        return ['假设1', '假设2', '假设3', '假设4']
    
    def create_hypothesis_verification_tasks(self, hypotheses):
        """创建假设验证任务"""
        # 实现验证任务创建逻辑
        return [
            {'description': f'验证假设: {h}', 'type': 'verification'}
            for h in hypotheses[:6]  # 最多6个高优先级假设
        ]
    
    def evaluate_hypothesis_exit_conditions(self, verification_results, round_num):
        """评估假设验证循环退出条件"""
        # 实现退出条件评估逻辑
        success_rate = verification_results.get('success_rate', 0)
        return success_rate >= 0.6 or round_num >= 3
    
    def create_fix_plan_for_round(self, round_num):
        """为指定轮次创建修复计划"""
        # 实现修复计划创建逻辑
        return ['修复项1', '修复项2']
    
    def implement_fixes(self, fix_plan):
        """实施修复"""
        # 实现修复实施逻辑
        return {'implemented_fixes': fix_plan}
    
    def evaluate_fix_exit_conditions(self, verification_result, round_num):
        """评估修复验证循环退出条件"""
        # 实现退出条件评估逻辑
        return verification_result.get('success', False)
    
    def create_diagnostic_summary(self):
        """创建诊断总结"""
        # 实现总结创建逻辑
        return "诊断总结内容"
    
    def generate_optimization_suggestions(self):
        """生成优化建议"""
        # 实现优化建议生成逻辑
        return ["优化建议1", "优化建议2"]
    
    def handle_flow_failure(self):
        """处理流程失败"""
        print(f"❌ TodoWrite架构诊断流程失败，当前状态: {self.current_state}")
        self.display_current_todos()


# 使用示例
def execute_todowrite_diagnostic(problem_description):
    """执行TodoWrite架构诊断流程"""
    
    flow = TodoWriteDiagnosticFlow(problem_description)
    flow.execute_diagnostic_flow()
    
    return flow.context
```
