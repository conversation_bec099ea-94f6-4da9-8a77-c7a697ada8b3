# 面向AI编程的系统性工作流程汇总 

> 🎯 **说明**: 把 `$ARGUMENTS`作为输入，从下面选择适合的流程，按照流程执行

---

## 📋 流程索引

- [🐛 系统性诊断与修复流程](#-系统性诊断与修复流程)
- [✅ 系统性合规检查流程](#-系统性合规检查流程)
- [🧪 测试驱动开发流程](#-测试驱动开发流程)
- [🔄 代码重构流程](#-代码重构流程)
- [🌐 API设计与实现流程](#-api设计与实现流程)

---

## 🐛 系统性诊断与修复流程

#### D1: Diagnose (诊断阶段)
1. **现象提取：What happened?**
   - 提取用户描述、日志、错误信息
   - 区分"实际现象"vs"期望行为"

2. **环境上下文：Where & When?**
   - 代码版本、数据状态、操作序列
   - 系统架构、组件关系

#### D2: Drill-down (深入分析)
3. **数据流回溯：How data flows?**
   - 从异常点向上游追溯数据来源
   - 识别每个处理环节的输入输出

4. **状态一致性检查：Which state is wrong?**
   - 对比不同层级的数据状态
   - 发现状态不一致的环节

#### A1: Analyze (根因分析)
5. **影响域映射：What components involved?**
   - 确定涉及的文件、类、方法
   - 分析组件间的依赖关系

6. **根因假设：Why it happens?**
   - 生成多个可能的根因假设
   - 按可信度排序，优先验证

#### R1: Repair (修复实施)
7. **最小化修复：How to fix minimally?**
   - 基于根因设计最小改动方案
   - 复用现有架构和模式

8. **多层验证：How to ensure quality?**
   - 语法检查、逻辑验证、集成测试
   - 考虑边界情况和回归风险

---

## ✅ 系统性合规检查流程

#### R1: Rules (规则明确阶段)
1. **规则提取：What are the standards?**
   - 从项目文档（CLAUDE.md、规范文档）中提取具体规则
   - 区分"必须遵守"vs"建议遵守"的规则等级
   - 明确每条规则的具体判断标准

2. **检查范围：What to check?**
   - 明确检查对象的边界和范围
   - 确定检查的粒度和深度
   - 识别检查的优先级和重要性

#### C1: Collect (现状收集阶段)
3. **现状获取：What is the current state?**
   - 系统性收集被检查对象的当前状态
   - 确保数据完整性和准确性
   - 使用适当的工具和方法获取信息

4. **结构化整理：How to organize data?**
   - 按规则类别整理现状数据
   - 建立便于对比的数据结构
   - 确保数据的可追溯性和完整性

#### A1: Assess (合规评估阶段)
5. **逐项对比：Does it comply?**
   - 将现状与规则逐一对比
   - 标记符合/不符合/部分符合状态
   - 记录具体的偏差和问题

6. **影响分析：What's the impact?**
   - 分析不合规项的影响程度和范围
   - 按风险级别和修复难度排序
   - 评估合规性对系统整体的影响

#### R2: Report (结果报告阶段)
7. **合规报告：What's the conclusion?**
   - 生成总体合规率和分类统计
   - 详细列出所有不合规项
   - 提供具体的、可执行的改进建议

8. **持续跟踪：How to maintain compliance?**
   - 建立合规检查的监控机制
   - 设定定期检查和更新周期
   - 预防未来的合规性问题

---

## 🧪 测试驱动开发流程

#### T1: Test Design (测试设计阶段)
1. **需求澄清：What exactly to build?**
   - 明确功能需求和验收标准
   - 识别关键业务逻辑和异常场景
   - 确定输入、输出和边界条件

2. **测试用例设计：How to verify correctness?**
   - 设计正向、边界和异常测试用例
   - 遵循AAA模式（Arrange-Act-Assert）
   - 确保测试的独立性和可重复性

#### D1: Development (开发阶段)
3. **红阶段：Make it fail**
   - 编写测试，确认失败（红灯）
   - 验证测试逻辑正确性
   - 记录失败原因和预期行为

4. **绿阶段：Make it work**
   - 编写最小可行代码使测试通过
   - 优先功能实现，暂不考虑优化
   - 确保所有测试通过（绿灯）

#### R1: Refactor (重构阶段)
5. **重构优化：Make it better**
   - 在保持测试通过的前提下优化代码
   - 消除重复代码和坏味道
   - 提高代码的可读性和可维护性

---

## 🔄 代码重构流程

#### A1: Analysis (分析阶段)
1. **代码扫描：What needs improvement?**
   - 使用静态分析工具扫描代码质量
   - 识别代码坏味道和复杂度问题
   - 评估代码的可读性和可维护性

2. **重构规划：How to improve systematically?**
   - 制定重构的优先级和计划
   - 选择合适的重构技术和模式
   - 评估重构的风险和收益

#### S1: Safety (安全保障阶段)
3. **测试保障：How to ensure safety?**
   - 建立完善的测试覆盖
   - 制定重构的验证检查点
   - 准备重构的回滚机制

#### R1: Refactoring (重构实施阶段)
4. **分步重构：How to refactor incrementally?**
   - 按计划分步骤实施重构
   - 每个步骤后运行测试验证
   - 保持代码始终处于可工作状态

5. **质量验证：How to verify improvements?**
   - 运行完整的测试套件
   - 检查重构后的代码质量指标
   - 确保功能行为完全一致

---

## 🌐 API设计与实现流程

#### D1: Design (设计阶段)
1. **需求分析：What API functionality needed?**
   - 分析API的业务需求和使用场景
   - 确定API的输入输出和数据格式
   - 识别API的性能和安全要求

2. **接口设计：How to structure the API?**
   - 设计API的URL结构和HTTP方法
   - 定义请求响应的数据模型
   - 规划API的版本管理策略

#### I1: Implementation (实现阶段)
3. **核心实现：How to implement business logic?**
   - 实现API的核心业务逻辑
   - 添加输入验证和错误处理
   - 确保代码的可测试性

4. **集成测试：How to verify API correctness?**
   - 编写API的集成测试用例
   - 测试正常流程和异常情况
   - 验证API的性能和安全性

#### D2: Documentation (文档阶段)
5. **API文档：How to document for users?**
   - 编写清晰的API使用文档
   - 提供示例代码和使用案例
   - 说明错误码和故障排除方法

---

## 📊 流程选择指南

### 🎯 选择原则

| 场景类型 | 推荐流程 | 关键特征 |
|---------|---------|---------|
| 🐛 问题修复 | 系统性诊断与修复 | 复杂问题、需要根因分析 |
| ✅ 规范检查 | 系统性合规检查 | 规范验证、标准化要求 |
| 🆕 新功能开发 | 测试驱动开发 | 需求明确、质量要求高 |
| 🔄 代码改进 | 代码重构 | 技术债务、代码质量问题 |
| 🌐 接口开发 | API设计与实现 | 接口设计、服务集成 |

### 🎯 流程组合使用

**新功能开发建议组合**:
```
测试驱动开发 → 合规检查 → API设计(如需要) → 代码重构(如需要)
```

**问题修复建议组合**:
```
诊断修复 → 合规检查 → 代码重构(如需要)
```

**代码质量提升建议组合**:
```
合规检查 → 代码重构 → 测试驱动开发(补充测试)
```
