[2025-07-23 14:45:30.71] SYSTEM: === LOG CLEARED BY ADMIN REQUEST ===
[2025-07-23 14:45:30.71] INFO: 📤 POST /api/admin/v1/logs/clear - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.15] INFO: 📥 GET /api/admin/v1/series - Client: 127.0.0.1
[2025-07-23 14:45:36.15] INFO: 📤 GET /api/admin/v1/series - Status: 200 - Time: 0.006s
[2025-07-23 14:45:36.16] INFO: 📥 GET /api/admin/v1/categories - Client: 127.0.0.1
[2025-07-23 14:45:36.19] INFO: 📤 GET /api/admin/v1/categories - Status: 200 - Time: 0.037s
[2025-07-23 14:45:36.20] INFO: 📥 GET /api/admin/v1/videos - Client: 127.0.0.1
[2025-07-23 14:45:36.20] INFO: 📤 GET /api/admin/v1/videos - Status: 200 - Time: 0.003s
[2025-07-23 14:45:36.20] INFO: 📥 GET /api/admin/v1/users - Client: 127.0.0.1
[2025-07-23 14:45:36.20] INFO: 📤 GET /api/admin/v1/users - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.20] INFO: 📥 GET /api/admin/v1/users/test-api-001 - Client: 127.0.0.1
[2025-07-23 14:45:36.20] INFO: 📤 GET /api/admin/v1/users/test-api-001 - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.21] INFO: 📥 GET /api/admin/v1/users/test-api-001/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.21] INFO: 📤 GET /api/admin/v1/users/test-api-001/purchases - Status: 200 - Time: 0.004s
[2025-07-23 14:45:36.21] INFO: 📥 GET /api/admin/v1/users/test-api-001/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.21] DEBUG: 🔍进度API：获取用户 test-api-001 基于购买记录的观看进度
[2025-07-23 14:45:36.21] INFO: 进度API成功：返回 50 条记录（基于购买记录）
[2025-07-23 14:45:36.21] INFO: 📤 GET /api/admin/v1/users/test-api-001/progress - Status: 200 - Time: 0.004s
[2025-07-23 14:45:36.22] INFO: 📥 GET /api/admin/v1/users/test-api-001/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.22] DEBUG: 🔍收藏API：获取用户 test-api-001 基于购买记录的收藏状态
[2025-07-23 14:45:36.22] INFO: 收藏API成功：返回 50 条记录（基于购买记录）
[2025-07-23 14:45:36.22] INFO: 📤 GET /api/admin/v1/users/test-api-001/favorites - Status: 200 - Time: 0.003s
[2025-07-23 14:45:36.22] INFO: 📥 GET /api/admin/v1/users/test-api-001/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.22] INFO: 📤 GET /api/admin/v1/users/test-api-001/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.22] INFO: 📥 GET /api/admin/v1/users/test-api-001/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.22] DEBUG: 🔍缓存API：获取用户 test-api-001 基于购买记录的缓存状态
[2025-07-23 14:45:36.22] INFO: 缓存API成功：返回 50 条记录（基于购买记录）
[2025-07-23 14:45:36.23] INFO: 📤 GET /api/admin/v1/users/test-api-001/cache - Status: 200 - Time: 0.004s
[2025-07-23 14:45:36.23] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001 - Client: 127.0.0.1
[2025-07-23 14:45:36.23] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001 - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.23] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.23] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/purchases - Status: 200 - Time: 0.003s
[2025-07-23 14:45:36.23] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.23] DEBUG: 🔍进度API：获取用户 test-plaintext-001 基于购买记录的观看进度
[2025-07-23 14:45:36.23] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.23] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/progress - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.23] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.23] DEBUG: 🔍收藏API：获取用户 test-plaintext-001 基于购买记录的收藏状态
[2025-07-23 14:45:36.24] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.24] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/favorites - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.24] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.24] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.24] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.24] DEBUG: 🔍缓存API：获取用户 test-plaintext-001 基于购买记录的缓存状态
[2025-07-23 14:45:36.24] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.24] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/cache - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.24] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb - Client: 127.0.0.1
[2025-07-23 14:45:36.24] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.24] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.25] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.25] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.25] DEBUG: 🔍进度API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的观看进度
[2025-07-23 14:45:36.25] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.25] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.25] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.25] DEBUG: 🔍收藏API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的收藏状态
[2025-07-23 14:45:36.25] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.25] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.25] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.25] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.26] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.26] DEBUG: 🔍缓存API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的缓存状态
[2025-07-23 14:45:36.26] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.26] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.26] INFO: 📥 GET /api/admin/v1/users/test-batch-001 - Client: 127.0.0.1
[2025-07-23 14:45:36.26] INFO: 📤 GET /api/admin/v1/users/test-batch-001 - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.26] INFO: 📥 GET /api/admin/v1/users/test-batch-001/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.26] INFO: 📤 GET /api/admin/v1/users/test-batch-001/purchases - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.26] INFO: 📥 GET /api/admin/v1/users/test-batch-001/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.26] DEBUG: 🔍进度API：获取用户 test-batch-001 基于购买记录的观看进度
[2025-07-23 14:45:36.26] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.26] INFO: 📤 GET /api/admin/v1/users/test-batch-001/progress - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.27] INFO: 📥 GET /api/admin/v1/users/test-batch-001/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.27] DEBUG: 🔍收藏API：获取用户 test-batch-001 基于购买记录的收藏状态
[2025-07-23 14:45:36.27] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.27] INFO: 📤 GET /api/admin/v1/users/test-batch-001/favorites - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.27] INFO: 📥 GET /api/admin/v1/users/test-batch-001/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.27] INFO: 📤 GET /api/admin/v1/users/test-batch-001/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.27] INFO: 📥 GET /api/admin/v1/users/test-batch-001/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.27] DEBUG: 🔍缓存API：获取用户 test-batch-001 基于购买记录的缓存状态
[2025-07-23 14:45:36.27] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.27] INFO: 📤 GET /api/admin/v1/users/test-batch-001/cache - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.27] INFO: 📥 GET /api/admin/v1/users/test-batch-002 - Client: 127.0.0.1
[2025-07-23 14:45:36.27] INFO: 📤 GET /api/admin/v1/users/test-batch-002 - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.28] INFO: 📥 GET /api/admin/v1/users/test-batch-002/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.28] INFO: 📤 GET /api/admin/v1/users/test-batch-002/purchases - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.28] INFO: 📥 GET /api/admin/v1/users/test-batch-002/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.28] DEBUG: 🔍进度API：获取用户 test-batch-002 基于购买记录的观看进度
[2025-07-23 14:45:36.28] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.28] INFO: 📤 GET /api/admin/v1/users/test-batch-002/progress - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.28] INFO: 📥 GET /api/admin/v1/users/test-batch-002/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.28] DEBUG: 🔍收藏API：获取用户 test-batch-002 基于购买记录的收藏状态
[2025-07-23 14:45:36.28] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.28] INFO: 📤 GET /api/admin/v1/users/test-batch-002/favorites - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.28] INFO: 📥 GET /api/admin/v1/users/test-batch-002/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.28] INFO: 📤 GET /api/admin/v1/users/test-batch-002/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.29] INFO: 📥 GET /api/admin/v1/users/test-batch-002/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.29] DEBUG: 🔍缓存API：获取用户 test-batch-002 基于购买记录的缓存状态
[2025-07-23 14:45:36.29] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.29] INFO: 📤 GET /api/admin/v1/users/test-batch-002/cache - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.29] INFO: 📥 GET /api/admin/v1/users/test-batch-003 - Client: 127.0.0.1
[2025-07-23 14:45:36.29] INFO: 📤 GET /api/admin/v1/users/test-batch-003 - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.29] INFO: 📥 GET /api/admin/v1/users/test-batch-003/purchases - Client: 127.0.0.1
[2025-07-23 14:45:36.29] INFO: 📤 GET /api/admin/v1/users/test-batch-003/purchases - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.29] INFO: 📥 GET /api/admin/v1/users/test-batch-003/progress - Client: 127.0.0.1
[2025-07-23 14:45:36.29] DEBUG: 🔍进度API：获取用户 test-batch-003 基于购买记录的观看进度
[2025-07-23 14:45:36.29] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.29] INFO: 📤 GET /api/admin/v1/users/test-batch-003/progress - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.30] INFO: 📥 GET /api/admin/v1/users/test-batch-003/favorites - Client: 127.0.0.1
[2025-07-23 14:45:36.30] DEBUG: 🔍收藏API：获取用户 test-batch-003 基于购买记录的收藏状态
[2025-07-23 14:45:36.30] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.30] INFO: 📤 GET /api/admin/v1/users/test-batch-003/favorites - Status: 200 - Time: 0.002s
[2025-07-23 14:45:36.30] INFO: 📥 GET /api/admin/v1/users/test-batch-003/settings - Client: 127.0.0.1
[2025-07-23 14:45:36.30] INFO: 📤 GET /api/admin/v1/users/test-batch-003/settings - Status: 200 - Time: 0.001s
[2025-07-23 14:45:36.30] INFO: 📥 GET /api/admin/v1/users/test-batch-003/cache - Client: 127.0.0.1
[2025-07-23 14:45:36.30] DEBUG: 🔍缓存API：获取用户 test-batch-003 基于购买记录的缓存状态
[2025-07-23 14:45:36.30] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-23 14:45:36.30] INFO: 📤 GET /api/admin/v1/users/test-batch-003/cache - Status: 200 - Time: 0.002s
