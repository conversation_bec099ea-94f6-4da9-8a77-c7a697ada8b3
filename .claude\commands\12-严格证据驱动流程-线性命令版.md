---
description: 基于 `$ARGUMENTS` 问题描述，按照 严格证据驱动流程-线性命令版 执行
version: 12
---

# 🚀 严格证据驱动流程-线性命令版

## 🔒 执行保障机制

```yaml
执行模式: 线性命令序列
分支处理: 全部转换为条件命令
积分系统: 自动计算，不可干预
证明要求: 每个命令必须有输出

违规即时惩罚:
  假装执行(无输出): -50分
  自己加分: -30分
  选择性跳过: -20分
  编造输出: -100分(终止)
```

## 📊 积分规则

```yaml
初始积分: 0
目标积分: >100(优秀)
失败阈值: <-20(重启)
作弊阈值: <-50(终止)
```

## 🎯 阶段0：项目认知命令序列

```bash
# ===== 阶段0开始 =====
# 当前积分：0 | 执行证明：0/9

# 检查项1：项目识别
cmd1: ls -la .
expect: file_list
if_empty: exit_with_error "空目录"
score: +5/-10

cmd2: find . -type f -name "*.py" | head -20
expect: python_files
if_empty: log_warning "无Python文件" 
score: +5/-5

# 检查项2：技术栈确认
cmd3: cat requirements.txt 2>/dev/null || cat setup.py 2>/dev/null || echo "未找到依赖文件"
expect: dependencies
if_contains: "未找到": -5
score: +5/-10

cmd4: grep -r "import\|from" --include="*.py" | grep -E "PyQt|tkinter|flask|django" | head -10
expect: framework_imports
if_empty: exit_with_error "无法识别框架"
score: +5/-10

# 检查项3：WSL环境确认
cmd5: pwd
expect: current_path
mandatory: true
score: +5/-10

cmd6: wslpath -w . 2>/dev/null || echo "非WSL环境"
expect: windows_path
if_contains: "非WSL": log_info "非WSL环境"
score: +5/0

cmd7: uname -a
expect: system_info
mandatory: true
score: +5/-10

# 检查项4：业务流程理解
cmd8: grep -r "class.*:" --include="*.py" | grep -E "(User|Course|Student|Teacher)" | head -10
expect: business_entities
if_empty: log_warning "未找到业务实体"
score: +5/-5

cmd9: find . -name "*model*.py" -o -name "*entity*.py" | xargs ls -la 2>/dev/null
expect: model_files
if_empty: log_info "无独立模型文件"
score: +5/-5

# 检查项5：数据库结构理解
cmd10: find . -name "*.sql" -o -name "*schema*" -o -name "*migration*" | head -10
expect: db_files
if_empty: log_info "无SQL文件"
score: +5/-5

cmd11: grep -r "CREATE TABLE\|class.*Model" --include="*.py" --include="*.sql" | head -10
expect: table_definitions
if_empty: log_warning "未找到表定义"
score: +5/-5

# 检查项6：API接口理解
cmd12: grep -r "@app.route\|@router\|path(" --include="*.py" | head -15
expect: api_routes
if_empty: log_warning "未找到API路由"
score: +5/-5

cmd13: find . -name "*api*.py" -o -name "*route*.py" -o -name "*view*.py" | xargs ls -la 2>/dev/null
expect: api_files
if_empty: log_info "无独立API文件"
score: +5/-5

# 检查项7：配置环境理解
cmd14: find . -name "*.ini" -o -name "*.conf" -o -name "*.yaml" -o -name "*.json" | grep -v node_modules | head -10
expect: config_files
if_empty: log_info "无配置文件"
score: +5/0

cmd15: ls -la *config* 2>/dev/null || echo "无config文件"
expect: config_pattern_files
score: +5/0

# 检查项8：规范提取（核心）
cmd16: cat CLAUDE.md | head -50
expect: project_rules
if_empty: exit_with_error "CLAUDE.md不存在"
mandatory: true
score: +5/-15

cmd17: grep -E "技术栈|规范|原则" CLAUDE.md | head -10
expect: key_rules
if_empty: log_warning "规范内容不完整"
score: +5/-10

# 检查项9：服务验证
cmd18: curl -s http://localhost:8000/ -o /dev/null -w "%{http_code}" || echo "000"
expect: http_code
if_equals: "000": log_info "服务未启动"
score: +5/0

cmd19: curl -s http://localhost:8000/ | head -20 || echo "无响应"
expect: service_response
if_contains: "无响应": log_info "服务不可用"
score: +5/0

cmd20: ps aux | grep -E "python.*server|uvicorn|gunicorn" | grep -v grep || echo "无Python服务进程"
expect: service_process
score: +5/0

# 阶段0积分审计
audit0: calculate_score
minimum_score: 45
if_less: restart_phase0
status: "阶段0完成 | 积分：{score}/90"
```

## 🔍 阶段1：强制证据收集序列

```bash
# ===== 阶段1开始 =====
# 前置条件：阶段0积分>=45
# 当前积分：{previous_score} | 执行证明：0/9

# 并行块1：日志收集（方法6）
parallel_start: log_collection

cmd21: find . -name "*.log" -type f | head -20
expect: log_files
mandatory: true
score: +5/-10

cmd22: tail -50 logs/*.log 2>/dev/null || echo "无日志文件"
expect: recent_logs
if_contains: "无日志": -5
score: +5/-10

cmd23: grep -i "error\|exception\|fail" logs/*.log 2>/dev/null | tail -30
expect: error_logs
score: +5/0

parallel_end: log_collection

# 并行块2：环境检查（方法8）
parallel_start: env_check

cmd24: ps aux | grep -E "python|java|node" | grep -v grep | head -10
expect: running_processes
mandatory: true
score: +5/-10

cmd25: netstat -tlnp 2>/dev/null | grep LISTEN || ss -tlnp | grep LISTEN
expect: listening_ports
mandatory: true
score: +5/-10

cmd26: df -h | grep -E "/$|/mnt"
expect: disk_usage
score: +5/-5

parallel_end: env_check

# 串行执行：分层症状确认（方法7）
wait_for: [log_collection, env_check]

cmd27: echo "表象层症状：" && echo "$ARGUMENTS" | head -3
expect: surface_symptoms
mandatory: true
score: +5/-10

cmd28: echo "系统层症状：" && echo "基于日志：$(grep -c ERROR logs/*.log 2>/dev/null || echo 0)个错误"
expect: system_symptoms
score: +5/-5

cmd29: echo "根因层线索：" && echo "待分析"
expect: root_clues
score: +5/-5

# 证据有效性验证（方法22）
validation_check: evidence_quality
criteria:
  - completeness: "日志文件>0 AND 错误信息明确"
  - relevance: "时间戳匹配问题时间"
  - specificity: "有具体错误代码或行号"
if_fail: return_to_cmd21
score: +10/-15

# 证据驱动分析（方法19）
analysis_requirement: evidence_based_only
forbidden_words: ["可能", "应该", "推测", "估计"]
if_violate: -20
score: +10/-20

# 阶段1积分审计
audit1: calculate_score
minimum_increment: 30
status: "阶段1完成 | 累计积分：{total_score}"
```

## 🎯 阶段2：逐层隔离验证序列

```bash
# ===== 阶段2开始 =====
# 前置条件：通过证据验证
# 当前积分：{previous_score} | 执行证明：0/8

# 并行验证块
parallel_start: isolation_check

# 服务层验证（方法3+2）
cmd30: curl -s http://localhost:8000/api/health || echo "健康检查失败"
expect: health_status
score: +5/-5

cmd31: curl -s http://localhost:8000/api/v1/test 2>/dev/null | jq . || echo "{}"
expect: api_response
score: +5/-5

# 路径层验证（方法9+10）
cmd32: grep -n "$ERROR_PATTERN" src/**/*.py 2>/dev/null | head -5
expect: error_location
score: +5/-5

cmd33: echo "数据流：输入->处理->输出" && echo "待追踪"
expect: data_flow
score: +5/-5

parallel_end: isolation_check

# 逐层隔离（方法11）
cmd34: echo "检查UI层..." && find . -name "*ui*.py" -o -name "*view*.py" | wc -l
expect: ui_layer_check
score: +5/-5

cmd35: echo "检查API层..." && grep -c "@router" src/**/*.py 2>/dev/null || echo "0"
expect: api_layer_check
score: +5/-5

cmd36: echo "检查DB层..." && grep -c "SELECT\|INSERT\|UPDATE" src/**/*.py 2>/dev/null || echo "0"
expect: db_layer_check
score: +5/-5

# 问题定位确认
location_confirm: layer_identified
if_not_clear: repeat_cmd34_36
score: +5/-10

# 阶段2积分审计
audit2: calculate_score
status: "阶段2完成 | 问题层次：{identified_layer} | 累计积分：{total_score}"
```

## 🔬 阶段3：根因验证序列

```bash
# ===== 阶段3开始 =====
# 前置条件：问题层次已确定
# 当前积分：{previous_score} | 执行证明：0/6

# 单一根因查找（方法12）
cmd37: echo "假设1：{based_on_evidence}" > hypothesis.tmp
expect: hypothesis_created
mandatory: true
score: +5/-10

# 代码路径验证（方法13）
cmd38: grep -A5 -B5 "{error_location}" {identified_file}
expect: code_context
mandatory: true
score: +5/-10

# 假设验证设计（方法20）
cmd39: echo "验证方案：{specific_test_command}" > verify_plan.tmp
expect: plan_created
mandatory: true
score: +5/-10

# 执行验证
cmd40: {execute_verification_command}
expect: verification_result
mandatory: true
score: +5/-10

# 结果判定
verify_result: check_hypothesis
if_success: proceed_to_phase4
if_partial: refine_hypothesis
if_fail: new_hypothesis
score: +20/-10

# 循环计数器
loop_count: hypothesis_attempts
max_loops: 3
if_exceed: return_to_phase1
score: -20

# 阶段3积分审计
audit3: calculate_score
status: "阶段3完成 | 根因确认：{root_cause} | 累计积分：{total_score}"
```

## 🛠️ 阶段4：修复实施序列

```bash
# ===== 阶段4开始 =====
# 前置条件：根因已验证
# 当前积分：{previous_score} | 执行证明：0/8

# 修复准备
parallel_start: fix_preparation

# 规范检查（方法17）
cmd41: grep "{relevant_rule_pattern}" CLAUDE.md
expect: applicable_rules
mandatory: true
score: +5/-10

# 功能重复检查（方法5）
cmd42: grep -r "{similar_function_pattern}" --include="*.py" | head -10
expect: existing_functions
score: +5/-5

parallel_end: fix_preparation

# 实施修复
cmd43: cp {target_file} {target_file}.backup
expect: backup_created
mandatory: true
score: +5/-10

cmd44: {apply_fix_command}
expect: fix_applied
mandatory: true
score: +5/-10

# 立即验证（方法4+1+2）
parallel_start: fix_verification

cmd45: diff {target_file}.backup {target_file}
expect: changes_made
mandatory: true
score: +5/-10

cmd46: {test_fix_command}
expect: fix_working
mandatory: true
score: +5/-10

cmd47: curl -X POST http://localhost:8000/api/test -d '{test_data}'
expect: api_success
score: +5/-5

parallel_end: fix_verification

# 修复结果判定
fix_result: evaluate_success
if_success: proceed_to_phase5 +30
if_technical_fail: retry_fix -10
if_logic_fail: return_to_phase3 -20
if_root_fail: return_to_phase1 -30

# 阶段4积分审计
audit4: calculate_score
status: "阶段4完成 | 修复状态：{fix_status} | 累计积分：{total_score}"
```

## ✅ 阶段5：完整性确认序列

```bash
# ===== 阶段5开始 =====
# 前置条件：修复已实施
# 当前积分：{previous_score} | 执行证明：0/6

# 效果验证（方法14）
cmd48: {original_problem_test}
expect: problem_solved
mandatory: true
score: +10/-15

# 并行完整性测试
parallel_start: integrity_tests

# 功能完整性（方法15）
cmd49: {related_feature_test}
expect: features_intact
score: +5/-10

# 根本解决确认（方法16）
cmd50: {root_cause_eliminated_test}
expect: permanent_fix
score: +5/-10

parallel_end: integrity_tests

# 合规性验证（方法18）
cmd51: {compliance_check_command}
expect: rules_followed
score: +5/-10

# 最终确认
final_check: all_tests_passed
if_any_fail: identify_failure_type
if_all_pass: mark_complete +10

# 失败处理（方法21）
failure_handler: trace_failure_cause
if_incomplete: return_to_phase4
if_wrong_root: return_to_phase3
if_bad_evidence: return_to_phase1

# 最终积分统计
final_audit: calculate_total_score
excellence_threshold: 150
pass_threshold: 100
status: "✅ 诊断修复完成 | 最终积分：{final_score} | 评级：{rating}"
```

## 📋 方法组件库

### 方法1：powershell.exe执行验证
在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作。
- 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
- 流程：执行程序 → 读取日志 → 验证功能效果
- 执行证明：必须显示程序输出或错误信息

### 方法2：API请求验证  
直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。
- 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
- 流程：调用API → 分析响应 → 验证业务逻辑
- 执行证明：必须显示HTTP响应码和响应体

### 方法3：服务状态验证
检查服务进程的真实运行状态，避免假设服务可用性。
- 验证策略：端口连通性 → 服务响应 → 进程状态
- 命令选项：
  - 基础验证：curl -s http://localhost:8000/ -o /dev/null -w "%{http_code}" || echo "000"
  - 根路径探测：curl -s http://localhost:8000/ | head -20
  - 进程验证：netstat -tlnp 2>/dev/null | grep :8000 || ss -tlnp | grep :8000
  - 智能发现：curl -s http://localhost:8000/ | grep -o '"[^"]*health[^"]*":[^,}]*' | head -1
- 执行证明：HTTP状态码、响应内容摘要或进程信息

### 方法4：代码生效验证
确认代码修改已实际应用并被执行，避免假设修改生效。
- 静态验证：文件变更是否存在（需显示diff）
- 动态验证：修改的代码路径是否被执行到（需显示日志）
- 执行证明：必须有git diff或文件对比输出

### 方法5：功能重复性检查
开发前搜索现有代码库，避免重复实现已存在的功能模块。
- 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数
- 执行证明：必须显示搜索结果，即使为空

### 方法6：日志优先症状收集
优先查看错误日志和异常堆栈，获取最直接证据。
- 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述
- 执行证明：必须列出查看的日志文件和关键内容

### 方法7：用户症状分层确认  
将用户描述分为表象→系统→根因三层，逐层收集症状。
- 表象层：用户直接看到的问题
- 系统层：服务和环境层面异常
- 根因层：代码和技术层面原因
- 执行证明：必须记录每层的具体症状

### 方法8：系统环境状态检查
并行检查服务状态、配置文件、依赖环境的实际状态。
- 检查维度：服务可用性 | 配置完整性 | 依赖满足性
- 并行策略：多维度同时检查，快速定位环境问题
- 执行证明：必须显示ps/netstat/config内容

### 方法9：执行路径反向确认
通过输出特征和日志模式反向定位真实执行的代码路径。
- 反向策略：从输出结果追溯到具体代码位置
- 执行证明：必须显示日志→代码的对应关系

### 方法10：数据流完整追踪
追踪数据从输入到输出的完整生命周期，识别异常节点。
- 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
- 异常识别：数据丢失、格式错误、转换失败、存储异常
- 执行证明：必须显示每个节点的数据状态

### 方法11：逐层隔离定位
按系统架构层次逐层隔离问题，避免跨层复杂化分析。
- 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次
- 执行证明：必须显示每层的测试结果

### 方法12：单一根因优先
优先查找单一明确的根因，避免多因素复杂化假设。
- 判断原则：一个问题对应一个主要原因
- 排除策略：先验证最直接最简单的可能性
- 执行证明：必须显示排除过程

### 方法13：代码逻辑直接验证
基于实际执行路径验证代码逻辑，不分析未执行代码。
- 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支
- 执行证明：必须显示代码执行路径

### 方法14：修复效果实时验证
修复后立即通过相同方法验证问题是否解决。
- 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入
- 执行证明：必须显示修复前后对比

### 方法15：功能完整性测试
验证修复不影响相关功能的正常工作。
- 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程
- 执行证明：必须显示测试用例和结果

### 方法16：根本解决确认
确认修复解决了原始根本问题，不是表面修复。
- 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除
- 执行证明：必须重现原始场景并验证

### 方法17：规范动态提取应用
根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。
- 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
- 应用原则：只应用相关规范，避免无关约束干扰
- 执行证明：必须引用具体规范条款

### 方法18：修复合规性验证
确保所有修复完全符合项目技术规范和架构原则。
- 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则
- 执行证明：必须逐项检查并显示结果

### 方法19：证据驱动分析
所有分析必须基于实际收集的证据，禁止无证据推理。
- 分析前提：先收集证据 → 再分析原因
- 禁止行为：基于经验推测 | 基于可能性判断
- 执行证明：每个结论必须标注证据来源

### 方法20：假设验证循环
每个根因假设都必须通过实际验证才能确认。
- 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定
- 执行证明：必须显示验证命令和结果

### 方法21：失败原因追溯
修复失败后必须基于新证据重新分析，不重复原方案。
- 追溯策略：识别失败点 → 收集新证据 → 调整分析方向
- 执行证明：必须显示失败点和新证据

### 方法22：证据有效性验证
验证收集的证据是否满足分析要求，确保证据质量合格。
- 验证维度：完整性（覆盖范围） | 相关性（直接关联） | 具体性（明确清晰）
- 失败处理：指出缺失内容 → 返回补充收集 → 禁止不完整分析
- 执行证明：必须列出所有证据并评估质量

## 🎯 执行要点（线性命令版）

1. **顺序执行** - 严格按照命令编号顺序执行
2. **并行标记** - parallel_start/end 之间的命令可并行
3. **条件处理** - if_empty/if_contains 自动处理分支
4. **积分自动** - 每个命令执行后自动计算积分
5. **强制命令** - mandatory:true 的命令必须成功
6. **审计节点** - audit 命令强制积分检查
7. **循环控制** - loop_count 自动管理重试次数
