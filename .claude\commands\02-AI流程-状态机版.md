---
description: 基于 `$ARGUMENTS` 按照 问题诊断与修复验证流程 执行
---

# 问题诊断与修复验证流程

### 方法1：powershell.exe执行验证

你处于wls环境，通过powershell.exe执行Windows命令，然后读取日志（/logs）输出，验证代码功能的真实执行效果。

- 命令格式：`powershell.exe "cd 目标目录; 执行命令"` + `Read 日志文件`

### 方法2：API请求验证  

直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。

- 用户管理：`curl -X GET http://localhost:8000/api/admin/v1/users`
- 业务逻辑：`curl -X GET http://localhost:8000/api/v1/courses`

### 方法3：服务状态验证

检查服务进程的真实运行状态，避免假设服务可用性。

- 命令格式：`curl -s http://localhost:8000/docs && echo "运行中" || echo "离线"`
  - 备用验证：`curl -s http://localhost:8000/openapi.json`

### 方法4：代码生效验证

确认代码修改已实际应用到运行环境，避免假设修改生效。

- 静态验证：确认代码修改已实际应用到文件
- 动态验证：数据流验证、功能执行验证、API调用验证、用户场景验证、执行路径验证

### 方法5：功能重复性验证

开发前搜索现有代码库，避免重复实现已存在的功能模块。

- 命令格式：`Grep "功能关键词" 项目目录`

### 方法6：日志优先症状收集

优先查看错误日志和异常堆栈，获取最直接证据。

- 操作格式：`Read 相关日志文件` + `Grep 错误关键词 项目目录`

### 方法7：用户症状分层确认  

将用户描述分为表象→中层→根因三层，逐层收集症状。

- 表象层：用户可见的错误现象、界面异常、功能失效
- 中层：系统级异常、服务错误、配置环境问题
- 根因层：代码逻辑错误、数据流异常、技术问题
- 操作格式：分层症状列举 + 用户确认修正

### 方法8：系统环境状态检查

并行检查服务状态、配置文件、依赖环境的实际状态。

- 操作格式：`curl 服务端点` + `Read 配置文件` + 环境完整性验证

### 方法9：执行路径反向确认

通过输出特征和日志模式反向定位真实执行的代码路径。

- 操作格式：`Grep 输出特征 代码目录` + 执行分支验证

### 方法10：数据流完整追踪

追踪数据从输入到输出的完整生命周期，识别异常节点。

- 数据源头：输入数据格式、来源正确性验证
- 数据传输：API调用、网络连接状态验证
- 数据转换：格式转换、类型匹配正确性验证
- 数据存储：数据库连接、存储完整性验证
- 操作格式：各环节并行验证 + 异常点定位

### 方法11：API实际状态验证

直接调用相关API接口，验证服务和业务逻辑的真实状态。

- 操作格式：`curl -X POST API端点 -d 测试数据` + 响应分析

### 方法12：逐层隔离定位

按系统架构层次逐层隔离问题，避免跨层复杂化分析。

- 前端层：界面异常、用户交互问题
- API层：接口调用、权限验证问题
- 服务层：业务逻辑、数据处理问题
- 数据库层：数据存储、查询问题
- 操作格式：逐层独立验证 + 问题层次定位

### 方法13：单一根因优先

优先查找单一明确的根因，避免多因素复杂化假设。

### 方法14：代码逻辑直接验证

基于实际执行路径验证代码逻辑，不分析未执行代码。

- 操作格式：`Read 确认执行的代码文件` + 逻辑分析

### 方法15：修复效果实时验证

修复后立即通过相同方法验证问题是否解决。

- 修复验证：当前修复解决预期问题
- 功能验证：修复后功能正常工作
- 根本验证：解决原始根本问题
- 完整验证：无新问题引入
- 操作格式：四层验证机制 + 实际测试记录

### 方法16：功能完整性测试

验证修复不影响相关功能的正常工作。

- 操作格式：相关功能API调用测试 + 回归验证

### 方法17：根本解决确认

确认修复解决了原始根本问题，不是表面修复。

- 操作格式：原始症状重现测试 + 根因消除确认

### 方法18：规范动态提取应用

根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。

### 方法19：修复合规性验证

确保所有修复完全符合项目技术规范和架构原则。

### 方法20：动态验证

  - 数据流验证（追踪数据处理过程）
  - 功能执行验证
  - API调用验证
  - 用户场景验证
  - 执行路径验证

**核心原则**
所有诊断修复必须基于实际证据和验证结果，禁止基于推理假设进行后续分析决策。

---

## 状态转换表

| 当前状态 | 触发条件 | 下一状态 | 执行操作 | 输出结果 |
|---------|---------|---------|---------|---------|
| **START** | 开始诊断 | 阶段0 | 流程初始化 | 项目基础 + 环境确认 |
| **阶段0** | 初始化完成 | 阶段1 | 症状收集 | 执行环境报告 + 项目技术基础 |
| **阶段1** | 症状收集完成 | 阶段2 | 证据验证 | 完整症状清单 + 环境状态报告 |
| **阶段2** | 证据验证完成 | 阶段3 | 根因定位 | 真实执行状态 + 数据流异常点 |
| **阶段3** | 根因定位完成 | 阶段4 | 修复实施 | 确切根因位置 + 具体技术原因 |
| **阶段4** | 技术性失败 | 阶段4 | 内部重试 | 修复方案调整 |
| **阶段4** | 逻辑性失败 | 阶段3 | 重新定位 | 返回根因定位 |
| **阶段4** | 根本性失败 | 阶段1 | 重新收集 | 返回症状收集 |
| **阶段4** | 修复验证成功 | 阶段5 | 修复验证 | 修复实施报告 + 代码变更记录 |
| **阶段5** | 验证失败 | 阶段1 | 重新诊断 | 返回症状收集 |
| **阶段5** | 验证成功 | END | 流程结束 | 验证结果报告 + 问题解决确认 |

## 状态详细定义

### 阶段0：流程初始化
- **输入**: 用户问题描述
- **操作**: 项目识别 + 技术栈确认 + WSL环境确认 + 规范提取 + 服务验证
- **输出**: 执行环境报告 + 项目技术基础 + 问题相关规范约束

### 阶段1：症状收集  
- **输入**: 项目基础信息
- **操作**: 方法6日志收集 + 方法8环境检查 + 方法7用户确认
- **输出**: 完整症状清单 + 环境状态报告

### 阶段2：证据验证
- **输入**: 症状清单
- **操作**: 方法3服务验证 + 方法11API验证 + 方法9路径确认 + 方法10数据追踪
- **输出**: 真实执行状态 + 数据流异常点

### 阶段3：根因定位
- **输入**: 证据验证结果  
- **操作**: 方法12逐层隔离 + 方法14代码验证 + 方法13单一根因确定
- **输出**: 确切根因位置 + 具体技术原因

### 阶段4：修复实施
- **输入**: 根因定位结果
- **操作**: 方法18规范提取 + 修复实施 + 方法4+1+20验证
- **输出**: 修复实施报告 + 代码变更记录
- **失败分类**:
  - 技术性失败: 代码错误、语法问题、配置异常
  - 逻辑性失败: 修复方向错误、根因理解偏差  
  - 根本性失败: 问题本质理解错误

### 阶段5：修复验证
- **输入**: 修复实施结果
- **操作**: 方法15四层验证 + 方法16功能测试 + 方法17根本确认  
- **输出**: 验证结果报告 + 问题解决确认

## 状态机特性

- **确定性**: 每个状态的转换条件明确
- **完整性**: 覆盖所有可能的状态和转换
- **容错性**: 失败状态有明确的恢复路径
- **终止性**: 有明确的成功结束状态