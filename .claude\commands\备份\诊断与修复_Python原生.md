---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🐍 问题诊断与修复流程 - Python原生架构

## 🎯 架构核心理念

Python原生架构完全基于Python语言的原生控制结构（for、while、if、try等），不引入复杂的框架或设计模式，通过直观的流程控制和异常处理机制实现诊断修复流程。这种架构最大的优势是简单直接、易于理解和调试。

### 🔑 关键特性
1. **原生控制流**：使用Python原生的循环、条件判断和异常处理
2. **简单直接**：避免过度设计，代码逻辑清晰可读
3. **易于调试**：标准的Python调试工具完全支持
4. **轻量级实现**：最小化依赖，最大化性能
5. **强制循环机制**：通过原生循环确保满足最小轮次要求
6. **原生异常处理**：使用try/except机制处理错误和回滚

## 🔄 Python原生实现

```python
import subprocess
import time
import os
import json
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass, asdict

def run_bash(command: str) -> str:
    """执行bash命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        return f"STDOUT: {result.stdout}\nSTDERR: {result.stderr}\nRETURN_CODE: {result.returncode}"
    except subprocess.TimeoutExpired:
        return "ERROR: 命令执行超时"
    except Exception as e:
        return f"ERROR: {str(e)}"

@dataclass
class ExecutionResult:
    """执行结果数据结构"""
    success: bool = False
    data: Any = None
    error_message: str = ""
    execution_time: float = 0.0
    quality_score: float = 0.0

class TodoTracker:
    """TodoWrite状态跟踪器"""
    def __init__(self):
        self.tasks = {}
        self.current_id = 0
    
    def add_task(self, description: str, status: str = "pending") -> str:
        self.current_id += 1
        task_id = f"task_{self.current_id}"
        self.tasks[task_id] = {
            "id": task_id,
            "description": description,
            "status": status,
            "created_at": time.time()
        }
        print(f"TodoWrite: [{status.upper()}] {task_id}: {description}")
        return task_id
    
    def update_task(self, task_id: str, status: str, description: str = None):
        if task_id in self.tasks:
            self.tasks[task_id]["status"] = status
            if description:
                self.tasks[task_id]["description"] = description
            print(f"TodoWrite: [{status.upper()}] {task_id}: {self.tasks[task_id]['description']}")
    
    def get_task_summary(self) -> Dict:
        total = len(self.tasks)
        completed = len([t for t in self.tasks.values() if t["status"] == "completed"])
        in_progress = len([t for t in self.tasks.values() if t["status"] == "in_progress"])
        pending = len([t for t in self.tasks.values() if t["status"] == "pending"])
        
        return {
            "total": total,
            "completed": completed,
            "in_progress": in_progress,
            "pending": pending,
            "completion_rate": (completed / total * 100) if total > 0 else 0
        }

# 全局TodoTracker实例
todo_tracker = TodoTracker()

def python_native_diagnosis_flow($ARGUMENTS: str) -> Dict:
    """
    Python原生诊断与修复流程主入口
    完全基于诊断修复流程规范，使用原生Python控制结构
    """
    
    # 流程初始化
    main_task_id = todo_tracker.add_task("Python原生诊断修复流程", "in_progress")
    flow_start_time = time.time()
    
    print("🐍 启动Python原生诊断修复流程")
    print(f"📝 问题描述: {$ARGUMENTS}")
    print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(flow_start_time))}")
    
    # 流程执行结果
    flow_result = {
        "success": False,
        "problem_description": $ARGUMENTS,
        "start_time": flow_start_time,
        "end_time": None,
        "phases": {},
        "error_message": "",
        "execution_summary": {}
    }
    
    try:
        # === 阶段1：信息收集与真实检查 ===
        print("\n🔍 === 阶段1：信息收集与真实检查 ===")
        phase1_result = execute_phase1_information_collection($ARGUMENTS)
        flow_result["phases"]["phase1"] = phase1_result
        
        if not phase1_result.success:
            raise Exception(f"阶段1失败: {phase1_result.error_message}")
        
        # === 阶段2：假设-验证循环（2-5轮强制执行） ===
        print("\n🧠 === 阶段2：假设-验证循环 ===")
        phase2_result = execute_phase2_hypothesis_verification_loop(phase1_result.data)
        flow_result["phases"]["phase2"] = phase2_result
        
        if not phase2_result.success:
            raise Exception(f"阶段2失败: {phase2_result.error_message}")
        
        # === 阶段3：修复实施与验证循环（1-5轮强制执行） ===
        print("\n🔧 === 阶段3：修复实施与验证循环 ===")
        phase3_result = execute_phase3_repair_implementation_loop(phase2_result.data)
        flow_result["phases"]["phase3"] = phase3_result
        
        if not phase3_result.success:
            raise Exception(f"阶段3失败: {phase3_result.error_message}")
        
        # === 阶段4：总结与发散优化 ===
        print("\n📊 === 阶段4：总结与发散优化 ===")
        phase4_result = execute_phase4_summary_optimization(flow_result["phases"])
        flow_result["phases"]["phase4"] = phase4_result
        
        if not phase4_result.success:
            raise Exception(f"阶段4失败: {phase4_result.error_message}")
        
        # 流程成功完成
        flow_result["success"] = True
        todo_tracker.update_task(main_task_id, "completed", "Python原生诊断修复流程完成")
        
    except Exception as e:
        flow_result["error_message"] = str(e)
        print(f"❌ 流程执行失败: {e}")
        todo_tracker.update_task(main_task_id, "failed", f"流程失败: {e}")
    
    finally:
        flow_result["end_time"] = time.time()
        flow_result["total_execution_time"] = flow_result["end_time"] - flow_start_time
        flow_result["execution_summary"] = generate_execution_summary(flow_result)
        
        print(f"\n⏰ 流程结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(flow_result['end_time']))}")
        print(f"⏱️ 总执行时间: {flow_result['total_execution_time']:.2f}秒")
        print(f"✅ 流程成功: {flow_result['success']}")
    
    return flow_result

def execute_phase1_information_collection($ARGUMENTS: str) -> ExecutionResult:
    """
    阶段1：信息收集与真实检查
    对应流程阶段1的完整实现
    """
    
    task_id = todo_tracker.add_task("阶段1-信息收集与真实检查", "in_progress")
    phase_start_time = time.time()
    
    # 阶段1结果数据结构
    phase1_data = {
        "collected_information": {},
        "core_symptoms": [],
        "environment_info": {},
        "quality_metrics": {
            "information_completeness": 0,
            "information_accuracy": 0,
            "verification_coverage": 0
        }
    }
    
    try:
        # 1.1 多维度信息搜索
        print("🔍 执行多维度信息搜索...")
        info_search_task = todo_tracker.add_task("多维度信息搜索", "in_progress")
        
        # 文件系统搜索
        file_search_cmd = "find . -name '*.py' -o -name '*.md' -o -name '*.json' | head -30"
        file_search_result = run_bash(file_search_cmd)
        phase1_data["collected_information"]["file_search"] = file_search_result
        
        # 环境检查
        env_check_cmd = "python -c \"import sys, os; print(f'Python: {sys.version}'); print(f'CWD: {os.getcwd()}'); print(f'Platform: {sys.platform}')\" && echo 'ENV_CHECK_OK'"
        env_result = run_bash(env_check_cmd)
        phase1_data["environment_info"]["python_env"] = env_result
        
        # WSL环境特殊检查
        wsl_check_cmd = "python -c \"import platform; print(f'System: {platform.system()}'); print(f'Release: {platform.release()}')\" && echo 'WSL_CHECK_OK'"
        wsl_result = run_bash(wsl_check_cmd)
        phase1_data["environment_info"]["wsl_info"] = wsl_result
        
        # 验证搜索结果
        search_verification_success = (
            "ENV_CHECK_OK" in env_result and 
            "WSL_CHECK_OK" in wsl_result and
            "STDOUT:" in file_search_result
        )
        
        if search_verification_success:
            phase1_data["quality_metrics"]["information_completeness"] = 85
            phase1_data["quality_metrics"]["information_accuracy"] = 90
            todo_tracker.update_task(info_search_task, "completed")
        else:
            phase1_data["quality_metrics"]["information_completeness"] = 45
            phase1_data["quality_metrics"]["information_accuracy"] = 50
            todo_tracker.update_task(info_search_task, "completed", "信息搜索部分成功")
        
        # 1.2 核心症状识别
        print("🎯 识别核心症状...")
        symptom_task = todo_tracker.add_task("核心症状识别", "in_progress")
        
        # 基于问题描述分析症状
        if "登录" in problem_description or "login" in problem_description.lower():
            phase1_data["core_symptoms"].append("登录功能异常")
        if "数据库" in problem_description or "database" in problem_description.lower():
            phase1_data["core_symptoms"].append("数据库连接问题")
        if "性能" in problem_description or "performance" in problem_description.lower():
            phase1_data["core_symptoms"].append("性能问题")
        if "错误" in problem_description or "error" in problem_description.lower():
            phase1_data["core_symptoms"].append("运行时错误")
        
        # 如果没有识别到具体症状，添加通用症状
        if not phase1_data["core_symptoms"]:
            phase1_data["core_symptoms"].append("通用系统异常")
        
        todo_tracker.update_task(symptom_task, "completed")
        
        # 1.3 验证收集到的信息
        print("✅ 验证信息收集质量...")
        verification_task = todo_tracker.add_task("信息质量验证", "in_progress")
        
        # 验证每个收集到的信息源
        verification_count = 0
        total_verifications = 3
        
        # 验证文件搜索结果
        if phase1_data["collected_information"]["file_search"] and "STDOUT:" in phase1_data["collected_information"]["file_search"]:
            verification_count += 1
        
        # 验证环境信息
        if "ENV_CHECK_OK" in phase1_data["environment_info"]["python_env"]:
            verification_count += 1
        
        # 验证症状识别
        if phase1_data["core_symptoms"]:
            verification_count += 1
        
        verification_coverage = (verification_count / total_verifications) * 100
        phase1_data["quality_metrics"]["verification_coverage"] = verification_coverage
        
        if verification_coverage >= 80:
            todo_tracker.update_task(verification_task, "completed")
        else:
            todo_tracker.update_task(verification_task, "completed", "验证覆盖率不足80%")
        
        # 阶段1门禁检查
        overall_quality = (
            phase1_data["quality_metrics"]["information_completeness"] * 0.4 +
            phase1_data["quality_metrics"]["information_accuracy"] * 0.3 +
            phase1_data["quality_metrics"]["verification_coverage"] * 0.3
        )
        
        if overall_quality >= 70:  # 要求的质量阈值
            print(f"✅ 阶段1门禁通过，质量分数: {overall_quality:.1f}")
            todo_tracker.update_task(task_id, "completed")
            
            return ExecutionResult(
                success=True,
                data=phase1_data,
                execution_time=time.time() - phase_start_time,
                quality_score=overall_quality
            )
        else:
            # 质量不达标，执行补强措施
            print(f"⚠️ 阶段1质量不达标({overall_quality:.1f})，执行补强措施...")
            enhancement_result = execute_information_enhancement(phase1_data)
            phase1_data.update(enhancement_result)
            
            # 重新计算质量分数
            enhanced_quality = overall_quality + 15  # 补强提升
            
            todo_tracker.update_task(task_id, "completed", "经补强后完成")
            
            return ExecutionResult(
                success=True,
                data=phase1_data,
                execution_time=time.time() - phase_start_time,
                quality_score=enhanced_quality
            )
    
    except Exception as e:
        todo_tracker.update_task(task_id, "failed", f"阶段1执行失败: {e}")
        return ExecutionResult(
            success=False,
            error_message=str(e),
            execution_time=time.time() - phase_start_time
        )

def execute_information_enhancement(phase1_data: Dict) -> Dict:
    """信息收集补强措施"""
    enhancement_task = todo_tracker.add_task("信息收集补强", "in_progress")
    
    enhancement_data = {}
    
    try:
        # 额外的系统信息收集
        system_info_cmd = "python -c \"import psutil, platform; print(f'CPU: {psutil.cpu_count()}'); print(f'Memory: {psutil.virtual_memory().total // (1024**3)}GB'); print('SYSTEM_INFO_OK')\" 2>/dev/null || echo 'SYSTEM_INFO_FALLBACK'"
        system_info = run_bash(system_info_cmd)
        enhancement_data["system_info"] = system_info
        
        # 额外的进程检查
        process_check_cmd = "python -c \"import os; print(f'PID: {os.getpid()}'); print('PROCESS_CHECK_OK')\""
        process_info = run_bash(process_check_cmd)
        enhancement_data["process_info"] = process_info
        
        # 更新质量分数
        if "SYSTEM_INFO_OK" in system_info or "PROCESS_CHECK_OK" in process_info:
            phase1_data["quality_metrics"]["information_completeness"] += 10
            phase1_data["quality_metrics"]["information_accuracy"] += 5
        
        todo_tracker.update_task(enhancement_task, "completed")
        
    except Exception as e:
        todo_tracker.update_task(enhancement_task, "failed", f"补强失败: {e}")
    
    return enhancement_data

def execute_phase2_hypothesis_verification_loop(phase1_data: Dict) -> ExecutionResult:
    """
    阶段2：假设-验证循环（2-5轮强制执行）
    对应流程阶段2的完整实现
    """
    
    task_id = todo_tracker.add_task("阶段2-假设验证循环", "in_progress")
    phase_start_time = time.time()
    
    # 阶段2结果数据结构
    phase2_data = {
        "total_rounds": 0,
        "rounds_data": [],
        "final_hypotheses": [],
        "confidence_scores": [],
        "quality_metrics": {
            "analysis_depth": 0,
            "analysis_confidence": 0,
            "hypothesis_validity": 0
        }
    }
    
    try:
        print("🔄 开始假设-验证循环（最少2轮，最多5轮）")
        
        # 强制执行2-5轮循环
        round_number = 1
        max_rounds = 5
        min_rounds = 2  # 强制要求最少2轮
        
        while round_number <= max_rounds:
            print(f"\n🔄 === 第{round_number}轮假设-验证 ===")
            
            round_task = todo_tracker.add_task(f"第{round_number}轮假设验证", "in_progress")
            round_start_time = time.time()
            
            # 当前轮次数据
            round_data = {
                "round_number": round_number,
                "hypotheses": [],
                "validations": [],
                "insights": [],
                "confidence_score": 0,
                "execution_time": 0
            }
            
            # 2.1 生成假设（每轮3个步骤的第1步）
            print(f"💡 步骤1：生成假设（第{round_number}轮）")
            hypotheses = generate_hypotheses_for_round(round_number, phase1_data, phase2_data["rounds_data"])
            round_data["hypotheses"] = hypotheses
            
            # 2.2 验证假设（每轮3个步骤的第2步）
            print(f"🔍 步骤2：验证假设（第{round_number}轮）")
            validations = []
            for hypothesis in hypotheses:
                validation_result = verify_hypothesis_with_real_checks(hypothesis, phase1_data)
                validations.append(validation_result)
            round_data["validations"] = validations
            
            # 2.3 调整假设（每轮3个步骤的第3步）
            print(f"🎯 步骤3：调整假设（第{round_number}轮）")
            insights = adjust_hypotheses_based_on_validation(hypotheses, validations)
            round_data["insights"] = insights
            
            # 计算本轮信心度
            valid_hypotheses = [v for v in validations if v["confidence"] >= 70]
            round_confidence = (len(valid_hypotheses) / len(validations) * 100) if validations else 0
            round_data["confidence_score"] = round_confidence
            round_data["execution_time"] = time.time() - round_start_time
            
            # 保存轮次数据
            phase2_data["rounds_data"].append(round_data)
            phase2_data["confidence_scores"].append(round_confidence)
            
            todo_tracker.update_task(round_task, "completed", f"信心度: {round_confidence:.1f}%")
            
            # 循环继续条件判断
            if round_number >= min_rounds:  # 至少执行2轮后才能判断是否退出
                # 检查是否达到退出条件
                if round_confidence >= 85:  # 高信心度，可以退出
                    print(f"✅ 达到高信心度({round_confidence:.1f}%)，提前结束循环")
                    break
                elif round_number >= 3 and len(phase2_data["confidence_scores"]) >= 2:
                    # 检查是否收敛（连续两轮改进幅度小于5%）
                    recent_improvement = phase2_data["confidence_scores"][-1] - phase2_data["confidence_scores"][-2]
                    if recent_improvement < 5:
                        print(f"📈 收敛检测：改进幅度{recent_improvement:.1f}% < 5%，结束循环")
                        break
            
            round_number += 1
        
        # 记录总轮次
        phase2_data["total_rounds"] = round_number - 1
        
        # 生成最终假设
        phase2_data["final_hypotheses"] = generate_final_hypotheses(phase2_data["rounds_data"])
        
        # 计算阶段2质量指标
        phase2_data["quality_metrics"]["analysis_depth"] = min(100, phase2_data["total_rounds"] * 20)  # 轮次越多深度越高
        phase2_data["quality_metrics"]["analysis_confidence"] = max(phase2_data["confidence_scores"]) if phase2_data["confidence_scores"] else 0
        phase2_data["quality_metrics"]["hypothesis_validity"] = calculate_hypothesis_validity(phase2_data["final_hypotheses"])
        
        # 阶段2门禁检查
        overall_quality = (
            phase2_data["quality_metrics"]["analysis_depth"] * 0.3 +
            phase2_data["quality_metrics"]["analysis_confidence"] * 0.4 +
            phase2_data["quality_metrics"]["hypothesis_validity"] * 0.3
        )
        
        if overall_quality >= 75:  # 要求的阶段2质量阈值
            print(f"✅ 阶段2门禁通过，质量分数: {overall_quality:.1f}，执行轮次: {phase2_data['total_rounds']}")
            todo_tracker.update_task(task_id, "completed")
            
            return ExecutionResult(
                success=True,
                data=phase2_data,
                execution_time=time.time() - phase_start_time,
                quality_score=overall_quality
            )
        else:
            raise Exception(f"阶段2质量不达标: {overall_quality:.1f} < 75")
    
    except Exception as e:
        todo_tracker.update_task(task_id, "failed", f"阶段2执行失败: {e}")
        return ExecutionResult(
            success=False,
            error_message=str(e),
            execution_time=time.time() - phase_start_time
        )
```

## 🎯 Python原生架构使用示例

```python
# 简单使用
result = python_native_diagnosis_flow("数据库连接超时问题")

# 检查结果
if result["success"]:
    print(f"诊断修复成功！")
    print(f"执行时间: {result['total_execution_time']:.2f}秒")
    
    # 查看各阶段结果
    for phase_name, phase_result in result["phases"].items():
        print(f"{phase_name}: {'成功' if phase_result.success else '失败'}")
else:
    print(f"诊断修复失败: {result['error_message']}")

# 查看TodoWrite跟踪状态
task_summary = todo_tracker.get_task_summary()
print(f"任务完成率: {task_summary['completion_rate']:.1f}%")
```

## 📊 Python原生架构总结

Python原生架构通过使用Python语言的原生控制结构，实现了：

1. **简单直接**：代码逻辑清晰，易于理解和维护
2. **高度可控**：使用原生循环确保严格执行最小轮次要求
3. **强制门禁**：通过异常处理机制强制执行质量检查
4. **完整追踪**：TodoWrite集成提供完整的执行状态跟踪
5. **易于调试**：标准Python调试工具完全支持
6. **轻量高效**：最小化依赖，最大化执行效率

这种架构特别适合需要精确控制流程执行和状态跟踪的诊断修复场景，是最接近原生Python编程习惯的实现方式。

## 📋 核心理念

使用最基础的Python语法实现诊断流程：for/while循环、if判断、简单函数调用。最小化认知复杂度，让AI能够直观理解和执行。

## 🐍 主流程实现

```python
def diagnose_and_fix(problem_description):
    """最简单的诊断修复流程"""
    
    print("🚀 开始诊断与修复")
    print(f"问题描述: {problem_description}")
    
    # 步骤1: 收集信息
    print("\n🔍 步骤1: 收集信息")
    info = collect_information(problem_description)
    
    if not info["success"]:
        print("❌ 信息收集失败，停止执行")
        return {"success": False, "reason": "信息收集失败"}
    
    print(f"✅ 收集到 {info['file_count']} 个相关文件")
    
    # 步骤2: 分析问题（至少2轮）
    print("\n🔬 步骤2: 分析问题")
    analysis_results = []
    
    for round_num in range(1, 6):  # 最多5轮
        print(f"\n--- 第{round_num}轮分析 ---")
        
        analysis = analyze_round(round_num, info, analysis_results)
        analysis_results.append(analysis)
        
        print(f"本轮发现: {analysis['new_findings']}个")
        print(f"确认问题: {analysis['confirmed_issues']}个")
        
        # 强制执行最少2轮
        if round_num < 2:
            print("继续下一轮分析（最少2轮要求）")
            continue
        
        # 检查是否可以停止
        if should_stop_analysis(analysis_results):
            print(f"分析完成，共执行{round_num}轮")
            break
    
    # 检查是否找到问题
    total_issues = sum(r["confirmed_issues"] for r in analysis_results)
    if total_issues == 0:
        print("❌ 未发现具体问题，停止执行")
        return {"success": False, "reason": "未发现问题"}
    
    print(f"✅ 总共确认 {total_issues} 个问题")
    
    # 步骤3: 实施修复
    print("\n🔧 步骤3: 实施修复")
    fix_success = implement_fixes(analysis_results)
    
    if not fix_success:
        print("❌ 修复失败")
        return {"success": False, "reason": "修复失败"}
    
    print("✅ 修复实施完成")
    
    # 步骤4: 验证结果
    print("\n✅ 步骤4: 验证结果")
    verification = verify_fixes()
    
    if verification["success"]:
        print("🎉 诊断修复成功完成")
        return {"success": True, "verification": verification}
    else:
        print("❌ 验证失败")
        return {"success": False, "reason": "验证失败", "details": verification}
```

---

## 🔍 步骤1: 信息收集

```python
def collect_information(problem_description):
    """简单的信息收集"""
    
    print("🔎 开始收集信息...")
    
    # 提取关键词
    keywords = extract_keywords(problem_description)
    print(f"提取关键词: {keywords}")
    
    # 搜索相关文件
    all_files = []
    
    # Glob搜索
    print("执行Glob搜索...")
    for keyword in keywords:
        files = glob_search(f"**/*{keyword}*")
        all_files.extend(files)
        print(f"  关键词'{keyword}': {len(files)}个文件")
    
    # Grep搜索
    print("执行Grep搜索...")
    for keyword in keywords:
        files = grep_search(keyword, include="*.py,*.js")
        all_files.extend(files)
        print(f"  内容匹配'{keyword}': {len(files)}个位置")
    
    # 去重统计
    unique_files = list(set(all_files))
    file_count = len(unique_files)
    
    print(f"总计找到 {file_count} 个相关文件")
    
    # 检查服务状态
    service_ok = check_service()
    print(f"服务状态: {'正常' if service_ok else '异常'}")
    
    # 简单判断是否成功
    success = file_count >= 3 and len(keywords) >= 2
    
    return {
        "success": success,
        "keywords": keywords,
        "files": unique_files,
        "file_count": file_count,
        "service_ok": service_ok
    }

def extract_keywords(description):
    """提取关键词"""
    keywords = []
    
    # 常见技术词汇映射
    word_map = {
        "显示": ["display", "show", "render"],
        "数据": ["data", "model", "database"],
        "用户": ["user", "account", "profile"],
        "错误": ["error", "exception", "fail"],
        "界面": ["ui", "view", "component"],
        "接口": ["api", "endpoint", "router"]
    }
    
    desc_lower = description.lower()
    
    # 查找映射词汇
    for chinese, english_words in word_map.items():
        if chinese in description:
            keywords.extend(english_words)
    
    # 提取英文单词
    import re
    english_words = re.findall(r'[a-zA-Z_]\w+', description)
    keywords.extend([w for w in english_words if len(w) > 2])
    
    # 去重并限制数量
    return list(set(keywords))[:8]

def check_service():
    """检查服务状态"""
    try:
        # 尝试连接服务
        response = requests.get("http://localhost:8000/docs", timeout=3)
        return response.status_code == 200
    except:
        return False
```

---

## 🔬 步骤2: 问题分析

```python
def analyze_round(round_num, info, previous_results):
    """执行一轮问题分析"""
    
    print(f"执行第{round_num}轮分析...")
    
    # 建立假设
    if round_num == 1:
        hypotheses = build_initial_hypotheses(info)
    else:
        hypotheses = build_refined_hypotheses(previous_results)
    
    print(f"建立假设: {len(hypotheses)}个")
    
    # 验证假设
    confirmed_issues = 0
    new_findings = 0
    verified_hypotheses = []
    
    for i, hypothesis in enumerate(hypotheses, 1):
        print(f"验证假设{i}: {hypothesis['description']}")
        
        # 执行验证
        result = verify_hypothesis(hypothesis)
        verified_hypotheses.append(result)
        
        if result["confirmed"]:
            confirmed_issues += 1
            print(f"  ✅ 确认问题: {result['evidence']}")
        else:
            print(f"  ❌ 假设被排除")
        
        # 检查是否有新发现
        if result.get("new_info"):
            new_findings += 1
    
    return {
        "round": round_num,
        "hypotheses": hypotheses,
        "verified": verified_hypotheses,
        "confirmed_issues": confirmed_issues,
        "new_findings": new_findings
    }

def build_initial_hypotheses(info):
    """建立初始假设"""
    hypotheses = []
    
    # 基础假设（总是包含）
    hypotheses.append({
        "id": "syntax",
        "description": "语法或导入错误",
        "type": "basic",
        "verify_method": "check_syntax"
    })
    
    hypotheses.append({
        "id": "config", 
        "description": "配置文件问题",
        "type": "basic",
        "verify_method": "check_config"
    })
    
    # 根据关键词添加特定假设
    keywords = info["keywords"]
    
    if any(word in keywords for word in ["display", "show", "ui"]):
        hypotheses.append({
            "id": "ui_data",
            "description": "UI数据绑定问题",
            "type": "ui",
            "verify_method": "check_ui_data"
        })
    
    if any(word in keywords for word in ["user", "account"]):
        hypotheses.append({
            "id": "user_logic",
            "description": "用户逻辑错误", 
            "type": "business",
            "verify_method": "check_user_logic"
        })
    
    if any(word in keywords for word in ["data", "database"]):
        hypotheses.append({
            "id": "data_issue",
            "description": "数据关联问题",
            "type": "data",
            "verify_method": "check_data_relations"
        })
    
    return hypotheses

def verify_hypothesis(hypothesis):
    """验证单个假设"""
    method = hypothesis["verify_method"]
    
    if method == "check_syntax":
        # 检查语法错误
        errors = find_syntax_errors()
        return {
            "confirmed": len(errors) > 0,
            "evidence": f"发现{len(errors)}个语法错误" if errors else None,
            "new_info": bool(errors)
        }
    
    elif method == "check_ui_data":
        # 检查UI数据问题
        ui_issues = find_ui_data_issues()
        return {
            "confirmed": len(ui_issues) > 0,
            "evidence": f"UI数据绑定错误: {ui_issues[0]}" if ui_issues else None,
            "new_info": bool(ui_issues)
        }
    
    elif method == "check_data_relations":
        # 检查数据关联
        relation_issues = find_data_relation_issues()
        return {
            "confirmed": len(relation_issues) > 0,
            "evidence": f"数据关联错误: {relation_issues[0]}" if relation_issues else None,
            "new_info": bool(relation_issues)
        }
    
    else:
        # 通用检查
        return {
            "confirmed": False,
            "evidence": None,
            "new_info": False
        }

def should_stop_analysis(results):
    """判断是否停止分析"""
    
    # 必须至少2轮
    if len(results) < 2:
        return False
    
    # 获取最近两轮结果
    last_round = results[-1]
    second_last = results[-2] if len(results) >= 2 else None
    
    # 停止条件1: 找到问题且最近两轮无新发现
    has_issues = last_round["confirmed_issues"] > 0
    no_new_findings = (
        last_round["new_findings"] == 0 and 
        (second_last is None or second_last["new_findings"] == 0)
    )
    
    if has_issues and no_new_findings:
        print("停止原因: 已找到问题且连续无新发现")
        return True
    
    # 停止条件2: 达到最大轮次
    if len(results) >= 5:
        print("停止原因: 达到最大分析轮次")
        return True
    
    return False
```

---

## 🔧 步骤3: 修复实施

```python
def implement_fixes(analysis_results):
    """实施修复"""
    
    print("开始实施修复...")
    
    # 收集所有确认的问题
    all_issues = []
    for result in analysis_results:
        for verified in result["verified"]:
            if verified["confirmed"]:
                all_issues.append(verified)
    
    print(f"需要修复的问题: {len(all_issues)}个")
    
    # 逐个修复
    success_count = 0
    
    for i, issue in enumerate(all_issues, 1):
        print(f"修复问题{i}: {issue.get('evidence', '未知问题')}")
        
        # 设计修复方案
        fix_plan = design_fix(issue)
        if not fix_plan:
            print(f"  ❌ 无法设计修复方案")
            continue
        
        # 执行修复
        fix_result = execute_fix(fix_plan)
        
        if fix_result["success"]:
            print(f"  ✅ 修复成功")
            success_count += 1
        else:
            print(f"  ❌ 修复失败: {fix_result['error']}")
    
    # 判断整体成功
    success_rate = success_count / len(all_issues) if all_issues else 0
    print(f"修复成功率: {success_rate*100:.1f}%")
    
    return success_rate == 1.0

def design_fix(issue):
    """设计修复方案"""
    evidence = issue.get("evidence", "")
    
    # 简单的修复策略映射
    if "语法错误" in evidence:
        return {
            "type": "syntax_fix",
            "description": "修复语法错误",
            "action": "fix_syntax_errors"
        }
    
    elif "UI数据绑定" in evidence:
        return {
            "type": "ui_fix", 
            "description": "修复UI数据绑定",
            "action": "fix_ui_data_binding"
        }
    
    elif "数据关联" in evidence:
        return {
            "type": "data_fix",
            "description": "修复数据关联",
            "action": "fix_data_relations"
        }
    
    else:
        return None

def execute_fix(fix_plan):
    """执行具体修复"""
    action = fix_plan["action"]
    
    try:
        if action == "fix_syntax_errors":
            return fix_syntax_errors_impl()
        
        elif action == "fix_ui_data_binding":
            return fix_ui_data_binding_impl()
        
        elif action == "fix_data_relations":
            return fix_data_relations_impl()
        
        else:
            return {"success": False, "error": "未知修复类型"}
            
    except Exception as e:
        return {"success": False, "error": str(e)}

def fix_data_relations_impl():
    """修复数据关联的具体实现"""
    
    # 查找数据关联问题
    issues = find_data_relation_issues()
    
    for issue in issues:
        file_path = issue["file"]
        line_num = issue["line"]
        wrong_field = issue["wrong_field"]
        correct_field = issue["correct_field"]
        
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 修复该行
        if line_num <= len(lines):
            old_line = lines[line_num - 1]
            new_line = old_line.replace(wrong_field, correct_field)
            lines[line_num - 1] = new_line
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            print(f"  修复文件: {file_path}:{line_num}")
            print(f"  替换: {wrong_field} -> {correct_field}")
    
    return {"success": True, "fixed_count": len(issues)}
```

---

## ✅ 步骤4: 验证结果

```python
def verify_fixes():
    """验证修复结果"""
    
    print("开始验证修复结果...")
    
    verification_results = {
        "syntax_check": False,
        "service_check": False,  
        "function_check": False,
        "regression_check": False
    }
    
    # 验证1: 语法检查
    print("验证语法...")
    syntax_errors = find_syntax_errors()
    verification_results["syntax_check"] = len(syntax_errors) == 0
    print(f"  语法检查: {'通过' if verification_results['syntax_check'] else '失败'}")
    
    # 验证2: 服务检查
    print("验证服务...")
    service_ok = check_service()
    verification_results["service_check"] = service_ok
    print(f"  服务检查: {'通过' if verification_results['service_check'] else '失败'}")
    
    # 验证3: 功能检查
    print("验证核心功能...")
    function_ok = test_core_functions()
    verification_results["function_check"] = function_ok
    print(f"  功能检查: {'通过' if verification_results['function_check'] else '失败'}")
    
    # 验证4: 回归检查
    print("验证回归...")
    regression_ok = test_regression()
    verification_results["regression_check"] = regression_ok
    print(f"  回归检查: {'通过' if verification_results['regression_check'] else '失败'}")
    
    # 总体结果
    all_passed = all(verification_results.values())
    passed_count = sum(verification_results.values())
    total_count = len(verification_results)
    
    print(f"验证结果: {passed_count}/{total_count} 通过")
    
    return {
        "success": all_passed,
        "details": verification_results,
        "pass_rate": passed_count / total_count
    }

def test_core_functions():
    """测试核心功能"""
    try:
        # 基础API测试
        response = requests.get("http://localhost:8000/api/v1/users")
        if response.status_code != 200:
            return False
        
        # 数据完整性测试
        data = response.json()
        if not isinstance(data, list):
            return False
        
        return True
    except:
        return False

def test_regression():
    """回归测试"""
    try:
        # 测试主要端点
        endpoints = [
            "/api/v1/users",
            "/api/v1/categories", 
            "/api/v1/purchases"
        ]
        
        for endpoint in endpoints:
            response = requests.get(f"http://localhost:8000{endpoint}")
            if response.status_code not in [200, 404]:  # 404也可接受（可能是空数据）
                return False
        
        return True
    except:
        return False
```

---

## 🔧 辅助函数

```python
def find_syntax_errors():
    """查找语法错误"""
    errors = []
    
    # 简单的语法检查逻辑
    python_files = glob_search("**/*.py")
    
    for file_path in python_files[:10]:  # 限制检查数量
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试编译
            compile(content, file_path, 'exec')
            
        except SyntaxError as e:
            errors.append({
                "file": file_path,
                "line": e.lineno,
                "error": str(e)
            })
        except:
            pass  # 忽略其他错误
    
    return errors

def find_ui_data_issues():
    """查找UI数据问题"""
    issues = []
    
    # 搜索UI相关文件
    ui_files = grep_search("render|display|show", include="*.py")
    
    for file_path in ui_files[:5]:  # 限制检查数量
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 查找数据绑定模式
                if "get(" in line and "None" in line:
                    issues.append({
                        "file": file_path,
                        "line": i,
                        "issue": "可能的数据绑定问题"
                    })
        except:
            pass
    
    return issues

def find_data_relation_issues():
    """查找数据关联问题"""
    issues = []
    
    # 这里实现具体的数据关联检查逻辑
    # 例如查找UUID使用错误等
    
    pattern_files = grep_search("purchased_entity_id", include="*.py")
    
    for file_path in pattern_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                # 查找可能的错误模式
                if "purchased_entity_id" in line and "id" in line and "category_id" not in line:
                    issues.append({
                        "file": file_path,
                        "line": i,
                        "wrong_field": "id",
                        "correct_field": "category_id",
                        "issue": "UUID字段使用错误"
                    })
        except:
            pass
    
    return issues

# 工具函数（简化实现）
def glob_search(pattern):
    """Glob搜索文件"""
    import glob
    return glob.glob(pattern, recursive=True)

def grep_search(pattern, include="*"):
    """Grep搜索内容"""
    # 简化实现，实际应该调用系统工具
    files = glob_search(include)
    matches = []
    
    for file_path in files[:20]:  # 限制搜索数量
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            if pattern.lower() in content.lower():
                matches.append(file_path)
        except:
            pass
    
    return matches
```

---

## 🚀 主程序入口

```python
if __name__ == "__main__":
    # 示例用法
    problem = "购买记录显示时分类名显示异常"
    
    result = diagnose_and_fix(problem)
    
    if result["success"]:
        print("🎉 诊断修复成功完成！")
    else:
        print(f"❌ 诊断修复失败: {result['reason']}")
```

