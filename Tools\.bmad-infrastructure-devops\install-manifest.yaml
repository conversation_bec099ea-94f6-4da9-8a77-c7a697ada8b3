version: 1.10.0
installed_at: '2025-07-23T22:09:56.501Z'
install_type: expansion-pack
expansion_pack_id: bmad-infrastructure-devops
expansion_pack_name: bmad-infrastructure-devops
ides_setup:
  - cursor
  - claude-code
  - windsurf
  - trae
  - roo
  - cline
  - gemini
  - github-copilot
files:
  - path: .bmad-infrastructure-devops/config.yaml
    hash: 11b2afdf38da58d9
    modified: false
  - path: .bmad-infrastructure-devops/README.md
    hash: bb81a20324b3ffd6
    modified: false
  - path: >-
      .bmad-infrastructure-devops/templates/infrastructure-platform-from-arch-tmpl.yaml
    hash: 5b6a3604a7e6912a
    modified: false
  - path: >-
      .bmad-infrastructure-devops/templates/infrastructure-architecture-tmpl.yaml
    hash: a3204be65e3ba95d
    modified: false
  - path: .bmad-infrastructure-devops/tasks/validate-infrastructure.md
    hash: 7f809521d26f2ca3
    modified: false
  - path: .bmad-infrastructure-devops/tasks/review-infrastructure.md
    hash: 4e9af9734a519ca1
    modified: false
  - path: .bmad-infrastructure-devops/tasks/execute-checklist.md
    hash: 035b3e0b944d1fca
    modified: false
  - path: .bmad-infrastructure-devops/tasks/create-doc.md
    hash: 395719b8a002f7f9
    modified: false
  - path: .bmad-infrastructure-devops/data/technical-preferences.md
    hash: 6530bed845540b0d
    modified: false
  - path: .bmad-infrastructure-devops/data/bmad-kb.md
    hash: a608321f9b01bc5d
    modified: false
  - path: .bmad-infrastructure-devops/utils/workflow-management.md
    hash: b148df3ebb1f9c61
    modified: false
  - path: .bmad-infrastructure-devops/utils/bmad-doc-template.md
    hash: 4b2f7c4408835b9e
    modified: false
  - path: .bmad-infrastructure-devops/agents/infra-devops-platform.md
    hash: 7a42f1366af5afa9
    modified: false
  - path: .bmad-infrastructure-devops/checklists/infrastructure-checklist.md
    hash: 354d52a34c3eb5aa
    modified: false
