---
description: Claude Code自定义指令最佳实践 - 如何通过CLAUDE.md实施AI问题诊断与修复流程框架集v3
---

# Claude Code自定义指令最佳实践：流程框架集V3实施指南

## 概述

基于Claude Code官方文档和全网最佳实践，本指南详细说明如何通过CLAUDE.md文件和自定义命令实施AI问题诊断与修复流程框架集v3。

---

## 1. CLAUDE.md文件结构设计

### 核心原则（官方最佳实践）
- **简洁性**：150行的精准CLAUDE.md比300行的冗长文档更有效
- **结构化**：使用标准Markdown标题组织信息
- **动态性**：CLAUDE.md是活文档，需迭代优化
- **层次化**：支持项目级和目录级CLAUDE.md文件

### 项目根目录CLAUDE.md结构

```markdown
# 水幕课程管理系统 - AI诊断与修复流程

## 项目规范
- **语言**: 中文交流，英文代码  
- **原则**: DRY，高内聚低耦合，禁止伪造、简化实现
- **日志**: 错误、警告、结果、状态
- **架构**: 四层同步乐观更新、UUID、版本化API

## 技术栈
- **前端**: PyQt6桌面应用
- **后端**: FastAPI + SQLAlchemy + MySQL
- **环境**: WSL环境下开发，跨平台执行
- **线程**: PyQt6原生线程+线程池模式

## AI诊断修复流程选择

### 快速选择决策树
```
问题诊断流程选择：
├─ AI是否频繁偷懒/凭推理？ → 执行: /workflow-strict
├─ 时间是否紧急（30分钟内）？ → 执行: /workflow-fast  
├─ 问题是否涉及多个系统层次？ → 执行: /workflow-layered
└─ 问题复杂且历史多次失败？ → 执行: /workflow-evidence
```

### 流程执行原则
1. **强制证据收集**：所有分析必须基于实际证据
2. **假设验证循环**：每个假设都必须通过验证才能确认
3. **失败强制追溯**：修复失败后必须深入分析原因
4. **禁止推理跳跃**：不得基于经验判断跳过验证步骤

## 方法组件库引用
方法组件库位置：`/mnt/d/01-shuimu_01/.claude/commands/01/AI问题诊断与修复组件库.md`
包含21个诊断修复方法，按需调用。

## 执行约束
- **WSL环境**：使用`powershell.exe -Command`执行Windows程序
- **服务验证**：必须检查localhost:8000服务状态
- **日志优先**：优先查看/logs目录下的错误日志
- **实时验证**：修复后立即验证效果，不延迟

## 失败处理机制
- 技术性失败 → 内部重试
- 逻辑性失败 → 执行方法21失败追溯 → 重新分析
- 根本性失败 → 执行方法21失败追溯 → 重新收集证据
```

---

## 2. 自定义命令结构（.claude/commands）

### 方案1：严格证据驱动流程
**文件：.claude/commands/workflow-evidence.md**

```markdown
---
description: 严格证据驱动诊断修复流程，适合复杂技术问题
---

# 严格证据驱动流程执行

基于 `$ARGUMENTS` 问题描述，执行严格证据驱动诊断修复流程。

## 阶段0：项目全景认知建立
1. 项目识别 + 技术栈确认 + WSL环境确认
2. 规范提取（方法17） + 服务连通验证（方法3）
3. 业务流程理解：核心业务实体和流程识别
4. 数据库结构理解：主要表结构和关系梳理
5. API接口理解：关键接口和数据流映射
6. 配置环境理解：服务配置和日志位置确认

## 阶段1：强制证据收集 [防偷懒核心]
1. 并行执行：方法6日志收集 + 方法8环境检查
2. 串行执行：方法7分层确认用户症状
3. **强制检查点**：方法19证据驱动分析
   - 必须基于实际收集的证据
   - 禁止基于经验推测或可能性判断
   - 证据不足时禁止继续，必须重新收集

## 阶段2：逐层隔离验证
1. 并行执行：方法3服务验证 + 方法2API验证
2. 并行执行：方法9路径确认 + 方法10数据追踪
3. 验证检查点：确认真实执行状态

## 阶段3：根因假设循环 [防推理核心]
1. 串行执行：方法11逐层隔离定位
2. 串行执行：方法13代码逻辑验证
3. 串行执行：方法12单一根因确定
4. **强制验证循环**：方法20假设验证循环
   - 每个根因假设都必须通过实际验证
   - 验证流程：提出假设 → 设计验证 → 执行验证 → 确认/否定
   - 假设未验证禁止确认，必须继续验证循环

## 阶段4：修复实施验证
1. 并行执行：方法17规范提取 + 修复方案设计
2. 修复实施：基于验证确认的根因进行针对性修复
3. 立即验证：方法4代码生效 + 方法1执行验证 + 方法2API验证

**失败处理**：
- 技术性失败 → 阶段4内部重试
- 逻辑性失败 → 方法21失败追溯 → 返回阶段3
- 根本性失败 → 方法21失败追溯 → 返回阶段1

## 阶段5：完整性确认
1. 串行执行：方法14修复效果实时验证
2. 并行执行：方法15功能完整性测试 + 方法16根本解决确认
3. 最终验证：方法18修复合规性验证

**验证失败处理**：
- 验证失败 → 方法21失败追溯 → 返回阶段1

## 执行要求
- 每个阶段必须完成才能进入下一阶段
- 强制检查点不可跳过
- 所有修复必须立即验证
- 失败必须追溯原因，不重复原方案
```

### 方案2：快速迭代验证流程
**文件：.claude/commands/workflow-fast.md**

```markdown
---
description: 快速迭代验证流程，适合简单紧急问题（15-30分钟内完成）
---

# 快速迭代验证流程执行

基于 `$ARGUMENTS` 问题描述，执行快速迭代验证流程。

## 快速诊断循环（15分钟内完成）
1. **方法6日志优先症状收集**（5分钟）
   - 优先查看错误日志和异常堆栈
   - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述

2. **方法3服务状态验证**（2分钟）
   - 检查服务进程的真实运行状态
   - `curl -s http://localhost:[端口]/[健康检查端点] || echo "服务不可用"`

3. **方法12单一根因优先**（3分钟）
   - 一个问题对应一个主要原因
   - 先验证最直接最简单的可能性

4. **方法20假设验证循环**（3分钟）
   - 立即验证：提出假设 → 设计验证 → 执行验证 → 确认/否定
   - 不允许假设未验证就当结论

5. **修复验证**（2分钟）
   - 方法1：powershell.exe执行验证
   - 方法14：修复效果实时验证

## 迭代失败处理
**失败时立即执行**：
- 方法21失败原因追溯
- 识别失败点 → 收集新证据 → 调整分析方向
- 重新进入快速循环（不重复原方案）

## 成功后补强验证（10分钟内完成）
1. 方法15功能完整性测试
2. 方法16根本解决确认
3. 方法18修复合规性验证

## 执行约束
- 时间严格控制：每个步骤不得超过预定时间
- 失败快速调整：不在单个假设上浪费时间
- 保持迭代思维：准备多轮快速调整
- 简单问题优先：复杂问题及时切换到其他流程
```

### 方案3：分层递进诊断流程
**文件：.claude/commands/workflow-layered.md**

```markdown
---
description: 分层递进诊断流程，适合多层架构问题（表象→系统→根因）
---

# 分层递进诊断流程执行

基于 `$ARGUMENTS` 问题描述，执行分层递进诊断流程。

## 第1层：表象问题分析
1. **方法7用户症状分层确认**
   - 表象层：用户直接看到的问题
   - 系统层：服务和环境层面异常
   - 根因层：代码和技术层面原因

2. **方法6日志优先症状收集**
   - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述

3. **方法8系统环境状态检查**
   - 检查维度：服务可用性 | 配置完整性 | 依赖满足性
   - 并行策略：多维度同时检查，快速定位环境问题

**层次确认**：表象问题清单完整

## 第2层：系统问题分析
1. **方法11逐层隔离定位**
   - 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次

2. **方法2API请求验证**
   - `curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']`
   - 流程：调用API → 分析响应 → 验证业务逻辑

3. **方法3服务状态验证**
   - 检查服务进程的真实运行状态

4. **证据检查**：方法19证据驱动分析
   - 确保所有分析基于实际证据

**层次确认**：系统异常点明确

## 第3层：根因问题分析
1. **方法10数据流完整追踪**
   - 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
   - 异常识别：数据丢失、格式错误、转换失败、存储异常

2. **方法9执行路径反向确认**
   - 反向策略：从输出结果追溯到具体代码位置

3. **方法13代码逻辑直接验证**
   - 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支

4. **假设验证**：方法20假设验证循环
   - 每个根因假设都必须通过实际验证

**层次确认**：根本原因确定

## 修复验证层
1. **方法1powershell.exe执行验证**
2. **方法14修复效果实时验证**
3. **方法15功能完整性测试**
4. **方法16根本解决确认**
5. **失败处理**：方法21失败原因追溯

## 执行特点
- 层次分明：不跨层分析，避免混乱
- 递进深入：从表象到根因逐步深入
- 系统性强：每层都有完整的分析验证
- 适合架构复杂的多层次问题
```

### 方案4：防偷懒强化流程
**文件：.claude/commands/workflow-strict.md**

```markdown
---
description: 防偷懒强化流程，专门约束AI偷懒行为，每节点强制验证
---

# 防偷懒强化流程执行

基于 `$ARGUMENTS` 问题描述，执行防偷懒强化约束流程。

## 强制证据阶段 [不可跳过]
1. **必须执行**：方法6日志优先症状收集
   - 必须查看实际日志文件，不得凭推测
   - 必须收集具体错误信息，不得泛泛而谈

2. **必须执行**：方法8系统环境状态检查
   - 必须检查服务实际状态，不得假设服务可用
   - 必须验证配置文件存在和正确性

3. **强制检查点**：方法19证据驱动分析
   - **无证据禁止继续** [强制阻断]
   - 必须基于实际收集的证据进行分析
   - 禁止基于经验推测或可能性判断
   - 证据不足时必须返回重新收集

4. **用户确认**：症状收集完整性确认
   - 必须用户确认证据充分且具体
   - **继续条件**：证据充分且具体

## 强制验证阶段 [不可跳过]
1. **必须执行**：方法2或方法3中至少一个进行验证
   - 不得跳过实际验证步骤
   - 必须获得具体验证结果

2. **必须执行**：方法9执行路径反向确认
   - 必须从实际输出追溯到代码位置
   - 不得凭推理确定执行路径

3. **强制检查点**：方法20假设验证循环
   - **无验证禁止确认** [强制阻断]
   - 每个假设都必须通过实际验证
   - 验证流程：提出假设 → 设计验证 → 执行验证 → 确认/否定
   - **继续条件**：所有假设都经过验证

## 强制修复阶段 [不可跳过]
1. **必须执行**：方法1powershell.exe执行验证
   - 必须实际执行程序验证功能
   - 不得假设修复生效

2. **必须执行**：方法14修复效果实时验证
   - 修复后立即验证，不得延迟
   - 必须确认修复解决了原始问题

3. **检查点**：方法16根本解决确认
   - 必须确认在相同触发条件下问题不再出现
   - 必须确认根本原因被消除

4. **失败处理**：方法21失败原因追溯
   - 任何失败都必须深入分析原因
   - 不得重复相同的修复方案

## 强制回归阶段 [不可跳过]
1. **必须执行**：方法15功能完整性测试
   - 必须测试直接相关功能、间接依赖功能、核心业务流程
   - 不得假设修复不影响其他功能

2. **必须执行**：方法18修复合规性验证
   - 必须验证代码风格、架构约束、命名规范、业务规则
   - 不得忽略项目规范要求

3. **最终确认**：问题完全解决且无副作用
   - **完成条件**：所有测试通过且无新问题引入

## 强制约束机制
### 禁止行为清单
- ❌ 基于经验推测根因
- ❌ 跳过证据收集阶段  
- ❌ 假设未验证就当结论
- ❌ 修复后不立即验证
- ❌ 失败后重复相同方案

### 强制要求清单
- ✅ 每个分析基于实际证据
- ✅ 每个假设都有验证结果
- ✅ 每个修复都有效果确认
- ✅ 每个失败都有原因追溯

### 执行监控
- 每个强制检查点都必须确认通过
- 不允许跳过任何强制执行步骤
- 失败时必须执行对应的失败处理流程
- 保持最高约束强度，防止任何偷懒行为
```

---

## 3. 实施配置步骤

### 步骤1：创建目录结构
```bash
# 项目根目录创建
mkdir -p .claude/commands

# 复制组件库到commands目录
cp "AI问题诊断与修复组件库.md" .claude/commands/
```

### 步骤2：创建CLAUDE.md
在项目根目录创建`CLAUDE.md`文件，包含上述内容结构。

### 步骤3：创建自定义命令
在`.claude/commands/`目录下创建四个流程命令文件：
- `workflow-evidence.md`
- `workflow-fast.md` 
- `workflow-layered.md`
- `workflow-strict.md`

### 步骤4：测试和优化
```bash
# 启动Claude Code
claude

# 测试命令
/workflow-fast "用户登录功能报错"
/workflow-evidence "数据库连接间歇性失败"
/workflow-layered "API调用链路异常"
/workflow-strict "AI之前偷懒导致的修复失败"
```

---

## 4. 使用指南

### 命令选择策略
```bash
# 简单紧急问题
/workflow-fast "简单错误描述"

# 复杂技术问题
/workflow-evidence "复杂问题描述"

# 多层架构问题
/workflow-layered "涉及多个系统层次的问题"

# AI偷懒严重时
/workflow-strict "需要强制约束的问题"
```

### 执行监控
- 观察AI是否跳过强制检查点
- 确认每个阶段都有实际验证结果
- 监控是否基于推理而非证据进行分析
- 检查失败处理是否执行方法21追溯

### 持续优化
- 记录每次执行效果
- 根据实际使用情况调整CLAUDE.md
- 优化命令模板内容
- 收集团队使用反馈

---

## 5. 官方最佳实践集成

### 性能优化
- **简洁性**：保持CLAUDE.md在150行以内
- **缓存机制**：利用Claude Code的15分钟缓存
- **上下文管理**：定期使用`/compact`压缩上下文

### 安全实践
- **权限控制**：只对读取操作设置自动执行
- **版本控制**：将CLAUDE.md和命令文件纳入Git管理
- **团队共享**：确保团队成员使用一致的流程

### 协作优化
- **文档同步**：CLAUDE.md文件团队共享
- **命令统一**：所有自定义命令存储在仓库中
- **反馈循环**：建立持续改进机制

---

## 总结

通过Claude Code的CLAUDE.md文件和自定义命令系统，我们成功实现了AI问题诊断与修复流程框架集v3的完整集成。这套系统结合了：

1. **官方最佳实践**：基于Anthropic和社区的成熟经验
2. **结构化流程**：四种不同场景的专业化诊断流程
3. **强制约束机制**：有效防止AI偷懒和凭推理行为
4. **工程化实现**：完整的配置、部署和使用指南

这套实施方案将显著提高AI辅助开发的效率和质量，确保问题诊断修复的成功率和一致性。

---

*基于Claude Code官方文档和全网最佳实践的专业实施指南*