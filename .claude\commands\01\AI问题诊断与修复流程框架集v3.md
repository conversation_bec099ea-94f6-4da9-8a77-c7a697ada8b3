---
description: 基于21个方法组件的问题诊断与修复流程框架集v3
---

# AI问题诊断与修复流程框架集v3

## 核心设计理念

**解决核心问题**：AI偷懒、凭推理找根因、循环失败
**设计原则**：强制证据收集、假设必须验证、失败强制反思
**方法调用**：21个方法组件在关键节点精准调用

---

## 方案1：严格证据驱动流程
**适用场景**：复杂技术问题、多系统交互问题、历史重复失败问题
**核心原则**：无证据不分析，无验证不确认

### 阶段0：项目认知建立
```
项目基础认知
├─ 调用方法18：规范动态提取应用
├─ 调用方法3：服务状态验证
├─ 业务架构理解：核心实体和流程识别
├─ 数据库结构理解：主要表结构和关系梳理
├─ API接口理解：关键接口和数据流映射
└─ 配置环境理解：服务配置和日志位置确认
```

### 阶段1：强制证据收集 [防偷懒核心]
```
证据收集阶段
├─ 调用方法6：日志优先症状收集
├─ 调用方法8：系统环境状态检查
├─ 调用方法7：用户症状分层确认
├─ 强制检查点：调用方法19：证据驱动分析
└─ 禁止继续条件：证据不足、推理分析、经验判断
```

### 阶段2：逐层隔离验证
```
层次验证阶段
├─ 调用方法12：逐层隔离定位
├─ 调用方法10：数据流完整追踪
├─ 调用方法9：执行路径反向确认
├─ 验证检查点：调用方法2：API请求验证
└─ 确认：每层问题独立验证清楚
```

### 阶段3：根因假设循环 [防推理核心]
```
假设验证循环
├─ 调用方法13：单一根因优先
├─ 强制验证：调用方法20：假设验证循环
├─ 调用方法14：代码逻辑直接验证
├─ 确认检查点：调用方法4：代码生效验证
└─ 循环条件：假设未验证则继续循环
```

### 阶段4：修复实施验证
```
修复执行阶段
├─ 调用方法1：powershell.exe执行验证
├─ 调用方法15：修复效果实时验证
├─ 调用方法19：修复合规性验证
└─ 实时确认：修复立即验证，失败立即调整
```

### 阶段5：完整性确认
```
完整性验证阶段
├─ 调用方法16：功能完整性测试
├─ 调用方法17：根本解决确认
├─ 失败处理：调用方法21：失败原因追溯
└─ 终止条件：问题完全解决且无副作用
```

---

## 方案2：快速迭代验证流程
**适用场景**：简单明确问题、紧急故障处理、快速验证需求
**核心原则**：快速假设，立即验证，失败快速调整

### 快速诊断循环
```
快速循环 (15分钟内完成)
┌─ 调用方法6：日志优先症状收集 (5分钟)
├─ 调用方法3：服务状态验证 (2分钟)
├─ 调用方法13：单一根因优先 (3分钟)
├─ 立即验证：调用方法20：假设验证循环 (3分钟)
├─ 修复验证：调用方法1+方法15 (2分钟)
└─ 失败 → 调用方法21：失败原因追溯 → 重新循环
```

### 成功后补强
```
补强验证 (10分钟内完成)
├─ 调用方法16：功能完整性测试
├─ 调用方法17：根本解决确认
└─ 调用方法19：修复合规性验证
```

### 迭代失败处理
```
失败快速调整
├─ 调用方法21：失败原因追溯
├─ 重新收集：调用方法8：系统环境状态检查
├─ 调整假设：调用方法13：单一根因优先
└─ 重新进入快速循环
```

---

## 方案3：分层递进诊断流程
**适用场景**：层次性问题、架构复杂问题、多层次交互问题
**核心原则**：表象→系统→根因，层层递进

### 第1层：表象问题分析
```
表象层诊断
├─ 调用方法7：用户症状分层确认
├─ 调用方法6：日志优先症状收集
├─ 调用方法8：系统环境状态检查
└─ 层次确认：表象问题清单完整
```

### 第2层：系统问题分析
```
系统层诊断
├─ 调用方法12：逐层隔离定位
├─ 调用方法2：API请求验证
├─ 调用方法3：服务状态验证
├─ 证据检查：调用方法19：证据驱动分析
└─ 层次确认：系统异常点明确
```

### 第3层：根因问题分析
```
根因层诊断
├─ 调用方法10：数据流完整追踪
├─ 调用方法9：执行路径反向确认
├─ 调用方法14：代码逻辑直接验证
├─ 假设验证：调用方法20：假设验证循环
└─ 层次确认：根本原因确定
```

### 修复验证层
```
修复层验证
├─ 调用方法1：powershell.exe执行验证
├─ 调用方法15：修复效果实时验证
├─ 调用方法16：功能完整性测试
├─ 调用方法17：根本解决确认
└─ 失败处理：调用方法21：失败原因追溯
```

---

## 方案4：防偷懒强化流程
**适用场景**：AI偷懒严重、多次失败、需要强制约束
**核心原则**：每个节点都有强制验证，防止跳过

### 强制证据阶段
```
证据强制收集 [不可跳过]
├─ 必须：调用方法6：日志优先症状收集
├─ 必须：调用方法8：系统环境状态检查
├─ 检查点：调用方法19：证据驱动分析 
│   └─ 无证据禁止继续 [强制阻断]
├─ 确认：用户确认症状收集完整性
└─ 继续条件：证据充分且具体
```

### 强制验证阶段
```
验证强制执行 [不可跳过]
├─ 必须：调用方法2/3的至少一个进行验证
├─ 必须：调用方法9：执行路径反向确认
├─ 检查点：调用方法20：假设验证循环
│   └─ 无验证禁止确认 [强制阻断]
├─ 确认：每个假设都有对应验证结果
└─ 继续条件：所有假设都经过验证
```

### 强制修复阶段
```
修复强制验证 [不可跳过]
├─ 必须：调用方法1：powershell.exe执行验证
├─ 必须：调用方法15：修复效果实时验证
├─ 检查点：调用方法17：根本解决确认
├─ 失败处理：调用方法21：失败原因追溯
└─ 继续条件：修复效果确认成功
```

### 强制回归阶段
```
回归强制测试 [不可跳过]
├─ 必须：调用方法16：功能完整性测试
├─ 必须：调用方法19：修复合规性验证
├─ 最终确认：问题完全解决且无副作用
└─ 完成条件：所有测试通过
```

---

## 流程选择决策树

```
问题类型判断
├─ 简单明确 + 时间紧急 → 方案2：快速迭代验证流程
├─ 复杂技术 + 多系统交互 → 方案1：严格证据驱动流程
├─ 层次性强 + 架构复杂 → 方案3：分层递进诊断流程
└─ AI偷懒严重 + 多次失败 → 方案4：防偷懒强化流程
```

## 失败处理统一机制

**所有方案的失败处理**：
1. 立即调用方法21：失败原因追溯
2. 重新评估问题复杂度
3. 考虑切换到更严格的流程
4. 重新开始，不重复原失败路径

---

## 核心防偷懒机制

### 强制验证点
- **证据检查点**：方法19必须通过才能继续
- **假设验证点**：方法20必须完成才能确认
- **效果验证点**：方法15必须成功才能完成

### 禁止行为清单
- ❌ 基于经验推测根因
- ❌ 跳过证据收集阶段
- ❌ 假设未验证就当结论
- ❌ 修复后不立即验证
- ❌ 失败后重复相同方案

### 强制要求清单
- ✅ 每个分析基于实际证据
- ✅ 每个假设都有验证结果
- ✅ 每个修复都有效果确认
- ✅ 每个失败都有原因追溯

---

*AI问题诊断与修复流程框架集v3 - 基于21个方法组件的完整解决方案*