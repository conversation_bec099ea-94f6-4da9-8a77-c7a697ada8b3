# AI问题诊断与修复流程框架

> 配合"AI问题诊断与修复组件库.md"使用，提供结构化的执行框架和积分激励机制

## 📊 AI积分奖励机制

### 积分规则

#### 正向激励
| 行为 | 积分 | 说明 |
|-----|------|-----|
| 找到真实根因 | +10 | 基于证据找到问题根源 |
| 一次修复成功 | +15 | 首次尝试即解决问题 |
| 主动收集证据 | +5 | 执行日志分析、环境检查等 |
| 验证假设成功 | +5 | 每个假设验证通过 |
| 发现隐藏问题 | +20 | 发现用户未报告的问题 |

#### 负向惩罚
| 行为 | 积分 | 说明 |
|-----|------|-----|
| 凭推理判断 | -15 | 无证据下做出判断 |
| 跳过验证步骤 | -10 | 未执行必要验证 |
| 修复失败 | -20 | 修复后问题仍存在 |
| 重复同样错误 | -30 | 第二次犯相同错误 |
| 忽略用户日志 | -25 | 用户提供日志但未分析 |

### 执行要求
- 每次诊断开始积分归零
- 积分 < 0 时强制重新分析
- 积分 > 50 可获得"高效诊断"标记

## 📋 方案A：标准诊断流程（推荐）

### 执行流程图
```
用户报告问题
    ↓
┌─────────────────┐
│  1. 证据收集    │ [并行执行 +5分/项]
├─────────────────┤
│ □ 方法6: 日志优先症状收集
│ □ 方法3: 服务状态验证
│ □ 方法8: 系统环境检查
└─────────────────┘
    ↓
┌─────────────────┐
│  2. 根因分析    │ [强制验证]
├─────────────────┤
│ → 方法19: 证据驱动分析（强制）
│ → 方法20: 假设验证循环
│   ├─ 验证通过 +10分
│   └─ 验证失败 -10分 → 重新假设
└─────────────────┘
    ↓
┌─────────────────┐
│  3. 实施修复    │
├─────────────────┤
│ → 方法13: 代码逻辑直接验证
│ → 执行修复操作
└─────────────────┘
    ↓
┌─────────────────┐
│  4. 验证修复    │ [强制执行]
├─────────────────┤
│ → 方法14: 修复效果实时验证
│   ├─ 成功 +15分 → 方法15: 功能完整性测试
│   └─ 失败 -20分 → 方法21: 失败原因追溯
└─────────────────┘
```

### 强制执行点
1. **必须**先收集证据（第1步）
2. **必须**验证每个假设（第2步）
3. **必须**验证修复效果（第4步）

### 适用场景
- 常规功能异常
- 一般性错误
- 非紧急问题

## 📋 方案B：快速响应流程（紧急情况）

### 触发条件
- 生产环境故障
- 服务完全不可用
- 需要立即恢复

### 执行步骤

#### 1. 紧急诊断 [2分钟内]
```yaml
执行:
  - 方法3: 服务状态验证
  - 方法6: 错误日志快速扫描
惩罚:
  - 跳过此步骤: -30分
```

#### 2. 快速定位 [5分钟内]
```yaml
执行:
  - 方法9: 执行路径反向确认
  - 方法12: 单一根因优先
奖励:
  - 找到根因: +20分
```

#### 3. 应急修复
```yaml
原则:
  - 最小改动
  - 快速恢复
验证:
  - 方法4: 代码生效验证
```

#### 4. 立即验证
```yaml
执行:
  - 方法1: powershell执行验证
  - 方法2: API请求验证
积分:
  - 成功: +30分
  - 失败: -40分
```

## 📋 方案C：深度诊断流程（复杂问题）

### 触发条件
- 间歇性问题
- 多次修复失败（自动触发）
- 数据异常
- 性能问题

### 执行阶段

#### 阶段1：全面信息收集 [必须完成所有]
```yaml
并行执行:
  - 方法6: 日志优先症状收集
  - 方法7: 用户症状分层确认
  - 方法8: 系统环境状态检查
  - 方法10: 数据流完整追踪
奖励: 每完成一项 +3分
```

#### 阶段2：系统性分析 [强制验证]
```yaml
顺序执行:
  - 方法11: 逐层隔离定位
  - 方法19: 证据驱动分析（强制）
  - 方法20: 假设验证循环（每个假设必须验证）
奖励: 正确定位 +25分
```

#### 阶段3：综合修复
```yaml
执行:
  - 方法5: 功能重复性检查（避免重复造轮子）
  - 方法17: 规范动态提取应用
  - 方法18: 修复合规性验证
```

#### 阶段4：完整验证
```yaml
必须全部通过:
  - 方法14: 修复效果实时验证
  - 方法15: 功能完整性测试
  - 方法16: 根本解决确认
奖励: 全部通过 +40分
```

## 🚨 强制中断机制

### 触发条件
| 条件 | 动作 |
|-----|------|
| 积分 < -20 | 强制重新开始 |
| 连续2次相同错误 | 强制切换方案 |
| 无证据分析 | 立即中断并扣30分 |

### 中断后操作
1. 显示当前积分和扣分原因
2. 强制执行方法21（失败原因追溯）
3. 切换到更严格的诊断方案

## 💡 自动方案选择

### 问题类型映射
```yaml
问题类型 → 推荐方案:
  "服务启动失败": 方案A
  "功能异常": 方案A
  "生产环境宕机": 方案B
  "数据丢失": 方案C
  "性能问题": 方案C
  "多次修复失败": 方案C（强制）
```

### 自动升级规则
- 方案A失败2次 → 自动升级到方案C
- 积分 < -30 → 强制使用方案C
- 用户明确要求深度分析 → 直接使用方案C

## 📝 执行示例

### 示例1：标准流程执行
```
用户：管理端登录失败

AI执行：
1. [证据收集] 
   - 执行方法6查看日志 (+5分)
   - 执行方法3检查服务 (+5分)
   - 发现API返回401错误
   
2. [根因分析]
   - 假设：token验证失败
   - 执行方法20验证 (+5分)
   - 确认：token过期逻辑错误 (+10分)
   
3. [修复]
   - 修改token验证逻辑
   
4. [验证]
   - 方法14验证成功 (+15分)
   
总积分：40分
```

### 示例2：紧急响应执行
```
用户：生产服务器挂了！

AI执行：
1. [2分钟诊断]
   - 方法3：服务已停止
   - 方法6：发现OOM错误
   
2. [快速定位] 
   - 方法12：内存泄漏是单一根因 (+20分)
   
3. [应急修复]
   - 增加内存限制
   - 重启服务
   
4. [验证]
   - 服务恢复正常 (+30分)
   
总积分：50分（获得高效诊断标记）
```

## ⚡ 快速参考卡

### 必须执行
- 证据收集（方法6/3/8）
- 假设验证（方法20）
- 修复验证（方法14）

### 禁止行为
- 跳过日志分析
- 无证据推理
- 不验证直接提交

### 积分速查
- 主动收集证据：+5
- 找到根因：+10
- 修复成功：+15
- 凭推理：-15
- 修复失败：-20