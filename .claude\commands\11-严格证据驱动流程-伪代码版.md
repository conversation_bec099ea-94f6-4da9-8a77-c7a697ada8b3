---
description: 基于 `$ARGUMENTS` 问题描述，按照 严格证据驱动流程-伪代码版 执行诊断和修复
version: 11
---

# 🤖 严格证据驱动流程-伪代码版

## 📊 全局变量定义

```python
# 积分系统
GLOBAL score = 0
GLOBAL evidence = {}
GLOBAL hypothesis = null
GLOBAL fix_attempts = 0
GLOBAL phase_history = []

# 常量定义
CONST MAX_HYPOTHESIS_RETRIES = 3
CONST MAX_FIX_RETRIES = 3
CONST FAILURE_THRESHOLD = -20
CONST SUCCESS_THRESHOLD = 100
CONST EXCELLENCE_THRESHOLD = 150

# 积分规则
CONST SCORE_RULES = {
    "complete_check": 5,
    "find_issue": 10,
    "locate_success": 20,
    "fix_success": 30,
    "skip_check": -10,
    "assume_without_evidence": -15,
    "fix_failure": -20,
    "repeat_error": -30,
    "fake_execution": -50
}
```

## 🚀 主执行函数

```python
FUNCTION main_diagnose_and_fix(problem_description):
    PRINT("🎯 开始诊断流程")
    PRINT(f"问题描述: {problem_description}")
    PRINT(f"初始积分: {score}")
    
    TRY:
        # 阶段0：项目全景认知
        IF NOT execute_phase0_project_understanding():
            RETURN failure("项目认知失败")
        
        # 阶段1：强制证据收集
        IF NOT execute_phase1_evidence_collection():
            RETURN failure("证据收集失败")
        
        # 阶段2：逐层隔离验证
        problem_layer = execute_phase2_isolation()
        IF problem_layer IS NULL:
            RETURN failure("问题定位失败")
        
        # 阶段3：根因假设循环
        root_cause = execute_phase3_hypothesis_loop()
        IF root_cause IS NULL:
            RETURN failure("根因分析失败")
        
        # 阶段4：修复实施
        fix_result = execute_phase4_implementation(root_cause)
        IF NOT fix_result.success:
            RETURN failure("修复实施失败")
        
        # 阶段5：完整性确认
        IF NOT execute_phase5_validation(fix_result):
            RETURN failure("验证未通过")
        
        RETURN success()
        
    CATCH Exception as e:
        PRINT(f"❌ 流程异常: {e}")
        RETURN handle_exception(e)
```

## 📋 阶段0：项目全景认知

```python
FUNCTION execute_phase0_project_understanding():
    PRINT("\n=== 阶段0：项目全景认知 ===")
    phase_start_score = score
    
    # 检查项列表
    checks = [
        {"name": "项目识别", "func": check_project_structure},
        {"name": "技术栈确认", "func": check_tech_stack},
        {"name": "WSL环境确认", "func": check_wsl_environment},
        {"name": "业务流程理解", "func": check_business_logic},
        {"name": "数据库结构理解", "func": check_database_schema},
        {"name": "API接口理解", "func": check_api_endpoints},
        {"name": "配置环境理解", "func": check_configurations},
        {"name": "规范提取(方法17)", "func": method17_extract_rules},
        {"name": "服务验证(方法3)", "func": method3_verify_service}
    ]
    
    FOR check IN checks:
        PRINT(f"\n检查项: {check.name}")
        result = check.func()
        
        IF result.success:
            score += SCORE_RULES["complete_check"]
            PRINT(f"✅ {check.name} 完成 (+5分)")
            
            IF result.first_try:
                score += 5
                PRINT(f"✅ 一次通过 (+5分)")
        ELSE:
            score += SCORE_RULES["skip_check"]
            PRINT(f"❌ {check.name} 失败 (-10分)")
            
            # 重试逻辑
            PRINT(f"🔄 重试 {check.name}...")
            retry_result = retry_with_guidance(check.func, result.error)
            IF NOT retry_result.success:
                PRINT(f"❌ 重试失败，当前积分: {score}")
                IF score < FAILURE_THRESHOLD:
                    RETURN False
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段0完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN score > 0
```

## 🔍 阶段1：强制证据收集

```python
FUNCTION execute_phase1_evidence_collection():
    PRINT("\n=== 阶段1：强制证据收集 ===")
    phase_start_score = score
    retries = 0
    
    WHILE retries < 3:
        PRINT(f"\n第{retries + 1}次证据收集")
        
        # 强制并行执行
        PRINT("🔒 强制并行执行：方法6 + 方法8")
        parallel_results = PARALLEL_EXECUTE([
            method6_collect_logs,
            method8_check_environment
        ])
        
        # 等待并行结果
        logs = parallel_results[0]
        env_status = parallel_results[1]
        
        IF logs.success AND env_status.success:
            score += 10
            PRINT("✅ 并行收集成功 (+10分)")
        ELSE:
            score -= 15
            PRINT("❌ 并行收集失败 (-15分)")
            retries += 1
            CONTINUE
        
        # 串行执行症状分层
        PRINT("\n执行方法7：分层症状确认")
        symptoms = method7_layer_symptoms(logs.data, env_status.data)
        IF symptoms.success:
            score += 5
            evidence = symptoms.data
            PRINT("✅ 症状分层完成 (+5分)")
        ELSE:
            score -= 5
            PRINT("❌ 症状分层失败 (-5分)")
            retries += 1
            CONTINUE
        
        # 证据验证（方法22）
        PRINT("\n🚨 执行方法22：证据有效性验证")
        validation = method22_validate_evidence(evidence)
        
        IF validation.complete AND validation.relevant AND validation.specific:
            score += 10
            PRINT("✅ 证据验证通过 (+10分)")
            PRINT(f"  完整性: ✓ 相关性: ✓ 具体性: ✓")
            
            # 证据驱动分析（方法19）
            PRINT("\n🚨 执行方法19：证据驱动分析")
            IF can_analyze_with_evidence(evidence):
                score += 10
                PRINT("✅ 可以基于证据分析 (+10分)")
                BREAK
            ELSE:
                score -= 20
                PRINT("❌ 证据不足以分析 (-20分)")
                retries += 1
        ELSE:
            score -= 15
            PRINT("❌ 证据验证失败 (-15分)")
            PRINT(f"  完整性: {'✓' if validation.complete else '✗'}")
            PRINT(f"  相关性: {'✓' if validation.relevant else '✗'}")
            PRINT(f"  具体性: {'✓' if validation.specific else '✗'}")
            
            # 补充收集
            IF NOT validation.complete:
                PRINT("📋 需要补充日志范围")
                evidence = supplement_logs(evidence)
            IF NOT validation.relevant:
                PRINT("📋 需要更相关的证据")
                evidence = get_relevant_evidence(evidence)
            IF NOT validation.specific:
                PRINT("📋 需要更具体的信息")
                evidence = get_specific_details(evidence)
            
            retries += 1
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段1完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN retries < 3
```

## 🎯 阶段2：逐层隔离验证

```python
FUNCTION execute_phase2_isolation():
    PRINT("\n=== 阶段2：逐层隔离验证 ===")
    phase_start_score = score
    problem_layer = NULL
    
    # 并行验证
    PRINT("🔄 并行执行验证")
    parallel_checks = PARALLEL_EXECUTE([
        ["服务层", method3_service_check, method2_api_check],
        ["路径层", method9_path_verification, method10_data_trace]
    ])
    
    # 分析并行结果
    service_ok = parallel_checks[0][0].success AND parallel_checks[0][1].success
    path_ok = parallel_checks[1][0].success AND parallel_checks[1][1].success
    
    IF service_ok AND path_ok:
        score += 20
        PRINT("✅ 并行验证完成 (+20分)")
    ELSE:
        score += 10
        PRINT("⚠️ 部分验证失败 (+10分)")
    
    # 问题层次判断
    IF NOT problem_layer:
        PRINT("\n执行方法11：逐层隔离")
        layers = ["UI层", "API层", "业务逻辑层", "数据库层"]
        
        FOR layer IN layers:
            PRINT(f"\n检查 {layer}")
            layer_result = check_layer(layer)
            
            IF layer_result.has_problem:
                problem_layer = layer
                score += 15
                PRINT(f"✅ 定位到问题层：{layer} (+15分)")
                
                # 深入分析
                deep_analysis = analyze_layer_deeply(layer, evidence)
                IF deep_analysis.success:
                    score += 5
                    evidence.update(deep_analysis.data)
                    PRINT(f"✅ {layer}深入分析完成 (+5分)")
                BREAK
        
        IF NOT problem_layer:
            score -= 10
            PRINT("❌ 无法确定问题层次 (-10分)")
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段2完成，本阶段得分: {phase_score}，总积分: {score}")
    PRINT(f"问题定位：{problem_layer}")
    RETURN problem_layer
```

## 🔬 阶段3：根因假设循环

```python
FUNCTION execute_phase3_hypothesis_loop():
    PRINT("\n=== 阶段3：根因假设循环 ===")
    phase_start_score = score
    hypothesis_count = 0
    confirmed_root_cause = NULL
    
    # 单一根因优先（方法12）
    PRINT("执行方法12：单一根因优先")
    single_cause = method12_single_root_cause(evidence)
    IF single_cause.found:
        score += 5
        hypothesis = single_cause.hypothesis
        PRINT(f"✅ 识别到单一可能根因 (+5分): {hypothesis}")
    ELSE:
        hypothesis = generate_hypothesis_from_evidence(evidence)
        PRINT(f"📝 生成假设: {hypothesis}")
    
    # 假设验证循环（方法20）
    WHILE hypothesis_count < MAX_HYPOTHESIS_RETRIES AND NOT confirmed_root_cause:
        hypothesis_count += 1
        PRINT(f"\n🚨 执行方法20：假设验证循环 (第{hypothesis_count}次)")
        
        # 代码逻辑验证（方法13）
        PRINT("执行方法13：代码逻辑验证")
        code_check = method13_verify_code_logic(hypothesis)
        IF code_check.confirms_hypothesis:
            score += 5
            PRINT("✅ 代码逻辑支持假设 (+5分)")
        ELSE:
            PRINT("❌ 代码逻辑不支持假设")
        
        # 设计验证方案
        PRINT("\n设计验证方案:")
        verification_plan = design_verification(hypothesis)
        PRINT(f"  验证命令: {verification_plan.command}")
        PRINT(f"  预期结果: {verification_plan.expected}")
        score += 5
        
        # 执行验证
        PRINT("\n执行验证...")
        result = EXECUTE_COMMAND(verification_plan.command)
        PRINT(f"实际结果: {result}")
        
        # 判断验证结果
        IF result == verification_plan.expected:
            score += 20
            confirmed_root_cause = hypothesis
            PRINT(f"✅ 假设验证成功！(+20分)")
            PRINT(f"确认根因: {confirmed_root_cause}")
        ELIF partially_matches(result, verification_plan.expected):
            score += 5
            PRINT("⚠️ 部分匹配，细化假设 (+5分)")
            hypothesis = refine_hypothesis(hypothesis, result)
        ELSE:
            score -= 10
            PRINT("❌ 假设验证失败 (-10分)")
            hypothesis = generate_new_hypothesis(evidence, result)
            PRINT(f"新假设: {hypothesis}")
    
    IF NOT confirmed_root_cause:
        score -= 20
        PRINT("❌ 无法确认根因 (-20分)")
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段3完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN confirmed_root_cause
```

## 🛠️ 阶段4：修复实施

```python
FUNCTION execute_phase4_implementation(root_cause):
    PRINT("\n=== 阶段4：修复实施 ===")
    phase_start_score = score
    fix_result = {"success": False}
    attempts = 0
    
    # 修复准备（并行）
    PRINT("🔄 并行准备修复")
    prep_results = PARALLEL_EXECUTE([
        method17_extract_relevant_rules,  # 规范提取
        method5_check_duplicate_functions  # 功能重复检查
    ])
    
    rules = prep_results[0]
    duplicates = prep_results[1]
    
    IF rules.success AND duplicates.success:
        score += 10
        PRINT("✅ 修复准备完成 (+10分)")
        PRINT(f"  适用规范: {rules.data}")
        PRINT(f"  可复用函数: {duplicates.data}")
    
    WHILE attempts < MAX_FIX_RETRIES AND NOT fix_result.success:
        attempts += 1
        PRINT(f"\n第{attempts}次修复尝试")
        
        # 实施修复
        fix_plan = create_fix_plan(root_cause, rules.data, duplicates.data)
        PRINT(f"修复方案: {fix_plan.description}")
        
        # 备份原文件
        backup_files(fix_plan.affected_files)
        
        # 应用修复
        apply_result = apply_fix(fix_plan)
        IF NOT apply_result.success:
            score -= 10
            PRINT(f"❌ 修复应用失败 (-10分): {apply_result.error}")
            restore_backup()
            CONTINUE
        
        # 强制立即验证
        PRINT("\n🔒 强制立即验证")
        verification = PARALLEL_EXECUTE([
            method4_code_effect_check,    # 代码生效验证
            method1_execution_check,       # 执行验证
            method2_api_verification       # API验证
        ])
        
        all_passed = ALL(v.success for v in verification)
        
        IF all_passed:
            score += 30
            fix_result = {"success": True, "details": fix_plan}
            PRINT("✅ 修复验证通过！(+30分)")
        ELSE:
            # 分析失败类型
            failure_type = analyze_failure(verification)
            
            IF failure_type == "TECHNICAL":
                score -= 10
                PRINT("❌ 技术性失败，调整实现 (-10分)")
                fix_plan = adjust_technical_approach(fix_plan)
            ELIF failure_type == "LOGICAL":
                score -= 20
                PRINT("❌ 逻辑性失败，返回阶段3 (-20分)")
                RETURN execute_phase3_hypothesis_loop()
            ELSE:  # FUNDAMENTAL
                score -= 30
                PRINT("❌ 根本性失败，返回阶段1 (-30分)")
                RETURN execute_phase1_evidence_collection()
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段4完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN fix_result
```

## ✅ 阶段5：完整性确认

```python
FUNCTION execute_phase5_validation(fix_result):
    PRINT("\n=== 阶段5：完整性确认 ===")
    phase_start_score = score
    all_tests_passed = True
    
    # 原问题验证（方法14）
    PRINT("执行方法14：修复效果验证")
    original_test = method14_verify_original_problem()
    IF original_test.success:
        score += 10
        PRINT("✅ 原问题已解决 (+10分)")
    ELSE:
        score -= 15
        all_tests_passed = False
        PRINT("❌ 原问题未解决 (-15分)")
    
    # 并行完整性测试
    PRINT("\n🔄 并行执行完整性测试")
    integrity_tests = PARALLEL_EXECUTE([
        method15_functional_integrity,  # 功能完整性
        method16_permanent_solution     # 根本解决确认
    ])
    
    FOR test IN integrity_tests:
        IF test.success:
            score += 5
            PRINT(f"✅ {test.name} 通过 (+5分)")
        ELSE:
            all_tests_passed = False
            PRINT(f"❌ {test.name} 失败")
    
    # 合规性验证（方法18）
    PRINT("\n执行方法18：合规性验证")
    compliance = method18_compliance_check(fix_result.details)
    IF compliance.success:
        score += 5
        PRINT("✅ 符合项目规范 (+5分)")
    ELSE:
        all_tests_passed = False
        PRINT(f"❌ 违反规范: {compliance.violations}")
    
    # 最终判定
    IF NOT all_tests_passed:
        PRINT("\n🚨 执行方法21：失败原因追溯")
        failure_analysis = method21_trace_failure()
        
        IF failure_analysis.type == "INCOMPLETE_FIX":
            score -= 10
            PRINT("📍 修复不完整，返回阶段4")
            RETURN execute_phase4_implementation(root_cause)
        ELIF failure_analysis.type == "WRONG_ROOT_CAUSE":
            score -= 20
            PRINT("📍 根因错误，返回阶段3")
            RETURN execute_phase3_hypothesis_loop()
        ELSE:  # INSUFFICIENT_EVIDENCE
            score -= 30
            PRINT("📍 证据不足，返回阶段1")
            RETURN execute_phase1_evidence_collection()
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段5完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN all_tests_passed
```

## 📋 方法函数定义

```python
# 方法1：powershell.exe执行验证
FUNCTION method1_execution_check():
    # 在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作
    # - 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
    # - 流程：执行程序 → 读取日志 → 验证功能效果
    # - 执行证明：必须显示程序输出或错误信息
    # - 返回：{success: bool, data: 执行结果, score: ±5}

# 方法2：API请求验证
FUNCTION method2_api_verification():
    # 直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果
    # - 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
    # - 流程：调用API → 分析响应 → 验证业务逻辑
    # - 执行证明：必须显示HTTP响应码和响应体
    # - 返回：{success: bool, data: API响应, score: ±5}

# 方法3：服务状态验证
FUNCTION method3_service_check():
    # 检查服务进程的真实运行状态，避免假设服务可用性
    # - 验证策略：端口连通性 → 服务响应 → 进程状态
    # - 命令选项：基础验证、根路径探测、进程验证、智能发现
    # - 执行证明：HTTP状态码、响应内容摘要或进程信息
    # - 返回：{success: bool, data: {port, health, process}, score: ±5}

# 方法4：代码生效验证
FUNCTION method4_code_effect_check():
    # 确认代码修改已实际应用并被执行，避免假设修改生效
    # - 静态验证：文件变更是否存在（需显示diff）
    # - 动态验证：修改的代码路径是否被执行到（需显示日志）
    # - 执行证明：必须有git diff或文件对比输出
    # - 返回：{success: bool, data: 执行跟踪, score: ±5}

# 方法5：功能重复性检查
FUNCTION method5_check_duplicate_functions():
    # 开发前搜索现有代码库，避免重复实现已存在的功能模块
    # - 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数
    # - 执行证明：必须显示搜索结果，即使为空
    # - 返回：{success: bool, data: 重复函数列表, score: 0~5}

# 方法6：日志优先症状收集
FUNCTION method6_collect_logs():
    # 优先查看错误日志和异常堆栈，获取最直接证据
    # - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述
    # - 执行证明：必须列出查看的日志文件和关键内容
    # - 返回：{success: bool, data: {errors, warnings, recent}, score: ±5}

# 方法7：用户症状分层确认
FUNCTION method7_layer_symptoms(logs, env_status):
    # 将用户描述分为表象→系统→根因三层，逐层收集症状
    # - 表象层：用户直接看到的问题
    # - 系统层：服务和环境层面异常
    # - 根因层：代码和技术层面原因
    # - 执行证明：必须记录每层的具体症状
    # - 返回：{success: bool, data: 分层症状, score: +5}

# 方法8：系统环境状态检查
FUNCTION method8_check_environment():
    # 并行检查服务状态、配置文件、依赖环境的实际状态
    # - 检查维度：服务可用性 | 配置完整性 | 依赖满足性
    # - 并行策略：多维度同时检查，快速定位环境问题
    # - 执行证明：必须显示ps/netstat/config内容
    # - 返回：{success: bool, data: 环境状态, score: +5}

# 方法9：执行路径反向确认
FUNCTION method9_path_verification():
    # 通过输出特征和日志模式反向定位真实执行的代码路径
    # - 反向策略：从输出结果追溯到具体代码位置
    # - 执行证明：必须显示日志→代码的对应关系
    # - 返回：{success: bool, data: 代码路径, score: ±5}

# 方法10：数据流完整追踪
FUNCTION method10_data_trace():
    # 追踪数据从输入到输出的完整生命周期，识别异常节点
    # - 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
    # - 异常识别：数据丢失、格式错误、转换失败、存储异常
    # - 执行证明：必须显示每个节点的数据状态
    # - 返回：{success: bool, data: {追踪链, 异常点}, score: +5}

# 方法11：逐层隔离定位
FUNCTION method11_layer_isolation():
    # 按系统架构层次逐层隔离问题，避免跨层复杂化分析
    # - 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次
    # - 执行证明：必须显示每层的测试结果
    # - 返回：{success: bool, data: 问题层次, score: +5~10}

# 方法12：单一根因优先
FUNCTION method12_single_root_cause(evidence):
    # 优先查找单一明确的根因，避免多因素复杂化假设
    # - 判断原则：一个问题对应一个主要原因
    # - 排除策略：先验证最直接最简单的可能性
    # - 执行证明：必须显示排除过程
    # - 返回：{found: bool, hypothesis: 单一根因, confidence: 置信度}

# 方法13：代码逻辑直接验证
FUNCTION method13_verify_code_logic(hypothesis):
    # 基于实际执行路径验证代码逻辑，不分析未执行代码
    # - 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支
    # - 执行证明：必须显示代码执行路径
    # - 返回：{confirms_hypothesis: bool, confirmations: 验证结果}

# 方法14：修复效果实时验证
FUNCTION method14_verify_original_problem():
    # 修复后立即通过相同方法验证问题是否解决
    # - 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入
    # - 执行证明：必顺重现原始场景并验证
    # - 返回：{success: bool, data: 验证结果}

# 方法15：功能完整性测试
FUNCTION method15_functional_integrity():
    # 验证修复不影响相关功能的正常工作
    # - 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程
    # - 执行证明：必须显示测试用例和结果
    # - 返回：{success: bool, data: 测试结果, name: 方法名}

# 方法16：根本解决确认
FUNCTION method16_permanent_solution():
    # 确认修复解决了原始根本问题，不是表面修复
    # - 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除
    # - 执行证明：必须多次测试并验证
    # - 返回：{success: bool, data: 确认结果, name: 方法名}

# 方法17：规范动态提取应用
FUNCTION method17_extract_relevant_rules():
    # 根据问题和修改代码类型，从CLAUDE.md提取相关规范约束
    # - 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
    # - 应用原则：只应用相关规范，避免无关约束干扰
    # - 执行证明：必须引用具体规范条款
    # - 返回：{success: bool, data: 相关规范, score: +5}

# 方法18：修复合规性验证
FUNCTION method18_compliance_check(fix_details):
    # 确保所有修复完全符合项目技术规范和架构原则
    # - 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则
    # - 执行证明：必须逐项检查并显示结果
    # - 返回：{success: bool, violations: 违规列表}

# 方法19：证据驱动分析
FUNCTION method19_evidence_driven_analysis(evidence):
    # 所有分析必须基于实际收集的证据，禁止无证据推理
    # - 分析前提：先收集证据 → 再分析原因
    # - 禁止行为：基于经验推测 | 基于可能性判断
    # - 执行证明：每个结论必须标注证据来源
    # - 返回：{can_analyze: bool, analysis_base: 分析基础}

# 方法20：假设验证循环
FUNCTION method20_hypothesis_verification(hypothesis):
    # 每个根因假设都必须通过实际验证才能确认
    # - 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定
    # - 执行证明：必须显示验证命令和结果
    # - 返回：{verified: bool, plan: 验证计划, results: 验证结果}

# 方法21：失败原因追溯
FUNCTION method21_trace_failure():
    # 修复失败后必须基于新证据重新分析，不重复原方案
    # - 追溯策略：识别失败点 → 收集新证据 → 调整分析方向
    # - 失败分类：技术失败|逻辑失败|根本失败|证据不足
    # - 返回：{type: 失败类型, reason: 失败原因}

# 方法22：证据有效性验证
FUNCTION method22_validate_evidence(evidence):
    # 验证收集的证据是否满足分析要求，确保证据质量合格
    # - 验证维度：完整性（覆盖范围） | 相关性（直接关联） | 具体性（明确清晰）
    # - 失败处理：指出缺失内容 → 返回补充收集 → 禁止不完整分析
    # - 执行证明：必须列出所有证据并评估质量
    # - 返回：{complete: bool, relevant: bool, specific: bool, missing: 缺失项}
```

## 🎮 执行控制器

```python
# 并行执行器
FUNCTION PARALLEL_EXECUTE(function_list):
    threads = []
    results = []
    
    FOR func IN function_list:
        thread = CREATE_THREAD(func)
        threads.append(thread)
        thread.start()
    
    FOR thread IN threads:
        thread.join()
        results.append(thread.result)
    
    RETURN results

# 命令执行器
FUNCTION EXECUTE_COMMAND(command):
    PRINT(f"$ {command}")
    result = system_execute(command)
    PRINT(result.output[:500])  # 只显示前500字符
    RETURN result

# 积分监控器
FUNCTION check_score():
    PRINT(f"\n💯 当前积分: {score}")
    
    IF score < FAILURE_THRESHOLD:
        PRINT("❌ 积分过低，强制重启流程")
        RETURN restart_process()
    ELIF score > EXCELLENCE_THRESHOLD:
        PRINT("🌟 表现卓越！")
    ELIF score > SUCCESS_THRESHOLD:
        PRINT("✅ 表现优秀！")

# 异常处理器
FUNCTION handle_exception(exception):
    IF exception.type == "SCORE_TOO_LOW":
        RETURN restart_process()
    ELIF exception.type == "MAX_RETRIES_EXCEEDED":
        RETURN escalate_to_human()
    ELSE:
        log_error(exception)
        RETURN failure(f"未处理异常: {exception}")
```


## 📊 伪代码优势总结

1. **执行路径清晰** - 每个分支都有明确的条件判断
2. **状态管理精确** - 全局变量追踪整个流程状态
3. **积分自动计算** - 无需人工判断加减分
4. **并行执行明确** - PARALLEL_EXECUTE函数标准化
5. **异常处理规范** - try/catch结构处理各种失败
6. **回退机制可靠** - 函数调用实现精确跳转
7. **日志输出统一** - PRINT函数格式化所有输出

---
> 版本：11 | 创新：伪代码格式，AI执行更精确