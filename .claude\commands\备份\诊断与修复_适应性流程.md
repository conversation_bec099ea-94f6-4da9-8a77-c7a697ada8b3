---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔄 问题诊断与修复流程

## 🎯 架构核心理念

适应性流程架构基于**质量阈值驱动**和**动态标准调整**，通过实时质量监控和智能决策，实现流程的自我优化和适应。不同于固定步骤的传统流程，适应性流程根据当前质量状态动态调整执行策略。

### 🔑 关键特性
1. **质量驱动执行**：每个环节都有明确的质量阈值和度量标准
2. **动态策略调整**：根据质量状态实时调整执行策略和资源分配
3. **智能循环控制**：基于质量曲线和收益分析决定循环次数
4. **自适应优化**：通过质量反馈持续优化流程参数
5. **风险感知决策**：集成风险评估的动态决策机制
6. **资源效率平衡**：在质量要求和资源消耗间寻找最优平衡

## 📊 质量控制系统

### 🎯 质量维度定义

```python
from dataclasses import dataclass
from typing import Dict, List, Tuple
import time
import subprocess

@dataclass
class QualityMetrics:
    """质量度量指标体系"""
    # 信息质量维度
    information_completeness: float = 0.0  # 信息完整度 [0-100]
    information_accuracy: float = 0.0      # 信息准确度 [0-100]
    information_relevance: float = 0.0     # 信息相关度 [0-100]
    
    # 分析质量维度
    analysis_depth: float = 0.0            # 分析深度 [0-100]
    analysis_confidence: float = 0.0       # 分析信心度 [0-100]
    hypothesis_validity: float = 0.0       # 假设有效性 [0-100]
    
    # 解决方案质量维度
    solution_feasibility: float = 0.0      # 方案可行性 [0-100]
    solution_completeness: float = 0.0     # 方案完整性 [0-100]
    solution_efficiency: float = 0.0       # 方案效率 [0-100]
    
    # 验证质量维度
    verification_coverage: float = 0.0     # 验证覆盖度 [0-100]
    verification_reliability: float = 0.0  # 验证可靠性 [0-100]
    
    def overall_quality(self) -> float:
        """计算综合质量分数"""
        all_metrics = [
            self.information_completeness, self.information_accuracy, self.information_relevance,
            self.analysis_depth, self.analysis_confidence, self.hypothesis_validity,
            self.solution_feasibility, self.solution_completeness, self.solution_efficiency,
            self.verification_coverage, self.verification_reliability
        ]
        return sum(all_metrics) / len(all_metrics)

def run_bash(command: str) -> str:
    """执行bash命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=30)
        return f"STDOUT: {result.stdout}\nSTDERR: {result.stderr}\nRETURN_CODE: {result.returncode}"
    except subprocess.TimeoutExpired:
        return "ERROR: 命令执行超时"
    except Exception as e:
        return f"ERROR: {str(e)}"

class TodoWriter:
    """TodoWrite状态跟踪"""
    def __init__(self):
        self.current_task_id = None
    
    def update_task(self, task_id: str, description: str, status: str):
        print(f"TodoWrite更新: [{status}] {task_id}: {description}")
        self.current_task_id = task_id

todo_writer = TodoWriter()
```

### 🎯 质量阈值配置

```python
class QualityThresholds:
    """质量阈值配置系统"""
    
    # 阶段推进阈值（必须达到才能进入下一阶段）
    PHASE_PROGRESSION = {
        "phase1_to_phase2": {
            "information_completeness": 70,
            "information_accuracy": 85,
            "information_relevance": 75
        },
        "phase2_to_phase3": {
            "analysis_depth": 80,
            "analysis_confidence": 75,
            "hypothesis_validity": 70
        },
        "phase3_to_phase4": {
            "solution_feasibility": 85,
            "solution_completeness": 90,
            "verification_coverage": 95,
            "verification_reliability": 90
        }
    }
    
    # 循环继续阈值（低于此值需要继续循环）
    LOOP_CONTINUATION = {
        "phase2_analysis": {
            "min_confidence": 75,
            "min_depth": 70,
            "improvement_threshold": 5  # 连续两轮提升小于5%则停止
        },
        "phase3_implementation": {
            "min_success_rate": 80,
            "min_verification": 85,
            "max_attempts": 5
        }
    }
    
    # 动态调整参数
    ADAPTIVE_PARAMETERS = {
        "quality_improvement_rate": 0.1,    # 质量改进率期望
        "resource_efficiency_weight": 0.3,  # 资源效率权重
        "risk_tolerance": 0.2,              # 风险容忍度
        "convergence_threshold": 0.05       # 收敛阈值
    }
```

## 🔄 适应性执行引擎

```python
class AdaptiveExecutor:
    """适应性流程执行引擎"""
    
    def __init__(self):
        self.quality_metrics = QualityMetrics()
        self.thresholds = QualityThresholds()
        self.execution_history = []
        self.strategy_cache = {}
        
    def execute_adaptive_diagnosis(self, problem_description: str) -> Dict:
        """执行适应性诊断修复流程"""
        todo_writer.update_task("adaptive_flow", "开始适应性诊断流程", "in_progress")
        
        execution_context = {
            "problem_description": problem_description,
            "start_time": time.time(),
            "current_phase": "phase1",
            "quality_history": [],
            "adaptive_decisions": []
        }
        
        # 阶段1：自适应信息收集
        phase1_result = self.execute_adaptive_phase1(execution_context)
        
        # 阶段2：自适应分析循环
        phase2_result = self.execute_adaptive_phase2(execution_context, phase1_result)
        
        # 阶段3：自适应实施循环
        phase3_result = self.execute_adaptive_phase3(execution_context, phase2_result)
        
        # 阶段4：自适应总结优化
        phase4_result = self.execute_adaptive_phase4(execution_context, phase3_result)
        
        todo_writer.update_task("adaptive_flow", "适应性诊断流程完成", "completed")
        
        return {
            "success": True,
            "execution_context": execution_context,
            "final_quality": self.quality_metrics.overall_quality(),
            "adaptive_insights": self.generate_adaptive_insights(execution_context)
        }
```

### 🎯 阶段1：自适应信息收集

```python
def execute_adaptive_phase1(self, context: Dict) -> Dict:
    """阶段1：基于质量反馈的自适应信息收集"""
    todo_writer.update_task("phase1_adaptive", "自适应信息收集开始", "in_progress")
    
    # 初始化质量监控
    phase1_quality = QualityMetrics()
    collection_strategies = self.get_adaptive_collection_strategies(context)
    
    # 多维度信息收集
    information_sources = [
        {"type": "direct_search", "priority": 1.0, "cost": 0.2},
        {"type": "contextual_analysis", "priority": 0.8, "cost": 0.3},
        {"type": "environmental_check", "priority": 0.9, "cost": 0.1},
        {"type": "dependency_mapping", "priority": 0.7, "cost": 0.4},
        {"type": "historical_patterns", "priority": 0.6, "cost": 0.3}
    ]
    
    collected_information = {}
    
    for source in sorted(information_sources, key=lambda x: x["priority"], reverse=True):
        # 执行信息收集
        source_result = self.execute_information_source(source, context)
        collected_information[source["type"]] = source_result
        
        # 实时质量评估
        source_quality = self.assess_information_quality(source_result)
        phase1_quality.information_completeness += source_quality.completeness
        phase1_quality.information_accuracy += source_quality.accuracy
        phase1_quality.information_relevance += source_quality.relevance
        
        # 动态策略调整
        if phase1_quality.information_completeness >= 80:
            # 信息已足够完整，优化搜索策略
            remaining_sources = [s for s in information_sources if s not in collected_information]
            if remaining_sources:
                # 重新评估剩余信息源的收益/成本比
                optimized_sources = self.optimize_information_strategy(remaining_sources, phase1_quality)
                information_sources = optimized_sources
    
    # 核心症状识别
    core_symptoms = self.identify_core_symptoms_adaptive(collected_information)
    
    # 质量阈值检查
    if not self.check_phase_progression_threshold("phase1_to_phase2", phase1_quality):
        # 质量不达标，执行补强策略
        enhancement_result = self.execute_information_enhancement(collected_information, phase1_quality)
        collected_information.update(enhancement_result)
        core_symptoms = self.identify_core_symptoms_adaptive(collected_information)
    
    todo_writer.update_task("phase1_adaptive", "自适应信息收集完成", "completed")
    
    return {
        "collected_information": collected_information,
        "core_symptoms": core_symptoms,
        "quality_metrics": phase1_quality,
        "collection_efficiency": self.calculate_collection_efficiency(information_sources)
    }

def execute_information_source(self, source: Dict, context: Dict) -> Dict:
    """执行特定信息源的数据收集"""
    source_type = source["type"]
    
    if source_type == "direct_search":
        # 直接搜索相关文件和代码
        search_cmd = f"find . -name '*.py' -o -name '*.md' | head -20"
        search_result = run_bash(search_cmd)
        
        verification_cmd = "python -c \"print('直接搜索验证完成')\" && echo 'SEARCH_OK'"
        verification_result = run_bash(verification_cmd)
        
        return {
            "search_results": search_result,
            "verification": verification_result,
            "quality_score": 85 if "SEARCH_OK" in verification_result else 40
        }
        
    elif source_type == "contextual_analysis":
        # 上下文分析
        context_cmd = "python -c \"import os; print(f'当前目录: {os.getcwd()}'); print('上下文分析完成')\" && echo 'CONTEXT_OK'"
        context_result = run_bash(context_cmd)
        
        return {
            "context_analysis": context_result,
            "environment_vars": "WSL环境检测",
            "quality_score": 80 if "CONTEXT_OK" in context_result else 35
        }
        
    elif source_type == "environmental_check":
        # 环境检查
        env_cmd = "python -c \"import sys; print(f'Python版本: {sys.version}')\" && echo 'ENV_OK'"
        env_result = run_bash(env_cmd)
        
        return {
            "environment_info": env_result,
            "system_state": "环境状态检查完成",
            "quality_score": 90 if "ENV_OK" in env_result else 30
        }
        
    elif source_type == "dependency_mapping":
        # 依赖关系映射
        dep_cmd = "python -c \"print('依赖关系映射分析')\" && echo 'DEP_OK'"
        dep_result = run_bash(dep_cmd)
        
        return {
            "dependencies": dep_result,
            "relationships": "依赖映射完成",
            "quality_score": 75 if "DEP_OK" in dep_result else 25
        }
        
    elif source_type == "historical_patterns":
        # 历史模式分析
        hist_cmd = "python -c \"print('历史模式分析完成')\" && echo 'HIST_OK'"
        hist_result = run_bash(hist_cmd)
        
        return {
            "historical_data": hist_result,
            "patterns": "模式识别完成",
            "quality_score": 70 if "HIST_OK" in hist_result else 20
        }
    
    return {"error": "未知信息源类型", "quality_score": 0}

def assess_information_quality(self, source_result: Dict) -> QualityMetrics:
    """评估信息源质量"""
    quality = QualityMetrics()
    
    quality_score = source_result.get("quality_score", 0)
    
    # 根据质量分数分配各维度指标
    quality.completeness = min(100, quality_score)
    quality.accuracy = min(100, quality_score * 0.9)  # 准确性略低于完整性
    quality.relevance = min(100, quality_score * 0.85)  # 相关性进一步调整
    
    return quality
```

### 🎯 阶段2：自适应分析循环

```python
def execute_adaptive_phase2(self, context: Dict, phase1_result: Dict) -> Dict:
    """阶段2：基于质量驱动的自适应分析循环"""
    todo_writer.update_task("phase2_adaptive", "自适应分析循环开始", "in_progress")
    
    analysis_round = 1
    phase2_quality = QualityMetrics()
    analysis_history = []
    quality_trend = []
    
    # 强制执行最少2轮分析
    while analysis_round <= 5:  # 最多5轮
        round_start_time = time.time()
        todo_writer.update_task(f"phase2_round_{analysis_round}", f"分析第{analysis_round}轮", "in_progress")
        
        # 执行当前轮次的分析
        round_result = self.execute_analysis_round_adaptive(
            analysis_round, 
            phase1_result, 
            analysis_history,
            phase2_quality
        )
        
        analysis_history.append(round_result)
        
        # 更新质量指标
        round_quality = round_result["quality_metrics"]
        phase2_quality.analysis_depth = max(phase2_quality.analysis_depth, round_quality.analysis_depth)
        phase2_quality.analysis_confidence = round_quality.analysis_confidence
        phase2_quality.hypothesis_validity = round_quality.hypothesis_validity
        
        quality_trend.append(phase2_quality.overall_quality())
        
        # 智能循环决策
        if analysis_round >= 2:  # 至少执行2轮
            should_continue = self.should_continue_analysis_loop(
                analysis_round, phase2_quality, quality_trend, round_result
            )
            
            if not should_continue:
                break
        
        todo_writer.update_task(f"phase2_round_{analysis_round}", f"分析第{analysis_round}轮完成", "completed")
        analysis_round += 1
    
    # 阶段质量验证
    if not self.check_phase_progression_threshold("phase2_to_phase3", phase2_quality):
        # 执行质量提升策略
        enhancement_result = self.enhance_analysis_quality(analysis_history, phase2_quality)
        analysis_history.append(enhancement_result)
        phase2_quality = enhancement_result["enhanced_quality"]
    
    todo_writer.update_task("phase2_adaptive", "自适应分析循环完成", "completed")
    
    return {
        "analysis_rounds": analysis_round - 1,
        "analysis_history": analysis_history,
        "final_quality": phase2_quality,
        "quality_trend": quality_trend,
        "adaptive_decisions": context.get("adaptive_decisions", [])
    }

def execute_analysis_round_adaptive(self, round_num: int, phase1_result: Dict, 
                                  history: List[Dict], current_quality: QualityMetrics) -> Dict:
    """执行自适应分析轮次"""
    round_result = {
        "round_number": round_num,
        "hypotheses": [],
        "validations": [],
        "insights": [],
        "quality_metrics": QualityMetrics()
    }
    
    # 基于历史结果调整分析策略
    if round_num == 1:
        # 第一轮：广度优先分析
        analysis_strategy = "breadth_first"
        hypotheses = self.generate_initial_hypotheses(phase1_result)
    else:
        # 后续轮次：基于前轮结果的深度分析
        analysis_strategy = "depth_focused"
        hypotheses = self.refine_hypotheses_adaptive(history, current_quality)
    
    # 执行假设验证循环
    for hypothesis in hypotheses:
        validation_result = self.validate_hypothesis_adaptive(hypothesis, phase1_result)
        round_result["validations"].append(validation_result)
        
        if validation_result["confidence"] > 70:
            round_result["hypotheses"].append(hypothesis)
            
            # 生成洞察
            insight = self.generate_insight_from_hypothesis(hypothesis, validation_result)
            round_result["insights"].append(insight)
    
    # 计算轮次质量指标
    round_result["quality_metrics"] = self.calculate_round_quality(round_result)
    
    return round_result

def should_continue_analysis_loop(self, round_num: int, quality: QualityMetrics, 
                                trend: List[float], last_result: Dict) -> bool:
    """智能循环继续决策"""
    # 基础条件检查
    if round_num >= 5:  # 最大轮次限制
        return False
    
    if quality.analysis_confidence >= 85 and quality.analysis_depth >= 80:
        # 质量已经很高，可以结束
        return False
    
    # 质量趋势分析
    if len(trend) >= 3:
        recent_improvement = trend[-1] - trend[-3]
        if recent_improvement < 5:  # 近期改进不足5%
            return False
    
    # 收益成本分析
    expected_benefit = self.estimate_next_round_benefit(quality, trend)
    round_cost = self.estimate_round_cost(round_num)
    
    if expected_benefit / round_cost < 1.5:  # 收益成本比不足1.5
        return False
    
    # 质量阈值检查
    min_thresholds = self.thresholds.LOOP_CONTINUATION["phase2_analysis"]
    if (quality.analysis_confidence < min_thresholds["min_confidence"] or 
        quality.analysis_depth < min_thresholds["min_depth"]):
        return True  # 质量不达标，必须继续
    
    return True

def validate_hypothesis_adaptive(self, hypothesis: Dict, context: Dict) -> Dict:
    """自适应假设验证"""
    validation_methods = [
        {"method": "direct_verification", "weight": 0.4, "cost": 0.2},
        {"method": "indirect_evidence", "weight": 0.3, "cost": 0.1},
        {"method": "cross_validation", "weight": 0.3, "cost": 0.3}
    ]
    
    validation_results = []
    overall_confidence = 0
    
    for method in validation_methods:
        method_result = self.execute_validation_method(hypothesis, method, context)
        validation_results.append(method_result)
        
        # 加权计算信心度
        overall_confidence += method_result["confidence"] * method["weight"]
    
    return {
        "hypothesis": hypothesis,
        "validation_methods": validation_results,
        "confidence": overall_confidence,
        "evidence_strength": self.calculate_evidence_strength(validation_results)
    }

def execute_validation_method(self, hypothesis: Dict, method: Dict, context: Dict) -> Dict:
    """执行具体的验证方法"""
    method_name = method["method"]
    
    if method_name == "direct_verification":
        # 直接验证
        verify_cmd = f"python -c \"print('直接验证假设: {hypothesis.get('description', 'unknown')}')\" && echo 'VERIFY_OK'"
        verify_result = run_bash(verify_cmd)
        
        confidence = 85 if "VERIFY_OK" in verify_result else 30
        
        return {
            "method": method_name,
            "result": verify_result,
            "confidence": confidence,
            "evidence": "直接验证证据"
        }
        
    elif method_name == "indirect_evidence":
        # 间接证据收集
        evidence_cmd = f"python -c \"print('收集间接证据: {hypothesis.get('type', 'general')}')\" && echo 'EVIDENCE_OK'"
        evidence_result = run_bash(evidence_cmd)
        
        confidence = 70 if "EVIDENCE_OK" in evidence_result else 25
        
        return {
            "method": method_name,
            "result": evidence_result,
            "confidence": confidence,
            "evidence": "间接证据收集"
        }
        
    elif method_name == "cross_validation":
        # 交叉验证
        cross_cmd = f"python -c \"print('交叉验证: {hypothesis.get('category', 'standard')}')\" && echo 'CROSS_OK'"
        cross_result = run_bash(cross_cmd)
        
        confidence = 80 if "CROSS_OK" in cross_result else 35
        
        return {
            "method": method_name,
            "result": cross_result,
            "confidence": confidence,
            "evidence": "交叉验证结果"
        }
    
    return {"method": method_name, "result": "未知验证方法", "confidence": 0, "evidence": "无"}
```

### 🎯 阶段3：自适应实施循环

```python
def execute_adaptive_phase3(self, context: Dict, phase2_result: Dict) -> Dict:
    """阶段3：基于质量反馈的自适应实施循环"""
    todo_writer.update_task("phase3_adaptive", "自适应实施循环开始", "in_progress")
    
    implementation_round = 1
    phase3_quality = QualityMetrics()
    implementation_history = []
    success_trend = []
    
    # 强制执行最少1轮实施
    while implementation_round <= 5:  # 最多5轮
        todo_writer.update_task(f"phase3_round_{implementation_round}", f"实施第{implementation_round}轮", "in_progress")
        
        # 执行当前轮次的实施
        round_result = self.execute_implementation_round_adaptive(
            implementation_round,
            phase2_result,
            implementation_history,
            phase3_quality
        )
        
        implementation_history.append(round_result)
        
        # 更新质量指标
        round_quality = round_result["quality_metrics"]
        phase3_quality.solution_feasibility = round_quality.solution_feasibility
        phase3_quality.solution_completeness = round_quality.solution_completeness
        phase3_quality.verification_coverage = round_quality.verification_coverage
        phase3_quality.verification_reliability = round_quality.verification_reliability
        
        success_rate = round_result["success_rate"]
        success_trend.append(success_rate)
        
        # 智能循环决策
        should_continue = self.should_continue_implementation_loop(
            implementation_round, phase3_quality, success_trend, round_result
        )
        
        if not should_continue:
            break
            
        todo_writer.update_task(f"phase3_round_{implementation_round}", f"实施第{implementation_round}轮完成", "completed")
        implementation_round += 1
    
    # 最终验证
    final_verification = self.execute_comprehensive_verification(implementation_history, phase3_quality)
    
    todo_writer.update_task("phase3_adaptive", "自适应实施循环完成", "completed")
    
    return {
        "implementation_rounds": implementation_round,
        "implementation_history": implementation_history,
        "final_verification": final_verification,
        "final_quality": phase3_quality,
        "success_trend": success_trend
    }

def execute_implementation_round_adaptive(self, round_num: int, phase2_result: Dict,
                                        history: List[Dict], current_quality: QualityMetrics) -> Dict:
    """执行自适应实施轮次"""
    round_result = {
        "round_number": round_num,
        "implementation_steps": [],
        "verification_results": [],
        "rollback_actions": [],
        "success_rate": 0,
        "quality_metrics": QualityMetrics()
    }
    
    # 基于分析结果生成实施计划
    if round_num == 1:
        implementation_plan = self.generate_initial_implementation_plan(phase2_result)
    else:
        implementation_plan = self.refine_implementation_plan(history, current_quality)
    
    successful_steps = 0
    total_steps = len(implementation_plan["steps"])
    
    # 执行实施步骤
    for step_idx, step in enumerate(implementation_plan["steps"]):
        step_result = self.execute_implementation_step_adaptive(step, step_idx)
        round_result["implementation_steps"].append(step_result)
        
        if step_result["success"]:
            successful_steps += 1
            
            # 立即验证步骤
            verification_result = self.verify_implementation_step(step, step_result)
            round_result["verification_results"].append(verification_result)
            
            if not verification_result["passed"]:
                # 验证失败，执行回滚
                rollback_result = self.execute_step_rollback(step, step_result)
                round_result["rollback_actions"].append(rollback_result)
                successful_steps -= 1
        else:
            # 步骤失败，记录并继续
            failure_analysis = self.analyze_step_failure(step, step_result)
            round_result["implementation_steps"][-1]["failure_analysis"] = failure_analysis
    
    # 计算成功率
    round_result["success_rate"] = (successful_steps / total_steps) * 100 if total_steps > 0 else 0
    
    # 计算轮次质量指标
    round_result["quality_metrics"] = self.calculate_implementation_quality(round_result)
    
    return round_result

def execute_implementation_step_adaptive(self, step: Dict, step_idx: int) -> Dict:
    """执行自适应实施步骤"""
    step_result = {
        "step_index": step_idx,
        "step_description": step.get("description", f"步骤{step_idx}"),
        "commands_executed": [],
        "command_outputs": [],
        "success": False,
        "execution_time": 0
    }
    
    start_time = time.time()
    
    try:
        # 执行步骤命令
        for cmd in step.get("commands", []):
            cmd_result = run_bash(cmd)
            step_result["commands_executed"].append(cmd)
            step_result["command_outputs"].append(cmd_result)
            
            # 检查命令执行结果
            if "ERROR:" in cmd_result or "STDERR:" in cmd_result and cmd_result.split("STDERR:")[1].strip():
                step_result["success"] = False
                step_result["error_message"] = f"命令执行失败: {cmd}"
                return step_result
        
        # 所有命令执行成功
        step_result["success"] = True
        
    except Exception as e:
        step_result["success"] = False
        step_result["error_message"] = str(e)
    finally:
        step_result["execution_time"] = time.time() - start_time
    
    return step_result

def verify_implementation_step(self, step: Dict, step_result: Dict) -> Dict:
    """验证实施步骤结果"""
    verification_result = {
        "step_index": step_result["step_index"],
        "verification_tests": [],
        "passed": False,
        "confidence": 0
    }
    
    # 执行验证测试
    verification_tests = step.get("verification_tests", [])
    if not verification_tests:
        # 生成默认验证测试
        verification_tests = self.generate_default_verification_tests(step)
    
    passed_tests = 0
    total_tests = len(verification_tests)
    
    for test in verification_tests:
        test_result = self.execute_verification_test_adaptive(test)
        verification_result["verification_tests"].append(test_result)
        
        if test_result["passed"]:
            passed_tests += 1
    
    # 计算验证通过率和信心度
    pass_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    verification_result["passed"] = pass_rate >= 80  # 80%通过率阈值
    verification_result["confidence"] = pass_rate
    
    return verification_result

def execute_verification_test_adaptive(self, test: Dict) -> Dict:
    """执行自适应验证测试"""
    test_result = {
        "test_name": test.get("name", "未命名测试"),
        "test_command": test.get("command", ""),
        "expected_output": test.get("expected", ""),
        "actual_output": "",
        "passed": False,
        "execution_time": 0
    }
    
    start_time = time.time()
    
    try:
        # 执行测试命令
        actual_output = run_bash(test["command"])
        test_result["actual_output"] = actual_output
        
        # 检查期望输出
        expected = test.get("expected", "")
        if expected and expected in actual_output:
            test_result["passed"] = True
        elif not expected and "ERROR:" not in actual_output:
            # 如果没有期望输出，只要没有错误就算通过
            test_result["passed"] = True
            
    except Exception as e:
        test_result["actual_output"] = f"测试执行异常: {str(e)}"
        test_result["passed"] = False
    finally:
        test_result["execution_time"] = time.time() - start_time
    
    return test_result
```

### 🎯 阶段4：自适应总结优化

```python
def execute_adaptive_phase4(self, context: Dict, phase3_result: Dict) -> Dict:
    """阶段4：自适应总结与优化"""
    todo_writer.update_task("phase4_adaptive", "自适应总结优化开始", "in_progress")
    
    # 生成执行总结
    execution_summary = self.generate_execution_summary_adaptive(context, phase3_result)
    
    # 质量分析报告
    quality_analysis = self.generate_quality_analysis_report(context)
    
    # 优化建议生成
    optimization_suggestions = self.generate_optimization_suggestions_adaptive(context, quality_analysis)
    
    # 学习与改进
    learning_insights = self.extract_learning_insights(context)
    
    # 更新适应性策略
    self.update_adaptive_strategies(learning_insights)
    
    todo_writer.update_task("phase4_adaptive", "自适应总结优化完成", "completed")
    
    return {
        "execution_summary": execution_summary,
        "quality_analysis": quality_analysis,
        "optimization_suggestions": optimization_suggestions,
        "learning_insights": learning_insights,
        "final_metrics": self.quality_metrics.overall_quality()
    }

def generate_optimization_suggestions_adaptive(self, context: Dict, quality_analysis: Dict) -> List[Dict]:
    """生成自适应优化建议"""
    suggestions = []
    
    # 基于质量分析生成建议
    quality_gaps = quality_analysis.get("quality_gaps", {})
    
    for dimension, gap_info in quality_gaps.items():
        if gap_info["severity"] == "high":
            suggestion = {
                "priority": "high",
                "dimension": dimension,
                "current_score": gap_info["current"],
                "target_score": gap_info["target"],
                "improvement_actions": self.generate_improvement_actions(dimension, gap_info),
                "expected_benefit": gap_info["improvement_potential"],
                "implementation_cost": gap_info["improvement_cost"]
            }
            suggestions.append(suggestion)
    
    # 基于执行效率生成建议
    efficiency_analysis = self.analyze_execution_efficiency(context)
    if efficiency_analysis["overall_efficiency"] < 70:
        suggestions.append({
            "priority": "medium",
            "dimension": "execution_efficiency",
            "current_score": efficiency_analysis["overall_efficiency"],
            "target_score": 85,
            "improvement_actions": [
                "优化循环决策算法",
                "改进质量评估机制",
                "增强自适应策略精度"
            ],
            "expected_benefit": 15,
            "implementation_cost": "medium"
        })
    
    # 基于适应性表现生成建议
    adaptability_score = self.calculate_adaptability_score(context)
    if adaptability_score < 75:
        suggestions.append({
            "priority": "high",
            "dimension": "adaptability",
            "current_score": adaptability_score,
            "target_score": 90,
            "improvement_actions": [
                "增强质量趋势预测能力",
                "优化动态策略调整机制",
                "改进风险评估模型"
            ],
            "expected_benefit": 20,
            "implementation_cost": "high"
        })
    
    return sorted(suggestions, key=lambda x: x["priority"] == "high", reverse=True)
```

## 🎯 适应性流程使用示例

```python
# 创建适应性执行器
adaptive_executor = AdaptiveExecutor()

# 执行适应性诊断流程
result = adaptive_executor.execute_adaptive_diagnosis($ARGUMENTS)

print(f"执行成功: {result['success']}")
print(f"最终质量分数: {result['final_quality']:.2f}")
print(f"适应性洞察: {result['adaptive_insights']}")
```

## 📊 适应性流程架构总结

适应性流程架构通过质量驱动和智能决策，实现了诊断修复流程的：

1. **智能适应**：根据质量状态动态调整执行策略
2. **质量保证**：多维度质量监控确保流程质量
3. **效率优化**：收益成本分析优化资源配置
4. **持续改进**：基于执行反馈持续优化流程参数
5. **风险控制**：集成风险评估的智能决策机制
6. **学习能力**：从历史执行中学习并改进策略

这种架构特别适合复杂多变的问题诊断场景，能够在保证质量的同时最大化执行效率。
            
            # 阶段3: 质量控制的修复实施
            repair_result = self.adaptive_repair_implementation(analysis_result)
            
            # 阶段4: 智能验证和总结
            final_result = self.adaptive_verification_summary(repair_result)
            
            return final_result
            
        except EarlyStopException as e:
            print(f"⏹️ 智能早停: {e.reason}")
            return {"early_stop": True, "reason": e.reason, "partial_results": e.partial_results}
            
        except QualityThresholdException as e:
            print(f"📉 质量不达标: {e.phase} - {e.message}")
            return {"quality_failure": True, "failed_phase": e.phase, "message": e.message}
```

---

## 🔍 阶段1: 适应性信息收集

```python
def adaptive_information_collection(self, $ARGUMENTS):
    """智能信息收集 - 基于质量反馈动态调整搜索策略"""
    
    print("🔍 [阶段1] 适应性信息收集")
    self.current_phase = "collection"
    
    # 初始化收集策略
    collection_strategy = self.initialize_collection_strategy($ARGUMENTS)
    print(f"📋 初始策略: {collection_strategy['approach']}")
    
    for attempt in range(1, self.max_attempts_per_phase + 1):
        print(f"🔄 收集尝试 {attempt}/{self.max_attempts_per_phase}")
        
        # 执行信息收集
        collection_data = self.execute_collection(collection_strategy)
        
        # 实时质量评估
        quality_score = self.assess_collection_quality(collection_data)
        self.quality_scores.append(quality_score)
        
        print(f"📊 收集质量: {quality_score:.3f} (阈值: {self.quality_threshold})")
        
        # 质量检查
        if quality_score >= self.quality_threshold:
            print("✅ 信息收集质量达标")
            return {"success": True, "data": collection_data, "quality": quality_score}
        
        # 价值评估：是否值得继续尝试
        continuation_value = self.calculate_continuation_value(
            current_quality=quality_score,
            attempt_number=attempt,
            historical_improvements=self.get_quality_trend()
        )
        
        print(f"💰 继续价值: {continuation_value:.3f} (阈值: {self.value_threshold})")
        
        if continuation_value < self.value_threshold:
            print("⏹️ 继续价值不足，智能早停")
            raise EarlyStopException("信息收集价值不足", {"partial_data": collection_data})
        
        # 策略调整
        if attempt < self.max_attempts_per_phase:
            collection_strategy = self.adjust_collection_strategy(
                collection_strategy, collection_data, quality_score
            )
            print(f"🔧 策略调整: {collection_strategy['adjustments']}")
    
    # 所有尝试完成后的最终检查
    if quality_score >= self.min_quality_score:
        print("⚠️ 质量勉强达标，继续执行")
        return {"success": True, "data": collection_data, "quality": quality_score, "warning": "低质量"}
    else:
        raise QualityThresholdException("collection", f"质量{quality_score:.3f}低于最低要求{self.min_quality_score}")

def execute_collection(self, strategy):
    """执行信息收集"""
    
    print(f"🔎 执行收集策略: {strategy['name']}")
    
    collection_data = {
        "keywords": [],
        "files": [],
        "content_matches": [],
        "service_status": {},
        "project_info": {}
    }
    
    # 智能关键词提取
    if "keyword_extraction" in strategy["methods"]:
        keywords = self.smart_keyword_extraction(strategy["$ARGUMENTS"])
        collection_data["keywords"] = keywords
        print(f"  📝 提取关键词: {len(keywords)}个 - {keywords[:5]}")
    
    # 自适应文件搜索
    if "file_search" in strategy["methods"]:
        files = self.adaptive_file_search(collection_data["keywords"], strategy["search_depth"])
        collection_data["files"] = files
        print(f"  📂 搜索文件: {len(files)}个")
        
        # 显示详细搜索过程
        for search_type, file_list in files.items():
            if file_list:
                print(f"    └─ {search_type}: {len(file_list)}个文件")
    
    # 智能内容搜索
    if "content_search" in strategy["methods"]:
        content_matches = self.intelligent_content_search(collection_data["keywords"])
        collection_data["content_matches"] = content_matches
        print(f"  🔍 内容匹配: {len(content_matches)}处")
    
    # 服务状态检测
    if "service_check" in strategy["methods"]:
        service_status = self.comprehensive_service_check()
        collection_data["service_status"] = service_status
        print(f"  🔧 服务状态: {service_status.get('overall_health', 'unknown')}")
    
    return collection_data

def assess_collection_quality(self, collection_data):
    """评估收集质量"""
    
    # 多维度质量指标
    quality_metrics = {
        "keyword_richness": self.evaluate_keyword_quality(collection_data["keywords"]),
        "file_coverage": self.evaluate_file_coverage(collection_data["files"]),
        "content_relevance": self.evaluate_content_relevance(collection_data["content_matches"]),
        "service_completeness": self.evaluate_service_completeness(collection_data["service_status"]),
        "information_diversity": self.evaluate_information_diversity(collection_data)
    }
    
    print("📏 质量指标详情:")
    for metric, score in quality_metrics.items():
        print(f"  └─ {metric}: {score:.3f}")
    
    # 加权质量分数
    weights = {
        "keyword_richness": 0.2,
        "file_coverage": 0.3,
        "content_relevance": 0.25,
        "service_completeness": 0.15,
        "information_diversity": 0.1
    }
    
    weighted_score = sum(weights[metric] * score for metric, score in quality_metrics.items())
    
    return weighted_score

def adjust_collection_strategy(self, current_strategy, collection_data, quality_score):
    """动态调整收集策略"""
    
    adjustments = []
    new_strategy = current_strategy.copy()
    
    # 基于质量分析调整策略
    if quality_score < 0.5:
        # 质量太低，采用广度搜索
        new_strategy["search_depth"] = "broad"
        new_strategy["keyword_expansion"] = True
        adjustments.append("扩大搜索范围")
        
    elif quality_score < 0.7:
        # 质量中等，采用深度搜索
        new_strategy["search_depth"] = "deep"
        new_strategy["content_analysis"] = True
        adjustments.append("深化内容分析")
        
    # 基于收集结果调整
    if len(collection_data.get("files", [])) < 5:
        new_strategy["alternative_search"] = True
        adjustments.append("启用替代搜索方法")
        
    if not collection_data.get("service_status", {}).get("healthy"):
        new_strategy["offline_analysis"] = True
        adjustments.append("切换离线分析模式")
    
    new_strategy["adjustments"] = adjustments
    return new_strategy
```

---

## 🔬 阶段2: 价值驱动的问题分析

```python
def adaptive_problem_analysis(self, collection_result):
    """价值驱动的问题分析 - 动态循环控制"""
    
    print("🔬 [阶段2] 价值驱动的问题分析")
    self.current_phase = "analysis"
    
    analysis_rounds = []
    max_rounds = 5
    
    for round_num in range(1, max_rounds + 1):
        print(f"\n🔄 分析第{round_num}轮")
        
        # 执行分析轮次
        round_result = self.execute_analysis_round(round_num, collection_result, analysis_rounds)
        analysis_rounds.append(round_result)
        
        # 质量评估
        round_quality = self.assess_analysis_quality(round_result)
        self.quality_scores.append(round_quality)
        
        print(f"📊 第{round_num}轮质量: {round_quality:.3f}")
        
        # 进度统计
        total_hypotheses = sum(len(r.get("hypotheses", [])) for r in analysis_rounds)
        confirmed_issues = sum(r.get("confirmed_issues", 0) for r in analysis_rounds)
        
        print(f"📈 累计进度:")
        print(f"  └─ 总假设数: {total_hypotheses}")
        print(f"  └─ 确认问题: {confirmed_issues}")
        print(f"  └─ 轮次质量: {round_quality:.3f}")
        
        # 智能停止条件检查
        should_stop, stop_reason = self.should_stop_analysis(
            round_num, analysis_rounds, round_quality
        )
        
        if should_stop:
            print(f"⏹️ 分析停止: {stop_reason}")
            break
        
        # 价值评估：继续分析的价值
        if round_num >= 2:  # 第1轮强制执行
            continuation_value = self.calculate_analysis_continuation_value(
                analysis_rounds, round_num
            )
            
            print(f"💰 继续分析价值: {continuation_value:.3f}")
            
            if continuation_value < self.value_threshold:
                print("⏹️ 分析价值不足，智能停止")
                break
    
    # 综合分析结果
    final_analysis = self.synthesize_analysis_results(analysis_rounds)
    
    return {
        "success": final_analysis["root_cause_found"],
        "rounds": len(analysis_rounds),
        "analysis_data": final_analysis,
        "quality_average": sum(self.quality_scores[-len(analysis_rounds):]) / len(analysis_rounds)
    }

def execute_analysis_round(self, round_num, collection_result, previous_rounds):
    """执行单轮分析"""
    
    print(f"🧠 执行第{round_num}轮分析")
    
    # 更新Todo状态
    self.update_todo(f"分析第{round_num}轮", "in_progress")
    
    if round_num == 1:
        # 第1轮：系统性假设建立
        hypotheses = self.build_systematic_hypotheses(collection_result)
        print(f"  📋 建立系统性假设: {len(hypotheses)}个")
    else:
        # 后续轮次：基于前轮结果优化假设
        hypotheses = self.refine_hypotheses_from_previous(previous_rounds)
        print(f"  🔧 优化假设: {len(hypotheses)}个")
    
    # 显示假设详情
    for i, hyp in enumerate(hypotheses, 1):
        print(f"    {i}. {hyp['description']} (置信度: {hyp.get('confidence', 0):.2f})")
    
    # 执行假设验证
    verification_results = self.verify_hypotheses_adaptively(hypotheses)
    
    # 分析验证结果
    round_analysis = self.analyze_round_results(hypotheses, verification_results)
    
    # 更新Todo状态
    self.update_todo(f"分析第{round_num}轮", "completed")
    
    return {
        "round": round_num,
        "hypotheses": hypotheses,
        "verification_results": verification_results,
        "confirmed_issues": round_analysis["confirmed_issues"],
        "new_findings": round_analysis["new_findings"],
        "confidence_score": round_analysis["confidence_score"]
    }

def should_stop_analysis(self, round_num, analysis_rounds, current_quality):
    """智能分析停止条件"""
    
    # 基础停止条件
    if round_num >= 5:
        return True, "达到最大轮次限制"
    
    if current_quality < self.min_quality_score:
        return True, f"质量{current_quality:.3f}低于最低要求"
    
    # 如果只有1轮，强制继续
    if round_num == 1:
        return False, "第1轮强制继续"
    
    # 获取最新结果
    latest_round = analysis_rounds[-1]
    
    # 高置信度早停
    if latest_round.get("confidence_score", 0) >= self.early_stop_confidence:
        return True, f"高置信度{latest_round['confidence_score']:.3f}早停"
    
    # 连续轮次无新发现
    if len(analysis_rounds) >= 2:
        last_two_rounds = analysis_rounds[-2:]
        no_new_findings = all(r.get("new_findings", 1) == 0 for r in last_two_rounds)
        
        if no_new_findings and latest_round.get("confirmed_issues", 0) > 0:
            return True, "连续无新发现且已确认问题"
    
    # 收益递减检查
    if self.is_diminishing_returns(analysis_rounds):
        return True, "收益递减，停止分析"
    
    return False, "继续分析"

def calculate_analysis_continuation_value(self, analysis_rounds, current_round):
    """计算继续分析的价值"""
    
    # 基础价值因子
    base_factors = {
        "problem_urgency": 0.8,      # 问题紧急程度
        "solution_complexity": 0.6,   # 解决方案复杂度
        "time_investment": 0.7        # 时间投入价值
    }
    
    # 进度价值因子
    latest_round = analysis_rounds[-1]
    progress_factors = {
        "new_findings_rate": latest_round.get("new_findings", 0) / max(len(latest_round.get("hypotheses", [])), 1),
        "confidence_improvement": self.calculate_confidence_trend(analysis_rounds),
        "quality_trend": self.calculate_quality_trend_value()
    }
    
    # 成本因子 (随轮次增加而降低价值)
    cost_factor = max(0.1, 1.0 - (current_round - 1) * 0.2)
    
    # 综合价值计算
    base_value = sum(base_factors.values()) / len(base_factors)
    progress_value = sum(progress_factors.values()) / len(progress_factors)
    
    total_value = (base_value * 0.4 + progress_value * 0.6) * cost_factor
    
    print(f"💰 价值计算详情:")
    print(f"  └─ 基础价值: {base_value:.3f}")
    print(f"  └─ 进度价值: {progress_value:.3f}")
    print(f"  └─ 成本因子: {cost_factor:.3f}")
    print(f"  └─ 综合价值: {total_value:.3f}")
    
    return total_value
```

---

## 🛠️ 阶段3: 质量控制的修复实施

```python
def adaptive_repair_implementation(self, analysis_result):
    """质量控制的修复实施 - 即时反馈和调整"""
    
    print("🛠️ [阶段3] 质量控制的修复实施")
    self.current_phase = "repair"
    
    repair_attempts = []
    max_attempts = 3
    
    for attempt in range(1, max_attempts + 1):
        print(f"\n🔧 修复尝试 {attempt}/{max_attempts}")
        
        # 设计修复方案
        if attempt == 1:
            fix_plan = self.design_initial_fix_plan(analysis_result)
        else:
            fix_plan = self.adjust_fix_plan(repair_attempts[-1])
        
        print(f"📋 修复方案: {fix_plan['description']}")
        
        # 执行修复
        implementation_result = self.execute_repair_with_monitoring(fix_plan, attempt)
        
        # 即时质量验证
        repair_quality = self.assess_repair_quality(implementation_result)
        
        print(f"📊 修复质量: {repair_quality:.3f}")
        
        repair_attempts.append({
            "attempt": attempt,
            "plan": fix_plan,
            "implementation": implementation_result,
            "quality": repair_quality
        })
        
        # 质量检查
        if repair_quality >= self.quality_threshold:
            print("✅ 修复质量达标")
            return {
                "success": True,
                "attempts": attempt,
                "final_repair": repair_attempts[-1],
                "quality": repair_quality
            }
        
        # 失败价值评估
        if attempt < max_attempts:
            retry_value = self.calculate_retry_value(repair_attempts)
            print(f"💰 重试价值: {retry_value:.3f}")
            
            if retry_value < self.value_threshold:
                print("⏹️ 重试价值不足，停止修复")
                break
    
    # 所有尝试完成，检查是否达到最低质量
    best_attempt = max(repair_attempts, key=lambda x: x["quality"])
    
    if best_attempt["quality"] >= self.min_quality_score:
        print("⚠️ 修复质量勉强达标")
        return {"success": True, "attempts": len(repair_attempts), "final_repair": best_attempt, "warning": "低质量修复"}
    else:
        raise QualityThresholdException("repair", f"最佳修复质量{best_attempt['quality']:.3f}仍低于要求")

def execute_repair_with_monitoring(self, fix_plan, attempt_num):
    """带监控的修复执行"""
    
    print(f"⚙️ 执行修复 (尝试{attempt_num})")
    
    implementation_log = []
    
    for step_num, action in enumerate(fix_plan["actions"], 1):
        print(f"  🔨 步骤{step_num}: {action['description']}")
        
        try:
            # 执行修复动作
            step_result = self.execute_repair_action(action)
            
            # 即时验证
            step_verification = self.verify_step_immediately(step_result)
            
            implementation_log.append({
                "step": step_num,
                "action": action,
                "result": step_result,
                "verification": step_verification,
                "success": step_verification["passed"]
            })
            
            if step_verification["passed"]:
                print(f"    ✅ 步骤{step_num}成功")
            else:
                print(f"    ❌ 步骤{step_num}失败: {step_verification['reason']}")
                
                # 决定是否继续
                if action.get("critical", False):
                    print(f"    🛑 关键步骤失败，停止修复")
                    break
                    
        except Exception as e:
            print(f"    ❌ 步骤{step_num}异常: {str(e)}")
            implementation_log.append({
                "step": step_num,
                "action": action,
                "error": str(e),
                "success": False
            })
            
            if action.get("critical", False):
                break
    
    # 计算实施成功率
    successful_steps = sum(1 for log in implementation_log if log.get("success"))
    total_steps = len(implementation_log)
    success_rate = successful_steps / total_steps if total_steps > 0 else 0
    
    print(f"📊 实施统计: {successful_steps}/{total_steps} 成功率{success_rate*100:.1f}%")
    
    return {
        "plan": fix_plan,
        "implementation_log": implementation_log,
        "success_rate": success_rate,
        "total_steps": total_steps,
        "successful_steps": successful_steps
    }
```

---

## ✅ 阶段4: 智能验证和总结

```python
def adaptive_verification_summary(self, repair_result):
    """智能验证和总结 - 全面但高效的验证"""
    
    print("✅ [阶段4] 智能验证和总结")
    self.current_phase = "verification"
    
    # 智能验证策略选择
    verification_strategy = self.select_verification_strategy(repair_result)
    print(f"🎯 验证策略: {verification_strategy['name']}")
    
    # 执行验证
    verification_results = self.execute_smart_verification(verification_strategy)
    
    # 生成总结报告
    summary_report = self.generate_adaptive_summary(verification_results)
    
    return {
        "verification_passed": verification_results["overall_success"],
        "verification_details": verification_results,
        "summary_report": summary_report,
        "total_quality_score": self.calculate_overall_quality(),
        "execution_efficiency": self.calculate_execution_efficiency()
    }

def execute_smart_verification(self, strategy):
    """执行智能验证"""
    
    verification_suites = strategy["test_suites"]
    results = {}
    
    for suite_name, suite_config in verification_suites.items():
        print(f"🧪 验证套件: {suite_name}")
        
        suite_result = self.run_verification_suite(suite_config)
        results[suite_name] = suite_result
        
        # 显示详细结果
        if suite_result["passed"]:
            print(f"  ✅ {suite_name}: 通过 ({suite_result['pass_rate']*100:.1f}%)")
        else:
            print(f"  ❌ {suite_name}: 失败 ({suite_result['pass_rate']*100:.1f}%)")
            for failure in suite_result.get("failures", []):
                print(f"    └─ 失败: {failure}")
        
        # 智能早停：如果关键验证失败
        if suite_config.get("critical") and not suite_result["passed"]:
            print(f"🛑 关键验证{suite_name}失败，停止后续验证")
            break
    
    # 计算总体验证结果
    overall_success = all(r["passed"] for r in results.values() if r.get("required", True))
    total_tests = sum(r["total_tests"] for r in results.values())
    passed_tests = sum(r["passed_tests"] for r in results.values())
    
    print(f"📊 验证总结: {passed_tests}/{total_tests} 通过率{passed_tests/total_tests*100:.1f}%")
    
    return {
        "overall_success": overall_success,
        "suite_results": results,
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "pass_rate": passed_tests / total_tests if total_tests > 0 else 0
    }
```

---

## 🧮 价值计算和质量评估

```python
class ValueCalculator:
    """价值计算器 - 评估继续执行的价值"""
    
    def calculate_continuation_value(self, current_quality, attempt_number, historical_improvements):
        """计算继续执行的价值"""
        
        # 质量改进潜力
        quality_potential = max(0, (1.0 - current_quality) * 0.8)
        
        # 历史改进趋势
        improvement_trend = self.analyze_improvement_trend(historical_improvements)
        
        # 尝试次数惩罚 (边际效用递减)
        attempt_penalty = 1.0 / (1.0 + 0.3 * (attempt_number - 1))
        
        # 时间成本考虑
        time_cost_factor = max(0.1, 1.0 - 0.1 * attempt_number)
        
        # 综合价值
        total_value = (
            quality_potential * 0.4 +
            improvement_trend * 0.3 +
            attempt_penalty * 0.2 +
            time_cost_factor * 0.1
        )
        
        return max(0, min(1, total_value))

class QualityAssessor:
    """质量评估器 - 多维度质量评估"""
    
    def assess_phase_quality(self, phase_name, phase_data):
        """评估阶段质量"""
        
        if phase_name == "collection":
            return self.assess_collection_quality(phase_data)
        elif phase_name == "analysis":
            return self.assess_analysis_quality(phase_data)
        elif phase_name == "repair":
            return self.assess_repair_quality(phase_data)
        elif phase_name == "verification":
            return self.assess_verification_quality(phase_data)
        
        return 0.5  # 默认中等质量
    
    def assess_collection_quality(self, data):
        """评估信息收集质量"""
        
        metrics = {
            "completeness": len(data.get("keywords", [])) >= 3,
            "coverage": len(data.get("files", [])) >= 5,
            "relevance": self.calculate_relevance_score(data),
            "accuracy": self.verify_data_accuracy(data)
        }
        
        weights = {"completeness": 0.3, "coverage": 0.3, "relevance": 0.25, "accuracy": 0.15}
        
        return sum(weights[k] * (1.0 if v else 0.0) if isinstance(v, bool) else weights[k] * v 
                  for k, v in metrics.items())

# 异常定义
class EarlyStopException(Exception):
    def __init__(self, reason, partial_results=None):
        self.reason = reason
        self.partial_results = partial_results or {}
        super().__init__(f"Early stop: {reason}")

class QualityThresholdException(Exception):
    def __init__(self, phase, message):
        self.phase = phase
        self.message = message
        super().__init__(f"Quality threshold not met in {phase}: {message}")
```

