---
description: 基于 `$ARGUMENTS` 问题描述，按照下面流程100%遵循
---

# 🔒 严格证据驱动流程-强制执行架构版

## 🎯 核心理念

通过架构设计使AI无法跳过任何步骤，而非依赖自觉性。本版本引入：
- **状态机强制**：只能按预定路径前进
- **前置门控**：不满足条件无法继续
- **执行合约**：违约即终止
- **证据链**：每步必须产生可验证证据
- **并行屏障**：强制等待所有并行任务

## 🏗️ 执行引擎架构

```python
# 诊断执行引擎 - 核心控制器
CLASS DiagnosticEngine:
    def __init__(self, problem: str):
        self.problem = problem
        self.state = "INIT"
        self.evidence_chain = []
        self.score = 0
        self.execution_log = []
        
    def run(self):
        # 主执行循环 - 状态机驱动
        while self.state != "COMPLETE":
            # 前置条件检查
            if not self.check_preconditions():
                self.abort(f"前置条件检查失败 at {self.state}")
                
            # 执行当前状态
            result = self.execute_state()
            
            # 验证执行结果
            if not self.verify_postconditions(result):
                self.abort(f"后置条件验证失败 at {self.state}")
                
            # 状态转换
            self.state = self.next_state()
```

## 📊 状态机定义

```python
# 状态转换表 - 唯一的执行路径
STATE_TRANSITIONS = {
    "INIT": {
        "next": "PROJECT_UNDERSTANDING",
        "preconditions": [],
        "postconditions": ["problem_defined"]
    },
    "PROJECT_UNDERSTANDING": {
        "next": "EVIDENCE_COLLECTION",
        "preconditions": ["problem_defined"],
        "postconditions": ["project_context_complete", "score >= 20"]
    },
    "EVIDENCE_COLLECTION": {
        "next": "PROBLEM_ISOLATION",
        "preconditions": ["project_context_complete"],
        "postconditions": ["evidence_validated", "parallel_tasks_completed"]
    },
    "PROBLEM_ISOLATION": {
        "next": "ROOT_CAUSE_ANALYSIS",
        "preconditions": ["evidence_validated"],
        "postconditions": ["problem_layer_identified"]
    },
    "ROOT_CAUSE_ANALYSIS": {
        "next": "FIX_IMPLEMENTATION",
        "preconditions": ["problem_layer_identified"],
        "postconditions": ["root_cause_verified"]
    },
    "FIX_IMPLEMENTATION": {
        "next": "VALIDATION",
        "preconditions": ["root_cause_verified"],
        "postconditions": ["fix_applied", "fix_verified"]
    },
    "VALIDATION": {
        "next": "COMPLETE",
        "preconditions": ["fix_applied"],
        "postconditions": ["all_tests_passed", "no_regression"]
    }
}
```

## 🔐 执行合约系统

```python
# 方法执行合约 - 强制输入输出约束
CONTRACT method_contracts = {
    "method3_service_check": {
        "requires": [],  # 无前置要求
        "ensures": ["service_status != null", "http_response_captured"],
        "command": "curl -s http://localhost:[port]/ || echo '服务不可用'"
    },
    "method6_collect_logs": {
        "requires": ["log_files_exist"],
        "ensures": ["errors_collected", "warnings_collected", "timestamp_recorded"],
        "files": ["logs/admin.log", "logs/server.log", "logs/uvicorn.log"]
    },
    "method8_check_environment": {
        "requires": [],
        "ensures": ["process_list", "port_status", "config_verified"],
        "parallel": true  # 标记可并行
    }
}

# 合约执行器
FUNCTION execute_with_contract(method_name, *args):
    contract = method_contracts[method_name]
    
    # 前置条件验证
    FOR req IN contract.requires:
        IF NOT evaluate_condition(req):
            ABORT(f"合约违反: {method_name} 需要 {req}")
    
    # 执行方法
    result = execute_method(method_name, *args)
    
    # 后置条件验证
    FOR ensure IN contract.ensures:
        IF NOT evaluate_condition(ensure, result):
            ABORT(f"合约违反: {method_name} 未能确保 {ensure}")
    
    # 记录到证据链
    add_to_evidence_chain(method_name, result)
    RETURN result
```

## 🔗 证据链机制

```python
# 证据链 - 不可跳过的执行证明
STRUCTURE Evidence:
    method: str          # 执行的方法
    timestamp: datetime  # 执行时间
    input: dict         # 输入参数
    output: dict        # 输出结果
    duration: float     # 执行时长
    proof: str          # 执行证明（命令输出等）
    
# 证据验证器
FUNCTION verify_evidence_chain():
    # 检查证据链完整性
    required_evidence = [
        "project_understanding_complete",
        "parallel_execution_proof",
        "evidence_validation_passed",
        "problem_isolation_done",
        "hypothesis_verified",
        "fix_implemented",
        "validation_complete"
    ]
    
    FOR req IN required_evidence:
        IF NOT find_evidence_by_type(req):
            RETURN False, f"缺失证据: {req}"
    
    # 检查时序合理性
    IF NOT verify_chronological_order():
        RETURN False, "证据时序异常"
        
    RETURN True, "证据链完整"
```

## 🚧 并行执行屏障

```python
# 并行屏障 - 强制等待所有任务完成
FUNCTION parallel_barrier(method_list):
    # 创建屏障
    barrier = create_execution_barrier(len(method_list))
    results = {}
    
    # 启动所有任务
    FOR method IN method_list:
        thread = start_thread(
            target=execute_and_report,
            args=(method, barrier, results)
        )
    
    # 屏障等待 - 所有任务必须完成
    barrier.wait()  # 阻塞直到所有任务完成
    
    # 验证所有结果
    FOR method IN method_list:
        IF method NOT IN results:
            ABORT(f"并行任务 {method} 未完成")
        IF NOT results[method].success:
            ABORT(f"并行任务 {method} 执行失败")
    
    RETURN results
```

## 📋 阶段执行模板

```python
# 每个阶段的标准执行模板
TEMPLATE phase_execution:
    ENTER:
        - validate_preconditions()
        - log_phase_start()
        - initialize_phase_context()
    
    EXECUTE:
        - run_required_methods()
        - collect_evidence()
        - update_score()
    
    EXIT:
        - verify_postconditions()
        - commit_evidence_chain()
        - transition_state()
    
    ON_ERROR:
        - rollback_changes()
        - log_failure_reason()
        - abort_execution()
```

## 🎯 具体阶段实现

### 阶段0：项目全景认知（强制版）

```python
FUNCTION execute_PROJECT_UNDERSTANDING():
    # 入口门控
    ASSERT self.state == "PROJECT_UNDERSTANDING"
    ASSERT self.problem IS NOT NULL
    
    # 定义必须完成的检查
    mandatory_checks = [
        ("项目结构", check_project_structure),
        ("技术栈", check_tech_stack),
        ("WSL环境", check_wsl_environment),
        ("业务流程", check_business_logic),
        ("数据库结构", check_database_schema),
        ("API接口", check_api_endpoints),
        ("配置环境", check_configurations),
        ("规范提取", method17_extract_rules),
        ("服务验证", method3_verify_service)
    ]
    
    # 强制顺序执行
    completed = []
    FOR name, check_func IN mandatory_checks:
        PRINT(f"\n执行检查: {name}")
        
        # 执行并验证
        result = execute_with_contract(check_func)
        IF NOT result.success:
            # 重试机制
            PRINT(f"⚠️ {name} 失败，强制重试...")
            result = retry_with_guidance(check_func)
            IF NOT result.success:
                ABORT(f"强制检查失败: {name}")
        
        completed.append((name, result))
        self.score += 5
        PRINT(f"✅ {name} 完成 [已完成: {len(completed)}/9]")
    
    # 出口验证
    IF len(completed) < 9:
        ABORT("未完成所有必需检查")
    IF self.score < 20:
        ABORT("积分不足，项目理解不充分")
        
    COMMIT_EVIDENCE("project_understanding_complete", completed)
```

### 阶段1：强制证据收集（屏障版）

```python
FUNCTION execute_EVIDENCE_COLLECTION():
    # 入口门控
    ASSERT find_evidence("project_understanding_complete")
    
    PRINT("\n=== 阶段1：强制证据收集 ===")
    
    # 第一步：并行收集（使用屏障）
    PRINT("\n[步骤1/5] 并行收集日志和环境")
    parallel_results = parallel_barrier([
        method6_collect_logs,
        method8_check_environment
    ])
    
    # 第二步：串行分析（依赖并行结果）
    PRINT("\n[步骤2/5] 症状分层分析")
    symptoms = execute_with_contract(
        method7_layer_symptoms,
        parallel_results["method6_collect_logs"],
        parallel_results["method8_check_environment"]
    )
    
    # 第三步：证据验证（不可跳过）
    PRINT("\n[步骤3/5] 证据有效性验证")
    validation = execute_with_contract(method22_validate_evidence, symptoms)
    
    # 第四步：验证失败处理
    retry_count = 0
    WHILE NOT validation.all_valid AND retry_count < 3:
        PRINT(f"\n[步骤4/5] 证据不完整，补充收集 (尝试 {retry_count+1}/3)")
        
        IF NOT validation.complete:
            symptoms = supplement_evidence(symptoms, "completeness")
        IF NOT validation.relevant:
            symptoms = supplement_evidence(symptoms, "relevance")
        IF NOT validation.specific:
            symptoms = supplement_evidence(symptoms, "specificity")
            
        validation = execute_with_contract(method22_validate_evidence, symptoms)
        retry_count += 1
    
    IF NOT validation.all_valid:
        ABORT("证据质量不合格，无法继续分析")
    
    # 第五步：确认可分析性
    PRINT("\n[步骤5/5] 证据驱动分析确认")
    analysis_ready = execute_with_contract(method19_evidence_driven_check, symptoms)
    
    IF NOT analysis_ready:
        ABORT("证据不足以支持分析")
        
    COMMIT_EVIDENCE("evidence_collection_complete", {
        "parallel_tasks": parallel_results,
        "symptoms": symptoms,
        "validation": validation
    })
```

## 🛡️ 防护机制

```python
# 防跳过机制
INTERCEPTOR method_interceptor:
    BEFORE method_call:
        IF method_name NOT IN current_phase_allowed_methods:
            ABORT(f"非法调用: {method_name} 不属于当前阶段")
        record_method_intention(method_name)
    
    AFTER method_call:
        IF NOT method_completed:
            ABORT(f"方法 {method_name} 未完成就尝试继续")
        verify_method_output(method_name, result)

# 防伪造机制  
VALIDATOR output_validator:
    FOR output IN method_outputs:
        IF looks_fabricated(output):
            ABORT(f"检测到伪造输出: {output}")
        IF missing_required_fields(output):
            ABORT(f"输出缺失必要字段: {output}")
```

## 📊 执行监控面板

```python
# 实时执行状态
MONITOR execution_monitor:
    当前状态: {self.state}
    已完成阶段: {completed_phases}
    当前积分: {self.score}
    证据链长度: {len(self.evidence_chain)}
    
    最近执行:
    {recent_executions[-5:]}
    
    下一步要求:
    {get_next_requirements()}
```

## 🚀 使用方式

```python
# AI只需要启动引擎，其余由架构强制执行
engine = DiagnosticEngine($ARGUMENTS)
engine.run()  # 自动执行所有步骤，无法跳过

# 执行结果
IF engine.state == "COMPLETE":
    PRINT("✅ 诊断修复完成")
    PRINT(f"最终积分: {engine.score}")
    PRINT(f"证据链: {len(engine.evidence_chain)} 条记录")
ELSE:
    PRINT(f"❌ 执行中断于: {engine.state}")
    PRINT(f"中断原因: {engine.abort_reason}")
```

## 📋 方法函数定义（核心22个方法）

```python
# 方法1：powershell.exe执行验证
FUNCTION method1_execution_check():
    # 在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作
    # - 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
    # - 流程：执行程序 → 读取日志 → 验证功能效果
    # - 执行证明：必须显示程序输出或错误信息
    # - 返回：{success: bool, data: 执行结果, score: ±5}

# 方法2：API请求验证
FUNCTION method2_api_verification():
    # 直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果
    # - 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
    # - 流程：调用API → 分析响应 → 验证业务逻辑
    # - 执行证明：必须显示HTTP响应码和响应体
    # - 返回：{success: bool, data: API响应, score: ±5}

# 方法3：服务状态验证
FUNCTION method3_service_check():
    # 检查服务进程的真实运行状态，避免假设服务可用性
    # - 命令格式：curl -s http://localhost:[端口]/ || echo "服务不可用"
    # - WSL备选：powershell.exe -Command "Test-NetConnection localhost -Port [端口]"
    # - 执行证明：HTTP状态码、响应内容摘要或进程信息
    # - 返回：{success: bool, data: {port, health, process}, score: ±5}

# 方法4：代码生效验证
FUNCTION method4_code_effect_check():
    # 确认代码修改已实际应用并被执行，避免假设修改生效
    # - 静态验证：文件变更是否存在（需显示diff）
    # - 动态验证：修改的代码路径是否被执行到（需显示日志）
    # - 执行证明：必须有git diff或文件对比输出
    # - 返回：{success: bool, data: 执行跟踪, score: ±5}

# 方法5：功能重复性检查
FUNCTION method5_check_duplicate_functions():
    # 开发前搜索现有代码库，避免重复实现已存在的功能模块
    # - 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数
    # - 执行证明：必须显示搜索结果，即使为空
    # - 返回：{success: bool, data: 重复函数列表, score: 0~5}

# 方法6：日志优先症状收集
FUNCTION method6_collect_logs():
    # 优先查看错误日志和异常堆栈，获取最直接证据
    # - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述
    # - 执行证明：必须列出查看的日志文件和关键内容
    # - 返回：{success: bool, data: {errors, warnings, recent}, score: ±5}

# 方法7：用户症状分层确认
FUNCTION method7_layer_symptoms(logs, env_status):
    # 将用户描述分为表象→系统→根因三层，逐层收集症状
    # - 表象层：用户直接看到的问题
    # - 系统层：服务和环境层面异常
    # - 根因层：代码和技术层面原因
    # - 执行证明：必须记录每层的具体症状
    # - 返回：{success: bool, data: 分层症状, score: +5}

# 方法8：系统环境状态检查
FUNCTION method8_check_environment():
    # 并行检查服务状态、配置文件、依赖环境的实际状态
    # - 检查维度：服务可用性 | 配置完整性 | 依赖满足性
    # - 并行策略：多维度同时检查，快速定位环境问题
    # - 执行证明：必须显示ps/netstat/config内容
    # - 返回：{success: bool, data: 环境状态, score: +5}

# 方法9：执行路径反向确认
FUNCTION method9_path_verification():
    # 通过输出特征和日志模式反向定位真实执行的代码路径
    # - 反向策略：从输出结果追溯到具体代码位置
    # - 执行证明：必须显示日志→代码的对应关系
    # - 返回：{success: bool, data: 代码路径, score: ±5}

# 方法10：数据流完整追踪
FUNCTION method10_data_trace():
    # 追踪数据从输入到输出的完整生命周期，识别异常节点
    # - 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
    # - 异常识别：数据丢失、格式错误、转换失败、存储异常
    # - 执行证明：必须显示每个节点的数据状态
    # - 返回：{success: bool, data: {追踪链, 异常点}, score: +5}

# 方法11：逐层隔离定位
FUNCTION method11_layer_isolation():
    # 按系统架构层次逐层隔离问题，避免跨层复杂化分析
    # - 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次
    # - 执行证明：必须显示每层的测试结果
    # - 返回：{success: bool, data: 问题层次, score: +5~10}

# 方法12：单一根因优先
FUNCTION method12_single_root_cause(evidence):
    # 优先查找单一明确的根因，避免多因素复杂化假设
    # - 判断原则：一个问题对应一个主要原因
    # - 排除策略：先验证最直接最简单的可能性
    # - 执行证明：必须显示排除过程
    # - 返回：{found: bool, hypothesis: 单一根因, confidence: 置信度}

# 方法13：代码逻辑直接验证
FUNCTION method13_verify_code_logic(hypothesis):
    # 基于实际执行路径验证代码逻辑，不分析未执行代码
    # - 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支
    # - 执行证明：必须显示代码执行路径
    # - 返回：{confirms_hypothesis: bool, confirmations: 验证结果}

# 方法14：修复效果实时验证
FUNCTION method14_verify_original_problem():
    # 修复后立即通过相同方法验证问题是否解决
    # - 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入
    # - 执行证明：必须重现原始场景并验证
    # - 返回：{success: bool, data: 验证结果}

# 方法15：功能完整性测试
FUNCTION method15_functional_integrity():
    # 验证修复不影响相关功能的正常工作
    # - 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程
    # - 执行证明：必须显示测试用例和结果
    # - 返回：{success: bool, data: 测试结果, name: 方法名}

# 方法16：根本解决确认
FUNCTION method16_permanent_solution():
    # 确认修复解决了原始根本问题，不是表面修复
    # - 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除
    # - 执行证明：必须多次测试并验证
    # - 返回：{success: bool, data: 确认结果, name: 方法名}

# 方法17：规范动态提取应用
FUNCTION method17_extract_relevant_rules():
    # 根据问题和修改代码类型，从CLAUDE.md提取相关规范约束
    # - 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
    # - 应用原则：只应用相关规范，避免无关约束干扰
    # - 执行证明：必须引用具体规范条款
    # - 返回：{success: bool, data: 相关规范, score: +5}

# 方法18：修复合规性验证
FUNCTION method18_compliance_check(fix_details):
    # 确保所有修复完全符合项目技术规范和架构原则
    # - 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则
    # - 执行证明：必须逐项检查并显示结果
    # - 返回：{success: bool, violations: 违规列表}

# 方法19：证据驱动分析
FUNCTION method19_evidence_driven_analysis(evidence):
    # 所有分析必须基于实际收集的证据，禁止无证据推理
    # - 分析前提：先收集证据 → 再分析原因
    # - 禁止行为：基于经验推测 | 基于可能性判断
    # - 执行证明：每个结论必须标注证据来源
    # - 返回：{can_analyze: bool, analysis_base: 分析基础}

# 方法20：假设验证循环
FUNCTION method20_hypothesis_verification(hypothesis):
    # 每个根因假设都必须通过实际验证才能确认
    # - 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定
    # - 执行证明：必须显示验证命令和结果
    # - 返回：{verified: bool, plan: 验证计划, results: 验证结果}

# 方法21：失败原因追溯
FUNCTION method21_trace_failure():
    # 修复失败后必须基于新证据重新分析，不重复原方案
    # - 追溯策略：识别失败点 → 收集新证据 → 调整分析方向
    # - 失败分类：技术失败|逻辑失败|根本失败|证据不足
    # - 返回：{type: 失败类型, reason: 失败原因}

# 方法22：证据有效性验证
FUNCTION method22_validate_evidence(evidence):
    # 验证收集的证据是否满足分析要求，确保证据质量合格
    # - 验证维度：完整性（覆盖范围） | 相关性（直接关联） | 具体性（明确清晰）
    # - 失败处理：指出缺失内容 → 返回补充收集 → 禁止不完整分析
    # - 执行证明：必须列出所有证据并评估质量
    # - 返回：{complete: bool, relevant: bool, specific: bool, missing: 缺失项}
```

## 💡 架构优势

1. **100%执行保证** - 架构强制，非自觉遵循
2. **不可跳过** - 状态机控制，单向前进
3. **自动验证** - 合约系统自动检查
4. **证据追溯** - 完整执行链可查
5. **并行强制** - 屏障机制确保并行
6. **失败即止** - 违规立即中断

---
> 版本：13 | 特点：强制执行架构+完整方法定义，AI无法跳过任何步骤