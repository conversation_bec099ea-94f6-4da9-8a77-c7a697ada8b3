---
description: Task技术与TodoWrite任务管理的多阶段循环架构设计规范和实现指南
---

# 🏗️ Task+TodoWrite多阶段循环架构

## 📋 架构概览

### 🎯 架构定位

这是一个通用的技术架构框架，专门用于构建需要**多阶段执行**、**循环控制**、**任务跟踪**和**质量保证**的复杂流程系统。

### 🔧 核心技术

**Task技术**：Claude Code的智能代理工具
- 开放式搜索和复杂分析
- 并行执行和结果整合
- 自动回退和容错机制

**TodoWrite技术**：Claude Code的任务管理工具
- 实时任务状态跟踪
- 进度可视化管理
- 强制质量验证

### 🎨 架构优势

**1. 可视化管理**
- 三层任务结构提供清晰的执行视图
- 实时状态更新让进度透明可控
- 用户可随时了解执行情况和剩余任务

**2. 质量保证**
- 强制性任务完成验证
- 门禁条件的任务化检查
- 防止步骤遗漏和质量下降

**3. 容错性强**
- Task失败时自动回退到传统方法
- 状态不一致时的自动修复机制
- 循环异常时的安全退出策略

**4. 高度可复用**
- 模块化组件设计支持灵活组合
- 标准化接口便于扩展和定制
- 通用模式适用于各种复杂流程

### 🌍 适用场景

**✅ 适合使用的场景：**
- 需要多个阶段顺序执行的复杂流程
- 包含循环验证或迭代优化的流程
- 对执行质量和进度跟踪有严格要求的流程
- 需要智能搜索和分析能力的流程

**📋 典型应用案例：**
- 问题诊断与修复流程
- 代码重构和优化流程
- 系统部署和验证流程
- 数据迁移和清理流程
- 安全审计和合规检查流程
- 测试执行和缺陷修复流程

## 🏗️ 核心组件架构

### 📊 组件总览

```
Task+TodoWrite多阶段循环架构
├── TodoWriteManager (任务管理器)
│   ├── 三层任务结构管理
│   ├── 状态同步和更新
│   └── 任务生命周期控制
├── TaskTodoIntegration (Task集成器)
│   ├── Task执行与状态同步
│   ├── 并行Task管理
│   └── 失败回退机制
├── DynamicTaskManager (动态管理器)
│   ├── 循环轮次管理
│   ├── 动态任务创建
│   └── 任务清理机制
└── GateConditionManager (门禁管理器)
    ├── 门禁条件定义
    ├── 任务化验证
    └── 质量评估体系
```

### 🎛️ 1. TodoWriteManager - 任务管理器

**职责**：负责三层任务结构的创建、管理和状态维护

```python
class TodoWriteManager:
    """三层任务管理架构"""
    
    # 任务层级定义
    TASK_LEVELS = {
        'stage': '阶段级任务',      # 宏观进度跟踪
        'step': '步骤级任务',       # 具体执行内容  
        'verification': '验证级任务' # 细节质量保证
    }
    
    # 任务状态定义
    TASK_STATES = {
        'pending': '待执行',
        'in_progress': '执行中',
        'completed': '已完成',
        'failed': '执行失败',
        'skipped': '已跳过'
    }
    
    def create_stage_tasks(self, stage_config):
        """创建阶段级任务及其子任务"""
        pass
        
    def create_dynamic_tasks(self, task_configs):
        """动态创建任务列表"""
        pass
        
    def update_task_status(self, task_id, status):
        """更新任务状态"""
        pass
        
    def get_current_progress(self):
        """获取当前执行进度"""
        pass
        
    def cleanup_completed_tasks(self):
        """清理已完成任务"""
        pass
```

**核心特性**：
- **三层结构**：阶段→步骤→验证的层次化任务组织
- **状态同步**：任务状态与实际执行状态保持一致
- **动态创建**：根据执行情况动态生成任务
- **自动清理**：定期清理已完成任务保持列表简洁

### 🔄 2. TaskTodoIntegration - Task集成器

**职责**：负责Task执行与TodoWrite状态的无缝集成

```python
class TaskTodoIntegration:
    """Task执行与TodoWrite状态同步"""
    
    def execute_task_with_sync(self, task_config, todo_id):
        """执行Task并同步状态"""
        # 1. 开始前更新状态
        self.todo_manager.update_task_status(todo_id, 'in_progress')
        
        try:
            # 2. 执行Task
            result = Task(
                description=task_config['description'],
                prompt=task_config['prompt']
            )
            
            # 3. 成功完成
            self.todo_manager.update_task_status(todo_id, 'completed')
            return {'success': True, 'result': result}
            
        except Exception as e:
            # 4. 失败处理
            return self.handle_task_failure(task_config, todo_id, e)
    
    def execute_parallel_tasks(self, task_configs):
        """并行执行多个Task"""
        pass
        
    def handle_task_failure(self, task_config, todo_id, error):
        """处理Task失败情况"""
        pass
        
    def get_fallback_method(self, task_type):
        """获取回退方法"""
        pass
```

**核心特性**：
- **状态同步**：Task执行状态实时反映到TodoWrite
- **并行支持**：支持多个Task并行执行和状态管理
- **失败回退**：Task失败时自动使用传统方法
- **质量跟踪**：记录Task执行质量和成功率

### 🔁 3. DynamicTaskManager - 动态管理器

**职责**：负责循环流程的动态任务管理和轮次控制

```python
class DynamicTaskManager:
    """动态任务管理和循环控制"""
    
    def manage_loop_execution(self, loop_config):
        """管理循环执行"""
        for round_num in range(1, loop_config['max_rounds'] + 1):
            # 1. 创建轮次任务
            round_tasks = self.create_round_tasks(round_num, loop_config)
            
            # 2. 执行轮次任务  
            round_result = self.execute_round_tasks(round_tasks)
            
            # 3. 评估退出条件
            if self.should_exit_loop(round_result, round_num):
                break
                
            # 4. 清理和准备下一轮
            self.cleanup_round_tasks(round_num)
    
    def create_round_tasks(self, round_num, config):
        """为轮次创建任务"""
        pass
        
    def evaluate_exit_conditions(self, context, round_num):
        """评估循环退出条件"""
        pass
        
    def cleanup_round_tasks(self, round_num):
        """清理轮次任务"""
        pass
```

**核心特性**：
- **轮次管理**：自动创建和管理循环轮次的任务
- **动态调整**：根据执行结果动态调整后续任务
- **退出控制**：基于客观条件自动控制循环退出
- **资源优化**：及时清理已完成轮次的任务

### 🚪 4. GateConditionManager - 门禁管理器

**职责**：负责质量门禁的定义、验证和管理

```python
class GateConditionManager:
    """门禁条件管理"""
    
    def define_gate_conditions(self, stage, context):
        """定义阶段门禁条件"""
        pass
        
    def verify_gate_conditions(self, stage, context):
        """验证门禁条件"""
        gate_results = {}
        
        for condition_name, config in self.get_gate_configs(stage).items():
            # 为每个条件创建验证任务
            gate_task = self.create_gate_task(condition_name, config)
            
            # 执行验证并更新状态
            result = self.execute_gate_verification(gate_task, context)
            gate_results[condition_name] = result
            
        return self.evaluate_gate_results(gate_results)
    
    def create_gate_task(self, condition_name, config):
        """创建门禁验证任务"""
        pass
        
    def execute_gate_verification(self, gate_task, context):
        """执行门禁验证"""
        pass
```

**核心特性**：
- **条件定义**：标准化的门禁条件定义接口
- **任务化验证**：将门禁条件转换为可跟踪的验证任务
- **质量评估**：提供量化的质量评估指标
- **灵活配置**：支持自定义门禁条件和阈值

## 🎨 架构模式

### 📊 1. 三层任务结构模式

**设计原理**：
```
阶段级任务 (Stage Level)
├── 职责：宏观进度跟踪和阶段转换控制
├── 粒度：整个流程的主要阶段
└── 示例：信息收集阶段、假设验证阶段、修复实施阶段

步骤级任务 (Step Level)  
├── 职责：具体执行内容和逻辑控制
├── 粒度：每个阶段内的关键步骤
└── 示例：问题信息解析、并行搜索验证、门禁条件检查

验证级任务 (Verification Level)
├── 职责：细节质量保证和验证确认
├── 粒度：动态创建的验证和检查任务
└── 示例：Task搜索验证、假设验证、修复效果验证
```

**实现模式**：
```python
def create_three_layer_tasks(stage_config):
    """创建三层任务结构"""
    
    # 1. 创建阶段级任务
    stage_task = {
        'content': stage_config['name'],
        'status': 'pending',
        'priority': 'high',
        'id': f'stage_{stage_config["id"]}',
        'level': 'stage'
    }
    
    # 2. 创建步骤级任务
    step_tasks = []
    for step in stage_config['steps']:
        step_task = {
            'content': f'  - {step["name"]}',
            'status': 'pending', 
            'priority': step['priority'],
            'id': f'step_{stage_config["id"]}_{step["id"]}',
            'level': 'step',
            'parent_id': stage_task['id']
        }
        step_tasks.append(step_task)
    
    # 3. 验证级任务动态创建
    verification_tasks = []
    if stage_config.get('verifications'):
        for verification in stage_config['verifications']:
            verification_task = {
                'content': f'    - {verification["name"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'verify_{verification["type"]}_{verification["id"]}',
                'level': 'verification',
                'parent_id': step_task['id']  # 关联到相关步骤
            }
            verification_tasks.append(verification_task)
    
    return [stage_task] + step_tasks + verification_tasks
```

### 🔄 2. 状态同步模式

**设计原理**：
确保TodoWrite任务状态与实际执行状态始终保持一致，提供可靠的进度跟踪。

**同步策略**：
```python
class StateSyncPattern:
    """状态同步模式"""
    
    def sync_task_execution(self, task_config, todo_id):
        """任务执行状态同步"""
        
        # 阶段1：准备阶段
        self.update_task_status(todo_id, 'in_progress')
        self.log_task_start(todo_id, task_config)
        
        try:
            # 阶段2：执行阶段
            result = self.execute_actual_task(task_config)
            
            # 阶段3：成功完成
            self.update_task_status(todo_id, 'completed')
            self.log_task_success(todo_id, result)
            
            return {'success': True, 'result': result}
            
        except Exception as e:
            # 阶段4：异常处理
            self.log_task_error(todo_id, e)
            
            # 尝试回退方法
            fallback_result = self.try_fallback_method(task_config)
            
            if fallback_result['success']:
                self.update_task_status(todo_id, 'completed')
                self.log_fallback_success(todo_id, fallback_result)
            else:
                self.update_task_status(todo_id, 'failed')
                self.log_fallback_failure(todo_id)
            
            return fallback_result
    
    def batch_sync_tasks(self, task_list):
        """批量任务状态同步"""
        pass
        
    def force_sync_all_tasks(self):
        """强制同步所有任务状态"""
        pass
```

### 🔁 3. 循环管理模式

**设计原理**：
提供标准化的循环轮次管理，支持动态任务创建、执行监控和自动退出控制。

**循环模式**：
```python
class LoopManagementPattern:
    """循环管理模式"""
    
    def execute_managed_loop(self, loop_config):
        """管理化的循环执行"""
        
        loop_context = self.initialize_loop_context(loop_config)
        
        for round_num in range(1, loop_config['max_rounds'] + 1):
            print(f"\n--- 第{round_num}轮{loop_config['name']}循环 ---")
            
            # 1. 轮次准备
            round_context = self.prepare_round(round_num, loop_context)
            
            # 2. 创建轮次任务
            round_tasks = self.create_round_tasks(round_num, round_context)
            self.todo_manager.add_tasks(round_tasks)
            
            # 3. 执行轮次任务
            round_results = self.execute_round_tasks(round_tasks, round_context)
            
            # 4. 评估退出条件
            exit_evaluation = self.evaluate_exit_conditions(
                round_results, round_num, loop_context
            )
            
            # 5. 更新循环上下文
            loop_context = self.update_loop_context(
                loop_context, round_results, round_num
            )
            
            # 6. 退出判断
            if exit_evaluation['should_exit']:
                print(f"✅ 循环退出条件满足：{exit_evaluation['reason']}")
                break
            
            # 7. 轮次清理
            self.cleanup_round_tasks(round_num, round_tasks)
        
        return self.finalize_loop_results(loop_context, round_num)
    
    def create_round_tasks(self, round_num, context):
        """创建轮次任务的标准模式"""
        return [
            {
                'content': f'第{round_num}轮{context["operation_name"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'{context["operation_id"]}_round_{round_num}',
                'round': round_num
            },
            {
                'content': f'第{round_num}轮执行验证',
                'status': 'pending',
                'priority': 'high', 
                'id': f'{context["operation_id"]}_verify_round_{round_num}',
                'round': round_num
            },
            {
                'content': f'第{round_num}轮退出条件评估',
                'status': 'pending',
                'priority': 'medium',
                'id': f'{context["operation_id"]}_exit_round_{round_num}',
                'round': round_num
            }
        ]
```

### 🚪 4. 门禁验证模式

**设计原理**：
将质量门禁条件转换为可跟踪的验证任务，确保每个阶段的质量标准得到严格执行。

**门禁模式**：
```python
class GateVerificationPattern:
    """门禁验证模式"""
    
    def execute_gate_verification(self, stage, context):
        """执行门禁验证的标准模式"""
        
        # 1. 获取门禁配置
        gate_configs = self.get_gate_configurations(stage)
        
        # 2. 创建门禁验证任务
        gate_tasks = []
        for condition_name, config in gate_configs.items():
            gate_task = {
                'content': f'    - 门禁验证: {config["description"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'gate_{stage.lower()}_{condition_name}',
                'gate_condition': condition_name,
                'verification_method': config['method']
            }
            gate_tasks.append(gate_task)
        
        # 3. 添加到任务管理器
        self.todo_manager.add_tasks(gate_tasks)
        
        # 4. 执行门禁验证
        gate_results = {}
        for gate_task in gate_tasks:
            self.todo_manager.update_task_status(gate_task['id'], 'in_progress')
            
            try:
                # 执行具体验证逻辑
                result = self.verify_gate_condition(
                    gate_task['gate_condition'],
                    gate_task['verification_method'],
                    context
                )
                
                gate_results[gate_task['gate_condition']] = result
                
                if result:
                    self.todo_manager.update_task_status(gate_task['id'], 'completed')
                    print(f"✅ 门禁条件通过: {gate_configs[gate_task['gate_condition']]['description']}")
                else:
                    self.todo_manager.update_task_status(gate_task['id'], 'failed')
                    print(f"❌ 门禁条件未满足: {gate_configs[gate_task['gate_condition']]['description']}")
                    
            except Exception as e:
                print(f"❌ 门禁条件检查异常: {gate_task['gate_condition']} - {str(e)}")
                gate_results[gate_task['gate_condition']] = False
                self.todo_manager.update_task_status(gate_task['id'], 'failed')
        
        # 5. 评估整体门禁结果
        return self.evaluate_overall_gate_result(gate_results, stage)
    
    def get_gate_configurations(self, stage):
        """获取标准门禁配置"""
        # 返回阶段特定的门禁条件配置
        pass
        
    def verify_gate_condition(self, condition_name, method, context):
        """验证具体门禁条件"""
        # 根据验证方法执行具体检查
        pass
```

## 🔧 实现模板

### 📋 主流程控制器模板

```python
class MultiStageFlowController:
    """多阶段流程控制器模板"""
    
    def __init__(self, flow_config):
        self.flow_config = flow_config
        self.current_stage = 'INIT'
        self.context = {}
        
        # 初始化核心组件
        self.todo_manager = TodoWriteManager()
        self.task_integration = TaskTodoIntegration(self.todo_manager)
        self.dynamic_manager = DynamicTaskManager(self.todo_manager, self.task_integration)
        self.gate_manager = GateConditionManager(self.todo_manager)
    
    def execute_flow(self, input_data):
        """执行完整流程"""
        
        print(f"🚀 启动{self.flow_config['name']}流程")
        
        try:
            # 初始化TodoWrite任务列表
            self.initialize_todo_list()
            
            # 执行各个阶段
            for stage_config in self.flow_config['stages']:
                if self.should_execute_stage(stage_config):
                    self.execute_stage(stage_config)
                else:
                    break
            
            # 流程完成
            self.current_stage = 'COMPLETED'
            self.finalize_todo_list()
            
            print(f"🏁 {self.flow_config['name']}流程完成")
            
        except Exception as e:
            self.current_stage = 'FAILED'
            print(f"❌ 流程执行失败: {str(e)}")
            self.handle_flow_failure(e)
    
    def execute_stage(self, stage_config):
        """执行单个阶段"""
        
        print(f"\n📍 [STAGE_{stage_config['id']}] {stage_config['name']}")
        
        # 创建阶段任务
        stage_tasks = self.todo_manager.create_stage_tasks(stage_config)
        self.todo_manager.add_tasks(stage_tasks)
        
        # 更新阶段状态
        stage_task_id = f"stage_{stage_config['id']}"
        self.todo_manager.update_task_status(stage_task_id, 'in_progress')
        
        try:
            # 执行阶段步骤
            for step_config in stage_config['steps']:
                self.execute_step(step_config, stage_config)
            
            # 执行循环（如果需要）
            if stage_config.get('has_loop'):
                self.execute_stage_loop(stage_config)
            
            # 门禁条件检查
            gate_result = self.gate_manager.verify_gate_conditions(
                stage_config['id'], self.context
            )
            
            if gate_result['passed']:
                self.todo_manager.update_task_status(stage_task_id, 'completed')
                self.current_stage = stage_config.get('next_stage', 'COMPLETED')
                print(f"✅ {stage_config['name']}门禁条件满足")
            else:
                print(f"❌ {stage_config['name']}门禁条件不满足")
                self.current_stage = 'FAILED'
                
        except Exception as e:
            print(f"❌ 阶段执行失败: {stage_config['name']} - {str(e)}")
            self.current_stage = 'FAILED'
    
    def execute_step(self, step_config, stage_config):
        """执行步骤"""
        pass
        
    def execute_stage_loop(self, stage_config):
        """执行阶段循环"""
        pass
```

### 🔄 Task执行模板

```python
class TaskExecutionTemplate:
    """Task执行标准模板"""
    
    def execute_single_task(self, task_config, todo_id):
        """单个Task执行模板"""
        
        # 1. 状态准备
        self.todo_manager.update_task_status(todo_id, 'in_progress')
        print(f"🚀 启动Task: {task_config['description']}")
        
        try:
            # 2. Task执行
            result = Task(
                description=task_config['description'],
                prompt=task_config['prompt']
            )
            
            # 3. 成功处理
            self.todo_manager.update_task_status(todo_id, 'completed')
            print(f"✅ Task完成: {task_config['description']}")
            
            return {
                'success': True,
                'result': result,
                'task_id': todo_id,
                'execution_time': self.get_execution_time()
            }
            
        except Exception as e:
            # 4. 失败处理
            print(f"❌ Task失败: {task_config['description']} - {str(e)}")
            return self.handle_task_failure(task_config, todo_id, e)
    
    def execute_parallel_tasks(self, task_configs):
        """并行Task执行模板"""
        
        # 1. 创建验证任务
        verification_tasks = []
        for i, config in enumerate(task_configs):
            todo_id = f"parallel_task_{i+1}"
            verification_task = {
                'content': f'    - {config["description"]}',
                'status': 'pending',
                'priority': 'high',
                'id': todo_id
            }
            verification_tasks.append(verification_task)
        
        self.todo_manager.add_tasks(verification_tasks)
        
        # 2. 并行执行
        results = []
        for i, config in enumerate(task_configs):
            todo_id = f"parallel_task_{i+1}"
            result = self.execute_single_task(config, todo_id)
            results.append(result)
        
        # 3. 结果整合
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"📊 并行Task执行完成: {success_count}/{total_count} 成功")
        
        return {
            'results': results,
            'success_rate': success_count / total_count,
            'total_count': total_count,
            'success_count': success_count
        }
    
    def handle_task_failure(self, task_config, todo_id, error):
        """Task失败处理模板"""
        
        print(f"🔄 启动回退机制...")
        
        try:
            # 尝试回退方法
            fallback_result = self.get_fallback_method(task_config['type'])(task_config)
            
            if fallback_result['success']:
                self.todo_manager.update_task_status(todo_id, 'completed')
                print(f"✅ 回退方法成功: {task_config['description']}")
            else:
                print(f"⚠️ 回退方法也失败，标记为需要人工干预")
            
            return fallback_result
            
        except Exception as fallback_error:
            print(f"❌ 回退方法异常: {str(fallback_error)}")
            return {'success': False, 'result': None, 'fallback_used': True}
```

### 🔁 循环控制模板

```python
class LoopControlTemplate:
    """循环控制标准模板"""
    
    def execute_managed_loop(self, loop_config, max_rounds=5):
        """标准循环执行模板"""
        
        current_round = 1
        loop_context = {'results': [], 'metrics': {}}
        
        while current_round <= max_rounds:
            print(f"\n--- 第{current_round}轮{loop_config['name']}循环 ---")
            
            # 1. 创建轮次任务
            round_tasks = self.create_standard_round_tasks(current_round, loop_config)
            self.todo_manager.add_tasks(round_tasks)
            
            # 2. 执行轮次操作
            round_result = self.execute_round_operation(current_round, loop_config, loop_context)
            
            # 3. 更新循环上下文
            loop_context['results'].append(round_result)
            loop_context['metrics'][f'round_{current_round}'] = round_result.get('metrics', {})
            
            # 4. 评估退出条件
            exit_evaluation = self.evaluate_exit_conditions(round_result, current_round, loop_context)
            
            # 5. 更新退出条件评估任务
            exit_task_id = f"exit_condition_round_{current_round}"
            self.todo_manager.update_task_status(exit_task_id, 'in_progress')
            
            if exit_evaluation['should_exit']:
                self.todo_manager.update_task_status(exit_task_id, 'completed')
                print(f"✅ 循环退出条件满足：{exit_evaluation['reason']}")
                break
            else:
                self.todo_manager.update_task_status(exit_task_id, 'completed')
                print(f"🔄 继续下一轮循环：{exit_evaluation['reason']}")
            
            # 6. 轮次清理
            self.cleanup_round_tasks(current_round)
            current_round += 1
        
        return {
            'total_rounds': current_round,
            'exit_reason': exit_evaluation.get('reason', 'max_rounds_reached'),
            'loop_context': loop_context
        }
    
    def create_standard_round_tasks(self, round_num, loop_config):
        """创建标准轮次任务"""
        return [
            {
                'content': f'第{round_num}轮{loop_config["operation_name"]}',
                'status': 'pending',
                'priority': 'high',
                'id': f'{loop_config["id"]}_operation_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮结果验证',
                'status': 'pending',
                'priority': 'high',
                'id': f'{loop_config["id"]}_verify_round_{round_num}'
            },
            {
                'content': f'第{round_num}轮退出条件评估',
                'status': 'pending',
                'priority': 'medium',
                'id': f'exit_condition_round_{round_num}'
            }
        ]
    
    def evaluate_exit_conditions(self, round_result, round_num, loop_context):
        """标准退出条件评估"""
        
        # 成功率检查
        if round_result.get('success_rate', 0) >= 0.8:
            return {
                'should_exit': True,
                'reason': f'成功率达标: {round_result["success_rate"]:.2f}'
            }
        
        # 最大轮次检查
        if round_num >= 5:
            return {
                'should_exit': True,
                'reason': '达到最大轮次限制'
            }
        
        # 质量阈值检查
        if round_result.get('quality_score', 0) >= 0.7:
            return {
                'should_exit': True,
                'reason': f'质量分数达标: {round_result["quality_score"]:.2f}'
            }
        
        return {
            'should_exit': False,
            'reason': '条件未满足，继续下一轮'
        }
```

## 📊 最佳实践

### 🎯 1. 任务粒度控制

**原则**：合理的任务粒度平衡可视性和管理复杂度

```python
# ✅ 良好的任务粒度
GOOD_TASK_GRANULARITY = {
    'stage_level': {
        'count': 3-6,           # 3-6个主要阶段
        'description': '宏观进度跟踪',
        'example': '信息收集阶段、假设验证阶段、修复实施阶段'
    },
    'step_level': {
        'count': 2-5,           # 每阶段2-5个步骤
        'description': '具体执行内容',
        'example': '问题信息解析、并行搜索验证、门禁条件检查'
    },
    'verification_level': {
        'count': '动态',        # 根据需要动态创建
        'description': '细节质量保证',
        'example': 'Task验证、条件检查、结果确认'
    }
}

# ❌ 避免的问题
AVOID_PATTERNS = {
    'too_granular': '任务过于细碎，如为每个文件读取创建单独任务',
    'too_coarse': '任务过于宽泛，如整个流程只有一个任务',
    'inconsistent': '同一层级任务粒度不一致',
    'redundant': '任务之间有重叠和冗余'
}
```

### 🔄 2. 状态同步策略

**核心要求**：任务状态必须与实际执行状态保持一致

```python
class StateSyncBestPractices:
    """状态同步最佳实践"""
    
    def immediate_update_pattern(self):
        """立即更新模式"""
        
        # ✅ 正确的状态更新时机
        def execute_with_immediate_sync(task_config, todo_id):
            # 1. 开始前立即更新
            self.todo_manager.update_task_status(todo_id, 'in_progress')
            
            try:
                result = execute_actual_task(task_config)
                # 2. 完成后立即更新
                self.todo_manager.update_task_status(todo_id, 'completed')
                return result
            except Exception:
                # 3. 异常时立即处理状态
                fallback_result = try_fallback()
                if fallback_result['success']:
                    self.todo_manager.update_task_status(todo_id, 'completed')
                # 注意：失败时保持in_progress，等待人工干预
                return fallback_result
    
    def batch_sync_pattern(self):
        """批量同步模式（避免）"""
        
        # ❌ 错误的批量更新（会导致状态不一致）
        def wrong_batch_update():
            # 执行多个任务但不及时更新状态
            for task in tasks:
                execute_task(task)  # 状态仍然是pending
            
            # 最后统一更新状态 - 这会导致中间状态不准确
            for task in tasks:
                self.todo_manager.update_task_status(task['id'], 'completed')
    
    def force_sync_recovery(self):
        """强制同步恢复机制"""
        
        def periodic_state_check():
            """定期检查状态一致性"""
            for todo in self.todo_manager.current_todos:
                actual_status = get_actual_execution_status(todo['id'])
                if todo['status'] != actual_status:
                    print(f"⚠️ 状态不一致检测: {todo['id']}")
                    self.todo_manager.update_task_status(todo['id'], actual_status)
```

### 📈 3. 性能优化策略

**目标**：在保证功能完整性的前提下优化执行效率

```python
class PerformanceOptimization:
    """性能优化策略"""
    
    def task_count_control(self):
        """任务数量控制"""
        
        OPTIMAL_TASK_COUNTS = {
            'concurrent_display': 20,      # 同时显示的任务数量
            'parallel_tasks': 6,           # 并行Task数量
            'verification_tasks': 10,      # 验证任务数量
            'cleanup_threshold': 50        # 清理阈值
        }
        
        def manage_task_count():
            current_count = len(self.todo_manager.current_todos)
            
            if current_count > OPTIMAL_TASK_COUNTS['concurrent_display']:
                # 清理已完成的任务
                self.todo_manager.cleanup_completed_tasks()
            
            if current_count > OPTIMAL_TASK_COUNTS['cleanup_threshold']:
                # 强制清理
                self.todo_manager.force_cleanup_old_tasks()
    
    def memory_management(self):
        """内存管理"""
        
        def periodic_cleanup():
            """定期清理策略"""
            
            # 1. 清理已完成超过3轮的任务
            self.cleanup_old_completed_tasks(rounds_ago=3)
            
            # 2. 保留失败任务用于调试
            self.preserve_failed_tasks_for_debug()
            
            # 3. 压缩历史记录
            self.compress_task_history()
    
    def parallel_execution_optimization(self):
        """并行执行优化"""
        
        def optimize_parallel_tasks(task_configs):
            """优化并行Task配置"""
            
            # 根据Task类型分组
            task_groups = {
                'search': [],
                'verification': [],
                'analysis': []
            }
            
            for config in task_configs:
                task_groups[config['type']].append(config)
            
            # 控制每组的并行数量
            optimized_configs = []
            for group_type, configs in task_groups.items():
                max_parallel = {
                    'search': 4,
                    'verification': 6,
                    'analysis': 3
                }.get(group_type, 2)
                
                optimized_configs.extend(configs[:max_parallel])
            
            return optimized_configs
```

### 🔒 4. 质量保证机制

**目标**：确保流程执行的质量和可靠性

```python
class QualityAssurance:
    """质量保证机制"""
    
    def mandatory_verification(self):
        """强制验证机制"""
        
        def enforce_task_completion():
            """强制任务完成验证"""
            
            # ✅ 只有真正完成才能标记completed
            def mark_task_completed(task_id, result):
                if self.is_truly_completed(result):
                    self.todo_manager.update_task_status(task_id, 'completed')
                else:
                    # 部分完成或有问题时保持in_progress
                    print(f"⚠️ 任务未完全完成，保持in_progress状态: {task_id}")
            
            def is_truly_completed(result):
                """验证任务是否真正完成"""
                return (
                    result.get('success', False) and
                    result.get('verified', False) and
                    not result.get('has_errors', True)
                )
    
    def gate_condition_enforcement(self):
        """门禁条件强制执行"""
        
        def strict_gate_verification(stage, context):
            """严格的门禁验证"""
            
            gate_results = self.verify_all_gate_conditions(stage, context)
            
            # 100%通过才能继续
            if gate_results['pass_rate'] < 1.0:
                print(f"❌ 门禁条件不满足: {gate_results['pass_rate']:.2f}")
                return False
            
            # 记录质量指标
            self.record_quality_metrics(stage, gate_results)
            return True
    
    def error_prevention(self):
        """错误预防机制"""
        
        def validate_task_dependencies():
            """验证任务依赖关系"""
            
            for todo in self.todo_manager.current_todos:
                if todo.get('dependencies'):
                    for dep_id in todo['dependencies']:
                        dep_status = self.get_task_status(dep_id)
                        if dep_status != 'completed':
                            print(f"⚠️ 任务依赖未满足: {todo['id']} 依赖 {dep_id}")
                            return False
            return True
        
        def prevent_state_corruption():
            """防止状态损坏"""
            
            # 定期验证状态一致性
            inconsistencies = self.check_state_consistency()
            
            if inconsistencies:
                print(f"⚠️ 检测到状态不一致: {len(inconsistencies)}个")
                self.auto_fix_state_inconsistencies(inconsistencies)
```

## 🚀 应用指南

### 📋 如何基于此架构创建新流程

#### 1. 流程分析和设计

**步骤1：确定阶段结构**
```python
# 分析你的流程，确定主要阶段
flow_stages = {
    'stage1': {
        'name': '数据收集与验证',
        'has_loop': False,
        'steps': ['数据源识别', '数据提取', '数据验证']
    },
    'stage2': {
        'name': '数据处理循环',
        'has_loop': True,
        'max_rounds': 5,
        'steps': ['数据清理', '格式转换', '质量检查']
    },
    'stage3': {
        'name': '结果输出与确认',
        'has_loop': False,
        'steps': ['结果生成', '质量评估', '最终确认']
    }
}
```

**步骤2：定义Task集成点**
```python
# 确定哪些步骤需要使用Task技术
task_integration_points = {
    'stage1': {
        'data_source_discovery': {
            'type': 'search',
            'description': '智能搜索数据源',
            'parallel': True
        }
    },
    'stage2': {
        'data_quality_verification': {
            'type': 'verification',
            'description': '数据质量智能验证',
            'parallel': False
        }
    }
}
```

**步骤3：设计门禁条件**
```python
# 定义每个阶段的质量门禁
gate_conditions = {
    'stage1': {
        'data_completeness': {'threshold': 0.95, 'description': '数据完整性'},
        'source_reliability': {'threshold': 0.8, 'description': '数据源可靠性'}
    },
    'stage2': {
        'processing_accuracy': {'threshold': 0.9, 'description': '处理准确性'},
        'format_compliance': {'threshold': 1.0, 'description': '格式合规性'}
    }
}
```

#### 2. 实现流程控制器

```python
class DataProcessingFlow(MultiStageFlowController):
    """数据处理流程（基于通用架构）"""
    
    def __init__(self, input_data):
        # 使用通用架构的配置
        flow_config = {
            'name': '数据处理流程',
            'stages': self.define_stages()
        }
        super().__init__(flow_config)
        self.input_data = input_data
    
    def define_stages(self):
        """定义具体的阶段配置"""
        return [
            {
                'id': 'DATA_COLLECTION',
                'name': '数据收集与验证',
                'steps': [
                    {'id': 'discover_sources', 'name': '数据源发现'},
                    {'id': 'extract_data', 'name': '数据提取'},
                    {'id': 'validate_data', 'name': '数据验证'}
                ],
                'has_loop': False,
                'next_stage': 'DATA_PROCESSING'
            },
            {
                'id': 'DATA_PROCESSING', 
                'name': '数据处理循环',
                'steps': [
                    {'id': 'clean_data', 'name': '数据清理'},
                    {'id': 'transform_format', 'name': '格式转换'},
                    {'id': 'check_quality', 'name': '质量检查'}
                ],
                'has_loop': True,
                'max_rounds': 5,
                'next_stage': 'RESULT_OUTPUT'
            }
            # ... 其他阶段
        ]
    
    def execute_step(self, step_config, stage_config):
        """重写步骤执行逻辑"""
        
        step_method = getattr(self, f"execute_{step_config['id']}", None)
        if step_method:
            return step_method(step_config, stage_config)
        else:
            print(f"⚠️ 未找到步骤执行方法: {step_config['id']}")
    
    def execute_discover_sources(self, step_config, stage_config):
        """数据源发现步骤"""
        
        # 使用Task技术进行智能搜索
        task_config = {
            'description': '智能发现数据源',
            'prompt': f'分析输入数据并发现相关数据源: {self.input_data}',
            'type': 'search'
        }
        
        step_task_id = f"step_{stage_config['id']}_discover_sources"
        result = self.task_integration.execute_task_with_sync(task_config, step_task_id)
        
        # 更新上下文
        self.context['discovered_sources'] = result.get('result', [])
        
        return result
```

#### 3. 自定义组件扩展

```python
class CustomGateConditionManager(GateConditionManager):
    """自定义门禁条件管理器"""
    
    def get_gate_configurations(self, stage):
        """重写门禁配置以适应特定流程"""
        
        custom_gates = {
            'DATA_COLLECTION': {
                'data_volume_check': {
                    'description': '数据量检查',
                    'method': 'verify_data_volume',
                    'threshold': 1000
                },
                'source_availability': {
                    'description': '数据源可用性',
                    'method': 'verify_source_availability', 
                    'threshold': 0.9
                }
            },
            'DATA_PROCESSING': {
                'processing_quality': {
                    'description': '处理质量',
                    'method': 'verify_processing_quality',
                    'threshold': 0.85
                }
            }
        }
        
        return custom_gates.get(stage, {})
    
    def verify_data_volume(self, context):
        """自定义数据量验证"""
        data_count = len(context.get('processed_data', []))
        threshold = 1000
        return data_count >= threshold
    
    def verify_source_availability(self, context):
        """自定义数据源可用性验证"""
        sources = context.get('discovered_sources', [])
        available_sources = [s for s in sources if s.get('available', False)]
        availability_rate = len(available_sources) / len(sources) if sources else 0
        return availability_rate >= 0.9
```

### 🔧 组件复用和定制

#### 1. 任务管理器定制

```python
class CustomTodoWriteManager(TodoWriteManager):
    """定制化任务管理器"""
    
    def create_custom_task_template(self, task_type, config):
        """创建自定义任务模板"""
        
        templates = {
            'data_processing': {
                'content': f'数据处理: {config["operation"]}',
                'priority': 'high',
                'estimated_time': config.get('estimated_time', 300)
            },
            'quality_check': {
                'content': f'质量检查: {config["check_type"]}',
                'priority': 'medium',
                'validation_rules': config.get('rules', [])
            }
        }
        
        return templates.get(task_type, self.get_default_template())
    
    def add_custom_task_metadata(self, task, metadata):
        """为任务添加自定义元数据"""
        task.update({
            'created_at': datetime.now(),
            'estimated_duration': metadata.get('duration'),
            'dependencies': metadata.get('dependencies', []),
            'custom_fields': metadata.get('custom_fields', {})
        })
        return task
```

#### 2. Task集成定制

```python
class CustomTaskIntegration(TaskTodoIntegration):
    """定制化Task集成"""
    
    def add_custom_task_types(self):
        """添加自定义Task类型"""
        
        self.custom_task_handlers = {
            'data_analysis': self.handle_data_analysis_task,
            'format_conversion': self.handle_format_conversion_task,
            'quality_assessment': self.handle_quality_assessment_task
        }
    
    def handle_data_analysis_task(self, task_config):
        """处理数据分析类型的Task"""
        
        analysis_prompt = f"""
        分析以下数据并提供洞察：
        数据类型：{task_config['data_type']}
        分析目标：{task_config['analysis_goal']}
        数据样本：{task_config['data_sample']}
        """
        
        return Task(
            description=f"数据分析: {task_config['analysis_goal']}",
            prompt=analysis_prompt
        )
    
    def get_custom_fallback_method(self, task_type):
        """获取自定义回退方法"""
        
        fallback_methods = {
            'data_analysis': self.fallback_statistical_analysis,
            'format_conversion': self.fallback_manual_conversion,
            'quality_assessment': self.fallback_rule_based_check
        }
        
        return fallback_methods.get(task_type, self.default_fallback)
```

### 🔄 集成其他技术的扩展点

#### 1. 外部系统集成

```python
class ExternalSystemIntegration:
    """外部系统集成扩展"""
    
    def integrate_database_operations(self):
        """集成数据库操作"""
        
        def create_db_task(operation, params):
            """创建数据库操作任务"""
            return {
                'content': f'数据库操作: {operation}',
                'status': 'pending',
                'priority': 'high',
                'operation_type': 'database',
                'sql_command': params.get('sql'),
                'connection_params': params.get('connection')
            }
    
    def integrate_api_calls(self):
        """集成API调用"""
        
        def create_api_task(endpoint, method, data):
            """创建API调用任务"""
            return {
                'content': f'API调用: {method} {endpoint}',
                'status': 'pending',
                'priority': 'medium',
                'operation_type': 'api_call',
                'endpoint': endpoint,
                'method': method,
                'payload': data
            }
    
    def integrate_file_operations(self):
        """集成文件操作"""
        
        def create_file_task(operation, file_path):
            """创建文件操作任务"""
            return {
                'content': f'文件操作: {operation} {file_path}',
                'status': 'pending',
                'priority': 'low',
                'operation_type': 'file_operation',
                'file_path': file_path,
                'operation': operation
            }
```

#### 2. 监控和日志集成

```python
class MonitoringIntegration:
    """监控和日志集成"""
    
    def setup_task_monitoring(self):
        """设置任务监控"""
        
        def log_task_metrics(task_id, start_time, end_time, result):
            """记录任务指标"""
            metrics = {
                'task_id': task_id,
                'duration': (end_time - start_time).total_seconds(),
                'success': result.get('success', False),
                'memory_usage': self.get_memory_usage(),
                'timestamp': datetime.now()
            }
            
            # 发送到监控系统
            self.send_to_monitoring_system(metrics)
    
    def setup_progress_reporting(self):
        """设置进度报告"""
        
        def generate_progress_report():
            """生成进度报告"""
            total_tasks = len(self.todo_manager.current_todos)
            completed_tasks = len([t for t in self.todo_manager.current_todos 
                                 if t['status'] == 'completed'])
            
            progress = {
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'progress_percentage': (completed_tasks / total_tasks) * 100 if total_tasks > 0 else 0,
                'estimated_completion': self.estimate_completion_time()
            }
            
            return progress
```
