---
description: 基于症状驱动的问题诊断与修复状态机流程
---

# 🔍 问题诊断与修复流程

## 📋 核心理念

使用有限状态机(FSM)模型，确保诊断流程按照严格的状态转换路径执行，同时通过症状驱动方法论增强针对性诊断能力。

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理
- ✅ 症状驱动假设：基于具体问题症状生成针对性假设，避免通用假设的盲目性

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：
- 文件读取、编辑、搜索、修改
- 代码分析和静态检查
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证

**不能做的**：
- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 工具调用规范

**正确的function calls方式**：
```xml
<!-- Glob工具调用 -->
<function_calls>
<invoke name="Glob">
<parameter name="pattern">**/*.py</parameter>
</invoke>
</function_calls>

<!-- Grep工具调用 -->
<function_calls>
<invoke name="Grep">
<parameter name="pattern">connection.*timeout</parameter>
<parameter name="include">*.py</parameter>
</invoke>
</function_calls>

<!-- Read工具调用 -->
<function_calls>
<invoke name="Read">
<parameter name="file_path">/path/to/file.py</parameter>
</invoke>
</function_calls>

<!-- Task工具调用 -->
<function_calls>
<invoke name="Task">
<parameter name="description">复杂搜索任务</parameter>
<parameter name="prompt">详细的任务描述</parameter>
</invoke>
</function_calls>
```

**禁止的错误方式**：
```python
# ❌ 错误：不要使用import调用
from tools import Glob, Grep, Read
files = Glob(pattern="**/*.py").execute()
```

### 依赖检测规则

**避免误判原则**：
- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：
- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **分离假设-验证** - 禁止将假设建立和验证人为分离，必须保持完整循环
- ❌ **人工循环决策** - 禁止在循环中设置人工决策点，必须基于客观条件自动循环
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖
- ❌ **通用假设盲目性** - 禁止使用与具体问题症状无关的通用假设

## 🎛️ 状态机定义

```python
class DiagnosticStateMachine:
    """诊断修复状态机 - 症状驱动增强版"""
    
    STATES = {
        # 初始化
        'INIT': 'Initial State - 流程启动',
        
        # 阶段1: 症状解析与信息收集
        'STAGE1_COLLECTING': 'Stage 1 - 症状解析与信息收集',
        
        # 阶段2: 症状驱动假设-验证循环（2-5轮，每轮3步）
        'STAGE2_HYPOTHESIS_LOOP': 'Stage 2 - 症状驱动假设验证循环',
        
        # 阶段3: 修复实施与验证循环（1-5轮，每轮4步）
        'STAGE3_FIX_LOOP': 'Stage 3 - 修复实施验证循环',
        
        # 阶段4: 总结与发散优化
        'STAGE4_SUMMARY': 'Stage 4 - 总结与发散优化',
        
        # 终结状态
        'COMPLETED': 'Completion - 流程完成',
        'FAILED': 'Failed - 流程失败'
    }
    
    TRANSITIONS = {
        'INIT': ['STAGE1_COLLECTING'],
        'STAGE1_COLLECTING': ['STAGE2_HYPOTHESIS_LOOP', 'FAILED'],
        'STAGE2_HYPOTHESIS_LOOP': ['STAGE3_FIX_LOOP', 'FAILED'],
        'STAGE3_FIX_LOOP': ['STAGE4_SUMMARY', 'FAILED'],
        'STAGE4_SUMMARY': ['COMPLETED', 'FAILED'],
        'COMPLETED': [],
        'FAILED': []
    }
```

## 🏗️ 状态机流程图

```mermaid
stateDiagram-v2
    [*] --> INIT
    INIT --> STAGE1_COLLECTING: 环境检查通过
    
    STAGE1_COLLECTING --> STAGE2_HYPOTHESIS_LOOP: 阶段1门禁条件满足
    STAGE1_COLLECTING --> FAILED: 症状解析失败
    
    STAGE2_HYPOTHESIS_LOOP --> STAGE2_HYPOTHESIS_LOOP: 循环2-5轮，每轮3步
    STAGE2_HYPOTHESIS_LOOP --> STAGE3_FIX_LOOP: 阶段2门禁条件满足
    STAGE2_HYPOTHESIS_LOOP --> FAILED: 症状驱动假设验证失败
    
    STAGE3_FIX_LOOP --> STAGE3_FIX_LOOP: 循环1-5轮，每轮4步
    STAGE3_FIX_LOOP --> STAGE4_SUMMARY: 阶段3门禁条件满足
    STAGE3_FIX_LOOP --> FAILED: 修复验证失败
    
    STAGE4_SUMMARY --> COMPLETED: 阶段4完成条件满足
    STAGE4_SUMMARY --> FAILED: 总结不完整
    
    COMPLETED --> [*]
    FAILED --> [*]
```

---

## 🔄 状态实现

### **STATE: INIT - 初始化状态**

```python
class InitState:
    """初始状态 - 流程启动和环境检查"""
    
    def __init__(self, $ARGUMENTS):
        self.$ARGUMENTS = $ARGUMENTS
        self.state_name = "INIT"
    
    def can_enter(self, previous_state=None):
        """进入条件检查"""
        return previous_state is None  # 只能从空状态进入
    
    def execute(self):
        """执行状态操作"""
        print(f"🚀 [{self.state_name}] 诊断流程启动")
        print(f"📝 问题描述: {self.$ARGUMENTS}")
        
        # 执行环境检查
        env_check = self.check_environment()
        print(f"🔧 环境检查: {'✅ 通过' if env_check else '❌ 失败'}")
        
        # 初始化TodoWrite
        self.init_todo_tracking()
        print("📋 任务跟踪初始化完成")
        
        return {
            "success": True,
            "environment_ok": env_check,
            "problem_description": self.$ARGUMENTS,
            "next_state": "STAGE1_COLLECTING"
        }
    
    def can_exit_to(self, next_state):
        """退出条件检查"""
        return next_state == "STAGE1_COLLECTING"
    
    def check_environment(self):
        """检查执行环境"""
        print("  正在执行环境检查...")
        
        checks = {
            "working_directory": self.check_working_dir(),
            "basic_tools": self.check_basic_tools(),
            "project_structure": self.check_project_structure(),
            "service_status": self.check_service_status()
        }
        
        for check_name, result in checks.items():
            status = "✅" if result else "❌"
            print(f"  └─ {check_name}: {status}")
        
        return all(checks.values())
    
    def check_service_status(self):
        """检查后端服务状态并智能启动"""
        try:
            # 步骤1: 服务状态检测 - 使用function call方式
            print("    检查服务状态...")
            
            # 注意：在实际执行时，这里需要使用真实的function call
            # 这里用伪代码表示调用逻辑
            check_result = self.call_function_tool(
                tool_name="Bash",
                command='curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs',
                description="检查服务状态"
            )
            
            if check_result["success"] and check_result["output"].strip() == "200":
                print("    服务已运行 (HTTP 200)")
                return True
            
            print("    服务未运行，需要手动启动mock_server")
            print("    命令: cd mock_server && python src/main.py")
            return False
                
        except Exception as e:
            print(f"    ❌ 服务检查异常: {str(e)}")
            print("    请手动在Windows环境中启动mock_server")
            return False
    
    def call_function_tool(self, tool_name, **kwargs):
        """调用function tool的wrapper方法"""
        # 在实际执行时，这里需要转换为真实的function call
        # 这里用伪代码表示调用逻辑
        print(f"    调用{tool_name}工具...")
        return {"success": True, "output": "200"}  # 模拟返回
    
    def init_todo_tracking(self):
        """初始化任务跟踪"""
        # 在实际执行时需要调用TodoWrite function call
        todos = [
            {"content": f"诊断状态: {self.state_name}", "status": "completed", "priority": "high", "id": "1"},
            {"content": "下一状态: STAGE1_COLLECTING", "status": "pending", "priority": "high", "id": "2"}
        ]
        return todos
    
    def check_working_dir(self):
        """检查工作目录"""
        # 使用function call检查当前工作目录
        return True  # 简化实现
    
    def check_basic_tools(self):
        """检查基础工具可用性"""
        # 检查Glob、Grep、Read、Task等工具
        return True  # 简化实现
    
    def check_project_structure(self):
        """检查项目结构"""
        # 使用Glob检查基本项目结构
        return True  # 简化实现
```

### **STATE: STAGE1_COLLECTING - 阶段1：症状解析与信息收集**

```python
class Stage1CollectingState:
    """阶段1状态 - 症状解析与信息收集 (症状驱动增强版)
    
    主要改进：
    - 症状解析驱动搜索策略
    - 针对性信息收集
    - 并发工具调用优化
    - 强制真实检查机制
    """
    
    def __init__(self, $ARGUMENTS):
        self.$ARGUMENTS = $ARGUMENTS
        self.state_name = "STAGE1_COLLECTING"
        self.collection_results = {}
        self.extracted_symptoms = []
    
    def can_enter(self, previous_state):
        """进入条件检查"""
        return previous_state == "INIT"
    
    def execute(self):
        """执行阶段1：症状解析与信息收集"""
        print(f"🔍 [{self.state_name}] 阶段1：症状解析与信息收集")
        print("⚠️ 症状驱动增强：本阶段基于具体症状进行针对性信息搜索")
        
        # 更新Todo状态
        self.update_todo_status("in_progress")
        
        # 步骤1.1: 症状解析与分析
        symptom_analysis = self.step_1_1_symptom_analysis()
        
        # 步骤1.2: 症状驱动信息搜索（并发执行）
        search_results = self.step_1_2_symptom_driven_search()
        
        # 步骤1.3: 搜索结果验证与整合
        verification_results = self.step_1_3_search_verification()
        
        # 评估阶段1完成质量
        stage1_quality = self.evaluate_stage1_quality()
        print(f"📊 阶段1质量评分: {stage1_quality:.2f}/1.0")
        
        # 检查阶段1→阶段2门禁条件
        gate_check = self.check_stage1_to_stage2_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE2_HYPOTHESIS_LOOP"
            print("✅ 阶段1门禁条件满足，进入阶段2")
        else:
            next_state = "FAILED"
            print("❌ 阶段1门禁条件不满足，流程失败")
            print(f"失败原因: {gate_check['failure_reasons']}")
        
        self.update_todo_status("completed")
        
        return {
            "success": gate_check["can_proceed"],
            "symptom_analysis": symptom_analysis,
            "search_results": search_results,
            "verification_results": verification_results,
            "stage1_quality": stage1_quality,
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def step_1_1_symptom_analysis(self):
        """步骤1.1: 症状解析与分析 (核心改进)"""
        print("执行步骤1.1: 症状解析与分析...")
        
        # 1.1.1 核心症状提取
        core_symptoms = self.extract_core_symptoms()
        self.extracted_symptoms = core_symptoms
        
        # 1.1.2 症状分类与优先级
        symptom_classification = self.classify_symptoms(core_symptoms)
        
        # 1.1.3 症状关联分析
        symptom_relations = self.analyze_symptom_relations(core_symptoms)
        
        # 1.1.4 搜索策略生成
        search_strategy = self.generate_symptom_driven_search_strategy(
            core_symptoms, symptom_classification
        )
        
        print(f"  提取核心症状：{len(core_symptoms)}个")
        print(f"  症状分类：{len(symptom_classification['categories'])}类")
        print(f"  搜索策略：{len(search_strategy['file_patterns'])}个文件模式，{len(search_strategy['content_patterns'])}个内容模式")
        
        for i, symptom in enumerate(core_symptoms[:3], 1):  # 显示前3个症状
            print(f"    症状{i}: {symptom['text']} (类型: {symptom['type']}, 优先级: {symptom['priority']})")
        
        return {
            "core_symptoms": core_symptoms,
            "symptom_classification": symptom_classification,
            "symptom_relations": symptom_relations,
            "search_strategy": search_strategy
        }
    
    def extract_core_symptoms(self):
        """核心症状提取"""
        problem_desc = str(self.$ARGUMENTS).lower()
        symptoms = []
        
        # 错误信息症状
        error_patterns = [
            r'error[:\s]*(.*?)(?:[.\n]|$)',
            r'exception[:\s]*(.*?)(?:[.\n]|$)',
            r'fail[a-z]*[:\s]*(.*?)(?:[.\n]|$)',
            r'timeout[:\s]*(.*?)(?:[.\n]|$)',
            r'连接[超失]时',
            r'数据库.*[错误失败]',
            r'登录.*[失败错误]'
        ]
        
        # 功能异常症状
        function_patterns = [
            r'无法.*?([登录连接访问打开].*?)(?:[.\n]|$)',
            r'不能.*?([执行运行启动].*?)(?:[.\n]|$)',
            r'(用户|管理员|系统).*?[异常错误]'
        ]
        
        # 性能问题症状
        performance_patterns = [
            r'(慢|卡|延迟|超时)',
            r'(响应时间|加载时间).*?([0-9]+)',
            r'CPU.*?([0-9]+%)',
            r'内存.*?([0-9]+%)'
        ]
        
        import re
        
        # 提取症状
        for pattern_set, symptom_type in [
            (error_patterns, "error"),
            (function_patterns, "function"),
            (performance_patterns, "performance")
        ]:
            for pattern in pattern_set:
                matches = re.findall(pattern, problem_desc, re.IGNORECASE)
                for match in matches:
                    symptom_text = match if isinstance(match, str) else ' '.join(filter(None, match))
                    if symptom_text.strip():
                        symptoms.append({
                            "text": symptom_text.strip(),
                            "type": symptom_type,
                            "priority": self.calculate_symptom_priority(symptom_text, symptom_type),
                            "keywords": self.extract_keywords_from_symptom(symptom_text)
                        })
        
        # 如果没有提取到具体症状，提取关键词作为通用症状
        if not symptoms:
            words = re.findall(r'\b\w{3,}\b', problem_desc)
            for word in words[:5]:  # 最多取5个关键词
                symptoms.append({
                    "text": word,
                    "type": "keyword",
                    "priority": "medium",
                    "keywords": [word]
                })
        
        # 按优先级排序
        priority_order = {"high": 3, "medium": 2, "low": 1}
        symptoms.sort(key=lambda x: priority_order.get(x["priority"], 1), reverse=True)
        
        return symptoms[:10]  # 最多返回10个症状
    
    def calculate_symptom_priority(self, symptom_text, symptom_type):
        """计算症状优先级"""
        high_priority_keywords = ["error", "fail", "exception", "timeout", "crash", "无法", "失败", "错误", "异常"]
        medium_priority_keywords = ["slow", "delay", "warning", "慢", "延迟", "警告"]
        
        symptom_lower = symptom_text.lower()
        
        for keyword in high_priority_keywords:
            if keyword in symptom_lower:
                return "high"
        
        for keyword in medium_priority_keywords:
            if keyword in symptom_lower:
                return "medium"
        
        return "low"
    
    def extract_keywords_from_symptom(self, symptom_text):
        """从症状中提取关键词"""
        import re
        words = re.findall(r'\b\w{3,}\b', symptom_text.lower())
        # 过滤掉常见停用词
        stop_words = {"the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"}
        return [word for word in words if word not in stop_words]
    
    def classify_symptoms(self, symptoms):
        """症状分类与优先级"""
        categories = {
            "error": [],
            "function": [],
            "performance": [],
            "keyword": []
        }
        
        for symptom in symptoms:
            categories[symptom["type"]].append(symptom)
        
        return {
            "categories": categories,
            "primary_type": max(categories.keys(), key=lambda k: len(categories[k])),
            "total_count": len(symptoms)
        }
    
    def analyze_symptom_relations(self, symptoms):
        """症状关联分析"""
        relations = []
        
        # 查找关键词重叠的症状
        for i, symptom1 in enumerate(symptoms):
            for j, symptom2 in enumerate(symptoms[i+1:], i+1):
                common_keywords = set(symptom1["keywords"]) & set(symptom2["keywords"])
                if common_keywords:
                    relations.append({
                        "symptom1": symptom1["text"],
                        "symptom2": symptom2["text"],
                        "common_keywords": list(common_keywords),
                        "relation_strength": len(common_keywords)
                    })
        
        return relations
    
    def generate_symptom_driven_search_strategy(self, symptoms, classification):
        """生成症状驱动的搜索策略 (核心改进)"""
        file_patterns = []
        content_patterns = []
        
        # 基于症状类型生成文件模式
        for symptom in symptoms:
            keywords = symptom["keywords"]
            
            for keyword in keywords:
                # 文件名模式
                file_patterns.extend([
                    f"**/*{keyword}*.py",
                    f"**/*{keyword}*.json",
                    f"**/*{keyword}*.yaml",
                    f"**/*{keyword}*.log"
                ])
                
                # 内容搜索模式
                content_patterns.extend([
                    keyword,
                    f"{keyword}.*error",
                    f"def.*{keyword}",
                    f"class.*{keyword}",
                    f"{keyword}.*config"
                ])
        
        # 添加通用重要文件模式
        file_patterns.extend([
            "**/main.py",
            "**/app.py", 
            "**/server.py",
            "**/*router*.py",
            "**/*api*.py",
            "**/*database*.py",
            "**/*config*.py",
            "**/settings*.py",
            "**/*.env*"
        ])
        
        # 去重
        file_patterns = list(set(file_patterns))
        content_patterns = list(set(content_patterns))
        
        return {
            "file_patterns": file_patterns[:20],  # 限制数量避免过多
            "content_patterns": content_patterns[:15],
            "search_priority": "symptom_driven"  # 标记为症状驱动搜索
        }
    
    def step_1_2_symptom_driven_search(self):
        """步骤1.2: 症状驱动信息搜索 (并发执行优化)"""
        print("执行步骤1.2: 症状驱动信息搜索...")
        print("⚠️ 症状驱动：基于提取的症状进行针对性搜索，而非通用搜索")
        
        # 获取搜索策略
        search_strategy = self.collection_results.get("symptom_analysis", {}).get("search_strategy", {})
        
        # 并发执行多种搜索（这里用伪代码表示，实际执行时需要真实的function calls）
        search_results = {
            "glob_results": self.execute_symptom_driven_glob_search(search_strategy),
            "grep_results": self.execute_symptom_driven_grep_search(search_strategy),
            "task_results": self.execute_complex_symptom_search(),
            "read_results": self.execute_targeted_file_reading()
        }
        
        # 搜索结果汇总
        total_files = search_results["glob_results"]["total_files"]
        total_matches = search_results["grep_results"]["total_matches"]
        
        print(f"  症状驱动搜索统计:")
        print(f"    文件发现: {total_files}个")
        print(f"    内容匹配: {total_matches}处")
        print(f"    复杂搜索: {'完成' if search_results['task_results']['success'] else '失败'}")
        print(f"    目标文件: {len(search_results['read_results']['analyzed_files'])}个")
        
        self.collection_results["search_results"] = search_results
        
        return search_results
    
    def execute_symptom_driven_glob_search(self, search_strategy):
        """执行症状驱动的Glob搜索"""
        # 在实际执行时，这里需要使用真实的function calls
        # 示例：
        # <function_calls>
        # <invoke name="Glob">
        # <parameter name="pattern">**/*database*.py</parameter>
        # </invoke>
        # </function_calls>
        
        file_patterns = search_strategy.get("file_patterns", [])
        glob_results = {"files_by_pattern": {}, "total_files": 0}
        
        print(f"    执行Glob搜索 - {len(file_patterns)}个症状驱动模式")
        
        # 这里应该是并发的function calls
        for pattern in file_patterns[:10]:  # 限制模式数量
            # 模拟调用结果
            mock_files = [f"/mock/path/{pattern.replace('**/', '').replace('*.', 'file.')}" for _ in range(2)]
            glob_results["files_by_pattern"][pattern] = mock_files
            glob_results["total_files"] += len(mock_files)
        
        return glob_results
    
    def execute_symptom_driven_grep_search(self, search_strategy):
        """执行症状驱动的Grep搜索"""
        content_patterns = search_strategy.get("content_patterns", [])
        grep_results = {"matches_by_pattern": {}, "total_matches": 0}
        
        print(f"    执行Grep搜索 - {len(content_patterns)}个症状驱动模式")
        
        # 这里应该是并发的function calls
        for pattern in content_patterns[:10]:  # 限制模式数量
            # 模拟调用结果
            mock_matches = [f"/mock/path/file{i}.py" for i in range(3)]
            grep_results["matches_by_pattern"][pattern] = mock_matches
            grep_results["total_matches"] += len(mock_matches)
        
        return grep_results
    
    def execute_complex_symptom_search(self):
        """执行复杂症状搜索"""
        # 使用Task工具进行复杂的症状关联搜索
        symptoms_text = ', '.join([s["text"] for s in self.extracted_symptoms[:3]])
        
        # 实际执行时需要使用真实的Task function call
        task_prompt = f"""
        基于以下症状进行项目分析：{symptoms_text}
        
        请执行：
        1. 查找与这些症状相关的代码文件
        2. 分析可能的错误原因
        3. 识别相关的配置文件
        4. 检查日志文件是否存在
        """
        
        # 模拟Task调用结果
        return {
            "success": True,
            "findings": ["模拟发现1", "模拟发现2"],
            "related_files": ["file1.py", "file2.py"],
            "prompt_used": task_prompt
        }
    
    def execute_targeted_file_reading(self):
        """执行目标文件读取"""
        # 基于搜索结果读取关键文件
        read_results = {"analyzed_files": [], "key_findings": []}
        
        # 优先读取与症状最相关的文件
        priority_files = [
            "/mock/path/main.py",
            "/mock/path/database.py", 
            "/mock/path/config.py"
        ]
        
        for file_path in priority_files:
            # 实际执行时需要使用真实的Read function call
            read_results["analyzed_files"].append(file_path)
            read_results["key_findings"].append(f"分析{file_path}的关键发现")
        
        return read_results
    
    def step_1_3_search_verification(self):
        """步骤1.3: 搜索结果验证与整合"""
        print("执行步骤1.3: 搜索结果验证与整合...")
        
        search_results = self.collection_results.get("search_results", {})
        
        # 验证搜索完整性
        verification = {
            "file_coverage": self.verify_file_coverage(search_results),
            "content_relevance": self.verify_content_relevance(search_results),
            "symptom_alignment": self.verify_symptom_alignment(search_results)
        }
        
        print(f"  搜索验证结果:")
        print(f"    文件覆盖度: {verification['file_coverage']['score']:.2f}")
        print(f"    内容相关性: {verification['content_relevance']['score']:.2f}")
        print(f"    症状对齐度: {verification['symptom_alignment']['score']:.2f}")
        
        return verification
    
    def verify_file_coverage(self, search_results):
        """验证文件覆盖度"""
        total_files = search_results.get("glob_results", {}).get("total_files", 0)
        analyzed_files = len(search_results.get("read_results", {}).get("analyzed_files", []))
        
        score = min(total_files / 10, 1.0) * 0.7 + min(analyzed_files / 5, 1.0) * 0.3
        
        return {
            "score": score,
            "total_files": total_files,
            "analyzed_files": analyzed_files
        }
    
    def verify_content_relevance(self, search_results):
        """验证内容相关性"""
        total_matches = search_results.get("grep_results", {}).get("total_matches", 0)
        task_success = search_results.get("task_results", {}).get("success", False)
        
        score = min(total_matches / 20, 1.0) * 0.8 + (0.2 if task_success else 0)
        
        return {
            "score": score,
            "total_matches": total_matches,
            "task_success": task_success
        }
    
    def verify_symptom_alignment(self, search_results):
        """验证症状对齐度"""
        # 检查搜索结果是否与提取的症状一致
        symptom_keywords = set()
        for symptom in self.extracted_symptoms:
            symptom_keywords.update(symptom["keywords"])
        
        # 简化计算：假设搜索结果与症状关键词的重叠度
        alignment_score = min(len(symptom_keywords) / 5, 1.0)
        
        return {
            "score": alignment_score,
            "symptom_count": len(self.extracted_symptoms),
            "keyword_count": len(symptom_keywords)
        }
    
    def evaluate_stage1_quality(self):
        """评估阶段1完成质量"""
        symptom_score = min(len(self.extracted_symptoms) / 5, 1.0) * 0.3
        search_score = min(self.collection_results.get("search_results", {}).get("glob_results", {}).get("total_files", 0) / 10, 1.0) * 0.4
        verification_score = 0.8  # 简化评估
        
        return symptom_score + search_score + verification_score * 0.3
    
    def check_stage1_to_stage2_gate(self):
        """检查阶段1→阶段2门禁条件"""
        print("    执行阶段1→阶段2门禁条件检查...")
        
        gate_checks = {
            "症状提取完成": len(self.extracted_symptoms) > 0,
            "搜索策略生成": "search_strategy" in self.collection_results.get("symptom_analysis", {}),
            "多工具搜索执行": len(self.collection_results.get("search_results", {})) >= 3,
            "文件发现达标": self.collection_results.get("search_results", {}).get("glob_results", {}).get("total_files", 0) >= 3,
            "症状驱动验证": all(s.get("keywords") for s in self.extracted_symptoms[:3])
        }
        
        failure_reasons = []
        
        for check_name, passed in gate_checks.items():
            status = "✅" if passed else "❌"
            print(f"      {check_name}: {status}")
            if not passed:
                failure_reasons.append(check_name)
        
        passed_checks = sum(gate_checks.values())
        total_checks = len(gate_checks)
        can_proceed = passed_checks >= 4  # 至少通过4项检查
        
        print(f"    门禁条件检查结果: {passed_checks}/{total_checks} ({'✅ 通过' if can_proceed else '❌ 失败'})")
        
        return {
            "can_proceed": can_proceed,
            "gate_checks": gate_checks,
            "failure_reasons": failure_reasons,
            "passed_checks": passed_checks,
            "total_checks": total_checks
        }
    
    def update_todo_status(self, status):
        """更新Todo状态"""
        # 实际执行时需要使用TodoWrite function call
        print(f"  更新Todo状态: {status}")
```

### **STATE: STAGE2_HYPOTHESIS_LOOP - 阶段2：症状驱动假设验证循环**

```python
class Stage2HypothesisLoopState:
    """阶段2状态 - 症状驱动假设验证循环 (症状驱动增强版)
    
    主要改进：
    - 症状驱动假设建立（替代通用4层次假设）
    - 基于症状的针对性验证策略
    - 保持原有的循环控制机制
    """
    
    def __init__(self, stage1_data):
        self.stage1_data = stage1_data
        self.state_name = "STAGE2_HYPOTHESIS_LOOP"
        self.loop_results = []
        self.current_round = 0
        self.max_rounds = 5
        self.min_rounds = 2
        self.extracted_symptoms = stage1_data.get("symptom_analysis", {}).get("core_symptoms", [])
    
    def execute(self):
        """执行阶段2：症状驱动假设验证循环"""
        print(f"🔬 [{self.state_name}] 阶段2：症状驱动假设验证循环")
        print("⚠️ 症状驱动增强：基于阶段1提取的症状建立针对性假设，而非通用假设")
        
        # 初始化循环
        self.initialize_loop()
        
        # 执行强制循环
        while self.current_round < self.max_rounds:
            self.current_round += 1
            print(f"\n--- 第{self.current_round}轮症状驱动假设验证循环 ---")
            
            # 更新TodoWrite状态
            self.update_todo_round_status("in_progress")
            
            # 执行一轮完整的3步循环
            round_result = self.execute_round()
            self.loop_results.append(round_result)
            
            # 更新TodoWrite状态
            self.update_todo_round_status("completed")
            
            # 检查循环退出条件
            if self.should_exit_loop():
                print(f"  循环退出条件满足，结束假设验证（共{self.current_round}轮）")
                break
            
            # 强制最少2轮检查
            if self.current_round < self.min_rounds:
                print(f"  继续执行（要求最少{self.min_rounds}轮）")
                continue
        
        # 检查阶段2→阶段3门禁条件
        gate_check = self.check_stage2_to_stage3_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE3_FIX_LOOP"
            print("✅ 阶段2门禁条件满足，进入阶段3")
        else:
            next_state = "FAILED"
            print("❌ 阶段2门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_rounds": self.current_round,
            "loop_results": self.loop_results,
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def execute_round(self):
        """执行一轮完整的假设验证循环（3步）"""
        round_result = {
            "round_number": self.current_round,
            "step_results": {}
        }
        
        # 步骤2.1: 症状驱动假设建立与调整 (核心改进)
        step_2_1_result = self.step_2_1_symptom_driven_hypothesis_building()
        round_result["step_results"]["2.1"] = step_2_1_result
        
        # 步骤2.2: 假设验证执行
        step_2_2_result = self.step_2_2_hypothesis_verification(step_2_1_result["hypotheses"])
        round_result["step_results"]["2.2"] = step_2_2_result
        
        # 步骤2.3: 结果分析与新发现
        step_2_3_result = self.step_2_3_result_analysis(step_2_2_result)
        round_result["step_results"]["2.3"] = step_2_3_result
        
        # 统计本轮结果
        round_result["summary"] = {
            "hypotheses_tested": len(step_2_1_result["hypotheses"]),
            "confirmed_issues": step_2_2_result["confirmed_count"],
            "new_findings": step_2_3_result["new_findings_count"],
            "root_cause_found": step_2_3_result.get("root_cause_analysis") is not None
        }
        
        print(f"  第{self.current_round}轮统计: 测试假设{round_result['summary']['hypotheses_tested']}个，确认问题{round_result['summary']['confirmed_issues']}个，新发现{round_result['summary']['new_findings']}个")
        
        return round_result
    
    def step_2_1_symptom_driven_hypothesis_building(self):
        """步骤2.1: 症状驱动假设建立与调整 (核心改进)
        
        替代原来的通用4层次假设体系，采用症状驱动的假设建立方法
        """
        print(f"执行步骤2.1: 建立第{self.current_round}轮症状驱动假设...")
        
        if self.current_round == 1:
            # 第1轮：基于症状的系统性假设建立
            hypotheses = self.build_symptom_driven_hypotheses()
            print(f"  第1轮症状驱动假设建立: 基于{len(self.extracted_symptoms)}个症状建立针对性假设")
        else:
            # 第2-N轮：基于前轮结果的假设调整
            previous_round = self.loop_results[-1]
            hypotheses = self.adjust_hypotheses_based_on_results(previous_round)
            print(f"  第{self.current_round}轮动态假设调整: 基于前轮发现调整假设")
        
        # 假设优先级排序
        prioritized_hypotheses = self.prioritize_symptom_driven_hypotheses(hypotheses)
        
        print(f"  建立假设总数：{len(prioritized_hypotheses)}个")
        for i, hyp in enumerate(prioritized_hypotheses[:5], 1):  # 显示前5个高优先级
            print(f"    假设{i}: {hyp['description']} (症状关联: {hyp['symptom_relation']}, 优先级: {hyp['priority']})")
        
        if len(prioritized_hypotheses) > 5:
            print(f"    ... 另有{len(prioritized_hypotheses)-5}个假设")
        
        return {"hypotheses": prioritized_hypotheses}
    
    def build_symptom_driven_hypotheses(self):
        """基于症状的系统性假设建立 (核心改进)
        
        不再使用通用的4层次假设体系，而是基于具体症状生成针对性假设
        """
        print("    建立症状驱动假设体系...")
        
        hypotheses = []
        
        for symptom in self.extracted_symptoms:
            symptom_hypotheses = self.generate_hypotheses_for_symptom(symptom)
            hypotheses.extend(symptom_hypotheses)
        
        # 添加症状间关联假设
        correlation_hypotheses = self.generate_symptom_correlation_hypotheses()
        hypotheses.extend(correlation_hypotheses)
        
        print(f"    症状驱动假设体系构建完成: {len(hypotheses)}个假设")
        
        return hypotheses
    
    def generate_hypotheses_for_symptom(self, symptom):
        """为单个症状生成针对性假设"""
        symptom_text = symptom["text"]
        symptom_type = symptom["type"]
        keywords = symptom.get("keywords", [])
        
        hypotheses = []
        
        if symptom_type == "error":
            # 错误类症状的假设
            hypotheses.extend([
                {
                    "description": f"代码语法错误导致{symptom_text}",
                    "category": "代码层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"检查包含'{keyword}'的代码文件语法错误"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "代码静态分析和语法检查",
                    "priority": "high",
                    "verification_cost": "low"
                },
                {
                    "description": f"配置错误导致{symptom_text}",
                    "category": "配置层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"检查涉及'{keyword}'的配置项"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "配置文件验证和参数检查",
                    "priority": "high",
                    "verification_cost": "low"
                },
                {
                    "description": f"依赖或环境问题导致{symptom_text}",
                    "category": "环境层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"检查'{keyword}'相关的依赖和环境配置"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "依赖检查和环境验证",
                    "priority": "medium",
                    "verification_cost": "medium"
                }
            ])
        
        elif symptom_type == "function":
            # 功能异常症状的假设
            hypotheses.extend([
                {
                    "description": f"业务逻辑错误导致{symptom_text}",
                    "category": "逻辑层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"分析'{keyword}'相关的业务逻辑实现"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "业务逻辑分析和数据流追踪",
                    "priority": "high",
                    "verification_cost": "medium"
                },
                {
                    "description": f"接口调用问题导致{symptom_text}",
                    "category": "集成层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"验证涉及'{keyword}'的API接口调用"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "接口测试和调用链分析",
                    "priority": "high",
                    "verification_cost": "medium"
                }
            ])
        
        elif symptom_type == "performance":
            # 性能问题症状的假设
            hypotheses.extend([
                {
                    "description": f"资源消耗过高导致{symptom_text}",
                    "category": "性能层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"监控'{keyword}'相关的资源使用"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "性能监控和资源分析",
                    "priority": "medium",
                    "verification_cost": "high"
                },
                {
                    "description": f"算法效率问题导致{symptom_text}",
                    "category": "算法层",
                    "symptom_relation": symptom_text,
                    "specific_checks": [
                        f"分析'{keyword}'相关的算法复杂度"
                        for keyword in keywords[:3]
                    ],
                    "verification_method": "算法分析和复杂度评估",
                    "priority": "medium",
                    "verification_cost": "high"
                }
            ])
        
        else:  # keyword类型
            # 关键词症状的通用假设
            hypotheses.append({
                "description": f"与'{symptom_text}'相关的配置或实现问题",
                "category": "通用层",
                "symptom_relation": symptom_text,
                "specific_checks": [
                    f"全面检查'{keyword}'的使用情况"
                    for keyword in keywords[:3]
                ],
                "verification_method": "关键词搜索和上下文分析",
                "priority": "medium",
                "verification_cost": "low"
            })
        
        return hypotheses
    
    def generate_symptom_correlation_hypotheses(self):
        """生成症状间关联假设"""
        if len(self.extracted_symptoms) < 2:
            return []
        
        correlation_hypotheses = []
        
        # 查找共同关键词的症状组合
        for i, symptom1 in enumerate(self.extracted_symptoms):
            for symptom2 in self.extracted_symptoms[i+1:]:
                common_keywords = set(symptom1["keywords"]) & set(symptom2["keywords"])
                
                if common_keywords:
                    correlation_hypotheses.append({
                        "description": f"共同根因导致'{symptom1['text']}'和'{symptom2['text']}'",
                        "category": "关联层",
                        "symptom_relation": f"{symptom1['text']} + {symptom2['text']}",
                        "specific_checks": [
                            f"分析'{keyword}'在多个症状中的作用"
                            for keyword in list(common_keywords)[:3]
                        ],
                        "verification_method": "关联分析和根因追溯",
                        "priority": "high",
                        "verification_cost": "medium",
                        "common_keywords": list(common_keywords)
                    })
        
        return correlation_hypotheses
    
    def prioritize_symptom_driven_hypotheses(self, hypotheses):
        """症状驱动假设优先级排序"""
        print("    执行症状驱动假设优先级排序...")
        
        # 优先级权重计算
        priority_weights = {"high": 3, "medium": 2, "low": 1}
        cost_weights = {"low": 3, "medium": 2, "high": 1}  # 成本低的优先级高
        
        for hypothesis in hypotheses:
            # 计算基础得分
            priority_score = priority_weights.get(hypothesis.get("priority", "medium"), 2)
            cost_score = cost_weights.get(hypothesis.get("verification_cost", "medium"), 2)
            
            # 症状关联度得分
            symptom_relevance = self.calculate_symptom_relevance(hypothesis)
            
            # 症状优先级得分
            symptom_priority = self.calculate_related_symptom_priority(hypothesis)
            
            # 综合得分
            hypothesis["total_score"] = (
                priority_score * 0.3 + 
                cost_score * 0.2 + 
                symptom_relevance * 0.3 + 
                symptom_priority * 0.2
            )
        
        # 按得分排序
        sorted_hypotheses = sorted(hypotheses, key=lambda h: h["total_score"], reverse=True)
        
        print(f"    症状驱动优先级排序完成: 选择前{min(10, len(sorted_hypotheses))}个高优先级假设进行验证")
        
        return sorted_hypotheses[:10]  # 返回前10个最高优先级假设
    
    def calculate_symptom_relevance(self, hypothesis):
        """计算假设与症状的关联度"""
        symptom_relation = hypothesis.get("symptom_relation", "")
        
        # 检查假设是否直接关联到高优先级症状
        for symptom in self.extracted_symptoms:
            if symptom["text"] in symptom_relation:
                if symptom["priority"] == "high":
                    return 3.0
                elif symptom["priority"] == "medium":
                    return 2.0
                else:
                    return 1.0
        
        return 0.5  # 无直接关联
    
    def calculate_related_symptom_priority(self, hypothesis):
        """计算相关症状的优先级得分"""
        symptom_relation = hypothesis.get("symptom_relation", "")
        
        # 查找相关症状的优先级
        for symptom in self.extracted_symptoms:
            if any(keyword in symptom_relation for keyword in symptom.get("keywords", [])):
                if symptom["priority"] == "high":
                    return 3.0
                elif symptom["priority"] == "medium":
                    return 2.0
                else:
                    return 1.0
        
        return 1.0  # 默认得分
    
    # 注意：步骤2.2和2.3以及其他状态的实现保持与原流程相同
    # 这里省略完整实现，重点是展示症状驱动的改进部分
    
    def step_2_2_hypothesis_verification(self, hypotheses):
        """步骤2.2: 假设验证执行 (保持原有逻辑，使用正确的function calls)"""
        # 实现与原流程相同，但使用正确的function calls调用工具
        pass
    
    def step_2_3_result_analysis(self, verification_results):
        """步骤2.3: 结果分析与新发现 (保持原有逻辑)"""
        # 实现与原流程相同
        pass
    
    def should_exit_loop(self):
        """检查循环退出条件 (保持原有逻辑)"""
        # 实现与原流程相同
        pass
    
    def check_stage2_to_stage3_gate(self):
        """检查阶段2→阶段3门禁条件 (保持原有逻辑)"""
        # 实现与原流程相同
        pass
```

### **STATE: STAGE3_FIX_LOOP - 阶段3：修复实施与验证循环**

```python
class Stage3FixLoopState:
    """阶段3状态 - 修复实施验证循环
    
    完整实现阶段3的所有内容：
    - 强制执行1-5轮修复-验证循环
    - 每轮包含4个步骤：方案设计→方案实施→效果验证→结果评估
    - 目标：验证通过率100%且无副作用
    """
    
    def __init__(self, stage2_data):
        self.stage2_data = stage2_data
        self.state_name = "STAGE3_FIX_LOOP"
        self.loop_results = []
        self.current_attempt = 0
        self.max_attempts = 5
        self.min_attempts = 1
    
    def execute(self):
        """执行阶段3：修复实施与验证循环"""
        print(f"🔧 [{self.state_name}] 阶段3：修复实施与验证循环")
        print("⚠️ 强制要求：本阶段必须执行完整修复-验证循环机制（1-5轮）")
        
        # 初始化循环
        self.initialize_fix_loop()
        
        # 执行强制循环
        while self.current_attempt < self.max_attempts:
            self.current_attempt += 1
            print(f"\n--- 第{self.current_attempt}轮修复实施与验证 ---")
            
            # 更新TodoWrite状态
            self.update_todo_attempt_status("in_progress")
            
            # 执行一轮完整的4步循环
            attempt_result = self.execute_attempt()
            self.loop_results.append(attempt_result)
            
            # 更新TodoWrite状态
            self.update_todo_attempt_status("completed")
            
            # 检查是否达到100%验证通过
            if self.is_fully_verified():
                print(f"  所有修复完成且验证通过，结束修复（共{self.current_attempt}轮）")
                break
        
        # 检查阶段3→阶段4门禁条件
        gate_check = self.check_stage3_to_stage4_gate()
        
        if gate_check["can_proceed"]:
            next_state = "STAGE4_SUMMARY"
            print("✅ 阶段3门禁条件满足，进入阶段4")
        else:
            next_state = "FAILED"
            print("❌ 阶段3门禁条件不满足，流程失败")
        
        return {
            "success": gate_check["can_proceed"],
            "total_attempts": self.current_attempt,
            "loop_results": self.loop_results,
            "final_verification_rate": self.calculate_final_verification_rate(),
            "gate_check": gate_check,
            "next_state": next_state
        }
    
    def execute_attempt(self):
        """执行一轮完整的修复-验证循环（4步）"""
        attempt_result = {
            "attempt_number": self.current_attempt,
            "step_results": {}
        }
        
        # 步骤3.1: 修复方案设计与调整
        step_3_1_result = self.step_3_1_fix_design()
        attempt_result["step_results"]["3.1"] = step_3_1_result
        
        # 步骤3.2: 修复方案实施
        step_3_2_result = self.step_3_2_fix_implementation(step_3_1_result["fix_plans"])
        attempt_result["step_results"]["3.2"] = step_3_2_result
        
        # 步骤3.3: 修复效果验证
        step_3_3_result = self.step_3_3_fix_verification(step_3_2_result)
        attempt_result["step_results"]["3.3"] = step_3_3_result
        
        # 步骤3.4: 验证结果评估
        step_3_4_result = self.step_3_4_result_evaluation(step_3_3_result)
        attempt_result["step_results"]["3.4"] = step_3_4_result
        
        # 统计本轮结果
        attempt_result["summary"] = {
            "implementation_success_rate": step_3_2_result["success_rate"],
            "verification_pass_rate": step_3_3_result["pass_rate"],
            "side_effects_detected": step_3_4_result["side_effects_count"] > 0,
            "overall_success": step_3_3_result["pass_rate"] == 1.0 and step_3_4_result["side_effects_count"] == 0
        }
        
        print(f"  第{self.current_attempt}轮统计: 实施成功率{attempt_result['summary']['implementation_success_rate']*100:.1f}%, 验证通过率{attempt_result['summary']['verification_pass_rate']*100:.1f}%")
        
        return attempt_result
    
    def step_3_1_fix_design(self):
        """步骤3.1: 修复方案设计与调整"""
        print(f"执行步骤3.1: 设计第{self.current_attempt}轮修复方案...")
        
        if self.current_attempt == 1:
            # 第1轮：初始修复方案设计
            fix_plans = self.design_initial_fix_plans()
        else:
            # 第2-N轮：基于前轮评估的方案调整
            previous_attempt = self.loop_results[-1]
            fix_plans = self.adjust_fix_plans(previous_attempt)
        
        print(f"  设计修复方案：{len(fix_plans)}个")
        for i, plan in enumerate(fix_plans, 1):
            print(f"    方案{i}: {plan['description']} (类型: {plan['type']})")
        
        return {"fix_plans": fix_plans}
    
    def step_3_2_fix_implementation(self, fix_plans):
        """步骤3.2: 修复方案实施"""
        print(f"执行步骤3.2: 实施第{self.current_attempt}轮修复...")
        
        implementations = []
        successful_count = 0
        
        for i, plan in enumerate(fix_plans, 1):
            print(f"  执行修复{i}: {plan['description']}")
            
            try:
                # 实施完整的真实修复（禁止简化、临时方案、伪代码）
                implementation = self.implement_complete_fix(plan)
                implementations.append(implementation)
                
                if implementation["success"]:
                    successful_count += 1
                    print(f"    ✅ 修复成功: {len(implementation['edit_results'])}个文件修改")
                else:
                    print(f"    ❌ 修复失败: {implementation['error_message']}")
                    
            except Exception as e:
                print(f"    ❌ 修复异常: {str(e)}")
                implementations.append({"success": False, "error": str(e)})
        
        success_rate = successful_count / len(fix_plans) if fix_plans else 0
        print(f"  实施统计: 成功率{success_rate*100:.1f}%")
        
        return {
            "implementations": implementations,
            "successful_count": successful_count,
            "total_plans": len(fix_plans),
            "success_rate": success_rate
        }
    
    def step_3_3_fix_verification(self, implementation_result):
        """步骤3.3: 修复效果验证"""
        print(f"执行步骤3.3: 验证第{self.current_attempt}轮修复...")
        
        # 系统功能验证
        verification_results = self.perform_comprehensive_verification()
        
        # 计算验证通过率
        pass_rate = self.calculate_verification_pass_rate(verification_results)
        
        print(f"  验证统计: 通过率{pass_rate*100:.1f}%")
        
        return {
            "verification_results": verification_results,
            "pass_rate": pass_rate,
            "details": verification_results
        }
    
    def step_3_4_result_evaluation(self, verification_result):
        """步骤3.4: 验证结果评估"""
        print(f"执行步骤3.4: 评估第{self.current_attempt}轮结果...")
        
        # 副作用检测
        side_effects = self.detect_side_effects()
        
        # 修复质量评估
        quality_assessment = self.assess_fix_quality(verification_result)
        
        print(f"  副作用检测: {len(side_effects)}个问题")
        print(f"  质量评估: {quality_assessment['score']:.2f}/1.0")
        
        return {
            "side_effects": side_effects,
            "side_effects_count": len(side_effects),
            "quality_assessment": quality_assessment
        }
    
    def implement_complete_fix(self, plan):
        """实施完整修复 - 使用正确的function calls"""
        implementation = {
            "success": False,
            "edit_results": [],
            "error_message": ""
        }
        
        try:
            # 基于修复方案执行真实的文件编辑
            # 这里需要使用真实的Edit或MultiEdit function calls
            
            # 示例：修复代码文件
            if plan["type"] == "code_fix":
                edit_results = self.perform_code_edits(plan)
                implementation["edit_results"] = edit_results
                implementation["success"] = len(edit_results) > 0
            
            # 示例：修复配置文件
            elif plan["type"] == "config_fix":
                config_results = self.perform_config_edits(plan)
                implementation["edit_results"] = config_results
                implementation["success"] = len(config_results) > 0
            
            else:
                implementation["error_message"] = f"未知修复类型: {plan['type']}"
        
        except Exception as e:
            implementation["error_message"] = str(e)
        
        return implementation
    
    def perform_code_edits(self, plan):
        """执行代码修复编辑"""
        # 实际执行时需要使用真实的Edit function calls
        edit_results = []
        
        for edit_action in plan.get("edit_actions", []):
            # <function_calls>
            # <invoke name="Edit">
            # <parameter name="file_path">{edit_action['file_path']}</parameter>
            # <parameter name="old_string">{edit_action['old_string']}</parameter>
            # <parameter name="new_string">{edit_action['new_string']}</parameter>
            # </invoke>
            # </function_calls>
            
            # 模拟编辑结果
            edit_results.append({
                "file": edit_action.get("file_path", "unknown"),
                "status": "success"
            })
        
        return edit_results
    
    def perform_config_edits(self, plan):
        """执行配置修复编辑"""
        # 实际执行时需要使用真实的Edit function calls
        return [{"file": "config.json", "status": "success"}]
    
    def perform_comprehensive_verification(self):
        """执行全面验证"""
        verification_results = {
            "service_verification": self.verify_service_functionality(),
            "integration_verification": self.verify_integration_points(),
            "regression_verification": self.verify_no_regression()
        }
        
        return verification_results
    
    def verify_service_functionality(self):
        """验证服务功能"""
        # 使用function calls验证服务功能
        return {"status": "pass", "details": "服务功能正常"}
    
    def verify_integration_points(self):
        """验证集成点"""
        return {"status": "pass", "details": "集成点正常"}
    
    def verify_no_regression(self):
        """验证无回归"""
        return {"status": "pass", "details": "无回归问题"}
    
    def calculate_verification_pass_rate(self, verification_results):
        """计算验证通过率"""
        total_checks = len(verification_results)
        passed_checks = sum(1 for result in verification_results.values() if result.get("status") == "pass")
        return passed_checks / total_checks if total_checks > 0 else 0
    
    def detect_side_effects(self):
        """检测副作用"""
        # 检测修复后是否产生新问题
        return []  # 简化实现
    
    def assess_fix_quality(self, verification_result):
        """评估修复质量"""
        return {"score": verification_result["pass_rate"]}
    
    def is_fully_verified(self):
        """检查是否完全验证通过"""
        if not self.loop_results:
            return False
        
        latest_result = self.loop_results[-1]
        return latest_result["summary"]["overall_success"]
    
    def calculate_final_verification_rate(self):
        """计算最终验证通过率"""
        if not self.loop_results:
            return 0.0
        
        return self.loop_results[-1]["summary"]["verification_pass_rate"]
    
    def check_stage3_to_stage4_gate(self):
        """检查阶段3→阶段4门禁条件"""
        # 门禁条件：验证通过率100% + 无副作用确认 + TodoWrite记录修复内容
        
        final_result = self.loop_results[-1] if self.loop_results else {}
        
        conditions = {
            "验证通过率100%": final_result.get("summary", {}).get("verification_pass_rate", 0) == 1.0,
            "无副作用确认": not final_result.get("summary", {}).get("side_effects_detected", True),
            "修复完整实施": final_result.get("summary", {}).get("implementation_success_rate", 0) == 1.0,
            "循环4步完整": all(f"3.{i}" in final_result.get("step_results", {}) for i in range(1, 5))
        }
        
        print("🤔 阶段3→阶段4门禁检查:")
        for condition, passed in conditions.items():
            print(f"  {condition}: {'✅' if passed else '❌'}")
        
        can_proceed = all(conditions.values())
        failure_reasons = [cond for cond, passed in conditions.items() if not passed]
        
        return {
            "can_proceed": can_proceed,
            "conditions": conditions,
            "failure_reasons": failure_reasons
        }
    
    def design_initial_fix_plans(self):
        """设计初始修复方案"""
        # 基于阶段2的问题发现设计修复方案
        return [
            {
                "description": "修复代码语法错误",
                "type": "code_fix",
                "edit_actions": []
            }
        ]
    
    def adjust_fix_plans(self, previous_attempt):
        """调整修复方案"""
        # 基于前轮结果调整方案
        return self.design_initial_fix_plans()
    
    def initialize_fix_loop(self):
        """初始化修复循环"""
        print("  初始化修复循环...")
    
    def update_todo_attempt_status(self, status):
        """更新Todo状态"""
        # 实际执行时需要使用TodoWrite function call
        print(f"  更新Todo状态: {status}")
```

### **STATE: STAGE4_SUMMARY - 阶段4：总结与发散优化**

```python
class Stage4SummaryState:
    """阶段4状态 - 总结与发散优化
    
    完整实现阶段4的所有内容：
    - 诊断过程总结
    - 解决方案总结  
    - 类似问题预防建议
    - 系统性优化建议
    - 发散性改进建议
    """
    
    def __init__(self, stage3_data):
        self.stage3_data = stage3_data
        self.state_name = "STAGE4_SUMMARY"
        self.summary_results = {}
    
    def execute(self):
        """执行阶段4：总结与发散优化"""
        print(f"📋 [{self.state_name}] 阶段4：总结与发散优化")
        print("⚠️ 强制要求：本阶段必须完成工作总结和发散性优化建议")
        
        # 更新Todo状态
        self.update_todo_status("in_progress")
        
        # 步骤4.1: 诊断过程总结
        step_4_1_result = self.step_4_1_diagnostic_summary()
        
        # 步骤4.2: 解决方案总结
        step_4_2_result = self.step_4_2_solution_summary()
        
        # 步骤4.3: 类似问题预防建议
        step_4_3_result = self.step_4_3_prevention_suggestions()
        
        # 步骤4.4: 系统性优化建议
        step_4_4_result = self.step_4_4_optimization_suggestions()
        
        # 步骤4.5: 发散性改进建议
        step_4_5_result = self.step_4_5_improvement_suggestions()
        
        # 整合所有总结结果
        self.summary_results = {
            "diagnostic_summary": step_4_1_result,
            "solution_summary": step_4_2_result,
            "prevention_suggestions": step_4_3_result,
            "optimization_suggestions": step_4_4_result,
            "improvement_suggestions": step_4_5_result
        }
        
        # 检查阶段4完成条件
        completion_check = self.check_stage4_completion()
        
        if completion_check["can_complete"]:
            next_state = "COMPLETED"
            print("✅ 阶段4完成条件满足，流程成功完成")
        else:
            next_state = "FAILED"
            print("❌ 阶段4完成条件不满足，总结不完整")
        
        self.update_todo_status("completed")
        
        return {
            "success": completion_check["can_complete"],
            "summary_results": self.summary_results,
            "completion_check": completion_check,
            "next_state": next_state
        }
    
    def step_4_1_diagnostic_summary(self):
        """步骤4.1: 诊断过程总结"""
        print("执行步骤4.1: 诊断过程总结...")
        
        return {
            "flow_review": "诊断流程完整执行",
            "key_findings": "症状驱动方法提升了诊断针对性",
            "methodology_assessment": "状态机架构确保了流程严谨性"
        }
    
    def step_4_2_solution_summary(self):
        """步骤4.2: 解决方案总结"""
        print("执行步骤4.2: 解决方案总结...")
        
        return {
            "fix_review": "修复方案有效解决了核心问题",
            "effect_assessment": "验证通过率达到100%",
            "alternative_analysis": "当前方案为最优选择"
        }
    
    def step_4_3_prevention_suggestions(self):
        """步骤4.3: 类似问题预防建议"""
        print("执行步骤4.3: 类似问题预防建议...")
        
        return {
            "measures": [
                "建立症状监控机制",
                "完善代码审查流程"
            ]
        }
    
    def step_4_4_optimization_suggestions(self):
        """步骤4.4: 系统性优化建议"""
        print("执行步骤4.4: 系统性优化建议...")
        
        return {
            "suggestions": [
                "优化错误处理机制",
                "增强日志记录",
                "完善监控体系"
            ]
        }
    
    def step_4_5_improvement_suggestions(self):
        """步骤4.5: 发散性改进建议"""
        print("执行步骤4.5: 发散性改进建议...")
        
        return {
            "improvements": [
                "实施持续集成",
                "建立自动化测试"
            ]
        }
    
    def check_stage4_completion(self):
        """检查阶段4完成条件"""
        # 门禁条件：总结报告完整 + 优化建议具体 + TodoWrite标记全部completed
        
        conditions = {
            "总结报告完整": all(key in self.summary_results for key in [
                "diagnostic_summary", "solution_summary", "optimization_suggestions"
            ]),
            "优化建议具体": len(self.summary_results.get("optimization_suggestions", {}).get("suggestions", [])) >= 3,
            "预防措施完备": len(self.summary_results.get("prevention_suggestions", {}).get("measures", [])) >= 2,
            "发散性改进充分": len(self.summary_results.get("improvement_suggestions", {}).get("improvements", [])) >= 2
        }
        
        print("🤔 阶段4完成条件检查:")
        for condition, passed in conditions.items():
            print(f"  {condition}: {'✅' if passed else '❌'}")
        
        can_complete = all(conditions.values())
        failure_reasons = [cond for cond, passed in conditions.items() if not passed]
        
        return {
            "can_complete": can_complete,
            "conditions": conditions,
            "failure_reasons": failure_reasons
        }
    
    def update_todo_status(self, status):
        """更新Todo状态"""
        # 实际执行时需要使用TodoWrite function call
        print(f"  更新Todo状态: {status}")
```
