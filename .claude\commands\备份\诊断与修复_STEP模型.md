---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程

**STEP四要素模型**：
- **S**tate (状态) - 明确当前处于什么状态，状态职责边界清晰
- **T**rigger (触发) - 什么条件触发状态转换，门禁条件明确 
- **E**xecute (执行) - 在此状态下具体执行什么
- **P**roof (证明) - 如何证明执行完成并可以转换，客观可验证

**核心原则**：
- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🛑 强制执行机制

### 阶段门禁条件（状态转换控制）
每个状态转换都需要满足明确的门禁条件：

```markdown
SEARCHING → ANALYZING: 必须完成多维度信息搜索 + 识别核心症状 + TodoWrite标记completed
ANALYZING → REPAIRING: 必须满足循环退出条件 + 假设验证充分性确认 + TodoWrite完成验证记录  
REPAIRING → VERIFYING: 必须验证通过率100% + 无副作用确认 + TodoWrite记录修复内容
VERIFYING → COMPLETED: 必须总结报告完整 + 优化建议具体 + TodoWrite标记全部completed
```

### 违规定义和后果
```markdown
🚨 严重违规（必须重新执行）：
- 跳过循环机制：ANALYZING、REPAIRING状态未执行循环→返回重新执行完整状态
- 凭想象猜测：未提供真实验证证据→必须补充验证步骤
- 提前退出状态：未满足门禁条件→必须补充完成所有必需步骤
- 简化实现逃避：临时方案、伪代码→必须提供完整真实修复
- 分离假设-验证：违反完整循环原则→必须重新执行包含假设调整的完整循环

⚠️ 轻微违规（警告并纠正）：
- TodoWrite更新不及时：立即补充更新
- 进度记录不准确：重新统计并记录
- 循环状态记录不完整：补充详细的轮次和步骤状态
```

## 🏗️ 流程状态图

```yaml
流程状态定义:
  INIT → SEARCHING → ANALYZING → REPAIRING → VERIFYING → COMPLETED
   ↑        ↓          ↓(2-5轮)     ↓(1-3轮)      ↓           ↓
  开始   信息收集与   假设-验证      修复实施与    全面验证     总结与发散
         真实检查     循环          验证循环                   优化
```

---

## 🔍 STATE-1: SEARCHING (信息搜索状态)

### 📍 **State Definition**
```yaml
State: SEARCHING
Description: 信息收集与真实检查完成
Duration: 预计5-10分钟
Status: ACTIVE | COMPLETED
对应: 流程阶段1
```

### 🎯 **Trigger Conditions**
```yaml
Entry Triggers:
  - FROM: INIT
  - WHEN: 用户提供问题描述
  - CONDITION: problem_description is not None

Exit Triggers:
  - TO: ANALYZING  
  - WHEN: 搜索质量达标
  - CONDITION: search_quality_score >= 0.8
```

### ⚡ **Execute Actions**
```python
def execute_searching_state($ARGUMENTS: str) -> SearchingResult:
    """SEARCHING状态的执行内容 - 对应阶段1完整实现"""
    
    print("🔍 开始SEARCHING状态：信息收集与真实检查")
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_searching_stage()
    
    # Action 1: 问题信息解析
    problem_analysis = parse_problem_description($ARGUMENTS)
    print(f"提取核心症状：{problem_analysis.symptoms_count}个")
    print(f"识别功能模块：{problem_analysis.modules_count}个")
    
    # Action 2: 多维度搜索策略制定
    search_strategy = create_search_strategy(problem_analysis)
    print(f"文件名模式：{len(search_strategy.file_patterns)}个")
    print(f"内容关键词：{len(search_strategy.content_keywords)}个")
    
    # Action 3: 强制真实检查 - 多工具组合搜索
    search_results = SearchResults()
    
    # 3.1 Glob文件名搜索
    print("执行Glob文件名搜索...")
    for pattern in search_strategy.file_patterns:
        files = glob_search(f"**/*{pattern}*")
        search_results.add_glob_result(pattern, files)
        print(f"  模式'{pattern}': {len(files)}个文件")
    
    # 3.2 Grep内容搜索
    print("执行Grep内容搜索...")
    for keyword in search_strategy.content_keywords:
        matches = grep_search(keyword, include="*.py,*.js,*.json")
        search_results.add_grep_result(keyword, matches)
        print(f"  关键词'{keyword}': {len(matches)}处匹配")
    
    # 3.3 错误模式搜索
    error_patterns = ["Error", "Exception", "异常", "错误", "failed", "失败"]
    for pattern in error_patterns:
        error_matches = grep_search(pattern, include="*.py,*.log")
        search_results.add_error_result(pattern, error_matches)
        print(f"  错误模式'{pattern}': {len(error_matches)}处匹配")
    
    # 3.4 Task工具复杂搜索（如需要）
    if search_results.total_files < 5:
        print("启用Task工具进行扩展搜索...")
        task_results = task_search_complex_patterns(problem_analysis)
        search_results.merge_task_results(task_results)
    
    # Action 4: 搜索完整性验证
    verification_result = verify_search_completeness(search_results)
    print(f"交叉验证一致性：{verification_result.consistency_score}")
    print(f"边界扩展发现：{verification_result.boundary_findings}个")
    
    # Action 5: 系统状态检查
    print("检查服务状态...")
    service_health = check_service_with_curl()
    print(f"服务状态：{service_health.status_code} ({service_health.description})")
    
    # Action 6: WSL环境边界确认
    environment_check = verify_wsl_capabilities()
    print(f"WSL环境检查：{environment_check.summary}")
    
    # 更新TodoWrite状态
    todo_tracker.complete_search_action("多维度信息搜索")
    todo_tracker.complete_search_action("核心症状识别")
    
    total_files = search_results.total_unique_files
    print(f"\n✅ SEARCHING状态执行完成")
    print(f"总计发现：{total_files}个相关文件")
    print(f"服务状态：{service_health.description}")
    
    return SearchingResult(
        problem_analysis=problem_analysis,
        search_strategy=search_strategy,
        search_results=search_results,
        verification_result=verification_result,
        service_health=service_health,
        environment_check=environment_check,
        todo_tracker=todo_tracker,
        total_files_found=total_files
    )

def parse_problem_description(description: str) -> ProblemAnalysis:
    """解析问题描述，提取核心信息"""
    analysis = ProblemAnalysis()
    
    # 提取症状模式
    symptom_patterns = ["显示", "异常", "错误", "失败", "不正常", "无法", "缺失"]
    for pattern in symptom_patterns:
        if pattern in description:
            analysis.add_symptom(pattern)
    
    # 识别功能模块
    module_keywords = ["用户", "管理", "界面", "数据", "API", "数据库", "购买", "记录"]
    for keyword in module_keywords:
        if keyword in description:
            analysis.add_module(keyword)
    
    # 分析影响范围
    if any(word in description for word in ["全部", "所有", "整个"]):
        analysis.impact_scope = "GLOBAL"
    elif any(word in description for word in ["部分", "某些", "个别"]):
        analysis.impact_scope = "PARTIAL"
    else:
        analysis.impact_scope = "LOCALIZED"
    
    return analysis

def check_service_with_curl() -> ServiceHealth:
    """使用curl检查服务状态"""
    try:
        # 执行curl命令检查服务
        result = bash_command('curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs')
        status_code = int(result.stdout.strip())
        
        if status_code == 200:
            return ServiceHealth(status_code, "healthy", "服务正常运行")
        elif status_code == 000:
            return ServiceHealth(status_code, "unavailable", "服务不可访问，需要启动")
        else:
            return ServiceHealth(status_code, "error", f"服务异常，状态码{status_code}")
    except Exception as e:
        return ServiceHealth(0, "unknown", f"服务检查失败：{str(e)}")

def verify_wsl_capabilities() -> EnvironmentCheck:
    """验证WSL环境能力边界"""
    capabilities = EnvironmentCheck()
    
    # 检查文件系统访问
    capabilities.file_access = check_file_system_access()
    
    # 检查网络连接能力
    capabilities.network_access = test_network_connectivity()
    
    # 确认Python运行限制
    capabilities.python_exec_limited = True  # WSL下不能直接运行PyQt6
    
    # 确认可用工具
    available_tools = ["curl", "grep", "find", "sed", "awk"]
    capabilities.available_tools = [tool for tool in available_tools if check_tool_available(tool)]
    
    capabilities.summary = f"文件访问{'✅' if capabilities.file_access else '❌'} 网络连接{'✅' if capabilities.network_access else '❌'} 可用工具{len(capabilities.available_tools)}个"
    
    return capabilities
```

### ✅ **Proof Requirements**
```python
def verify_searching_proof(result: SearchingResult) -> bool:
    """验证SEARCHING状态的完成证明 - 对应阶段1门禁条件"""
    
    print("🔍 验证SEARCHING状态完成证明...")
    
    # Proof 1: 多维度信息搜索完成
    search_methods_used = {
        'glob_search': len(result.search_results.glob_results) >= 3,  # 至少3个文件模式
        'grep_search': len(result.search_results.grep_results) >= 3,  # 至少3个内容关键词
        'error_search': len(result.search_results.error_results) >= 2  # 至少2个错误模式
    }
    multi_search_complete = all(search_methods_used.values())
    
    # Proof 2: 文件发现数量达标
    files_sufficient = result.total_files_found >= 5  # 要求：最少5个相关文件
    
    # Proof 3: 核心症状识别完成
    symptoms_identified = (
        result.problem_analysis.symptoms_count >= 1 and
        result.problem_analysis.modules_count >= 1
    )
    
    # Proof 4: 服务状态检查完成
    service_checked = result.service_health.status_code != 0  # 至少获得了状态码
    
    # Proof 5: 搜索完整性验证
    verification_passed = (
        result.verification_result.consistency_score >= 0.7 and
        result.verification_result.boundary_check_complete
    )
    
    # Proof 6: TodoWrite状态更新
    todo_tracking_complete = (
        result.todo_tracker.is_action_completed("多维度信息搜索") and
        result.todo_tracker.is_action_completed("核心症状识别")
    )
    
    # Proof 7: WSL环境能力确认
    environment_verified = result.environment_check.file_access
    
    # 打印证明检查结果
    proofs = {
        "多维度搜索完成": multi_search_complete,
        "文件数量达标": files_sufficient,
        "症状识别完成": symptoms_identified,
        "服务状态检查": service_checked,
        "完整性验证": verification_passed,
        "TodoWrite更新": todo_tracking_complete,
        "环境能力确认": environment_verified
    }
    
    for proof_name, passed in proofs.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {proof_name}")
    
    # 总体证明结果
    proof_passed = all(proofs.values())
    
    if proof_passed:
        print("✅ SEARCHING状态证明通过，可以转换到ANALYZING状态")
        result.todo_tracker.complete_stage("SEARCHING")
    else:
        failed_proofs = [name for name, passed in proofs.items() if not passed]
        print(f"❌ SEARCHING状态证明失败，未满足：{', '.join(failed_proofs)}")
    
    return proof_passed
```

### 📊 **State Metrics**
```python
searching_metrics = {
    "files_found": total_files,
    "search_coverage": coverage_percentage,
    "service_health": service_status,
    "execution_time": elapsed_seconds,
    "quality_score": calculate_search_quality()
}
```

---

## 🔬 STATE-2: ANALYZING (问题分析状态) 

### 📍 **State Definition**
```yaml
State: ANALYZING
Description: 假设-验证循环（2-5轮，每轮3步）
Duration: 预计10-20分钟  
Iterations: 2-5轮强制循环
Status: ROUND_1 | ROUND_2 | ROUND_3 | ROUND_4 | ROUND_5 | COMPLETED
对应: 流程阶段2
Loop_Requirement: 最少2轮，最多5轮
```

### 🎯 **Trigger Conditions**
```yaml
Entry Triggers:
  - FROM: SEARCHING
  - WHEN: 搜索证明完成
  - CONDITION: searching_proof_passed == True

Internal Triggers (循环控制):
  - CONTINUE: round < 5 AND new_findings > 0
  - EXIT: confirmed_issues >= 1 AND new_findings == 0 for 2 rounds

Exit Triggers:
  - TO: REPAIRING
  - WHEN: 根本问题确认
  - CONDITION: root_cause_identified == True
```

### ⚡ **Execute Actions**
```python
def execute_analyzing_state() -> List[AnalyzingResult]:
    """ANALYZING状态的执行内容 - 对应阶段2完整实现（2-5轮循环）"""
    
    print("🔬 开始ANALYZING状态：假设-验证循环")
    
    analyzing_results = []
    round_num = 1
    max_rounds = 5
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_analyzing_stage()
    
    while round_num <= max_rounds:
        print(f"\n--- 第{round_num}轮假设-验证循环 ---")
        
        # 步骤1: 假设建立
        if round_num == 1:
            hypotheses = create_initial_hypotheses()
            print(f"建立初始假设：{len(hypotheses)}个")
        else:
            previous_results = analyzing_results[-1]  # 获取上一轮结果
            hypotheses = refine_hypotheses_based_on_results(previous_results)
            print(f"调整假设：{len(hypotheses)}个")
        
        # 步骤2: 假设验证（完整循环，不分离）
        verification_results = []
        confirmed_issues = 0
        new_findings = 0
        
        for i, hypothesis in enumerate(hypotheses, 1):
            print(f"  验证假设{i}: {hypothesis.description}")
            
            # 真实验证（禁止凭想象猜测）
            verification = execute_real_verification(hypothesis)
            verification_results.append(verification)
            
            if verification.confirmed:
                confirmed_issues += 1
                print(f"    ✅ 确认问题: {verification.evidence}")
            else:
                print(f"    ❌ 假设被排除")
            
            # 检查新发现
            if verification.new_information:
                new_findings += 1
                print(f"    🔍 新发现: {verification.new_information}")
        
        # 步骤3: 假设调整（在同轮内完成）
        if confirmed_issues > 0:
            root_cause_analysis = analyze_root_cause(verification_results)
            print(f"  根因分析: {root_cause_analysis.summary if root_cause_analysis else '未确定'}")
        else:
            root_cause_analysis = None
        
        # 记录本轮结果
        round_result = AnalyzingResult(
            round_number=round_num,
            hypotheses=hypotheses,
            verification_results=verification_results,
            confirmed_issues=confirmed_issues,
            new_findings=new_findings,
            root_cause=root_cause_analysis
        )
        analyzing_results.append(round_result)
        
        # 更新TodoWrite
        todo_tracker.complete_round(f"假设-验证循环第{round_num}轮", confirmed_issues, new_findings)
        
        print(f"  第{round_num}轮统计: 确认问题{confirmed_issues}个，新发现{new_findings}个")
        
        # 强制最少2轮检查
        if round_num < 2:
            print(f"  继续执行（要求最少2轮）")
            round_num += 1
            continue
        
        # 循环退出条件检查（2轮后）
        if should_exit_analyzing_loop(analyzing_results):
            print(f"  循环退出条件满足，结束分析（共{round_num}轮）")
            break
        
        # 达到最大轮次
        if round_num >= max_rounds:
            print(f"  达到最大轮次限制，结束分析")
            break
        
        round_num += 1
    
    print(f"\n✅ ANALYZING状态执行完成，共执行{len(analyzing_results)}轮")
    return analyzing_results

def create_initial_hypotheses() -> List[Hypothesis]:
    """创建第1轮系统性假设"""
    return [
        # 基础技术层假设
        Hypothesis(
            id="H001",
            description="代码语法错误或导入问题",
            category="SYNTAX",
            verify_method="syntax_check",
            priority="HIGH"
        ),
        Hypothesis(
            id="H002",
            description="配置文件或环境变量错误",
            category="CONFIG",
            verify_method="config_check",
            priority="HIGH"
        ),
        # 数据层假设
        Hypothesis(
            id="H003",
            description="数据库字段映射错误",
            category="DATABASE",
            verify_method="field_mapping_check",
            priority="HIGH"
        ),
        Hypothesis(
            id="H004",
            description="数据关联或外键问题",
            category="DATA_RELATION",
            verify_method="relation_check",
            priority="MEDIUM"
        ),
        # 业务逻辑层假设
        Hypothesis(
            id="H005",
            description="UI数据绑定或渲染问题",
            category="UI_LOGIC",
            verify_method="ui_data_check",
            priority="HIGH"
        ),
        Hypothesis(
            id="H006",
            description="API端点或路由问题",
            category="API_LOGIC",
            verify_method="api_endpoint_check",
            priority="MEDIUM"
        )
    ]

def execute_real_verification(hypothesis: Hypothesis) -> VerificationResult:
    """执行真实的假设验证（禁止凭想象猜测）"""
    print(f"    执行真实验证: {hypothesis.verify_method}")
    
    if hypothesis.verify_method == "syntax_check":
        return verify_syntax_errors_real()
    elif hypothesis.verify_method == "field_mapping_check":
        return verify_field_mapping_real()
    elif hypothesis.verify_method == "ui_data_check":
        return verify_ui_data_binding_real()
    elif hypothesis.verify_method == "relation_check":
        return verify_data_relations_real()
    else:
        return verify_generic_hypothesis_real(hypothesis)

def verify_field_mapping_real() -> VerificationResult:
    """真实验证数据库字段映射"""
    # 搜索常见字段映射错误模式
    mapping_issues = []
    
    # 查找UUID字段使用错误
    uuid_files = grep_search("purchased_entity_id", include="*.py")
    for file_path in uuid_files:
        file_content = read_file(file_path)
        lines = file_content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # 检查是否存在错误的字段引用
            if "purchased_entity_id" in line and "id" in line and "category_id" not in line:
                # 进一步检查上下文
                context_lines = lines[max(0, line_num-2):line_num+2]
                if any("category" in ctx_line.lower() for ctx_line in context_lines):
                    mapping_issues.append({
                        "file": file_path,
                        "line": line_num,
                        "issue": "UUID字段映射错误：应该使用category_id而不是id",
                        "evidence": line.strip()
                    })
    
    if mapping_issues:
        evidence_summary = f"发现{len(mapping_issues)}处字段映射错误"
        detailed_evidence = "\n".join([f"{issue['file']}:{issue['line']} - {issue['issue']}" for issue in mapping_issues])
        
        return VerificationResult(
            confirmed=True,
            evidence=evidence_summary,
            detailed_evidence=detailed_evidence,
            new_information=f"具体错误位置：{mapping_issues[0]['file']}:{mapping_issues[0]['line']}",
            confidence=0.9
        )
    else:
        return VerificationResult(
            confirmed=False,
            evidence="未发现字段映射错误",
            confidence=0.8
        )

def should_exit_analyzing_loop(results: List[AnalyzingResult]) -> bool:
    """判断是否应该退出分析循环（退出条件）"""
    
    if len(results) < 2:
        return False  # 强制最少2轮
    
    last_result = results[-1]
    second_last_result = results[-2] if len(results) >= 2 else None
    
    # 退出条件1: 找到根本问题且连续2轮无新发现
    has_root_cause = last_result.root_cause is not None
    last_no_findings = last_result.new_findings == 0
    second_last_no_findings = second_last_result.new_findings == 0 if second_last_result else False
    
    condition1 = has_root_cause and last_no_findings and second_last_no_findings
    
    # 退出条件2: 确认问题数量稳定（连续2轮确认问题数相同且>0）
    confirmed_stable = (
        last_result.confirmed_issues > 0 and
        second_last_result and
        last_result.confirmed_issues == second_last_result.confirmed_issues
    )
    
    should_exit = condition1 or confirmed_stable
    
    if should_exit:
        exit_reason = "根本问题确定且连续无新发现" if condition1 else "确认问题数量稳定"
        print(f"    退出条件满足：{exit_reason}")
    
    return should_exit
```

### ✅ **Proof Requirements**
```python
def verify_analyzing_proof(results: List[AnalyzingResult]) -> bool:
    """验证ANALYZING状态的完成证明 - 对应阶段2门禁条件"""
    
    print("🔬 验证ANALYZING状态完成证明...")
    
    # Proof 1: 强制循环执行要求
    min_rounds_executed = len(results) >= 2  # 要求：最少2轮
    max_rounds_respected = len(results) <= 5  # 要求：最多5轮
    
    # Proof 2: 每轮3步完整性
    all_rounds_complete = all(
        result.hypotheses and  # 步骤1：假设建立
        result.verification_results and  # 步骤2：假设验证
        hasattr(result, 'root_cause')  # 步骤3：假设调整
        for result in results
    )
    
    # Proof 3: 假设验证充分性确认
    total_hypotheses = sum(len(r.hypotheses) for r in results)
    verified_hypotheses = sum(len(r.verification_results) for r in results)
    verification_completeness = verified_hypotheses == total_hypotheses  # 所有假设都被验证
    
    # Proof 4: 根本问题确认
    root_cause_identified = any(r.root_cause for r in results)
    
    # Proof 5: 循环退出条件满足
    if len(results) >= 2:
        last_result = results[-1]
        second_last = results[-2]
        
        # 标准退出条件：找到问题且连续无新发现
        exit_condition_met = (
            (last_result.confirmed_issues > 0 and 
             last_result.new_findings == 0 and 
             second_last.new_findings == 0) or
            len(results) >= 5  # 或达到最大轮次
        )
    else:
        exit_condition_met = False
    
    # Proof 6: 真实验证证据
    evidence_authenticity = all(
        any(vr.evidence and vr.detailed_evidence for vr in result.verification_results if vr.confirmed)
        for result in results
        if result.confirmed_issues > 0
    )
    
    # Proof 7: TodoWrite验证记录
    todo_tracking_complete = True  # 简化检查，实际应检查TodoWrite状态
    
    # 打印证明检查结果
    proofs = {
        "最少轮次执行": min_rounds_executed,
        "最多轮次遵守": max_rounds_respected,
        "每轮步骤完整": all_rounds_complete,
        "验证充分性确认": verification_completeness,
        "根本问题确认": root_cause_identified,
        "退出条件满足": exit_condition_met,
        "真实验证证据": evidence_authenticity,
        "TodoWrite记录": todo_tracking_complete
    }
    
    for proof_name, passed in proofs.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {proof_name}")
        
        # 详细说明关键指标
        if proof_name == "最少轮次执行":
            print(f"      执行轮次: {len(results)}/2")
        elif proof_name == "验证充分性确认":
            print(f"      假设总数: {total_hypotheses}, 验证总数: {verified_hypotheses}")
        elif proof_name == "根本问题确认":
            confirmed_count = sum(r.confirmed_issues for r in results)
            print(f"      确认问题总数: {confirmed_count}")
    
    # 总体证明结果
    proof_passed = all(proofs.values())
    
    if proof_passed:
        print("✅ ANALYZING状态证明通过，可以转换到REPAIRING状态")
        # 更新TodoWrite状态
        for result in results:
            if hasattr(result, 'todo_tracker'):
                result.todo_tracker.complete_stage("ANALYZING")
    else:
        failed_proofs = [name for name, passed in proofs.items() if not passed]
        print(f"❌ ANALYZING状态证明失败，未满足：{', '.join(failed_proofs)}")
    
    return proof_passed
```

### 📊 **State Metrics**
```python
analyzing_metrics = {
    "rounds_executed": len(results),
    "total_hypotheses": total_hypotheses,
    "confirmed_issues": confirmed_count,
    "verification_rate": verified_count / total_hypotheses,
    "root_cause_confidence": confidence_score
}
```

---

## 🔧 STATE-3: REPAIRING (修复实施状态)

### 📍 **State Definition**
```yaml
State: REPAIRING
Description: 修复实施与验证循环（1-5轮，每轮4步）
Duration: 预计15-30分钟
Iterations: 1-5轮修复尝试
Status: ATTEMPT_1 | ATTEMPT_2 | ATTEMPT_3 | ATTEMPT_4 | ATTEMPT_5 | COMPLETED
对应: 流程阶段3
Loop_Requirement: 最少1轮，最多5轮
```

### 🎯 **Trigger Conditions**
```yaml
Entry Triggers:
  - FROM: ANALYZING
  - WHEN: 根本问题确认
  - CONDITION: root_cause_identified == True

Internal Triggers (重试控制):
  - RETRY: fix_success_rate < 100% AND attempt < 3
  - COMPLETE: fix_success_rate == 100%

Exit Triggers:
  - TO: VERIFYING
  - WHEN: 所有修复完成
  - CONDITION: all_fixes_implemented == True
```

### ⚡ **Execute Actions**
```python
def execute_repairing_state(root_cause_list: List[RootCause]) -> List[RepairingResult]:
    """REPAIRING状态的执行内容 - 对应阶段3完整实现（1-5轮循环）"""
    
    print("🔧 开始REPAIRING状态：修复实施与验证循环")
    
    repairing_results = []
    attempt_num = 1
    max_attempts = 5
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_repairing_stage()
    
    while attempt_num <= max_attempts:
        print(f"\n--- 第{attempt_num}轮修复实施与验证 ---")
        
        # 步骤1: 修复方案设计
        if attempt_num == 1:
            fix_plans = design_comprehensive_fix_plans(root_cause_list)
            print(f"设计初始修复方案：{len(fix_plans)}个")
        else:
            previous_failures = repairing_results[-1].failed_implementations
            fix_plans = redesign_fix_plans_based_on_failures(previous_failures)
            print(f"重新设计修复方案：{len(fix_plans)}个")
        
        # 步骤2: 修复实施（完整真实修复，禁止简化逃避）
        implementations = []
        for i, plan in enumerate(fix_plans, 1):
            print(f"  执行修复{i}: {plan.description}")
            
            try:
                implementation = implement_complete_fix(plan)
                implementations.append(implementation)
                
                if implementation.success:
                    print(f"    ✅ 修复成功")
                else:
                    print(f"    ❌ 修复失败: {implementation.error_message}")
                    
            except Exception as e:
                failed_impl = FailedImplementation(plan, str(e))
                implementations.append(failed_impl)
                print(f"    ❌ 修复异常: {str(e)}")
        
        # 步骤3: 修复验证（每个修复立即验证）
        verification_results = []
        for impl in implementations:
            if impl.success:
                print(f"  验证修复: {impl.plan.description}")
                verification = execute_fix_verification(impl)
                verification_results.append(verification)
                
                if verification.passed:
                    print(f"    ✅ 验证通过")
                else:
                    print(f"    ❌ 验证失败: {verification.failure_reason}")
        
        # 步骤4: 副作用检查
        side_effects_check = check_for_side_effects(implementations)
        print(f"  副作用检查: {'无副作用' if side_effects_check.clean else f'发现{side_effects_check.issue_count}个副作用'}")
        
        # 记录本轮结果
        success_rate = calculate_implementation_success_rate(implementations)
        verification_rate = calculate_verification_pass_rate(verification_results)
        
        round_result = RepairingResult(
            attempt_number=attempt_num,
            fix_plans=fix_plans,
            implementations=implementations,
            verification_results=verification_results,
            side_effects_check=side_effects_check,
            implementation_success_rate=success_rate,
            verification_pass_rate=verification_rate
        )
        repairing_results.append(round_result)
        
        # 更新TodoWrite
        todo_tracker.complete_attempt(f"修复实施第{attempt_num}轮", success_rate, verification_rate)
        
        print(f"  第{attempt_num}轮统计: 实施成功率{success_rate*100:.1f}%, 验证通过率{verification_rate*100:.1f}%")
        
        # 检查是否完成所有修复
        if success_rate == 1.0 and verification_rate == 1.0 and side_effects_check.clean:
            print(f"  所有修复完成且验证通过，结束修复（共{attempt_num}轮）")
            break
        
        # 达到最大尝试次数
        if attempt_num >= max_attempts:
            print(f"  达到最大尝试次数，结束修复")
            break
        
        attempt_num += 1
    
    print(f"\n✅ REPAIRING状态执行完成，共执行{len(repairing_results)}轮")
    return repairing_results

def implement_complete_fix(plan: FixPlan) -> FixImplementation:
    """实施完整的真实修复（禁止简化、临时方案、伪代码）"""
    implementation = FixImplementation(plan)
    
    print(f"    执行修复操作: {plan.type}")
    
    try:
        if plan.type == "FIELD_MAPPING_FIX":
            # 修复字段映射错误
            for action in plan.actions:
                if action.type == "REPLACE_FIELD":
                    result = edit_file(
                        file_path=action.file_path,
                        old_string=action.old_field_usage,
                        new_string=action.new_field_usage
                    )
                    implementation.add_edit_result(result)
                    print(f"      替换字段: {action.old_field_usage} → {action.new_field_usage}")
        
        elif plan.type == "UI_DATA_BINDING_FIX":
            # 修复UI数据绑定
            for action in plan.actions:
                if action.type == "FIX_DATA_ACCESS":
                    result = edit_file(
                        file_path=action.file_path,
                        old_string=action.old_data_access,
                        new_string=action.new_data_access
                    )
                    implementation.add_edit_result(result)
                    print(f"      修复数据访问: {action.description}")
        
        elif plan.type == "API_ENDPOINT_FIX":
            # 修复API端点
            for action in plan.actions:
                if action.type == "UPDATE_ROUTE":
                    result = edit_file(
                        file_path=action.file_path,
                        old_string=action.old_route,
                        new_string=action.new_route
                    )
                    implementation.add_edit_result(result)
                    print(f"      更新路由: {action.description}")
        
        # 语法验证
        syntax_check = verify_syntax_after_changes(implementation.modified_files)
        implementation.syntax_valid = syntax_check.valid
        
        if not syntax_check.valid:
            implementation.success = False
            implementation.error_message = f"语法错误: {syntax_check.error_details}"
        else:
            implementation.success = True
            
    except Exception as e:
        implementation.success = False
        implementation.error_message = str(e)
    
    return implementation

def execute_fix_verification(implementation: FixImplementation) -> FixVerification:
    """执行修复验证（真实测试，不是理论推理）"""
    verification = FixVerification(implementation)
    
    try:
        # 1. 功能性验证
        function_test = test_fixed_functionality(implementation)
        verification.function_test_passed = function_test.passed
        
        # 2. 服务健康检查
        service_test = test_service_health_after_fix()
        verification.service_health_ok = service_test.healthy
        
        # 3. 数据完整性检查
        data_integrity_test = test_data_integrity_after_fix(implementation)
        verification.data_integrity_ok = data_integrity_test.passed
        
        # 4. 原问题解决验证
        original_issue_test = test_original_issue_resolved(implementation)
        verification.original_issue_resolved = original_issue_test.resolved
        
        # 综合判断
        verification.passed = all([
            verification.function_test_passed,
            verification.service_health_ok,
            verification.data_integrity_ok,
            verification.original_issue_resolved
        ])
        
        if not verification.passed:
            failed_tests = []
            if not verification.function_test_passed:
                failed_tests.append("功能测试")
            if not verification.service_health_ok:
                failed_tests.append("服务健康")
            if not verification.data_integrity_ok:
                failed_tests.append("数据完整性")
            if not verification.original_issue_resolved:
                failed_tests.append("原问题解决")
            
            verification.failure_reason = f"失败测试: {', '.join(failed_tests)}"
        
    except Exception as e:
        verification.passed = False
        verification.failure_reason = f"验证异常: {str(e)}"
    
    return verification

def check_for_side_effects(implementations: List[FixImplementation]) -> SideEffectsCheck:
    """检查修复是否产生副作用"""
    side_effects = SideEffectsCheck()
    
    # 检查语法回归
    all_modified_files = []
    for impl in implementations:
        all_modified_files.extend(impl.modified_files)
    
    syntax_regression = check_syntax_regression(all_modified_files)
    if syntax_regression.has_regression:
        side_effects.add_issue("语法回归", syntax_regression.details)
    
    # 检查功能回归
    function_regression = test_core_functions_regression()
    if function_regression.has_regression:
        side_effects.add_issue("功能回归", function_regression.details)
    
    # 检查API回归
    api_regression = test_api_endpoints_regression()
    if api_regression.has_regression:
        side_effects.add_issue("API回归", api_regression.details)
    
    side_effects.clean = side_effects.issue_count == 0
    
    return side_effects
```

### ✅ **Proof Requirements**
```python
def verify_repairing_proof(results: List[RepairingResult]) -> bool:
    """验证REPAIRING状态的完成证明 - 对应阶段3门禁条件"""
    
    print("🔧 验证REPAIRING状态完成证明...")
    
    latest_result = results[-1]
    
    # Proof 1: 验证通过率100%（要求）
    verification_pass_rate_100 = latest_result.verification_pass_rate == 1.0
    
    # Proof 2: 所有修复完整实施（禁止简化逃避）
    all_fixes_complete = (
        latest_result.implementation_success_rate == 1.0 and
        all(impl.success for impl in latest_result.implementations)
    )
    
    # Proof 3: 无副作用确认
    no_side_effects = latest_result.side_effects_check.clean
    
    # Proof 4: 循环执行完整性
    all_rounds_4_steps = all(
        result.fix_plans and  # 步骤1：修复方案设计
        result.implementations and  # 步骤2：修复实施
        result.verification_results and  # 步骤3：修复验证
        hasattr(result, 'side_effects_check')  # 步骤4：副作用检查
        for result in results
    )
    
    # Proof 5: 真实修复验证（不是理论推理）
    real_verification_evidence = all(
        verification.function_test_passed and
        verification.service_health_ok and
        verification.data_integrity_ok
        for result in results
        for verification in result.verification_results
        if verification.passed
    )
    
    # Proof 6: 原问题解决确认
    original_issue_resolved = any(
        verification.original_issue_resolved
        for result in results
        for verification in result.verification_results
    )
    
    # Proof 7: 语法有效性
    syntax_validity = all(
        impl.syntax_valid
        for result in results
        for impl in result.implementations
        if impl.success
    )
    
    # Proof 8: TodoWrite修复记录
    todo_repair_recorded = True  # 简化检查，实际应检查TodoWrite状态
    
    # 打印证明检查结果
    proofs = {
        "验证通过率100%": verification_pass_rate_100,
        "所有修复完整实施": all_fixes_complete,
        "无副作用确认": no_side_effects,
        "循环4步执行完整": all_rounds_4_steps,
        "真实验证证据": real_verification_evidence,
        "原问题解决确认": original_issue_resolved,
        "语法有效性": syntax_validity,
        "TodoWrite记录": todo_repair_recorded
    }
    
    for proof_name, passed in proofs.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {proof_name}")
        
        # 详细说明关键指标
        if proof_name == "验证通过率100%":
            print(f"      验证通过率: {latest_result.verification_pass_rate*100:.1f}%")
        elif proof_name == "所有修复完整实施":
            print(f"      实施成功率: {latest_result.implementation_success_rate*100:.1f}%")
        elif proof_name == "无副作用确认":
            if not no_side_effects:
                print(f"      副作用数量: {latest_result.side_effects_check.issue_count}")
    
    # 总体证明结果
    proof_passed = all(proofs.values())
    
    if proof_passed:
        print("✅ REPAIRING状态证明通过，可以转换到VERIFYING状态")
        # 更新TodoWrite状态
        for result in results:
            if hasattr(result, 'todo_tracker'):
                result.todo_tracker.complete_stage("REPAIRING")
    else:
        failed_proofs = [name for name, passed in proofs.items() if not passed]
        print(f"❌ REPAIRING状态证明失败，未满足：{', '.join(failed_proofs)}")
    
    return proof_passed
```

### 📊 **State Metrics**
```python
repairing_metrics = {
    "attempts_made": len(results),
    "fixes_planned": total_planned,
    "fixes_successful": successful_count,
    "success_rate": latest_result.success_rate,
    "syntax_errors": syntax_error_count
}
```

---

## ✅ STATE-4: VERIFYING (结果验证状态)

### 📍 **State Definition**
```yaml
State: VERIFYING
Description: 全面验证修复效果和系统稳定性
Duration: 预计10-15分钟
Iterations: 1轮全面验证
Status: TESTING | COMPLETED
```

### 🎯 **Trigger Conditions**
```yaml
Entry Triggers:
  - FROM: REPAIRING
  - WHEN: 所有修复完成
  - CONDITION: all_fixes_implemented == True

Exit Triggers:
  - TO: COMPLETED
  - WHEN: 验证全部通过
  - CONDITION: all_verifications_passed == True
```

### ⚡ **Execute Actions**
```python
def execute_verifying_state() -> VerifyingResult:
    """VERIFYING状态的执行内容 - 对应阶段4前半部分（全面验证）"""
    
    print("✅ 开始VERIFYING状态：全面验证与测试")
    
    verification_result = VerifyingResult()
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_verifying_stage()
    
    # Action 1: 服务层验证
    print("执行服务层验证...")
    service_tests = run_comprehensive_service_tests()
    verification_result.add_test_suite("service", service_tests)
    print(f"  服务层测试: {service_tests.passed_count}/{service_tests.total_count} 通过")
    
    # Action 2: 功能层验证
    print("执行功能层验证...")
    functional_tests = run_comprehensive_functional_tests()
    verification_result.add_test_suite("functional", functional_tests)
    print(f"  功能层测试: {functional_tests.passed_count}/{functional_tests.total_count} 通过")
    
    # Action 3: 数据层验证
    print("执行数据层验证...")
    data_tests = run_comprehensive_data_tests()
    verification_result.add_test_suite("data", data_tests)
    print(f"  数据层测试: {data_tests.passed_count}/{data_tests.total_count} 通过")
    
    # Action 4: 综合回归测试
    print("执行回归测试...")
    regression_tests = run_comprehensive_regression_tests()
    verification_result.add_test_suite("regression", regression_tests)
    print(f"  回归测试: {regression_tests.passed_count}/{regression_tests.total_count} 通过")
    
    # Action 5: 原问题解决验证
    print("执行原问题解决验证...")
    original_issue_test = verify_original_issue_completely_resolved()
    verification_result.add_test_suite("original_issue", original_issue_test)
    print(f"  原问题验证: {original_issue_test.passed_count}/{original_issue_test.total_count} 通过")
    
    # Action 6: 边界情况测试
    print("执行边界情况测试...")
    edge_case_tests = run_edge_case_tests()
    verification_result.add_test_suite("edge_cases", edge_case_tests)
    print(f"  边界测试: {edge_case_tests.passed_count}/{edge_case_tests.total_count} 通过")
    
    # Action 7: 性能影响评估
    print("执行性能影响评估...")
    performance_tests = run_performance_impact_tests()
    verification_result.add_test_suite("performance", performance_tests)
    print(f"  性能测试: {performance_tests.passed_count}/{performance_tests.total_count} 通过")
    
    # 计算总体验证结果
    verification_result.calculate_overall_result()
    
    # 更新TodoWrite
    todo_tracker.complete_verification_action("全面验证测试")
    
    print(f"\n✅ VERIFYING状态执行完成")
    print(f"总体通过率: {verification_result.overall_pass_rate*100:.1f}%")
    print(f"关键测试通过: {verification_result.critical_tests_passed}")
    
    verification_result.todo_tracker = todo_tracker
    return verification_result

def run_comprehensive_service_tests() -> TestSuite:
    """全面的服务层测试"""
    tests = TestSuite("service_layer")
    
    # 1. API健康检查
    api_health = test_api_health_comprehensive()
    tests.add_test("api_health", api_health)
    
    # 2. 所有关键端点测试
    critical_endpoints = [
        "/api/v1/users",
        "/api/v1/categories", 
        "/api/v1/purchases",
        "/api/admin/v1/users",
        "/api/admin/v1/categories"
    ]
    
    for endpoint in critical_endpoints:
        endpoint_test = test_single_endpoint(endpoint)
        tests.add_test(f"endpoint_{endpoint.replace('/', '_')}", endpoint_test)
    
    # 3. 数据库连接稳定性
    db_connection_test = test_database_connection_stability()
    tests.add_test("database_connection", db_connection_test)
    
    # 4. 认证和权限
    auth_test = test_authentication_and_authorization()
    tests.add_test("authentication", auth_test)
    
    return tests

def run_comprehensive_functional_tests() -> TestSuite:
    """全面的功能层测试"""
    tests = TestSuite("functional_layer")
    
    # 1. 用户管理功能
    user_management_test = test_user_management_functions()
    tests.add_test("user_management", user_management_test)
    
    # 2. 分类管理功能
    category_management_test = test_category_management_functions()
    tests.add_test("category_management", category_management_test)
    
    # 3. 购买记录功能
    purchase_records_test = test_purchase_records_functions()
    tests.add_test("purchase_records", purchase_records_test)
    
    # 4. 数据关联功能
    data_relations_test = test_data_relations_functions()
    tests.add_test("data_relations", data_relations_test)
    
    # 5. UI数据显示功能
    ui_display_test = test_ui_data_display_functions()
    tests.add_test("ui_display", ui_display_test)
    
    return tests

def run_comprehensive_data_tests() -> TestSuite:
    """全面的数据层测试"""
    tests = TestSuite("data_layer")
    
    # 1. 数据完整性
    data_integrity_test = test_data_integrity_comprehensive()
    tests.add_test("data_integrity", data_integrity_test)
    
    # 2. 外键约束
    foreign_key_test = test_foreign_key_constraints()
    tests.add_test("foreign_keys", foreign_key_test)
    
    # 3. UUID一致性
    uuid_consistency_test = test_uuid_consistency()
    tests.add_test("uuid_consistency", uuid_consistency_test)
    
    # 4. 字段映射正确性
    field_mapping_test = test_field_mapping_correctness()
    tests.add_test("field_mapping", field_mapping_test)
    
    return tests

def verify_original_issue_completely_resolved() -> TestSuite:
    """验证原问题完全解决"""
    tests = TestSuite("original_issue_resolution")
    
    # 1. 重现原问题场景
    reproduce_test = test_reproduce_original_issue_scenario()
    tests.add_test("reproduce_scenario", reproduce_test)
    
    # 2. 验证修复效果
    fix_effectiveness_test = test_fix_effectiveness()
    tests.add_test("fix_effectiveness", fix_effectiveness_test)
    
    # 3. 确认问题不再出现
    no_recurrence_test = test_no_issue_recurrence()
    tests.add_test("no_recurrence", no_recurrence_test)
    
    return tests
```

### ✅ **Proof Requirements**
```python
def verify_verifying_proof(result: VerifyingResult) -> bool:
    """验证VERIFYING状态的完成证明 - 为转换到COMPLETED状态准备"""
    
    print("✅ 验证VERIFYING状态完成证明...")
    
    # Proof 1: 所有关键测试套件100%通过
    critical_suites = ["service", "functional", "data", "original_issue"]
    all_critical_passed = all(
        result.test_suites[suite].pass_rate == 1.0
        for suite in critical_suites
        if suite in result.test_suites
    )
    
    # Proof 2: 原问题完全解决确认
    original_issue_resolved = (
        "original_issue" in result.test_suites and
        result.test_suites["original_issue"].passed and
        result.test_suites["original_issue"].pass_rate == 1.0
    )
    
    # Proof 3: 零回归问题确认
    no_regression_issues = (
        "regression" in result.test_suites and
        result.test_suites["regression"].passed and
        result.test_suites["regression"].failed_count == 0
    )
    
    # Proof 4: 核心业务功能完全正常
    core_functions_perfect = (
        "functional" in result.test_suites and
        result.test_suites["functional"].pass_rate == 1.0
    )
    
    # Proof 5: 数据完整性100%保持
    data_integrity_perfect = (
        "data" in result.test_suites and
        result.test_suites["data"].pass_rate == 1.0
    )
    
    # Proof 6: 服务稳定性确认
    service_stability_ok = (
        "service" in result.test_suites and
        result.test_suites["service"].pass_rate >= 0.95  # 允许少量非关键服务测试失败
    )
    
    # Proof 7: 性能无严重退化
    performance_acceptable = (
        "performance" not in result.test_suites or  # 如果没有性能测试套件则跳过
        result.test_suites["performance"].pass_rate >= 0.8  # 性能测试80%通过即可
    )
    
    # Proof 8: 边界情况处理正常
    edge_cases_handled = (
        "edge_cases" not in result.test_suites or  # 如果没有边界测试套件则跳过
        result.test_suites["edge_cases"].pass_rate >= 0.9  # 边界测试90%通过
    )
    
    # Proof 9: 总体验证通过率达标
    overall_pass_rate_sufficient = result.overall_pass_rate >= 0.95
    
    # 打印证明检查结果
    proofs = {
        "关键测试100%通过": all_critical_passed,
        "原问题完全解决": original_issue_resolved,
        "零回归问题": no_regression_issues,
        "核心功能完全正常": core_functions_perfect,
        "数据完整性100%": data_integrity_perfect,
        "服务稳定性确认": service_stability_ok,
        "性能无严重退化": performance_acceptable,
        "边界情况处理正常": edge_cases_handled,
        "总体通过率达标": overall_pass_rate_sufficient
    }
    
    for proof_name, passed in proofs.items():
        status = "✅" if passed else "❌"
        print(f"  {status} {proof_name}")
        
        # 详细说明关键指标
        if proof_name == "总体通过率达标":
            print(f"      总体通过率: {result.overall_pass_rate*100:.1f}%")
        elif proof_name == "关键测试100%通过":
            for suite in critical_suites:
                if suite in result.test_suites:
                    rate = result.test_suites[suite].pass_rate
                    print(f"      {suite}: {rate*100:.1f}%")
    
    # 总体证明结果
    proof_passed = all(proofs.values())
    
    if proof_passed:
        print("✅ VERIFYING状态证明通过，可以转换到COMPLETED状态")
        # 更新TodoWrite状态
        if hasattr(result, 'todo_tracker'):
            result.todo_tracker.complete_stage("VERIFYING")
    else:
        failed_proofs = [name for name, passed in proofs.items() if not passed]
        print(f"❌ VERIFYING状态证明失败，未满足：{', '.join(failed_proofs)}")
    
    return proof_passed
```

### 📊 **State Metrics**
```python
verifying_metrics = {
    "total_tests": total_test_count,
    "passed_tests": passed_test_count,
    "pass_rate": passed_test_count / total_test_count,
    "critical_tests_passed": critical_passed,
    "regression_detected": regression_count
}
```

---

## 🏁 STATE-5: COMPLETED (完成状态)

### 📍 **State Definition**
```yaml
State: COMPLETED
Description: 诊断修复流程成功完成，生成总结报告
Duration: 预计5分钟
Status: FINAL
```

### 🎯 **Trigger Conditions**
```yaml
Entry Triggers:
  - FROM: VERIFYING
  - WHEN: 所有验证通过
  - CONDITION: all_verifications_passed == True
```

### ⚡ **Execute Actions**
```python
def execute_completed_state(all_states_data: Dict[str, Any]) -> CompletedResult:
    """COMPLETED状态的执行内容 - 对应阶段4后半部分（总结与发散优化）"""
    
    print("🏁 开始COMPLETED状态：总结与发散优化")
    
    # 初始化TodoWrite跟踪
    todo_tracker = TodoTracker()
    todo_tracker.start_completed_stage()
    
    # Action 1: 执行过程总结
    print("生成执行过程总结...")
    execution_summary = generate_detailed_execution_summary(all_states_data)
    todo_tracker.complete_action("执行过程总结")
    
    # Action 2: 技术问题总结
    print("生成技术问题总结...")
    technical_summary = generate_comprehensive_technical_summary(all_states_data)
    todo_tracker.complete_action("技术问题总结")
    
    # Action 3: 根因分析报告
    print("生成根因分析报告...")
    root_cause_report = generate_root_cause_analysis_report(all_states_data)
    todo_tracker.complete_action("根因分析报告")
    
    # Action 4: 修复方案总结
    print("生成修复方案总结...")
    fix_summary = generate_fix_implementation_summary(all_states_data)
    todo_tracker.complete_action("修复方案总结")
    
    # Action 5: 验证结果分析
    print("生成验证结果分析...")
    verification_analysis = generate_verification_analysis(all_states_data)
    todo_tracker.complete_action("验证结果分析")
    
    # Action 6: 经验教训提取
    print("提取经验教训...")
    lessons_learned = extract_comprehensive_lessons_learned(all_states_data)
    todo_tracker.complete_action("经验教训提取")
    
    # Action 7: 发散优化建议
    print("生成发散优化建议...")
    optimization_suggestions = generate_optimization_suggestions(all_states_data)
    todo_tracker.complete_action("发散优化建议")
    
    # Action 8: 预防措施建议
    print("生成预防措施建议...")
    prevention_measures = generate_prevention_measures(all_states_data)
    todo_tracker.complete_action("预防措施建议")
    
    # Action 9: 质量评估
    print("执行质量评估...")
    quality_assessment = perform_quality_assessment(all_states_data)
    todo_tracker.complete_action("质量评估")
    
    # 计算总体指标
    total_execution_time = calculate_total_execution_time(all_states_data)
    overall_quality_grade = calculate_overall_quality_grade(all_states_data)
    
    # 标记所有TodoWrite任务完成
    todo_tracker.mark_all_completed()
    
    print(f"\n✅ COMPLETED状态执行完成")
    print(f"总执行时间: {total_execution_time}分钟")
    print(f"质量等级: {overall_quality_grade}")
    
    return CompletedResult(
        execution_summary=execution_summary,
        technical_summary=technical_summary,
        root_cause_report=root_cause_report,
        fix_summary=fix_summary,
        verification_analysis=verification_analysis,
        lessons_learned=lessons_learned,
        optimization_suggestions=optimization_suggestions,
        prevention_measures=prevention_measures,
        quality_assessment=quality_assessment,
        total_execution_time=total_execution_time,
        quality_grade=overall_quality_grade,
        todo_tracker=todo_tracker
    )

def generate_detailed_execution_summary(all_states_data: Dict[str, Any]) -> ExecutionSummary:
    """生成详细的执行过程总结"""
    summary = ExecutionSummary()
    
    # 统计各状态执行情况
    searching_data = all_states_data.get("SEARCHING")
    if searching_data:
        summary.searching_stats = {
            "files_found": searching_data.total_files_found,
            "search_tools_used": len(searching_data.search_results.get_all_tools()),
            "execution_time": searching_data.execution_time
        }
    
    analyzing_data = all_states_data.get("ANALYZING")
    if analyzing_data:
        summary.analyzing_stats = {
            "rounds_executed": len(analyzing_data),
            "total_hypotheses": sum(len(r.hypotheses) for r in analyzing_data),
            "confirmed_issues": sum(r.confirmed_issues for r in analyzing_data),
            "execution_time": sum(r.execution_time for r in analyzing_data)
        }
    
    repairing_data = all_states_data.get("REPAIRING")
    if repairing_data:
        summary.repairing_stats = {
            "attempts_made": len(repairing_data),
            "fixes_implemented": sum(len(r.implementations) for r in repairing_data),
            "success_rate": repairing_data[-1].implementation_success_rate,
            "execution_time": sum(r.execution_time for r in repairing_data)
        }
    
    verifying_data = all_states_data.get("VERIFYING")
    if verifying_data:
        summary.verifying_stats = {
            "test_suites_run": len(verifying_data.test_suites),
            "total_tests": sum(suite.total_count for suite in verifying_data.test_suites.values()),
            "pass_rate": verifying_data.overall_pass_rate,
            "execution_time": verifying_data.execution_time
        }
    
    return summary

def generate_optimization_suggestions(all_states_data: Dict[str, Any]) -> OptimizationSuggestions:
    """生成具体的优化建议"""
    suggestions = OptimizationSuggestions()
    
    # 基于发现的问题类型生成建议
    analyzing_data = all_states_data.get("ANALYZING")
    if analyzing_data:
        for round_result in analyzing_data:
            if round_result.root_cause:
                if round_result.root_cause.category == "FIELD_MAPPING":
                    suggestions.add_suggestion("代码质量", "建立字段映射规范和检查机制")
                    suggestions.add_suggestion("开发流程", "在代码审查中增加UUID使用检查")
                elif round_result.root_cause.category == "UI_DATA_BINDING":
                    suggestions.add_suggestion("架构改进", "优化UI数据绑定层的错误处理")
                    suggestions.add_suggestion("测试覆盖", "增加UI数据显示的自动化测试")
    
    # 基于修复过程生成建议
    repairing_data = all_states_data.get("REPAIRING")
    if repairing_data and len(repairing_data) > 1:
        suggestions.add_suggestion("工具改进", "建立修复脚本模板以减少手动错误")
    
    # 基于验证结果生成建议
    verifying_data = all_states_data.get("VERIFYING")
    if verifying_data:
        if verifying_data.overall_pass_rate < 1.0:
            suggestions.add_suggestion("测试完善", "增强测试覆盖率特别是边界情况")
    
    return suggestions
```

---

## 🔄 STEP执行控制器

```python
class STEPFlowController:
    """STEP模型流程控制器"""
    
    def __init__(self):
        self.current_state = "INIT"
        self.state_data = {}
        self.execution_log = []
    
    def run_diagnostic_flow(self, $ARGUMENTS: str):
        """执行完整的STEP诊断流程"""
        
        # 状态转换映射
        state_transitions = {
            "INIT": self.transition_to_searching,
            "SEARCHING": self.transition_to_analyzing, 
            "ANALYZING": self.transition_to_repairing,
            "REPAIRING": self.transition_to_verifying,
            "VERIFYING": self.transition_to_completed,
            "COMPLETED": None
        }
        
        while self.current_state != "COMPLETED":
            print(f"🔄 执行状态: {self.current_state}")
            
            # 执行当前状态
            result = self.execute_current_state()
            self.state_data[self.current_state] = result
            
            # 验证完成证明
            if self.verify_state_proof(result):
                # 状态转换
                transition_func = state_transitions[self.current_state]
                if transition_func:
                    transition_func()
                else:
                    break
            else:
                # 证明失败，重试当前状态
                print(f"❌ {self.current_state} 状态证明失败，重试")
                continue
        
        return self.state_data["COMPLETED"]
    
    def execute_current_state(self):
        """执行当前状态的操作"""
        if self.current_state == "SEARCHING":
            return execute_searching_state()
        elif self.current_state == "ANALYZING":
            return self.execute_analyzing_with_loops()
        elif self.current_state == "REPAIRING":
            return self.execute_repairing_with_retries()
        elif self.current_state == "VERIFYING":
            return execute_verifying_state()
        elif self.current_state == "COMPLETED":
            return execute_completed_state(self.state_data)
```

