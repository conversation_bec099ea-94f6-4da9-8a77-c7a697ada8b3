description: 基于 `$ARGUMENTS` 按照 完整问题诊断与修复流程 v1.3 执行

# 🔍 完整问题诊断与修复流程 v1.3

## 🎯 核心目标

**解决痛点**：
- AI容易在信息不完整时急于下结论，导致误判根因
- AI容易基于假设而非证据进行分析，导致多轮修复失败
- AI容易忽略数据流完整性和执行路径验证，导致分析错误代码
- ⭐ **v1.2新增**：AI容易症状与根因混淆，缺少系统性检查，修复验证不闭环
- 🆕 **v1.3新增**：AI容易忽略项目规范约定，缺少阶段执行质量控制，症状理解与用户意图偏差

**设计原则**：
- ✅ **证据驱动**：所有分析必须基于实际代码、数据流、执行路径等具体证据
- ✅ **数据流追踪**：从前端输入到后端存储的完整数据生命周期分析
- ✅ **执行路径验证**：确认代码真正执行的路径，避免分析错误代码
- ✅ **双强制循环机制**：第一阶段至少2轮分析循环+第二阶段至少2轮修复循环
- ⭐ **v1.2新增**：**五大通用原则**：症状分层、证据优先、假设验证、系统检查、修复闭环
- 🆕 **v1.3新增**：**三大质量机制**：项目规范对接、阶段检查点汇报、用户症状确认

**核心诊断方法论**：
```
🎯 症状分层 → 📊 证据优先 → 🔍 假设验证 → 🛠️ 系统检查 → ✅ 修复闭环
           ↓
🆕 规范对接 → 🆕 检查点汇报 → 🆕 用户症状确认
```

---

## 🎯 v1.2核心升级：五大通用原则

### **原则1：症状分层原则**
**目标**：建立表象→中层→根因的递进挖掘框架

#### **分层结构**
- **表象层症状**：用户可见的错误现象、界面异常、功能失效
- **中层症状**：系统级异常表现、服务错误、配置问题、环境异常  
- **根因症状**：技术层面的确切原因、代码逻辑错误、数据流异常

#### **递进规则**
- **禁止跳层**：不能从表象症状直接假设根因，必须逐层深入
- **完整覆盖**：每一层的症状收集必须完整，不能遗漏关键信息
- **层次验证**：每一层的症状分析必须有具体证据支撑

#### **应用示例**
```
❌ 错误：批处理文件启动不了 → 直接假设工作目录问题
✅ 正确：批处理文件启动不了 → 查看错误日志 → Python依赖缺失 → 缺少psutil包
```

### **原则2：最直接证据优先原则**
**目标**：日志、异常、错误信息优先于推测分析

#### **证据等级排序**
1. **一级证据**：错误日志、异常堆栈、具体错误信息
2. **二级证据**：调试输出、状态信息、配置内容
3. **三级证据**：代码逻辑分析、数据流推理
4. **禁用证据**：未验证假设、理论推测、"应该"判断

#### **强制优先级机制**
- **必须先查看**：所有可用的一级证据
- **然后查看**：相关的二级证据  
- **最后分析**：三级证据和逻辑推理
- **严格禁止**：基于假设开始修复

#### **应用示例**
```
✅ 正确流程：问题报告 → 立即查看admin.log → 发现ModuleNotFoundError → 定位psutil缺失
❌ 错误流程：问题报告 → 推测工作目录问题 → 修复目录 → 发现还有问题 → 才查看日志
```

### **原则3：假设识别与验证原则**
**目标**：显性化隐含假设并建立主动验证机制

#### **假设识别技巧**
- **语言标识**：识别"应该"、"可能"、"估计"、"通常"等假设性表述
- **逻辑标识**：识别缺少证据支撑的推理链条
- **经验标识**：识别基于经验而非当前证据的判断

#### **假设标记系统**
- **[假设]**：明确标记所有假设性判断
- **[验证]**：为每个假设设计具体验证步骤
- **[确认]**：记录假设验证的结果

#### **验证机制**
- **具体验证**：每个假设必须有可操作的验证步骤
- **证据验证**：验证结果必须基于具体证据
- **结果记录**：验证结果必须明确记录

#### **应用示例**
```
❌ 隐性假设：批处理文件启动不了，应该是工作目录问题
✅ 显性处理：
[假设] 可能是工作目录问题
[验证] 检查批处理文件中的路径设置和当前工作目录
[确认] 工作目录设置正确，此假设不成立，需要查看其他证据
```

### **原则4：主动完整性检查原则**
**目标**：系统性发现问题而非被动等待错误暴露

#### **系统性检查框架**
- **环境完整性**：系统环境、运行环境、依赖环境的系统检查
- **配置完整性**：配置文件、参数设置、路径配置的系统检查
- **代码完整性**：代码逻辑、数据流、执行路径的系统检查
- **数据完整性**：数据结构、字段映射、数据一致性的系统检查

#### **并行检查策略**
- **多源并查**：同时检查多个可能的问题源
- **全面覆盖**：确保检查覆盖所有关键环节
- **优先排序**：按照问题可能性排序检查项目

#### **检查清单模式**
- **标准清单**：针对常见问题类型建立标准检查清单
- **定制清单**：根据具体问题特征定制专项检查清单
- **完整记录**：记录每个检查项目的检查结果

#### **应用示例**
```
✅ 系统性检查：
□ 检查错误日志文件
□ 检查虚拟环境状态  
□ 检查依赖包完整性
□ 检查Python版本兼容性
□ 检查配置文件完整性
□ 检查路径和权限设置
```

### **原则5：修复闭环验证原则**
**目标**：每轮修复后的强制确认和根本问题解决验证

#### **修复验证层次**
- **修复验证**：验证当前修复是否解决了预期的问题
- **功能验证**：验证修复后的功能是否正常工作
- **根本验证**：验证是否解决了原始的根本问题
- **完整验证**：验证修复没有引入新的问题

#### **强制验证机制**
- **即时验证**：每次修复后立即进行验证，不能延后
- **具体验证**：验证必须基于具体操作和具体结果
- **完整验证**：验证必须覆盖修复的所有影响范围
- **结果记录**：验证结果必须详细记录

#### **闭环确认流程**
1. **修复实施** → 2. **即时验证** → 3. **功能确认** → 4. **根本确认** → 5. **完整确认**

#### **应用示例**
```
✅ 修复闭环：
1. 修复：添加缺失的psutil依赖
2. 即时验证：pip install成功
3. 功能确认：Python应用能正常启动
4. 根本确认：解决了原始的"批处理文件启动不了"问题
5. 完整确认：没有引入新的依赖冲突或其他问题
```

---

## 🆕 v1.3核心升级：三大质量机制

### **机制1：项目规范对接机制**
**目标**：确保所有诊断和修复严格遵循项目技术规范

#### **规范理解框架**
- **技术栈规范**：框架选择、版本约定、架构模式、开发规范
- **命名规范**：变量命名、函数命名、文件命名、数据库命名
- **API规范**：接口设计、版本控制、权限控制、错误处理
- **数据库规范**：表设计、字段约定、关系设计、索引策略

#### **任务相关提取**
- **当前任务识别**：明确当前诊断修复涉及的具体功能模块
- **相关规范筛选**：从项目规范中提取与当前任务直接相关的约定
- **冲突预警机制**：识别可能与现有规范产生冲突的修复方案
- **规范遵循验证**：确保所有分析和修复符合项目规范要求

#### **应用原则**
- **强制读取**：每次诊断必须首先读取并理解项目规范
- **相关提取**：只关注与当前任务相关的规范内容，避免信息过载
- **持续对照**：在整个诊断修复过程中持续对照项目规范
- **规范优先**：当技术方案与项目规范冲突时，优先遵循项目规范

### **机制2：阶段检查点汇报机制**
**目标**：确保每个阶段完整执行，质量可控，进度透明

#### **检查点汇报框架**
- **完成情况汇报**：详细汇报该阶段所有步骤的完成状态
- **关键发现总结**：总结该阶段发现的关键证据、问题点、技术洞察
- **质量自检确认**：对照该阶段完成标准进行质量自检
- **下阶段准备状态**：确认为下一阶段提供的输入是否完整和准确

#### **标准汇报格式**
```
## 📋 [阶段名称] 检查点汇报

### ✅ 完成情况
- [步骤1] 完成状态 + 关键成果
- [步骤2] 完成状态 + 关键成果
- [步骤N] 完成状态 + 关键成果

### 🔍 关键发现
- 主要证据：[具体证据内容]
- 核心问题：[问题描述]
- 技术洞察：[技术发现]

### ✅ 质量确认
- [ ] 所有完成标准已达成
- [ ] 证据收集完整可信
- [ ] 分析逻辑严谨无假设
- [ ] 项目规范遵循到位

### 🎯 下阶段准备
- 输入交付：[为下阶段提供的具体输入]
- 质量状态：[输入质量评估]
- 执行建议：[对下阶段执行的建议]
```

### **机制3：用户症状确认机制**
**目标**：确保诊断方向与用户真实问题完全一致，避免理解偏差

#### **症状列表生成框架**
- **用户描述解析**：深入理解用户问题描述的核心要点
- **结构化症状提取**：按照三层症状模式提取结构化症状列表
- **优先级排序**：根据影响程度和紧急程度对症状进行优先级排序
- **确认点标记**：标记需要用户确认的关键理解点

#### **用户确认交互**
- **症状列表展示**：以清晰的格式向用户展示提取的症状列表
- **理解确认请求**：明确请求用户确认症状理解的准确性
- **偏差修正机制**：基于用户反馈修正症状理解偏差
- **执行方向确认**：确认后续诊断修复的执行重点和方向

#### **标准确认格式**
```
## 🎯 症状列表用户确认

基于您的问题描述，我理解的症状如下：

### 📋 表象层症状（用户直接感受）
1. [症状1] - 优先级：高/中/低
2. [症状2] - 优先级：高/中/低

### 🔧 中层症状（系统表现推测）
1. [症状1] - 推测依据：[证据]
2. [症状2] - 推测依据：[证据]

### 🔍 关键确认点
- 确认点1：我理解的[具体方面]是否准确？
- 确认点2：优先解决的重点是否是[具体问题]？

请确认以上理解是否准确，如有偏差请指正，确认无误后我将基于此症状列表进行深度诊断。
```

---

## 📋 阶段0：基础建立（v1.3增强版本）

### **步骤0.0：项目规范与任务对接**

**目标**：`项目规范理解 + 任务相关提取 + 规范遵循确认 → 规范化执行基础`

#### **核心步骤**
- **项目规范读取**：完整读取CLAUDE.md项目规范文档内容
- **任务相关提取**：从项目规范中提取与当前任务直接相关的技术约定
- **规范理解确认**：确认对项目技术栈、命名规范、API规范、数据库规范的理解
- **执行约束建立**：基于项目规范建立后续诊断修复的执行约束

#### **v1.3核心要求**
- 🆕 **强制应用机制1**：必须首先理解项目规范，建立规范化执行基础
- ⭐ **应用原则2**：优先基于项目规范中的明确约定而非经验推测

#### **完成标准**
- [ ] 项目规范完整读取和理解
- [ ] 与当前任务相关的规范约定已明确提取
- [ ] 技术栈、命名、API、数据库规范理解到位
- [ ] 规范遵循的执行约束已建立
- [ ] 为规范化诊断修复提供基础保障

### **步骤0.1：数据流架构识别**

**目标**：`项目结构 + 数据流识别 + 执行路径映射 → 证据收集基础`

#### **核心步骤**
- **技术栈识别**：框架类型 + 数据库类型 + API架构 → 数据流特征
- **关键路径映射**：前端→API→服务→数据库的完整数据流路径
- **执行环境识别**：热加载机制 + 服务启动方式 + 调试能力 → 验证策略
- **日志系统定位**：日志文件位置 + 日志格式 + 调试信息输出方式

#### **v1.2增强 + v1.3对接**
- ⭐ **应用原则2**：优先查看最直接的配置和日志证据
- ⭐ **应用原则4**：系统性检查环境、配置、依赖完整性
- 🆕 **结合步骤0.0**：确保架构识别符合项目规范约定

#### **完成标准**
- [ ] 数据流架构清晰（前端→后端→数据库）
- [ ] 关键执行路径已映射
- [ ] 日志系统和调试方式已确定
- [ ] ⭐ 环境和依赖完整性已验证
- [ ] 🆕 架构识别符合项目规范要求
- [ ] 为症状收集提供技术基础

### **步骤0.2：执行路径验证能力建立**

**目标**：`WSL环境 + 服务连通 + 执行验证 → 证据收集能力`

#### **WSL环境能力重点**

```
证据收集能力边界
├── ✅ 代码分析：Read/Grep/Glob工具深度分析
├── ✅ 日志追踪：实时日志查看和模式匹配  
├── ✅ 数据流验证：curl + API测试验证数据流，端点：curl -s http://localhost:8000/docs、curl -s http://localhost:8000/openapi.json
├── ✅ 执行路径确认：通过日志模式确认真正执行的代码
└── ❌ 不能直接运行：假设Windows环境依赖已安装
```

#### **v1.2增强**
- ⭐ **应用原则3**：明确标记环境限制的假设并设计验证方案
- ⭐ **应用原则4**：建立系统性的执行路径验证清单

#### **完成标准**
- [ ] 服务连接正常（能获取API响应）
- [ ] 日志追踪能力确认
- [ ] 执行路径验证方法建立
- [ ] ⭐ 环境限制假设已明确标记
- [ ] 为深度分析提供工具基础

### **步骤0.3：症状完整性收集**

**目标**：`用户描述 + 系统表现 + 数据异常 → 完整症状清单`

#### **v1.2重点增强：三层症状收集**

**层次1：表象症状收集**
- 用户可见现象：具体操作步骤、触发条件、错误表现
- 界面异常：显示错误、功能失效、交互异常
- 系统行为：启动失败、运行中断、响应异常

**层次2：中层症状收集**
- 系统级异常：服务错误、配置问题、环境异常
- 数据流异常：API错误、数据传输问题、状态异常
- 服务响应：错误代码、异常信息、日志记录

**层次3：根因症状收集**
- 技术层面：代码逻辑错误、数据结构问题、依赖问题
- 执行路径：代码未执行、分支错误、调用异常
- 数据完整性：字段错误、类型不匹配、一致性问题

#### **v1.2核心要求**
- ⭐ **应用原则1**：严格按照三层进行症状收集，不能跳层
- ⭐ **应用原则2**：优先收集最直接的错误证据（日志、异常等）

#### **完成标准**
- [ ] 三层症状收集完整，层次清晰
- [ ] 最直接证据优先收集
- [ ] 具体异常现象记录清晰
- [ ] 为数据流追踪提供问题线索
- [ ] 症状优先级排序完成

### **步骤0.4：症状列表用户确认**

**目标**：`症状收集结果 + 结构化整理 + 用户交互确认 → 准确症状基础`

#### **核心步骤**
- **症状列表生成**：基于步骤0.3收集结果，生成结构化症状列表
- **优先级评估**：对症状按照影响程度和紧急程度进行优先级排序
- **理解要点标记**：标记需要用户确认的关键理解点和可能存在偏差的地方
- **用户确认交互**：以清晰格式展示症状列表，请求用户确认和修正

#### **v1.3核心要求**
- 🆕 **强制应用机制3**：必须向用户展示症状列表并获得确认
- ⭐ **应用原则1**：按照三层症状结构向用户展示
- ⭐ **应用原则2**：基于最直接证据向用户展示症状推测依据

#### **症状确认格式**
```
## 🎯 症状列表用户确认

基于您的问题描述和初步分析，我理解的症状如下：

### 📋 表象层症状（您直接感受到的问题）
1. [具体症状] - 优先级：高/中/低
2. [具体症状] - 优先级：高/中/低

### 🔧 中层症状（系统层面可能的表现）
1. [推测症状] - 推测依据：[具体证据或逻辑]
2. [推测症状] - 推测依据：[具体证据或逻辑]

### 🔍 关键确认点
- 确认点1：[需要确认的理解要点]
- 确认点2：[需要确认的优先级重点]

请确认以上理解是否准确，如有偏差请指正。确认无误后我将基于此症状列表进行深度诊断。
```

#### **完成标准**
- [ ] 结构化症状列表已生成并展示
- [ ] 症状优先级评估完成
- [ ] 关键理解点已标记
- [ ] 用户确认已获得，偏差已修正
- [ ] 为后续精准诊断提供准确症状基础

## 📋 阶段0检查点汇报

### ✅ 完成情况
- **步骤0.0** 项目规范理解 + 任务相关约定提取完成
- **步骤0.1** 数据流架构识别 + 执行路径映射完成
- **步骤0.2** WSL环境能力建立 + 验证方法确立完成
- **步骤0.3** 三层症状完整收集 + 优先级排序完成
- **步骤0.4** 症状列表用户确认 + 理解偏差修正完成

### 🔍 关键发现
- **项目规范**：[提取的关键技术约定和执行约束]
- **技术架构**：[识别的数据流特征和关键路径]
- **症状分析**：[确认的优先症状和证据线索]

### ✅ 质量确认
- [ ] 所有完成标准已达成
- [ ] 项目规范遵循到位
- [ ] 症状理解与用户意图一致
- [ ] 为第一阶段深度诊断提供完整基础

### 🎯 下阶段准备
- **输入交付**：规范化执行约束 + 准确症状列表 + 完整技术基础
- **质量状态**：基础建立质量良好，可支撑深度诊断循环
- **执行建议**：基于用户确认症状进行证据驱动的根因定位

---

## 📋 第一阶段：证据驱动的根因诊断循环（保持v1.1结构，增强原则应用）

**总目标**：`基于阶段0基础 → 证据驱动的根因定位`

## 🔄 强制循环控制器（第一阶段）

**循环要求**：
- **最少循环数**：强制执行至少2轮循环（无任何例外）
- **退出条件**：连续两轮循环无实质性新证据发现
- **最大限制**：5轮循环上限（防止无限循环）

**v1.2增强判断标准**：
- **✅ 实质性新证据**：新执行路径发现、新数据流异常、新字段映射问题、新代码逻辑错误（需有具体证据支撑）
- **❌ 非实质性信息**：重复已知信息、理论推测内容、未验证假设、常识性解释
- ⭐ **新增要求**：每轮循环必须应用v1.2五大原则

## 🔄 标准循环单元（第一阶段）

**每轮循环 = E1症状证据收集 → E2执行路径验证 → E3数据流完整追踪 → E4问题逐层隔离**

### **循环步骤E1：症状证据收集**

**目标**：`前轮发现 + 新症状线索 + 证据扩展 → 完整证据清单`

#### **v1.2重点增强**
- ⭐ **强制应用原则1**：症状分层收集，逐层深入
- ⭐ **强制应用原则2**：优先收集最直接证据（错误日志、异常堆栈）
- ⭐ **强制应用原则3**：识别并标记症状分析中的假设

#### **核心步骤**
- **前轮证据整合**：整合前轮循环发现的所有具体证据
- **分层症状追踪**：按照表象→中层→根因层次追踪症状
- **直接证据优先**：优先收集错误日志、异常信息等直接证据
- **假设识别标记**：标记症状分析中的假设性判断

#### **完成标准**
- [ ] 前轮证据完整整合
- [ ] 症状分层收集完成
- [ ] 最直接证据优先获取
- [ ] 假设性判断已明确标记
- [ ] 为执行路径验证提供线索

### **循环步骤E2：执行路径验证**

**目标**：`症状证据 + 代码分析 + 日志追踪 → 真实执行路径确认`

#### **v1.2重点增强**
- ⭐ **强制应用原则3**：明确标记路径分析中的假设
- ⭐ **强制应用原则4**：系统性检查所有可能的执行路径

#### **核心步骤**
- **日志反向追踪**：从错误日志反向找到产生日志的具体代码位置
- **系统性路径检查**：检查所有可能的代码执行路径
- **假设验证执行**：对路径分析中的假设进行具体验证
- **执行路径确认**：基于具体证据确认真实执行路径

#### **完成标准**
- [ ] 真实执行路径已确认
- [ ] 系统性路径检查完成
- [ ] 假设性分析已标记和验证
- [ ] 为数据流追踪提供准确路径

### **循环步骤E3：数据流完整追踪**

**目标**：`执行路径 + 数据结构 + 字段映射 → 数据流异常定位`

#### **v1.2重点增强**
- ⭐ **强制应用原则4**：系统性检查数据流的所有环节
- ⭐ **强制应用原则2**：优先基于实际数据和日志进行分析

#### **核心步骤**
- **数据生命周期追踪**：从前端输入→处理→传输→存储的完整生命周期
- **系统性数据检查**：系统检查数据结构、转换、传输的所有环节
- **实际数据验证**：基于实际数据和日志验证数据流正确性
- **数据异常定位**：精确定位数据流异常的具体位置

#### **完成标准**
- [ ] 数据完整生命周期已追踪
- [ ] 系统性数据流检查完成
- [ ] 基于实际数据的验证完成
- [ ] 数据异常点已精确定位

### **循环步骤E4：问题逐层隔离**

**目标**：`数据流分析 + 执行路径 + 逐层验证 → 根因精确定位`

#### **v1.2重点增强**
- ⭐ **强制应用原则1**：按照症状层次进行问题隔离
- ⭐ **强制应用原则4**：系统性检查所有相关层次

#### **逐层隔离策略**

**第1层：表象问题隔离**
- 检查用户界面和交互逻辑是否正确
- 验证用户操作和系统响应是否正常
- 确认表象问题的具体表现和触发条件

**第2层：中层问题隔离**  
- 检查系统服务和配置是否正确
- 验证环境设置和依赖关系
- 确认中层问题的具体原因和影响

**第3层：根因问题隔离**
- 检查代码逻辑和数据处理是否正确
- 验证技术实现和架构设计
- 确认根本问题的具体位置和机制

#### **完成标准**
- [ ] 问题已精确定位到具体层次和代码位置
- [ ] 按照症状层次完成系统性隔离
- [ ] 根因机制已清晰理解
- [ ] 修复策略已明确
- [ ] 循环决策判断完成（是否发现新证据）

## 📋 第一阶段检查点汇报

### ✅ 完成情况
- **循环轮数** [实际执行轮数] 轮诊断循环完成
- **E1症状证据收集** [每轮关键证据收集成果]
- **E2执行路径验证** [真实执行路径确认结果]
- **E3数据流完整追踪** [数据流异常定位成果]
- **E4问题逐层隔离** [根因精确定位结果]

### 🔍 关键发现
- **核心证据**：[发现的最关键的一级证据]
- **执行路径**：[确认的真实代码执行路径]
- **数据流异常**：[精确定位的数据流问题点]
- **根因定位**：[确定的问题根本原因和具体位置]

### ✅ 质量确认
- [ ] 强制循环要求已满足（至少2轮）
- [ ] 所有证据基于实际验证，无假设推测
- [ ] 根因定位精确到具体代码位置
- [ ] 修复策略清晰可执行
- [ ] 项目规范遵循持续到位

### 🎯 下阶段准备
- **输入交付**：精确根因定位 + 具体修复策略 + 完整证据链条
- **质量状态**：根因诊断质量良好，可支撑精确修复实施
- **执行建议**：基于根因定位进行针对性修复，避免盲目修复

---

## 📋 第二阶段：修复实施与验证强制循环（保持v1.1结构，增强验证机制）

**总目标**：`基于第一阶段输出 → 强制循环深化修复`

## 🔄 强制循环控制器（第二阶段）

**循环要求**：
- **最少循环数**：强制执行至少2轮循环（无任何例外）
- **退出条件**：连续两轮循环无实质性新修复需求
- **最大限制**：5轮循环上限（防止无限循环）

**v1.2增强判断标准**：
- **✅ 实质性新修复需求**：新功能缺陷、新代码问题、新质量问题、新验证失败（需有具体证据支撑）
- **❌ 非实质性信息**：重复已知问题、理论推测内容、一般性改进建议、常识性优化、未验证假设
- ⭐ **新增要求**：每轮循环必须应用v1.2修复闭环验证原则

## 🔄 标准循环单元（第二阶段）

**每轮循环 = F1修复方案设计 → F2修复实施执行 → F3修复效果验证 → F4修复质量评估**

### **循环步骤F1：修复方案设计**

**目标**：`前轮发现 + 根因分析 + 影响评估 → 精确修复方案`

#### **v1.2重点增强**
- ⭐ **强制应用原则3**：识别并标记修复方案中的假设
- ⭐ **强制应用原则4**：系统性评估修复的完整影响

#### **核心步骤**
- **前轮修复整合**：整合前轮循环的所有修复成果和新发现
- **根因修复设计**：基于第一阶段根因分析设计针对性修复
- **系统性影响评估**：系统评估修复对其他模块和功能的影响
- **假设标记验证**：标记修复方案中的假设并设计验证方法

#### **v1.3规范遵循增强**
- 🆕 **项目规范对照**：确保修复方案严格遵循项目技术规范
- 🆕 **命名规范检查**：修复涉及的命名必须符合项目命名约定
- 🆕 **API规范遵循**：修复涉及的接口设计必须符合项目API规范

#### **完成标准**
- [ ] 前轮修复成果完整整合
- [ ] 修复方案针对性强，直击根因
- [ ] 系统性影响分析完成，风险可控
- [ ] 修复假设已标记并有验证方案
- [ ] 🆕 修复方案完全遵循项目规范
- [ ] 为实施执行提供清晰指导

### **循环步骤F2：修复实施执行**

**目标**：`修复方案 + 工具协作 + 渐进实施 → 实际修复交付`

#### **v1.2重点增强**
- ⭐ **强制应用原则5**：每个修复步骤后立即进行验证

#### **核心步骤**
- **实施计划执行**：按照F1方案结果执行具体修复操作
- **渐进式修复**：Level 1核心修复→Level 2关联修复→Level 3边缘修复
- **步骤即时验证**：每个修复步骤完成后立即验证效果
- **修复记录维护**：详细记录每个修复的具体内容和验证结果

#### **v1.3规范执行增强**
- 🆕 **规范实施检查**：每个修复步骤都对照项目规范执行
- 🆕 **质量标准验证**：按照项目质量标准验证修复质量

#### **完成标准**
- [ ] 修复实施按计划完成
- [ ] 每个步骤都有即时验证记录
- [ ] 渐进式修复策略执行到位
- [ ] 所有变更都有详细记录
- [ ] 🆕 修复实施完全符合项目规范
- [ ] 为效果验证提供修复基础

### **循环步骤F3：修复效果验证**

**目标**：`修复结果 + 验证方案 + 真实测试 → 效果确认`

#### **v1.2重点增强**
- ⭐ **强制应用原则5**：完整的修复闭环验证
- ⭐ **强制应用原则2**：基于实际测试结果而非假设进行验证

#### **核心步骤**
- **修复验证**：验证当前修复是否解决了预期的具体问题
- **功能验证**：验证修复后的功能是否正常工作
- **根本验证**：验证是否解决了原始的根本问题
- **完整验证**：验证修复没有引入新的问题

#### **v1.3用户症状对照增强**
- 🆕 **症状列表验证**：对照阶段0确认的症状列表进行逐项验证
- 🆕 **用户体验确认**：确认修复后的用户体验是否符合预期

#### **完成标准**
- [ ] 修复验证：当前修复效果已确认
- [ ] 功能验证：相关功能正常工作
- [ ] 根本验证：原始问题已解决
- [ ] 完整验证：无新问题引入
- [ ] 🆕 症状列表逐项验证完成
- [ ] 所有验证基于实际测试结果

### **循环步骤F4：修复质量评估**

**目标**：`验证结果 + 质量标准 + 改进识别 → 质量确认与循环决策`

#### **v1.2重点增强禁止理论推测要求**
- ❌ **禁止行为**：基于"应该"/"理论上"的推测、基于修复内容推测效果、未验证断言
- ✅ **必须操作**：使用实际测试验证修复效果
- ✅ **必须记录**：测试命令、验证结果、具体数据
- ✅ **必须格式**：基于以上实际操作验证：[结论]

#### **v1.2增强验证操作要求**
- **修复效果验证**：实际测试+修复代码检查，记录测试结果、修复逻辑、效果数据
- **功能完整验证**：功能测试+系统级代码检查，记录功能状态、测试结果、底层修复逻辑
- **质量标准验证**：代码质量检查+依赖影响检查，记录质量指标、影响范围、改进建议
- **根本问题验证**：端到端测试+原始问题验证，记录问题解决状态、测试覆盖、完整性确认

#### **核心步骤**
- **质量标准验证**：按照预设质量标准验证修复效果
- **性能影响评估**：评估修复对系统性能的影响
- **代码质量评估**：评估修复代码的质量和可维护性
- **循环决策判断**：基于新修复需求判断是否继续循环

#### **完成标准**
- [ ] 修复质量评估基于实际操作完成
- [ ] 所有验证都有具体工具使用记录
- [ ] 无理论推测，全部基于实际验证
- [ ] ⭐ 原始问题的根本解决已确认
- [ ] 循环决策判断完成

## 📋 第二阶段检查点汇报

### ✅ 完成情况
- **循环轮数** [实际执行轮数] 轮修复循环完成
- **F1修复方案设计** [每轮修复方案设计成果]
- **F2修复实施执行** [具体修复实施成果]
- **F3修复效果验证** [修复效果验证结果]
- **F4修复质量评估** [修复质量评估结果]

### 🔍 关键成果
- **核心修复**：[实施的关键修复内容和具体位置]
- **验证结果**：[基于实际测试的验证结果数据]
- **质量评估**：[修复质量和代码质量评估结果]
- **根本解决**：[原始问题根本解决确认状态]

### ✅ 质量确认
- [ ] 强制循环要求已满足（至少2轮）
- [ ] 所有修复基于实际验证，无理论推测
- [ ] 修复完全遵循项目规范要求
- [ ] 原始症状列表已逐项解决
- [ ] 修复质量达到项目标准

### 🎯 最终交付
- **修复交付**：[完成的修复内容和质量状态]
- **验证报告**：[完整的验证测试报告和数据]
- **质量保证**：[修复质量保证和后续建议]

---

## 🎯 **核心诊断方法框架（保持v1.1精华）**

### **1. 数据流追踪框架**
**目标**：追踪数据从源头到终点的完整生命周期
- 识别数据的所有生成点和变化节点
- 验证数据在各环节的转换正确性
- 检测数据传递中的异常或丢失
- 确认数据一致性的关键验证点

### **2. 执行路径验证框架**
**目标**：确认代码真正执行的路径，排除分析干扰
- 通过输出特征反向定位执行代码
- 验证条件分支的实际执行情况
- 确认函数调用链的完整性
- 排除未执行代码的分析干扰

### **3. 数据结构一致性检查框架**
**目标**：验证数据发送方和接收方的结构匹配
- 对比数据结构的字段名称、类型、格式
- 追踪数据转换点的结构变化
- 识别数据格式不匹配导致的处理异常
- 确认接口契约的一致性

### **4. 逐层隔离诊断框架**
**目标**：按系统架构层次系统性排查问题
- 按照系统架构进行层次划分
- 每层独立验证功能正确性
- 识别问题的具体层次和位置
- 避免跨层问题的复杂化分析

### **5. 问题根因定位框架**
**目标**：从现象深入到本质，找到问题的真正原因
- 优先查找单一根因，避免多因素复杂化
- 重点关注数据流中断或异常的确切位置
- 从影响最小的修复点开始分析
- 基于证据而非假设进行根因推理

---

## 🚀 **v1.2实战应用案例**

### **案例：批处理文件启动失败问题**

#### **v1.1失效分析**
- ❌ 症状与根因混淆：把"批处理启动失败"当作问题本身
- ❌ 最直接证据被忽略：admin.log错误日志被延后查看
- ❌ 隐性假设驱动：基于"工作目录问题"假设开始修复
- ❌ 被动错误发现：问题逐个暴露，多轮修复
- ❌ 修复验证不闭环：每次修复后没有立即验证根本问题

#### **v1.2正确应用**
✅ **原则1应用**：症状分层
- 表象：批处理文件启动失败
- 中层：查看admin.log发现Python异常
- 根因：ModuleNotFoundError: No module named 'psutil'

✅ **原则2应用**：最直接证据优先  
- 立即查看admin.log错误日志（一级证据）
- 基于异常信息定位问题（二级证据）
- 避免基于推测开始修复

✅ **原则3应用**：假设识别与验证
- [假设] 可能是工作目录问题
- [验证] 检查admin.log发现不是路径问题
- [确认] 问题是Python依赖缺失

✅ **原则4应用**：主动完整性检查
- 系统检查：环境、配置、依赖、版本
- 发现：psutil依赖缺失，版本号错误
- 一次性发现所有问题

✅ **原则5应用**：修复闭环验证
- 修复：添加正确版本的psutil依赖
- 即时验证：pip install成功
- 功能确认：Python应用正常启动
- 根本确认：批处理文件启动成功
- 完整确认：无新问题引入

#### **v1.3增强应用**
🆕 **机制1应用**：项目规范对接
- 检查CLAUDE.md确认Python版本和依赖管理规范
- 确保依赖添加方式符合项目约定

🆕 **机制2应用**：阶段检查点汇报
- 阶段0：项目规范理解+症状确认完成
- 第一阶段：根因精确定位（psutil缺失）完成
- 第二阶段：规范化修复实施完成

🆕 **机制3应用**：用户症状确认
- 向用户确认：批处理启动失败是否是主要症状
- 确认优先级：立即解决启动问题还是深度排查
- 基于确认结果精准执行诊断修复

#### **效果对比**
- **v1.1结果**：5轮修复才成功（目录→环境→编码→依赖→版本）
- **v1.2预期**：1轮修复成功（直接定位psutil缺失问题）
- **v1.3预期**：1轮修复成功+规范遵循+用户体验确认

---

*本流程v1.3基于v1.2的五大通用原则精华，新增三大质量机制，进一步提升AI编程问题的执行规范性、质量可控性和用户体验，实现一次性成功率的显著提升。*