---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程 

## 📋 流程概览

```
阶段1: 信息收集与真实检查
阶段2: 假设-验证循环（2-5轮，每轮3步）
阶段3: 修复实施与验证循环（1-5轮，每轮4步）
阶段4: 总结与发散优化
```

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🛑 强制执行机制

### 阶段门禁条件（必须满足才能切换阶段）
- **阶段1→阶段2**：完成多维度信息搜索 + 识别核心症状 + TodoWrite标记completed
- **阶段2→阶段3**：满足循环退出条件 + 假设验证充分性确认 + TodoWrite完成验证记录
- **阶段3→阶段4**：验证通过率100% + 无副作用确认 + TodoWrite记录修复内容
- **阶段4完成**：总结报告完整 + 优化建议具体 + TodoWrite标记全部completed

### 违规定义和后果
#### 🚨 严重违规（必须重新执行）
- **跳过循环机制**：阶段2、3未执行循环→返回重新执行完整阶段
- **凭想象猜测**：未提供真实验证证据→必须补充验证步骤
- **提前退出阶段**：未满足门禁条件→必须补充完成所有必需步骤
- **简化实现逃避**：临时方案、伪代码→必须提供完整真实修复
- **分离假设-验证**：违反完整循环原则→必须重新执行包含假设调整的完整循环

#### ⚠️ 轻微违规（警告并纠正）
- **TodoWrite更新不及时**：立即补充更新
- **进度记录不准确**：重新统计并记录
- **循环状态记录不完整**：补充详细的轮次和步骤状态

### 强制自检清单（每阶段结束前必须完成）
在进入下一阶段前，AI必须回答以下问题（全部"是"才能继续）：
1. ✅ 我是否完成了本阶段的所有核心目标？
2. ✅ 我是否有任何猜测或未验证的假设？
3. ✅ 我是否满足了所有阶段切换条件？
4. ✅ 我的TodoWrite状态是否准确反映了执行情况？
5. ✅ 我是否执行了必需的循环机制（如适用于阶段2、3）？
6. ✅ 我是否保持了循环的完整性和自动化程度？

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：

- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **分离假设-验证** - 禁止将假设建立和验证人为分离，必须保持完整循环
- ❌ **人工循环决策** - 禁止在循环中设置人工决策点，必须基于客观条件自动循环
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：

- 文件读取、编辑、搜索、修改
- 代码分析和静态检查（wsl）
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证

**不能做的**：

- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 依赖检测规则

**避免误判原则**：

- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

---

## **阶段1: 信息收集与真实检查**

⚠️ **强制要求：本阶段必须完成多维度信息搜索和真实检查**
📋 **成功标准：识别核心症状 + 制定搜索策略 + 完成系统状态检查**
🚪 **门禁条件：必须满足所有检查要求才能进入阶段2**

### **步骤1.1: 问题信息解析**

- **1.1.1 问题描述分析**
  - 提取问题的核心症状和表现
  - 识别涉及的功能模块和组件
  - 分析问题的触发条件和环境
  - 确定问题的影响范围和严重程度

- **1.1.2 搜索策略制定**
  - **多维度搜索策略**：
    - 文件名模式搜索（Glob工具）
    - 内容关键词搜索（Grep工具）
    - 跨文件引用搜索（Task工具）
    - 配置文件搜索
    - 日志文件搜索
  - **搜索关键词库建立**：
    - 错误信息相关的所有关键词
    - 功能模块相关的所有关键词
    - 可能的变形和相似词汇

### **步骤1.2: 强制真实检查（禁止猜测）**

- **1.2.1 多工具组合搜索**

  ```
  必须使用的搜索组合：
  1. Glob: 按文件名模式搜索所有相关文件类型
  2. Grep: 按内容关键词搜索所有匹配内容
  3. Task: 复杂搜索和跨文件引用分析
  4. Read: 逐文件详细检查重要文件
  ```

- **1.2.2 搜索完整性验证**

  - **交叉验证**：用不同方法搜索相同内容，确保结果一致
  - **边界扩展**：从已发现文件出发，检查其引用和被引用
  - **遗漏检测**：检查是否存在预期但未找到的文件类型
  - **搜索报告**：详细记录所有搜索结果和发现的文件清单

- **1.2.3 系统状态检查**

  - **服务状态检测与智能启动**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，跳过启动
       - 如果返回非200或连接失败 → 需要启动服务
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 验证连接：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    6. 连接失败处理：
       - 如果在WSL环境中启动失败 → 提醒用户在Windows环境中启动服务
       - 记录具体错误，不允许假设成功
       - 暂停诊断流程，等待用户启动服务
    ```

  - **基础环境检查**：
    ```
    1. 检查关键文件是否存在
    2. 验证配置文件的正确性
    3. 检查日志文件的最新状态
    4. 确认数据库连接可用性
    ```

---

## **阶段2: 假设-验证循环**

⚠️ **强制要求：本阶段必须执行完整假设-验证循环机制（2-5轮）**
📋 **成功标准：系统性假设建立 + 充分验证迭代 + 动态假设优化 + 基于客观标准退出**
🚪 **门禁条件：满足科学化循环退出条件才能进入阶段3**

### **核心循环原则**

🔬 **科学方法论**：每轮循环必须包含完整的假设-验证闭环
🔄 **动态调整**：基于验证结果动态调整和补充假设体系
🎯 **自动化执行**：基于客观条件自动循环，无需人工决策

### **循环结构设计**

```
每轮循环包含3个步骤：
步骤2.1: 假设建立与调整
步骤2.2: 假设验证执行  
步骤2.3: 结果分析与新发现
自动循环判断（基于客观退出条件）

第1轮（强制系统性建立）：
- 建立完整的4层次假设体系
- 执行高优先级假设的验证
- 分析结果并识别新线索

第2-5轮（迭代优化）：
- 基于前轮发现调整假设
- 验证剩余和新增假设
- 持续优化假设体系直至满足退出条件
```

### **步骤2.1: 假设建立与调整**

#### **第1轮：系统性假设建立**

- **2.1.1 多层次问题假设建立**

  - **基础层假设**
    - **语法错误**：代码语法、拼写错误、导入错误
    - **配置错误**：配置文件格式、路径错误、参数错误
    - **权限错误**：文件权限、数据库权限、网络权限

  - **逻辑层假设**
    - **业务逻辑错误**：算法实现、条件判断、数据处理逻辑
    - **状态管理错误**：对象状态、数据状态、会话状态
    - **时序错误**：操作顺序、异步处理、事件时序

  - **系统层假设**
    - **架构设计问题**：模块耦合、接口设计、数据流设计
    - **性能问题**：资源消耗、响应时间、并发处理
    - **兼容性问题**：版本兼容、环境差异、平台差异

  - **集成层假设**
    - **模块集成问题**：接口调用、数据传递、错误传播
    - **外部集成问题**：数据库连接、API调用、文件系统访问
    - **环境集成问题**：开发环境、测试环境、生产环境差异

- **2.1.2 假设优先级排序**

  - **影响程度评估**
    - **高影响**：导致系统完全不可用的问题
    - **中影响**：导致功能异常但系统可用的问题
    - **低影响**：导致体验下降但功能正常的问题

  - **可能性评估**
    - **高可能性**：基于检查结果有明确证据支持的假设
    - **中可能性**：基于经验和逻辑推理的合理假设
    - **低可能性**：理论上可能但证据不足的假设

  - **验证成本评估**
    - **低成本**：简单检查或测试即可验证的假设
    - **中成本**：需要一定准备或配置才能验证的假设
    - **高成本**：需要大量时间或资源才能验证的假设

- **2.1.3 验证方法制定**
  - 为每个假设制定具体的验证方法
  - 确定验证所需的工具和步骤
  - 预估验证时间和资源需求

#### **第2-N轮：动态假设调整**

- **2.1.4 基于前轮结果的假设调整**
  - 分析前轮验证结果，识别需要调整的假设
  - 基于新发现的线索补充新假设
  - 合并相似假设，细化粗粒度假设
  - 淘汰已证伪的假设，重新排序优先级

### **步骤2.2: 假设验证执行**

#### **2.2.1 系统性假设验证方法**

- **代码层验证**：
  ```
  1. 语法检查：
     - Read: 详细检查相关代码文件
     - 分析导入语句的正确性
     - 检查函数调用的参数匹配
     - 验证变量名和类型的一致性
  
  2. 逻辑验证：
     - 分析业务逻辑的正确性
     - 检查条件判断的完整性
     - 验证异常处理的充分性
     - 确认数据流向的正确性
  ```

- **配置层验证**：
  ```
  1. 配置文件检查：
     - 验证JSON/YAML格式的正确性
     - 检查配置项的完整性
     - 确认配置值的合理性
     - 验证路径和URL的有效性
  
  2. 环境变量验证：
     - 检查必需环境变量的设置
     - 验证环境变量值的正确性
     - 确认环境变量的优先级
  ```

- **数据层验证**：
  ```
  1. 项目结构发现：
     - find . -name "*.py" | grep -E "(main|app|server|router|api)" | head -5
     - find . -name "*.json" -o -name "*.yaml" -o -name "*.toml" | head -10
     - grep -r "router\|@app\|@api" --include="*.py" . | head -10
  
  2. API端点自动发现：
     - curl -s http://localhost:8000/openapi.json | jq '.paths' | head -20
     - 自动选择测试端点（优先级：GET > 简单参数 > 常见功能）
     - 避免硬编码特定功能（如用户管理）
  
  3. 数据库连接验证：
     - 基于发现的API端点进行连接测试
     - 验证响应状态码和数据格式
     - 检查数据库表结构的正确性
     - 确认数据完整性和约束
  ```

- **功能层验证**：
  ```
  1. 渐进式API功能验证：
     - Level 1: 基础连通性验证（docs端点、openapi.json）
     - Level 2: 核心功能验证（基于发现的API端点）
     - Level 3: 业务流程验证（基于项目特定逻辑）
  
  2. 通用API测试策略：
     - 从OpenAPI文档自动选择测试端点
     - 优先测试GET类型的简单查询端点
     - 测试常见的健康检查和状态查询端点
     - 验证请求参数的处理和响应数据的正确性
  ```

#### **2.2.2 验证结果记录**

- **量化验证指标**：
  ```
  1. 假设验证统计：
     - 当前轮验证的假设数量
     - 验证通过的假设数量
     - 验证失败的假设数量
     - 需要进一步验证的假设数量
  
  2. 问题发现统计：
     - 确认的问题数量
     - 新发现的问题线索数量
     - 需要新建假设的发现数量
  ```

### **步骤2.3: 结果分析与新发现**

#### **2.3.1 验证结果分析**

- **成功验证分析**
  - 确认验证成功的假设，标记为已解决
  - 分析成功验证的模式，提取共同特征
  - 确定验证成功对问题定位的贡献

- **失败验证分析**
  - 分析验证失败的原因，区分是假设错误还是验证方法问题
  - 调整假设表述或验证方法
  - 从失败中获取新的问题线索

- **部分验证分析**
  - 识别需要进一步细化验证的假设
  - 将复杂假设分解为多个可验证的子假设
  - 制定下轮针对性验证策略

#### **2.3.2 新发现识别与整合**

- **新问题线索发现**
  - 在验证过程中发现的新问题症状
  - 意外的错误信息或异常行为
  - 与原假设不符的新证据

- **新假设产生**
  - 基于新发现提出新的问题假设
  - 调整现有假设的表述或范围
  - 重新评估假设的优先级

- **假设体系优化**
  - 合并相似或重复的假设
  - 细化过于宽泛的假设
  - 淘汰已被证伪的假设
  - 重新排列假设验证顺序

### **阶段2强制循环控制机制**

⚠️ **强制要求：本阶段必须执行完整假设-验证循环机制，不允许跳过**
📋 **循环追踪：使用TodoWrite工具记录每轮循环和3个步骤的状态**
🔄 **退出条件：必须满足科学化退出条件才能结束循环**

```
🛑 循环控制参数（强制执行）：
- 最小循环轮次：2轮（确保至少1轮调整优化）
- 最大循环轮次：5轮（防止无限循环）
- 进度量化：假设验证完成百分比 + 新发现统计
- 状态记录：每轮开始/结束和每步骤都必须更新TodoWrite

🔄 完整循环执行流程：
初始化：
1. 使用TodoWrite记录"阶段2第X轮循环"（X=1,2,3,4,5）
2. 设置循环计数器：当前轮次 = 1，最大轮次 = 5
3. 记录初始状态：总假设数 = 0，已验证数 = 0，问题确认数 = 0

每轮循环必须记录：
- 当前轮次：X/5
- 验证进度：已验证X个假设，剩余Y个，新增Z个
- 问题发现：确认X个问题，新发现Y个线索
- 假设调整：新增X个，调整Y个，淘汰Z个
- 步骤状态：2.1完成，2.2完成，2.3完成
- 退出条件检查：满足/不满足

循环执行：
2.1(假设建立/调整) → 2.2(验证执行) → 2.3(结果分析) → 自动循环判断

✅ 强制退出条件（满足任一即退出）：
- 假设验证完成率100% + 连续2轮无新假设产生 + 连续2轮无新问题发现
- 达到最大循环轮次5轮

❌ 禁止提前退出：
- 第1轮不允许退出（必须至少执行2轮）
- 假设验证完成率<80%不允许退出
- 未找到任何确认问题不允许退出
- 任何步骤(2.1-2.3)未完成不允许退出

🔧 强制执行要求：
- 每轮和每步骤开始必须更新TodoWrite状态为in_progress
- 每轮和每步骤结束必须记录完成状态和关键指标
- 必须保持假设-验证的完整循环，禁止分离执行
- 禁止设置人工决策点，必须基于客观条件自动循环
- 不允许停顿等待用户指令，必须自动进入下一轮
- 违反循环机制必须重新执行完整阶段2
```

---

## **阶段3: 修复实施与验证循环**

⚠️ **强制要求：本阶段必须执行完整修复-验证循环机制（1-5轮）**
📋 **成功标准：根本性修复实施 + 充分验证迭代 + 100%验证通过**
🚪 **门禁条件：验证通过率100%且无副作用才能进入阶段4**

### **核心循环原则**

🔧 **完整修复流程**：设计→实施→验证→评估形成完整闭环
🎯 **持续改进**：基于验证结果持续调整和改进修复方案
🚀 **自动化执行**：基于客观标准自动循环，确保修复质量

### **循环结构设计**

```
每轮循环包含4个步骤：
步骤3.1: 修复方案设计与调整
步骤3.2: 修复方案实施  
步骤3.3: 修复效果验证
步骤3.4: 验证结果评估
自动循环判断（基于客观退出条件）

第1轮（初始修复）：
- 基于阶段2发现设计初始修复方案
- 实施修复并验证效果
- 评估修复质量和副作用

第2-5轮（改进优化）：
- 基于前轮评估调整修复方案
- 实施改进修复并重新验证
- 持续优化直至达到100%验证通过
```

### **步骤3.1: 修复方案设计与调整**

#### **第1轮：初始修复方案设计**

- **3.1.1 根本性修复原则**
  - **禁止临时方案**：不允许临时绕过、简化处理、伪代码实现
  - **禁止症状修复**：必须修复根本原因，不是掩盖症状
  - **禁止破坏性修复**：修复不能破坏其他功能或引入新问题

- **3.1.2 修复方案设计**
  - **问题根因分析**：基于阶段2的确认问题进行根因分析
  - **修复策略制定**：制定针对根因的修复策略
  - **方案完整性设计**：确保修复方案覆盖所有相关问题
  - **影响评估**：评估修复对其他功能和模块的影响

#### **第2-N轮：修复方案调整**

- **3.1.3 基于前轮评估的方案调整**
  - 分析前轮验证中发现的问题和不足
  - 调整修复策略和实施方法
  - 优化修复方案的完整性和效率
  - 考虑新发现问题的修复集成

### **步骤3.2: 修复方案实施**

- **3.2.1 修复实施原则**
  - **原子性**：确保修复操作的原子性，避免部分修复状态
  - **可追踪性**：详细记录每个修复步骤，便于问题追踪
  - **可回滚性**：保留修复前状态，支持必要时的回滚操作

- **3.2.2 修复实施执行**
  - **代码修复**：修改相关源代码，确保逻辑正确性
  - **配置修复**：更新配置文件、环境变量、数据库配置
  - **数据修复**：修复数据问题、更新数据结构、恢复数据一致性
  - **文档同步**：同步更新相关文档、注释、API文档

- **3.2.3 修复质量检查**
  - 验证修复实施的完整性
  - 检查修复代码的质量和规范性
  - 确认修复未引入新的语法或逻辑错误

### **步骤3.3: 修复效果验证**

- **3.3.1 系统功能验证**

  - **服务端功能验证**：
    ```
    1. 服务端测试：
       - API文档测试：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
       - API结构测试：curl -s http://localhost:8000/openapi.json
       - 业务逻辑测试：基于openapi.json发现的端点进行测试
       - 数据处理测试：验证数据CRUD操作
    ```

  - **管理端功能验证**：
    ```
    1. 管理端测试：
       - 界面功能测试：验证界面响应和交互
       - 数据展示测试：验证数据正确展示
       - 功能完整性测试：验证核心功能可用
    ```

  - **模块间功能验证**：
    ```
    1. 模块间测试：
       - 模块通信测试：验证模块间数据传递
       - 接口调用测试：验证API调用正确性
       - 依赖关系测试：验证模块依赖正确
    ```

  - **端到端功能验证**：
    ```
    1. 端到端测试：
       - 业务流程测试：验证完整业务场景
       - 系统集成测试：验证系统整体功能
       - 性能基准测试：验证系统性能满足要求
    ```

- **3.3.2 WSL环境适配验证方法**

  ```
  1. 文件系统层验证：
     - find . -type f -name "*.log" | tail -5   # 发现日志文件
     - find . -name "config*" -o -name "settings*" | head -10  # 发现配置文件
     - ls -la [关键文件路径]  # 验证文件权限
  
  2. 服务层验证：
     - curl -s http://localhost:8000/docs  # 健康检查
     - curl -s http://localhost:8000/openapi.json | jq '.info'  # 服务信息
     - 基于发现的API端点进行功能验证
  
  3. 数据层验证：
     - 通过自动发现的API端点查询数据库状态
     - 验证数据一致性和完整性
     - 检查约束和索引的正确性
  
  4. 配置层验证：
     - 检查发现的配置文件正确性
     - 验证环境变量的设置
     - 确认路径和权限的正确性
  ```

- **3.3.3 验证维度**

  ```
  必须全覆盖的验证维度：
  1. 功能完整性验证：所有相关功能正常工作
  2. 数据一致性验证：数据完整性和一致性检查
  3. 业务逻辑验证：业务流程和逻辑正确性
  4. 性能稳定性验证：性能指标达到预期水平
  5. 无副作用验证：修复未引入新问题
  ```

### **步骤3.4: 验证结果评估**

- **3.4.1 验证结果分析**
  - **成功验证统计**：计算各维度验证通过率
  - **失败验证分析**：详细分析验证失败的原因和影响
  - **性能影响评估**：评估修复对系统性能的影响
  - **副作用检测**：识别修复可能引入的新问题

- **3.4.2 修复质量评估**
  - **完整性评估**：修复是否完全解决了根本问题
  - **稳定性评估**：修复后系统的稳定性和可靠性
  - **可维护性评估**：修复代码的可维护性和可扩展性
  - **符合性评估**：修复是否符合项目规范和最佳实践

- **3.4.3 改进建议制定**
  - 识别仍需改进的问题和不足
  - 制定下轮修复调整的具体建议
  - 评估是否需要修复策略的重大调整
  - 确定下轮修复的优先级和重点

### **阶段3强制循环控制机制**

⚠️ **强制要求：本阶段必须执行完整修复-验证循环机制，不允许跳过**
📋 **循环追踪：使用TodoWrite工具记录每轮循环和4个步骤的状态**
🔄 **退出条件：必须满足科学化退出条件才能结束循环**

```
🛑 循环控制参数（强制执行）：
- 最小循环轮次：1轮（但如有验证失败项必须执行第2轮）
- 最大循环轮次：5轮（防止无限循环）
- 进度量化：验证通过率 = 通过项/总验证项 × 100%
- 目标通过率：100%

🔄 完整循环执行流程：
初始化：
1. 使用TodoWrite记录"阶段3第X轮循环"（X=1,2,3,4,5）
2. 设置循环计数器：当前轮次 = 1，最大轮次 = 5
3. 记录初始验证项数量：统计需要验证的功能总数

每轮循环必须记录：
- 当前轮次：X/5
- 验证通过率：X%（通过项/总项×100%）
- 失败验证项：具体列表
- 修复调整：本轮修复的内容
- 步骤状态：3.1完成，3.2完成，3.3完成，3.4完成
- 退出条件检查：满足/不满足

循环执行：
3.1(方案设计/调整) → 3.2(方案实施) → 3.3(效果验证) → 3.4(结果评估) → 自动循环判断

✅ 强制退出条件（满足任一即退出）：
- 验证通过率达到100% + 连续2轮无新问题发现
- 达到最大循环轮次5轮

❌ 禁止提前退出：
- 验证通过率<100%且轮次<5不允许退出
- 仍有验证失败项不允许退出
- 未执行完整验证维度不允许退出
- 任何步骤(3.1-3.4)未完成不允许退出

🔧 强制执行要求：
- 每轮和每步骤开始必须更新TodoWrite状态为in_progress
- 每轮和每步骤结束必须记录完成状态和关键指标
- 必须保持修复-验证的完整循环，禁止分离执行
- 禁止设置人工决策点，必须基于客观条件自动循环
- 不允许停顿等待用户指令，必须自动进入下一轮
- 违反循环机制必须重新执行完整阶段3
```

### **步骤3.5: 修复记录与追踪（循环外）**

- **3.5.1 修复内容记录**
  - 详细记录最终修复方案的完整内容
  - 记录修复过程中的关键决策和调整
  - 建立修复前后的状态对比档案
  - 创建修复追踪和未来维护的指导文档

- **3.5.2 修复效果总结**
  - 总结修复解决的具体问题和效果
  - 记录修复后的性能指标和稳定性表现
  - 确认原始问题的完全消除
  - 验证修复的长期稳定性和可靠性

- **3.5.3 回滚预案建立**
  - 制定必要时的修复回滚方案
  - 保留关键的修复前状态信息
  - 建立修复问题的应急处理流程
  - 为未来类似问题的修复提供参考

---

## **阶段4: 总结与发散优化**

⚠️ **强制要求：本阶段必须完成工作总结和发散性优化建议**
📋 **成功标准：完整总结报告 + 具体优化建议 + 知识沉淀**
🚪 **门禁条件：所有总结内容完整且有价值才能完成整个流程**

### **步骤4.1: 诊断过程总结**

- **4.1.1 诊断流程回顾**
  - 记录各阶段执行情况和耗时
  - 分析循环轮次和退出原因
  - 评估流程效率和效果

- **4.1.2 关键发现总结**
  - 根本问题的识别过程
  - 重要假设的验证结果
  - 意外发现和副产品

- **4.1.3 方法论评估**
  - 使用的诊断方法有效性
  - 工具选择的合理性
  - 验证策略的充分性

### **步骤4.2: 解决方案总结**

- **4.2.1 修复方案回顾**
  - 修复策略的设计思路
  - 实施过程和调整历程
  - 最终方案的技术细节

- **4.2.2 修复效果评估**
  - 问题解决的完整性
  - 修复质量和稳定性
  - 性能影响和副作用

- **4.2.3 替代方案分析**
  - 其他可能的解决路径
  - 方案优劣对比分析
  - 未来改进的可能性

### **步骤4.3: 经验教训提炼**

- **4.3.1 成功经验总结**
  - 有效的诊断方法和技巧
  - 成功的假设建立策略
  - 优秀的修复实施方案

- **4.3.2 失误和改进点**
  - 诊断过程中的偏差和错误
  - 可以改进的方法和策略
  - 避免类似问题的建议

- **4.3.3 通用原则抽取**
  - 适用于类似问题的通用方法
  - 可复用的诊断模式
  - 经验化的最佳实践

### **步骤4.4: 类似问题预防建议**

- **4.4.1 根因类别分析**
  - 本次问题的根本原因类别
  - 类似问题的共同特征
  - 潜在的触发条件

- **4.4.2 预防措施建议**
  - 代码层面的预防措施
  - 流程层面的改进建议
  - 监控和检测机制

- **4.4.3 早期发现机制**
  - 问题征兆的识别方法
  - 自动化检测的可能性
  - 预警机制的设计建议

### **步骤4.5: 系统性优化建议**

- **4.5.1 架构层面优化**
  - 基于本次诊断的架构改进建议
  - 系统设计的优化方向
  - 技术栈的升级建议

- **4.5.2 流程层面优化**
  - 开发流程的改进建议
  - 质量保证流程的完善
  - 运维流程的优化

- **4.5.3 工具和方法优化**
  - 诊断工具的改进建议
  - 新工具的引入建议
  - 方法论的进一步完善

---

## ✅ **问题解决成功标准**

1. **问题定位准确**: 100% - 必须找到真正的根本原因，不是表面现象
2. **修复完整性**: 100% - 必须完全解决根本问题，不是临时绕过
3. **功能完整性**: 100% - 修复后功能完全正常，无副作用
4. **验证真实性**: 100% - 必须通过真实测试验证，不是理论推理
5. **问题复现性**: 0% - 在相同条件下问题不再复现
6. **旧问题残留**: 0% - 不允许任何相关问题残留未解决
7. **知识沉淀**: 100% - 完整的总结报告和优化建议
8. **流程完整性**: 100% - 严格按照4阶段循环流程执行
9. **循环自动化**: 100% - 严格按照客观条件自动循环，无人工决策点