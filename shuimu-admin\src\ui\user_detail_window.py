#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情窗口
显示用户的详细信息，包括基本信息、购买记录、学习进度等
"""

import sys
from typing import Dict, Any, List, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, 
    QTableWidget, QTableWidgetItem, QPushButton, QLineEdit,
    QComboBox, QTextEdit, QGroupBox, QFormLayout, QScrollArea,
    QMessageBox, QProgressBar, QFrame, QSplitter, QHeaderView
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QPixmap, QIcon, QColor
from services.user_service import UserService
from ui.thread_safe_ui_helper import get_thread_safe_ui_helper, safe_ui_call
from database.models import DatabaseManager
from utils.table_column_manager import setup_table_with_column_management
from ui.async_purchase_operations import AsyncPurchaseOperations
import logging

logger = logging.getLogger(__name__)

class UserSyncThread(QThread):
    """用户信息后台同步线程 - 实现真正的乐观更新"""
    sync_completed = pyqtSignal(bool, dict)  # success, result
    
    def __init__(self, user_service: UserService, user_id: str, user_data: dict):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
        self.user_data = user_data
    
    def run(self):
        try:
            result = self.user_service.update_user_info(self.user_id, self.user_data)
            
            # 检查同步结果 - 🎯 修复：服务器成功就算成功，不强制要求本地数据库成功
            if result and result.get('success'):
                sync_status = result.get('sync_status', {})
                server_success = sync_status.get('server_db', False)
                local_success = sync_status.get('local_db', False)
                cache_success = sync_status.get('memory_cache', False)
                
                if server_success:
                    # ✅ 服务器成功就算成功，本地同步失败只是警告
                    local_status = sync_status.get('local_db', False)
                    
                    self.sync_completed.emit(True, result)
                else:
                    # ❌ 服务器失败才需要回滚
                    self.sync_completed.emit(False, result)
            else:
                self.sync_completed.emit(False, result or {'message': '同步失败'})
                
        except Exception as e:
            self.sync_completed.emit(False, {'message': f'同步异常: {str(e)}'})

class UserDetailLoadThread(QThread):
    """用户详情数据加载线程"""
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, user_service: UserService, user_id: str):
        super().__init__()
        self.user_service = user_service
        self.user_id = user_id
    
    def run(self):
        try:
            # 获取用户基本信息
            user_info = self.user_service.get_user_by_id(self.user_id)

            # 获取用户购买记录
            purchases = self.user_service.get_user_purchases(self.user_id)

            # 获取用户观看进度
            progress = self.user_service.get_user_progress(self.user_id)

            # 获取用户收藏
            favorites = self.user_service.get_user_favorites(self.user_id)

            # 获取用户设置
            settings = self.user_service.get_user_settings(self.user_id)

            # 获取用户缓存
            cache_info = self.user_service.get_user_cache(self.user_id)

            result = {
                'success': True,
                'user_info': user_info,
                'purchases': purchases,
                'progress': progress,
                'favorites': favorites,
                'settings': settings,
                'cache_info': cache_info
            }

            self.data_loaded.emit(result)

        except Exception as e:
            import traceback
            traceback.print_exc()
            self.error_occurred.emit(str(e))

class UserDetailWindow(QWidget):
    """用户详情窗口"""
    
    # 🎯 定义完整的乐观更新信号 - 符合三层同步规则v7.0
    operation_started = pyqtSignal(str, str)  # entity_id, operation_type
    operation_completed = pyqtSignal(str, str, bool)  # entity_id, operation_type, success
    operation_failed = pyqtSignal(str, str, str)  # entity_id, operation_type, error_message
    progress_updated = pyqtSignal(str, int)  # entity_id, progress_percent
    
    def __init__(self, user_id: str, user_service: UserService, parent=None):
        super().__init__(parent)
        self.user_id = user_id
        self.user_service = user_service
        # 🎯 调试：打印用户详情窗口的用户ID
        print(f"🔍 用户详情窗口初始化：user_id = '{user_id}'")
        self.load_thread = None
        self.sync_thread = None  # 🎯 新增：后台同步线程
        
        # 🎯 新增：异步购买记录操作管理器
        self.async_purchase_ops = AsyncPurchaseOperations(self)

        self.setWindowTitle(f"用户详情 - {user_id}")
        self.setMinimumSize(1000, 700)

        # 设置窗口样式 - 经典管理端风格
        self.setStyleSheet("""
            QWidget {
                background-color: #ffffff;
                color: #333333;
                font-family: "Microsoft YaHei", "SimHei", sans-serif;
                font-size: 12px;
            }

            QTabWidget::pane {
                border: 1px solid #d0d0d0;
                background-color: #ffffff;
                border-radius: 4px;
            }

            QTabWidget::tab-bar {
                alignment: left;
            }

            QTabBar::tab {
                background-color: #f5f5f5;
                border: 1px solid #d0d0d0;
                border-bottom: none;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 80px;
            }

            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: 1px solid #ffffff;
                font-weight: bold;
            }

            QTabBar::tab:hover {
                background-color: #e8f4fd;
            }

            QGroupBox {
                font-weight: bold;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #fafafa;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: #ffffff;
                border: 1px solid #d0d0d0;
                border-radius: 3px;
            }

            QLabel {
                color: #333333;
                padding: 2px;
            }

            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-weight: bold;
            }

            QPushButton:hover {
                background-color: #45a049;
            }

            QPushButton:pressed {
                background-color: #3d8b40;
            }

            QProgressBar {
                border: 1px solid #d0d0d0;
                border-radius: 3px;
                text-align: center;
                background-color: #f0f0f0;
            }

            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 2px;
            }

            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: #ffffff;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }

            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }

            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }

            QHeaderView::section {
                background-color: #f8f9fa;
                color: #333333;
                padding: 8px;
                border: 1px solid #e0e0e0;
                font-weight: bold;
            }

            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)

        self.init_ui()
        self.load_user_detail()

    def closeEvent(self, event):
        """窗口关闭时清理线程资源"""
        try:
            # 停止数据加载线程
            if self.load_thread and self.load_thread.isRunning():
                self.load_thread.quit()
                self.load_thread.wait(3000)  # 等待最多3秒
            
            # 停止同步线程
            if self.sync_thread and self.sync_thread.isRunning():
                self.sync_thread.quit()
                self.sync_thread.wait(3000)  # 等待最多3秒
            
            # 🎯 修复：清理异步购买操作的所有线程，防止死锁
            if hasattr(self, 'async_purchase_ops') and self.async_purchase_ops:
                self.async_purchase_ops.cleanup_all_threads()
            
            print("✅ 用户详情窗口：所有线程已正确清理")
            
        except Exception as e:
            print(f"⚠️ 线程清理异常：{str(e)}")
        
        super().closeEvent(event)
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题区域
        title_layout = QHBoxLayout()
        self.title_label = QLabel(f"用户详情")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_user_detail)
        title_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(title_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 基本信息标签页
        self.basic_info_tab = self.create_basic_info_tab()
        self.tab_widget.addTab(self.basic_info_tab, "基本信息")
        
        # 购买记录标签页
        self.purchases_tab = self.create_purchases_tab()
        self.tab_widget.addTab(self.purchases_tab, "购买记录")
        
        # 学习进度标签页
        self.progress_tab = self.create_progress_tab()
        self.tab_widget.addTab(self.progress_tab, "学习进度")
        
        # 收藏管理标签页
        self.favorites_tab = self.create_favorites_tab()
        self.tab_widget.addTab(self.favorites_tab, "收藏管理")
        
        # 用户设置标签页
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "用户设置")
        
        # 设备缓存标签页
        self.cache_tab = self.create_cache_tab()
        self.tab_widget.addTab(self.cache_tab, "设备缓存")
        
        layout.addWidget(self.tab_widget)
    
    def create_basic_info_tab(self) -> QWidget:
        """创建基本信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 用户头像和基本信息卡片
        info_group = QGroupBox("👤 基本信息")
        info_layout = QFormLayout(info_group)
        info_layout.setSpacing(12)
        info_layout.setContentsMargins(20, 25, 20, 20)

        # 创建标签，设置更好的样式
        self.username_label = QLabel("-")
        self.username_label.setStyleSheet("font-weight: bold; color: #2c3e50;")

        self.email_label = QLabel("-")
        self.email_label.setStyleSheet("color: #34495e;")

        self.display_name_label = QLabel("-")
        self.display_name_label.setStyleSheet("color: #34495e;")

        self.phone_label = QLabel("-")
        self.phone_label.setStyleSheet("color: #34495e;")

        # 🎯 新增：密码字段显示
        self.password_label = QLabel("-")
        self.password_label.setStyleSheet("color: #34495e; font-family: monospace;")

        self.status_label = QLabel("-")
        self.admin_label = QLabel("-")
        self.created_label = QLabel("-")
        self.created_label.setStyleSheet("color: #7f8c8d;")

        self.last_login_label = QLabel("-")
        self.last_login_label.setStyleSheet("color: #7f8c8d;")

        # 🎯 修复：调整字段顺序，密码在用户名下面
        info_layout.addRow("👤 用户名:", self.username_label)
        info_layout.addRow("🔐 密码:", self.password_label)
        info_layout.addRow("📧 邮箱:", self.email_label)
        info_layout.addRow("🏷️ 显示名:", self.display_name_label)
        info_layout.addRow("📱 手机号:", self.phone_label)
        info_layout.addRow("🔄 状态:", self.status_label)
        info_layout.addRow("🔑 权限:", self.admin_label)
        info_layout.addRow("📅 注册时间:", self.created_label)
        info_layout.addRow("🕐 最后登录:", self.last_login_label)

        layout.addWidget(info_group)

        # 操作按钮卡片
        btn_group = QGroupBox("⚙️ 操作")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.edit_info_btn = QPushButton("✏️ 编辑信息")
        self.edit_info_btn.setStyleSheet("QPushButton { background-color: #3498db; }")
        # 🎯 新增：绑定事件处理函数
        self.edit_info_btn.clicked.connect(self.edit_user_info)

        self.reset_password_btn = QPushButton("🔒 重置密码")
        self.reset_password_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        # 🎯 新增：绑定事件处理函数
        self.reset_password_btn.clicked.connect(self.reset_user_password)

        self.toggle_status_btn = QPushButton("🔄 切换状态")
        self.toggle_status_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")
        # 🎯 新增：绑定事件处理函数
        self.toggle_status_btn.clicked.connect(self.toggle_user_status)

        self.toggle_admin_btn = QPushButton("👑 切换权限")
        self.toggle_admin_btn.setStyleSheet("QPushButton { background-color: #9b59b6; }")
        # 🎯 新增：绑定事件处理函数
        self.toggle_admin_btn.clicked.connect(self.toggle_user_admin)

        btn_layout.addWidget(self.edit_info_btn)
        btn_layout.addWidget(self.reset_password_btn)
        btn_layout.addWidget(self.toggle_status_btn)
        btn_layout.addWidget(self.toggle_admin_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        layout.addStretch()

        return widget
    
    def create_purchases_tab(self) -> QWidget:
        """创建购买记录标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💰 购买记录管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.add_purchase_btn = QPushButton("➕ 添加购买记录")
        self.add_purchase_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")
        self.add_purchase_btn.clicked.connect(self.add_purchase_record)

        self.delete_purchase_btn = QPushButton("🗑️ 删除选中记录")
        self.delete_purchase_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #e74c3c;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #c0392b;
            }
        """)
        self.delete_purchase_btn.clicked.connect(self.delete_selected_purchases)

        btn_layout.addWidget(self.add_purchase_btn)
        btn_layout.addWidget(self.delete_purchase_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 购买记录表格
        table_group = QGroupBox("📋 购买记录列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(8)  # 🎯 增加一列用于存储ID
        self.purchases_table.setHorizontalHeaderLabels([
            "分类名称", "所属系列名", "购买类型", "包含视频数", "价格", "状态", "购买时间", "ID"
        ])
        self.purchases_table.horizontalHeader().setStretchLastSection(True)
        self.purchases_table.setAlternatingRowColors(True)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        # 🎯 新增：启用多选模式，支持Ctrl和Shift多选
        self.purchases_table.setSelectionMode(QTableWidget.SelectionMode.ExtendedSelection)
        
        # 🔧 添加列宽管理 - 🎯 优化：匹配新的8列设计（ID列隐藏）
        purchase_default_widths = [200, 150, 100, 100, 100, 80, 150, 0]  # 分类名称, 所属系列名, 购买类型, 包含视频数, 价格, 状态, 购买时间, ID(隐藏)
        setup_table_with_column_management(self.purchases_table, 'user_detail_purchases_table', purchase_default_widths)
        # 🎯 隐藏ID列
        self.purchases_table.setColumnHidden(7, True)

        table_layout.addWidget(self.purchases_table)
        layout.addWidget(table_group)

        return widget
    
    def create_progress_tab(self) -> QWidget:
        """创建学习进度标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("📊 学习进度管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.reset_progress_btn = QPushButton("🔄 重置选中进度")
        self.reset_progress_btn.setStyleSheet("QPushButton { background-color: #f39c12; }")

        self.mark_completed_btn = QPushButton("✅ 标记为完成")
        self.mark_completed_btn.setStyleSheet("QPushButton { background-color: #27ae60; }")

        btn_layout.addWidget(self.reset_progress_btn)
        btn_layout.addWidget(self.mark_completed_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 学习进度表格
        table_group = QGroupBox("📈 学习进度详情")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.progress_table = QTableWidget()
        self.progress_table.setColumnCount(7)
        self.progress_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "观看进度", "观看次数", "最后观看"
        ])
        self.progress_table.horizontalHeader().setStretchLastSection(True)
        self.progress_table.setAlternatingRowColors(True)
        self.progress_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        progress_default_widths = [80, 180, 120, 120, 150, 80, 150]  # 视频ID, 视频标题, 所属分类, 所属系列, 观看进度, 观看次数, 最后观看
        setup_table_with_column_management(self.progress_table, 'user_detail_progress_table', progress_default_widths)

        table_layout.addWidget(self.progress_table)
        layout.addWidget(table_group)

        return widget
    
    def create_favorites_tab(self) -> QWidget:
        """创建收藏管理标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("❤️ 收藏管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        self.delete_favorite_btn = QPushButton("🗑️ 删除选中收藏")
        self.delete_favorite_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")
        btn_layout.addWidget(self.delete_favorite_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 收藏表格
        table_group = QGroupBox("📋 收藏列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.favorites_table = QTableWidget()
        self.favorites_table.setColumnCount(6)
        self.favorites_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "收藏时间", "操作"
        ])
        self.favorites_table.horizontalHeader().setStretchLastSection(True)
        self.favorites_table.setAlternatingRowColors(True)
        self.favorites_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        favorites_default_widths = [80, 180, 120, 120, 150, 80]  # 视频ID, 视频标题, 所属分类, 所属系列, 收藏时间, 操作
        setup_table_with_column_management(self.favorites_table, 'user_detail_favorites_table', favorites_default_widths)

        table_layout.addWidget(self.favorites_table)
        layout.addWidget(table_group)

        return widget

    def create_settings_tab(self) -> QWidget:
        """创建用户设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 设置表格
        table_group = QGroupBox("⚙️ 用户设置")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.settings_table = QTableWidget()
        self.settings_table.setColumnCount(3)
        self.settings_table.setHorizontalHeaderLabels([
            "设置键", "设置值", "操作"
        ])
        self.settings_table.horizontalHeader().setStretchLastSection(True)
        self.settings_table.setAlternatingRowColors(True)
        self.settings_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        settings_default_widths = [200, 300, 100]
        setup_table_with_column_management(self.settings_table, 'user_detail_settings_table', settings_default_widths)

        table_layout.addWidget(self.settings_table)
        layout.addWidget(table_group)

        return widget

    def create_cache_tab(self) -> QWidget:
        """创建设备缓存标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # 操作按钮组
        btn_group = QGroupBox("💾 缓存管理")
        btn_group_layout = QVBoxLayout(btn_group)
        btn_group_layout.setContentsMargins(20, 25, 20, 20)

        btn_layout = QHBoxLayout()
        btn_layout.setSpacing(10)

        self.clear_cache_btn = QPushButton("🗑️ 清除选中缓存")
        self.clear_cache_btn.setStyleSheet("QPushButton { background-color: #e74c3c; }")

        self.clear_all_cache_btn = QPushButton("🧹 清除所有缓存")
        self.clear_all_cache_btn.setStyleSheet("QPushButton { background-color: #c0392b; }")

        btn_layout.addWidget(self.clear_cache_btn)
        btn_layout.addWidget(self.clear_all_cache_btn)
        btn_layout.addStretch()

        btn_group_layout.addLayout(btn_layout)
        layout.addWidget(btn_group)

        # 缓存表格
        table_group = QGroupBox("📋 缓存列表")
        table_layout = QVBoxLayout(table_group)
        table_layout.setContentsMargins(20, 25, 20, 20)

        self.cache_table = QTableWidget()
        self.cache_table.setColumnCount(7)
        self.cache_table.setHorizontalHeaderLabels([
            "视频ID", "视频标题", "所属分类", "所属系列", "缓存状态", "文件大小", "缓存时间"
        ])
        self.cache_table.horizontalHeader().setStretchLastSection(True)
        self.cache_table.setAlternatingRowColors(True)
        self.cache_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 🔧 添加列宽管理
        cache_default_widths = [80, 160, 120, 120, 100, 100, 150]  # 视频ID, 视频标题, 所属分类, 所属系列, 缓存状态, 文件大小, 缓存时间
        setup_table_with_column_management(self.cache_table, 'user_detail_cache_table', cache_default_widths)

        table_layout.addWidget(self.cache_table)
        layout.addWidget(table_group)

        return widget
    
    def load_user_detail(self):
        """加载用户详情数据"""
        if self.load_thread and self.load_thread.isRunning():
            return
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        
        self.load_thread = UserDetailLoadThread(self.user_service, self.user_id)
        self.load_thread.data_loaded.connect(self.on_data_loaded)
        self.load_thread.error_occurred.connect(self.on_load_error)
        self.load_thread.start()
    
    def on_data_loaded(self, result):
        """数据加载完成"""
        self.progress_bar.setVisible(False)
        
        if result['success']:
            self.populate_user_info(result['user_info'])
            self.populate_purchases(result['purchases'])
            self.populate_progress(result['progress'])
            self.populate_favorites(result['favorites'])
            self.populate_settings(result['settings'])
            self.populate_cache(result['cache_info'])
        else:
            QMessageBox.warning(self, '错误', result.get('message', '加载用户详情失败'))
    
    def on_load_error(self, error_msg):
        """数据加载错误"""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, '错误', f'加载用户详情失败: {error_msg}')
    
    def populate_user_info(self, user_info):
        """填充用户基本信息"""
        if not user_info:
            return

        # 处理嵌套的数据结构
        if user_info.get('success') and user_info.get('data'):
            user_data = user_info['data']
        else:
            user_data = user_info

        self.username_label.setText(user_data.get('username', '-'))
        self.email_label.setText(user_data.get('email', '-'))
        self.display_name_label.setText(user_data.get('display_name', '-'))
        self.phone_label.setText(user_data.get('phone', '-'))

        # 🎯 新增：密码显示为明文（管理端专用）
        password_hash = user_data.get('password_hash', '')

        if password_hash:
            self.password_label.setText(password_hash)
        else:
            self.password_label.setText('未设置')
            print("⚠️ 密码字段为空，显示'未设置'")

        # 状态显示 - 使用图标和颜色
        is_active = user_data.get('is_active', False)
        if is_active:
            status_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
        else:
            status_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
        self.status_label.setText(status_html)

        # 权限显示 - 使用图标和颜色
        is_admin = user_data.get('is_admin', False)
        if is_admin:
            admin_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
        else:
            admin_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
        self.admin_label.setText(admin_html)

        self.created_label.setText(user_data.get('created_at', '-'))
        self.last_login_label.setText(user_data.get('last_login_at', '-'))

        # 更新窗口标题
        username = user_data.get('username', self.user_id)
        display_name = user_data.get('display_name', '')
        title = f"用户详情 - {username}"
        if display_name and display_name != username:
            title += f" ({display_name})"
        self.setWindowTitle(title)
        self.title_label.setText(title)
    
    def populate_purchases(self, purchases):
        """填充购买记录（以分类为单位显示）- 🎯 优化版本，支持新的7列布局"""
        if not purchases or not purchases.get('success'):
            self.purchases_table.setRowCount(0)
            return

        purchase_data = purchases.get('data', [])
        self.purchases_table.setRowCount(len(purchase_data))

        for row, purchase in enumerate(purchase_data):
            # 🎯 列0：分类名称
            category_name = purchase.get('category_name', '未知分类')
            self.purchases_table.setItem(row, 0, QTableWidgetItem(category_name))

            # 🎯 列1：所属系列名
            series_name = purchase.get('series_name', '未知系列')
            self.purchases_table.setItem(row, 1, QTableWidgetItem(series_name))

            # 🎯 列2：购买类型（全套|系列|分类）
            purchase_type = purchase.get('purchase_type', '分类')
            self.purchases_table.setItem(row, 2, QTableWidgetItem(purchase_type))

            # 🎯 列3：包含视频数（服务端动态计算）
            video_count = purchase.get('video_count', 0)
            # 🔧 修复：区分数据未加载和数据为0的情况
            if video_count is None:
                count_text = "待统计"
            else:
                count_text = f"{video_count} 个视频"
            self.purchases_table.setItem(row, 3, QTableWidgetItem(count_text))

            # 🎯 列4：价格
            amount = purchase.get('amount', 0)
            price_text = f"¥{amount:.2f}" if amount > 0 else "免费"
            self.purchases_table.setItem(row, 4, QTableWidgetItem(price_text))

            # 🎯 列5：状态（付费/免费）
            status = purchase.get('status', 'paid')
            is_auto_included = purchase.get('is_auto_included', False)  # 是否自动包含的免费内容
            
            if is_auto_included:
                status_text = "免费 (默认)"
                status_item = QTableWidgetItem(status_text)
                status_item.setBackground(QColor(230, 245, 255))  # 浅蓝色背景 - 自动包含的免费
                status_item.setToolTip("该分类属于免费系列，所有用户默认可访问")
            elif status == "free":
                status_text = "免费"
                status_item = QTableWidgetItem(status_text)
                status_item.setBackground(QColor(220, 220, 255))  # 蓝色背景 - 用户购买的免费
            else:
                status_text = "付费"
                status_item = QTableWidgetItem(status_text)
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景 - 付费
                
            self.purchases_table.setItem(row, 5, status_item)

            # 🎯 列6：购买时间
            purchase_date = purchase.get('purchase_date', '')
            if purchase_date:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(purchase_date.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = purchase_date
            else:
                formatted_date = '-'
            self.purchases_table.setItem(row, 6, QTableWidgetItem(formatted_date))
            
            # 🎯 列7：ID（隐藏列，存储真实的购买记录ID）
            purchase_id = purchase.get('id', '')
            self.purchases_table.setItem(row, 7, QTableWidgetItem(str(purchase_id)))
    
    def populate_progress(self, progress):
        """填充学习进度（以视频为单位，显示分类、系列信息）"""
        if not progress or not progress.get('success'):
            self.progress_table.setRowCount(0)
            return

        progress_data = progress.get('data', [])
        self.progress_table.setRowCount(len(progress_data))

        for row, prog in enumerate(progress_data):
            # 视频ID
            self.progress_table.setItem(row, 0, QTableWidgetItem(str(prog.get('video_id', ''))))

            # 视频标题
            video_title = prog.get('video_title', '') or prog.get('title', '')
            self.progress_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = prog.get('category_title', '') or prog.get('category_name', '')
            self.progress_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列  
            series_title = prog.get('series_title', '') or prog.get('series_name', '')
            self.progress_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 观看进度
            progress_seconds = prog.get('progress_seconds', 0)
            total_duration = prog.get('total_duration', 0)
            progress_percentage = prog.get('progress_percentage', 0)

            if total_duration > 0:
                # 转换为分钟:秒格式
                progress_min = progress_seconds // 60
                progress_sec = progress_seconds % 60
                total_min = total_duration // 60
                total_sec = total_duration % 60
                progress_text = f"{progress_min:02d}:{progress_sec:02d} / {total_min:02d}:{total_sec:02d} ({progress_percentage:.1f}%)"
            else:
                progress_text = f"{progress_seconds}s"

            progress_item = QTableWidgetItem(progress_text)
            # 根据进度设置背景色
            if progress_percentage >= 100:
                progress_item.setBackground(QColor(200, 255, 200))  # 绿色 - 已完成
            elif progress_percentage >= 50:
                progress_item.setBackground(QColor(255, 255, 200))  # 黄色 - 进行中
            elif progress_percentage > 0:
                progress_item.setBackground(QColor(255, 230, 230))  # 浅红色 - 刚开始

            self.progress_table.setItem(row, 4, progress_item)

            # 观看次数（显示完成次数）
            watch_count = prog.get('watch_count', 0)
            completed_count = prog.get('completed_count', 0)
            if completed_count > 0:
                count_text = f"{watch_count} 次 (完成 {completed_count} 次)"
            else:
                count_text = f"{watch_count} 次"
            self.progress_table.setItem(row, 5, QTableWidgetItem(count_text))

            # 最后观看时间
            last_watched = prog.get('last_watched', '')
            if last_watched:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(last_watched.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = last_watched
            else:
                formatted_date = '-'
            self.progress_table.setItem(row, 6, QTableWidgetItem(formatted_date))
    
    def populate_favorites(self, favorites):
        """填充收藏记录（以视频为单位显示）"""
        if not favorites or not favorites.get('success'):
            self.favorites_table.setRowCount(0)
            return

        favorites_data = favorites.get('data', [])
        self.favorites_table.setRowCount(len(favorites_data))

        for row, favorite in enumerate(favorites_data):
            # 视频ID
            video_id = favorite.get('favorited_entity_id', '') if favorite.get('favorited_entity_type') == 'video' else favorite.get('video_id', '')
            self.favorites_table.setItem(row, 0, QTableWidgetItem(str(video_id)))

            # 视频标题（如果是视频收藏，显示视频标题；否则显示相应的名称）
            favorited_entity_type = favorite.get('favorited_entity_type', '')
            if favorited_entity_type == 'video':
                video_title = favorite.get('item_name', '') or favorite.get('video_title', '')
            elif favorited_entity_type == 'category':
                video_title = f"[分类] {favorite.get('item_name', '')}"
            elif favorited_entity_type == 'series':
                video_title = f"[系列] {favorite.get('item_name', '')}"
            else:
                video_title = favorite.get('item_name', '')
            
            self.favorites_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = favorite.get('category_title', '') or favorite.get('category_name', '')
            if favorited_entity_type == 'category':
                category_title = favorite.get('item_name', '')  # 如果收藏的是分类，显示分类名
            self.favorites_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列
            series_title = favorite.get('series_title', '') or favorite.get('series_name', '')
            if favorited_entity_type == 'series':
                series_title = favorite.get('item_name', '')  # 如果收藏的是系列，显示系列名
            self.favorites_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 收藏时间
            created_at = favorite.get('created_at', '')
            if created_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = created_at
            else:
                formatted_date = '-'
            self.favorites_table.setItem(row, 4, QTableWidgetItem(formatted_date))

            # 操作按钮（暂时显示文本）
            self.favorites_table.setItem(row, 5, QTableWidgetItem("删除"))
    
    def populate_settings(self, settings):
        """填充用户设置"""
        if not settings or not settings.get('success'):
            self.settings_table.setRowCount(0)
            return

        settings_data = settings.get('data', [])
        self.settings_table.setRowCount(len(settings_data))

        for row, setting in enumerate(settings_data):
            # 设置键
            self.settings_table.setItem(row, 0, QTableWidgetItem(setting.get('key', '')))

            # 设置值
            value = setting.get('value', '')
            # 限制显示长度
            if len(str(value)) > 50:
                display_value = str(value)[:47] + "..."
            else:
                display_value = str(value)
            self.settings_table.setItem(row, 1, QTableWidgetItem(display_value))

            # 操作按钮（暂时显示文本）
            self.settings_table.setItem(row, 2, QTableWidgetItem("编辑"))

    def populate_cache(self, cache_info):
        """填充缓存信息（以视频为单位显示）"""
        if not cache_info or not cache_info.get('success'):
            self.cache_table.setRowCount(0)
            return

        cache_data = cache_info.get('data', [])
        self.cache_table.setRowCount(len(cache_data))

        for row, cache in enumerate(cache_data):
            # 视频ID
            self.cache_table.setItem(row, 0, QTableWidgetItem(str(cache.get('video_id', ''))))

            # 视频标题
            video_title = cache.get('video_title', '') or cache.get('title', '')
            self.cache_table.setItem(row, 1, QTableWidgetItem(video_title))

            # 所属分类
            category_title = cache.get('category_title', '') or cache.get('category_name', '')
            self.cache_table.setItem(row, 2, QTableWidgetItem(category_title))

            # 所属系列
            series_title = cache.get('series_title', '') or cache.get('series_name', '')
            self.cache_table.setItem(row, 3, QTableWidgetItem(series_title))

            # 缓存状态
            cache_status = cache.get('cache_status', False) or cache.get('is_cached', False)
            status_text = "已缓存" if cache_status else "未缓存"
            status_item = QTableWidgetItem(status_text)
            if cache_status:
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景
            else:
                status_item.setBackground(QColor(255, 230, 230))  # 浅红色背景
            self.cache_table.setItem(row, 4, status_item)

            # 文件大小
            file_size = cache.get('file_size', 0) or cache.get('size_bytes', 0)
            if file_size > 0:
                # 转换为可读的文件大小
                if file_size >= 1024 * 1024 * 1024:  # GB
                    size_text = f"{file_size / (1024 * 1024 * 1024):.2f} GB"
                elif file_size >= 1024 * 1024:  # MB
                    size_text = f"{file_size / (1024 * 1024):.2f} MB"
                elif file_size >= 1024:  # KB
                    size_text = f"{file_size / 1024:.2f} KB"
                else:
                    size_text = f"{file_size} B"
            else:
                size_text = "-"
            self.cache_table.setItem(row, 5, QTableWidgetItem(size_text))

            # 缓存时间
            cached_at = cache.get('cached_at', '') or cache.get('created_at', '')
            if cached_at:
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(cached_at.replace('Z', '+00:00'))
                    formatted_date = dt.strftime('%Y-%m-%d %H:%M')
                except:
                    formatted_date = cached_at
            else:
                formatted_date = '-'
            self.cache_table.setItem(row, 6, QTableWidgetItem(formatted_date))

    # 🎯 新增：操作按钮事件处理方法
    def edit_user_info(self):
        """编辑用户信息"""
        try:
            # 导入用户编辑对话框
            from ui.user_window import UserEditDialog

            # 🎯 修复：获取当前用户数据，包含完整字段
            status_text = self.status_label.text()
            admin_text = self.admin_label.text()

            # 移除HTML标签后再判断状态
            import re
            clean_status_text = re.sub(r'<[^>]+>', '', status_text)
            clean_admin_text = re.sub(r'<[^>]+>', '', admin_text)

            is_active = '激活' in clean_status_text
            is_admin = '管理员' in clean_admin_text

            current_user_data = {
                'username': self.username_label.text(),
                'email': self.email_label.text(),
                'display_name': self.display_name_label.text(),
                'phone': self.phone_label.text(),
                'is_active': is_active,
                'is_admin': is_admin
            }


            # 🎯 修复：查找顶级窗口作为对话框父窗口，避免嵌入式widget导致的模态问题
            top_level_window = self
            while top_level_window.parent():
                parent = top_level_window.parent()
                if isinstance(parent, QWidget):
                    top_level_window = parent
                else:
                    break
            
            
            dialog = UserEditDialog(parent=top_level_window, user_data=current_user_data)
            if dialog.exec() == dialog.DialogCode.Accepted:
                # 获取修改后的数据
                new_data = dialog.get_form_data()

                # 🎯 修复：实现乐观更新 - 立即更新UI
                self.username_label.setText(new_data.get('username', ''))
                self.email_label.setText(new_data.get('email', ''))
                self.display_name_label.setText(new_data.get('display_name', ''))
                self.phone_label.setText(new_data.get('phone', ''))

                # 🎯 修复：更新密码字段显示 - 统一使用password_hash字段
                if 'password' in new_data:
                    new_password = new_data['password']
                    self.password_label.setText(new_password)
                    print(f"🎯 乐观更新：立即更新密码显示为 '{new_password}'")
                elif 'password_hash' in new_data:
                    new_password = new_data['password_hash']
                    self.password_label.setText(new_password)
                    print(f"🎯 乐观更新：立即更新密码显示为 '{new_password}'")

                # 更新状态和权限显示
                if new_data.get('is_active'):
                    status_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
                else:
                    status_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
                self.status_label.setText(status_html)

                if new_data.get('is_admin'):
                    admin_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
                else:
                    admin_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
                self.admin_label.setText(admin_html)

                # 强制刷新UI
                self.update()
                print("🎯 第1层乐观更新：立即更新用户信息UI（包含密码字段）")

                # 🎯 第2、3、4层：在后台线程中异步执行，不阻塞UI
                self._start_background_sync(new_data)

        except Exception as e:
            QMessageBox.critical(self, '错误', f'打开编辑对话框失败：{str(e)}')

    def _start_background_sync(self, user_data: dict):
        """启动后台同步线程 - 真正的乐观更新实现"""
        try:
            # 如果之前的同步线程还在运行，先停止它
            if self.sync_thread and self.sync_thread.isRunning():
                self.sync_thread.quit()
                self.sync_thread.wait()
            
            # 创建新的后台同步线程
            self.sync_thread = UserSyncThread(self.user_service, self.user_id, user_data)
            self.sync_thread.sync_completed.connect(self._on_sync_completed)
            self.sync_thread.start()
            
            print("🚀 第2/3/4层：后台线程已启动，不阻塞UI")
            
        except Exception as e:
            print(f"❌ 启动后台同步失败：{str(e)}")
            self._rollback_ui_and_notify("启动同步失败", str(e))

    def _on_sync_completed(self, success: bool, result: dict):
        """后台同步完成的回调处理"""
        try:
            if success:
                # ✅ 后台同步成功：保持乐观更新的UI状态
                print("✅ 第4层补偿层：后台同步成功，保持乐观更新状态")
                
                # 🎯 新增：立即验证缓存状态
                self._verify_cache_state()
            else:
                # ❌ 后台同步失败：回滚UI到原始状态
                error_msg = result.get('message', '后台同步失败')
                print(f"❌ 第4层补偿层：后台同步失败，执行回滚 - {error_msg}")
                self._rollback_ui_and_notify("后台同步失败", error_msg)
                
        except Exception as e:
            print(f"❌ 同步回调处理异常：{str(e)}")
            self._rollback_ui_and_notify("回调处理异常", str(e))

    def _verify_cache_state(self):
        """验证缓存状态 - 用于诊断重启后数据问题"""
        try:
            print("🔍 缓存状态验证：开始检查缓存中的用户数据...")
            
            # 使用相同的数据加载逻辑来检查缓存
            user_info = self.user_service.get_user_by_id(self.user_id)
            if user_info and user_info.get('success'):
                user_data = user_info['data']
                cached_password = user_data.get('password_hash', 'NOT_FOUND')
                current_display_password = self.password_label.text()
                
                print(f"🔍 缓存状态验证：缓存中的密码 = '{cached_password}'")
                print(f"🔍 缓存状态验证：UI显示的密码 = '{current_display_password}'")
                
                if cached_password == current_display_password:
                    print("✅ 缓存状态验证：缓存与UI一致，重启后应该正常")
                else:
                    print("❌ 缓存状态验证：缓存与UI不一致，重启后会回到旧值！")
                    print(f"   问题：缓存='{cached_password}' vs UI='{current_display_password}'")
            else:
                print("❌ 缓存状态验证：无法获取缓存数据")
                
        except Exception as e:
            print(f"❌ 缓存状态验证异常：{str(e)}")

    def _rollback_ui_and_notify(self, title: str, message: str):
        """第4层补偿层：回滚UI并通知用户"""
        try:
            print(f"🔄 第4层补偿层：开始回滚UI - {title}")
            # 重新加载用户详情以回滚UI到原始状态
            self.load_user_detail()
            # 通知用户失败信息
            QMessageBox.warning(self, f'操作失败 - {title}', f'{message}\n\n已回滚到原状态。')
            print(f"✅ 第4层补偿层：UI回滚完成")
        except Exception as rollback_error:
            print(f"❌ 第4层补偿层：回滚失败 - {rollback_error}")
            QMessageBox.critical(self, '系统错误', f'回滚失败：{str(rollback_error)}')

    def reset_user_password(self):
        """重置用户密码 - 🎯 修复：直接执行，乐观更新显示明文密码"""
        try:
            # 🎯 修复：移除确认弹框，直接执行
            print(f"🚀 开始重置用户 {self.username_label.text()} 的密码")

            # 🎯 修复：乐观更新 - 立即显示新密码
            self.password_label.setText('ultrathink')
            # 🎯 修复：强制刷新UI，确保立即显示
            self.password_label.repaint()
            self.update()
            print("🎯 乐观更新：立即显示新密码 'ultrathink'，UI已强制刷新")

            # 🎯 后台API调用
            try:
                result = self.user_service.reset_user_password(self.user_id)
                if result and result.get('success'):
                    # ✅ 成功：保持UI更新，不弹框，不重新加载数据
                    print(f"✅ 密码重置成功，保持乐观更新的UI状态")
                    # 🎯 修复：不调用load_user_detail()，保持乐观更新的UI状态
                else:
                    # ❌ 失败：回滚UI并弹框提示
                    error_msg = result.get('message', '重置失败') if result else '重置失败'
                    self.password_label.setText('重置失败')
                    QMessageBox.warning(self, '操作失败', f'密码重置失败：{error_msg}\n\n已回滚显示。')
                    print(f"❌ 密码重置失败，已回滚：{error_msg}")
            except Exception as api_error:
                # ❌ 异常：回滚UI并弹框提示
                self.password_label.setText('重置异常')
                QMessageBox.critical(self, '操作异常', f'重置密码时发生错误：{str(api_error)}\n\n已回滚显示。')
                print(f"❌ 密码重置异常，已回滚：{str(api_error)}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'重置密码失败：{str(e)}')

    def toggle_user_status(self):
        """切换用户状态（激活/禁用）- 🎯 修复：真正的乐观更新"""
        try:
            current_status = '激活' in self.status_label.text()
            new_status = not current_status
            status_text = '激活' if new_status else '禁用'

            # 🎯 乐观更新：立即更新UI - 使用与正常显示一致的HTML格式
            if new_status:
                status_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
            else:
                status_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
            self.status_label.setText(status_html)
            # 🎯 修复：强制刷新UI，确保立即显示
            self.status_label.repaint()
            self.update()
            print(f"🚀 乐观更新：立即将用户状态显示为 {status_text}，UI已强制刷新")

            # 🎯 后台API调用
            try:
                result = self.user_service.toggle_user_status(self.user_id)
                if result and result.get('success'):
                    # ✅ 成功：保持UI更新，不弹框
                    print(f"✅ 状态切换成功：{status_text}")
                else:
                    # ❌ 失败：回滚UI并弹框提示
                    original_status_text = '禁用' if new_status else '激活'
                    if not new_status:  # 回滚到激活状态
                        rollback_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
                    else:  # 回滚到禁用状态
                        rollback_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
                    self.status_label.setText(rollback_html)
                    error_msg = result.get('message', '切换失败') if result else '切换失败'
                    QMessageBox.warning(self, '操作失败', f'状态切换失败：{error_msg}\n\n已回滚到原状态。')
                    print(f"❌ 状态切换失败，已回滚：{error_msg}")
            except Exception as api_error:
                # ❌ 异常：回滚UI并弹框提示
                original_status_text = '禁用' if new_status else '激活'
                if not new_status:  # 回滚到激活状态
                    rollback_html = '<span style="color: #27ae60; font-weight: bold;">🟢 激活</span>'
                else:  # 回滚到禁用状态
                    rollback_html = '<span style="color: #e74c3c; font-weight: bold;">🔴 禁用</span>'
                self.status_label.setText(rollback_html)
                QMessageBox.critical(self, '操作异常', f'切换状态时发生错误：{str(api_error)}\n\n已回滚到原状态。')
                print(f"❌ 状态切换异常，已回滚：{str(api_error)}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'切换用户状态失败：{str(e)}')

    def toggle_user_admin(self):
        """切换用户权限（普通用户/管理员）- 🎯 修复：真正的乐观更新"""
        try:
            current_admin = '管理员' in self.admin_label.text()
            new_admin = not current_admin
            role_text = '管理员' if new_admin else '普通用户'

            # 🎯 乐观更新：立即更新UI - 使用与正常显示一致的HTML格式
            if new_admin:
                admin_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
            else:
                admin_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
            self.admin_label.setText(admin_html)
            # 🎯 修复：强制刷新UI，确保立即显示
            self.admin_label.repaint()
            self.update()
            print(f"🚀 乐观更新：立即将用户权限显示为 {role_text}，UI已强制刷新")

            # 🎯 后台API调用
            try:
                result = self.user_service.toggle_user_admin(self.user_id)
                if result and result.get('success'):
                    # ✅ 成功：保持UI更新，不弹框
                    print(f"✅ 权限切换成功：{role_text}")
                else:
                    # ❌ 失败：回滚UI并弹框提示
                    original_role_text = '普通用户' if new_admin else '管理员'
                    if not new_admin:  # 回滚到管理员状态
                        rollback_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
                    else:  # 回滚到普通用户状态
                        rollback_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
                    self.admin_label.setText(rollback_html)
                    error_msg = result.get('message', '切换失败') if result else '切换失败'
                    QMessageBox.warning(self, '操作失败', f'权限切换失败：{error_msg}\n\n已回滚到原权限。')
                    print(f"❌ 权限切换失败，已回滚：{error_msg}")
            except Exception as api_error:
                # ❌ 异常：回滚UI并弹框提示
                original_role_text = '普通用户' if new_admin else '管理员'
                if not new_admin:  # 回滚到管理员状态
                    rollback_html = '<span style="color: #8e44ad; font-weight: bold;">👑 管理员</span>'
                else:  # 回滚到普通用户状态
                    rollback_html = '<span style="color: #34495e; font-weight: bold;">👤 普通用户</span>'
                self.admin_label.setText(rollback_html)
                QMessageBox.critical(self, '操作异常', f'切换权限时发生错误：{str(api_error)}\n\n已回滚到原权限。')
                print(f"❌ 权限切换异常，已回滚：{str(api_error)}")

        except Exception as e:
            QMessageBox.critical(self, '错误', f'切换用户权限失败：{str(e)}')

    def add_purchase_record(self):
        """添加购买记录 - 🎯 四层同步乐观更新架构（真正异步）"""
        try:
            from ui.purchase_dialogs import AddPurchaseDialog
            
            # 🎯 创建添加购买记录对话框
            dialog = AddPurchaseDialog(self.user_id, parent=self)
            # 🎯 UUID架构修复：每次打开对话框前刷新数据，确保筛选最新
            dialog.refresh_data()
            if dialog.exec() == dialog.DialogCode.Accepted:
                purchase_data = dialog.get_form_data()
                
                if not purchase_data:
                    print("❌ 调试：get_form_data()返回空数据，UI更新失败")
                    QMessageBox.warning(self, '数据错误', '获取表单数据失败，请检查选择项')
                    return
                
                # 🎯 新增：购买前重复检查
                if self._check_duplicate_purchase(purchase_data):
                    print("❌ 调试：检测到重复购买，阻止操作")
                    QMessageBox.warning(self, '重复购买', '该项目已经购买过了，请勿重复购买！')
                    return
                
                # 🔑 UUID架构：管理端生成购买记录的正式UUID - 唯一ID来源
                from utils.uuid_generator import generate_uuid
                if purchase_data.get('purchased_entity_type') == 'series_all_categories':
                    # 🔑 UUID架构：系列购买时使用购买对话框已生成的UUID，避免重复生成
                    categories = purchase_data.get('categories', [])
                    for category in categories:
                        # 🔍 检查是否已有购买UUID（由购买对话框生成）
                        existing_purchase_uuid = category.get('purchase_uuid')
                        
                        if existing_purchase_uuid:
                            # ✅ 使用购买对话框生成的UUID，保持数据流一致性
                            purchase_uuid = existing_purchase_uuid
                            print(f"🎯 UUID架构：使用购买对话框生成的UUID: {purchase_uuid} - 分类: {category.get('title')}")
                        else:
                            # 🆘 降级：如果没有预生成UUID，才在此处生成（不应该发生）
                            purchase_uuid = generate_uuid('purchase')
                            print(f"⚠️ UUID架构：降级生成UUID（不推荐）: {purchase_uuid} - 分类: {category.get('title')}")
                        
                        # 🔑 UUID架构：保留原分类ID用于关联，使用购买UUID作为记录主键
                        original_category_id = category.get('category_id') or category.get('id')  # 获取真实分类ID
                        
                        category['id'] = purchase_uuid  # 购买记录的正式UUID
                        category['category_id'] = original_category_id  # 关联的分类UUID  
                        category['original_category_id'] = original_category_id  # 备用字段
                        category['purchase_uuid'] = purchase_uuid  # 保持purchase_uuid字段
                        
                        print(f"🔑 UUID架构修复：购买记录UUID={purchase_uuid}, 分类ID={original_category_id} - {category.get('title')}")
                else:
                    # 🔑 UUID架构：单个购买记录使用管理端生成的UUID
                    purchase_data['id'] = generate_uuid('purchase')
                    print(f"🔑 UUID架构：管理端生成购买记录UUID: {purchase_data['id']}")
                
                # 🎯 第1层：UI层立即更新，用户立即看到效果
                print(f"🚀 [第1层] 开始UI更新，当前表格行数={self.purchases_table.rowCount()}")
                
                if purchase_data.get('purchased_entity_type') == 'series_all_categories':
                    # 🎯 购买整个系列：为每个分类创建购买记录
                    self._add_series_purchases_to_table(purchase_data)
                else:
                    # 🎯 购买单个分类
                    self._add_purchase_to_table(purchase_data)
                
                print(f"✅ [第1层] UI更新成功：购买记录已添加到表格，新行数={self.purchases_table.rowCount()}")
                
                # 🎯 立即更新待同步状态 - 解决连续购买筛选问题
                self._update_pending_purchase_state(purchase_data)
                
                # 🎯 启动异步管理器处理第2、3层操作
                self._start_async_purchase_process(purchase_data)
                    
        except ImportError:
            # 如果对话框类不存在，创建简化版本
            self._show_simple_add_purchase_dialog()
        except Exception as e:
            QMessageBox.critical(self, '错误', f'打开添加对话框失败：{str(e)}')

    def _update_pending_purchase_state(self, purchase_data):
        """更新待同步购买状态 - 立即生效，解决连续购买筛选问题"""
        try:
            from ui.pending_purchase_manager import pending_purchase_manager
            
            # 添加到待同步状态
            pending_purchase_manager.add_pending_purchase(self.user_id, purchase_data)
            
            # 调试信息
            stats = pending_purchase_manager.get_pending_stats(self.user_id)
            pending_purchase_manager.debug_print_pending(self.user_id)
            
        except Exception as e:
            print(f"❌ [待同步状态] 更新失败: {e}")

    def _start_async_purchase_process(self, purchase_data):
        """启动异步购买处理流程 - 使用线程池模式"""
        try:
            from ui.threadpool_purchase_workers import ThreadPoolPurchaseManager
            
            # 🎯 每次操作创建新的线程池管理器，避免状态混乱
            threadpool_manager = ThreadPoolPurchaseManager(self.user_service, self.user_id)
            
            # 连接信号
            threadpool_manager.purchase_completed.connect(self._on_async_purchase_completed)
            
            # 保存当前购买数据用于回滚
            self._current_purchase_data = purchase_data
            
            # 启动异步处理
            threadpool_manager.add_purchase_async(purchase_data)
            
        except Exception as e:
            print(f"❌ [线程池管理器] 启动失败: {e}")
            # 降级到同步处理
            self._fallback_sync_purchase_process(purchase_data)
    
    def _on_async_purchase_completed(self, success, message):
        """异步购买完成回调"""
        if success:
            print(f"✅ [异步回调] 购买流程完成: {message}")
            # 🎯 成功时清理待同步状态
            self._cleanup_pending_purchase_state()
            # 成功时不显示弹框，符合乐观更新原则
        else:
            print(f"❌ [异步回调] 购买流程失败: {message}")
            # 🎯 第4层：失败回滚UI更改
            self._rollback_purchase_add()
            # 🎯 失败时也要清理待同步状态
            self._cleanup_pending_purchase_state()
            QMessageBox.warning(self, '操作失败', f'购买记录添加失败：{message}')
            print(f"❌ [第4层回滚] 异步失败，已回滚UI - {message}")
    
    def _cleanup_pending_purchase_state(self):
        """清理待同步状态 - 同步完成后调用"""
        try:
            from ui.pending_purchase_manager import pending_purchase_manager
            
            if hasattr(self, '_current_purchase_data') and self._current_purchase_data:
                purchase_data = self._current_purchase_data
                
                if purchase_data.get('purchased_entity_type') == 'series_all_categories':
                    # 系列购买：清理系列和所有分类
                    series_id = purchase_data.get('series_id')
                    if series_id:
                        pending_purchase_manager.clear_pending_series(self.user_id, series_id)
                    
                    categories = purchase_data.get('categories', [])
                    for category in categories:
                        category_id = category.get('category_id') or category.get('original_category_id')
                        if category_id:
                            pending_purchase_manager.clear_pending_purchase(self.user_id, category_id)
                else:
                    # 单个分类购买：清理分类
                    category_id = purchase_data.get('purchased_entity_id')
                    if category_id:
                        pending_purchase_manager.clear_pending_purchase(self.user_id, category_id)
                
                print(f"🧹 [待同步状态] 清理完成")
                
        except Exception as e:
            print(f"❌ [待同步状态] 清理失败: {e}")
    
    def _fallback_sync_purchase_process(self, purchase_data):
        """降级到同步处理 - 异步失败时的备选方案"""
        try:
            print("🔄 [降级处理] 异步启动失败，降级到同步处理...")
            
            # 同步API调用
            result = self.user_service.add_user_purchase(self.user_id, purchase_data)
            if result and result.get('success'):
                print("✅ [降级处理] 同步API调用成功")
                
                # 同步缓存更新
                try:
                    from cache.global_data_manager import global_data_manager
                    global_data_manager.add_user_purchase_to_cache(self.user_id, purchase_data)
                    print("✅ [降级处理] 同步缓存更新成功")
                except Exception as cache_error:
                    print(f"⚠️ [降级处理] 缓存更新失败: {cache_error}")
                    
            else:
                # 同步失败，回滚UI
                error_msg = result.get('message', '添加失败') if result else '添加失败'
                print(f"❌ [降级处理] 同步API调用失败: {error_msg}")
                self._rollback_purchase_add()
                QMessageBox.warning(self, '操作失败', f'购买记录添加失败：{error_msg}')
                
        except Exception as e:
            print(f"❌ [降级处理] 同步处理也失败: {e}")
            self._rollback_purchase_add()
            QMessageBox.critical(self, '操作异常', f'添加购买记录时发生错误：{str(e)}')

    def delete_selected_purchases(self):
        """删除选中的购买记录 - 🎯 四层异步架构实现"""
        try:
            # 🎯 获取选中的行 - 使用ExtendedSelection模式
            selected_items = self.purchases_table.selectedItems()
            if not selected_items:
                QMessageBox.information(self, '提示', '请先选择要删除的购买记录')
                return
            
            # 获取选中行的行号（去重）
            selected_rows = list(set(item.row() for item in selected_items))
            selected_rows.sort()
            
            # 🎯 使用异步操作管理器执行删除
            self.async_purchase_ops.delete_purchases_async(
                selected_rows,
                self.purchases_table,
                self._get_table_row_data,
                self._rollback_purchase_deletions
            )
                
        except Exception as e:
            QMessageBox.critical(self, '错误', f'删除购买记录失败：{str(e)}')

    def _add_purchase_to_table(self, purchase_data):
        """将购买记录添加到表格（乐观更新）- 🎯 修复：新记录插入到顶部按时间倒序"""
        try:
            from cache.global_data_manager import global_data_manager
            
            row_count = self.purchases_table.rowCount()
            
            # 🎯 修复：在顶部插入新行（索引0），因为数据通常按时间倒序排列
            self.purchases_table.insertRow(0)
            
            # 🔧 修复：从全局缓存获取真实的实体名称，而不是使用默认值
            category_id = purchase_data.get('purchased_entity_id') or purchase_data.get('category_id')
            series_id = purchase_data.get('series_id')
            
            # 获取真实的分类名称
            category_name = purchase_data.get('category_name', '新分类')
            if category_id and global_data_manager.is_data_loaded():
                category_detail = global_data_manager.get_category_detail(category_id)
                if category_detail and category_detail.get('success'):
                    category_data = category_detail.get('data', {})
                    category_name = category_data.get('title', category_name)
            
            # 获取真实的系列名称
            series_name = purchase_data.get('series_name', '新系列')
            if series_id and global_data_manager.is_data_loaded():
                series_detail = global_data_manager.get_series_detail(series_id)
                if series_detail and series_detail.get('success'):
                    series_data = series_detail.get('data', {})
                    series_name = series_data.get('title', series_name)
            
            # 🔧 修复：系列购买应该显示"系列"而不是"分类"
            purchase_entity_type = purchase_data.get('purchased_entity_type', 'category')
            if purchase_entity_type == 'series_all_categories':
                purchase_type = '系列'  # 系列购买显示为"系列"
            else:
                purchase_type = purchase_data.get('purchase_type', '分类')
            
            amount = purchase_data.get('amount', 0)
            
            
            # 🎯 填充到第0行
            self.purchases_table.setItem(0, 0, QTableWidgetItem(category_name))
            self.purchases_table.setItem(0, 1, QTableWidgetItem(series_name))
            self.purchases_table.setItem(0, 2, QTableWidgetItem(purchase_type))
            
            # 🎯 从全局数据管理器获取准确的视频数量
            video_count = self._get_category_video_count(purchase_data)
            # 🔧 修复：区分数据未加载和数据为0的情况
            if video_count is None:
                video_count_text = "待统计"
            else:
                video_count_text = f"{video_count} 个视频"
            self.purchases_table.setItem(0, 3, QTableWidgetItem(video_count_text))
            
            # 🎯 格式化价格显示
            price_text = f"¥{amount:.2f}" if amount > 0 else "免费"
            self.purchases_table.setItem(0, 4, QTableWidgetItem(price_text))
            
            # 🎯 状态显示
            status_text = "付费" if amount > 0 else "免费"
            status_item = QTableWidgetItem(status_text)
            if amount > 0:
                status_item.setBackground(QColor(200, 255, 200))  # 绿色背景 - 付费
            else:
                status_item.setBackground(QColor(220, 220, 255))  # 蓝色背景 - 免费
            self.purchases_table.setItem(0, 5, status_item)
            
            # 🎯 时间显示
            from datetime import datetime
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
            self.purchases_table.setItem(0, 6, QTableWidgetItem(current_time))
            
            # 🎯 ID列（隐藏列）- UUID架构：使用客户端生成的正式UUID
            purchase_id = purchase_data.get('id', 'missing_uuid')
            if purchase_id == 'missing_uuid':
                print(f"⚠️ UUID架构缺失：purchase_data中缺少'id'字段，自动修复中...")
                # 🔑 UUID架构：自动修复缺失的UUID，确保功能连续性
                from utils.uuid_generator import generate_uuid
                purchase_id = generate_uuid('purchase')
                purchase_data['id'] = purchase_id
                print(f"🔧 UUID架构自动修复：为购买记录生成UUID {purchase_id}")
            
            self.purchases_table.setItem(0, 7, QTableWidgetItem(purchase_id))
            
            print(f"✅ 调试：表格数据填充完成，新记录已添加到顶部第0行，UUID={purchase_id}")
            
            # 🎯 强制刷新表格显示并选中新添加的行
            self.purchases_table.selectRow(0)  # 选中新添加的行
            self.purchases_table.scrollToTop()  # 滚动到顶部
            self.purchases_table.update()
            self.purchases_table.repaint()
            print(f"✅ 调试：表格强制刷新完成，已选中新记录")
            
        except Exception as e:
            print(f"❌ 添加到表格失败：{e}")
            import traceback
            traceback.print_exc()

    def _get_actual_video_count_for_purchase(self, purchase) -> int:
        """为购买记录获取实际视频数量（从全局数据管理器）"""
        try:
            from cache.global_data_manager import global_data_manager
            
            if not global_data_manager.is_data_loaded():
                print("⚠️ 全局数据未加载，视频数量返回0")
                return 0
            
            # 🎯 根据购买记录类型获取视频数量
            purchase_type = purchase.get('purchase_type', '分类')
            
            if purchase_type == '分类':
                # 分类购买：获取单个分类的视频数量
                category_id = purchase.get('category_id') or purchase.get('purchased_entity_id')
                if category_id:
                    category_detail = global_data_manager.get_category_detail(category_id)
                    if category_detail and category_detail.get('success'):
                        category_data = category_detail.get('data', {})
                        video_count = len(category_data.get('videos', []))
                        print(f"🔍 分类 {category_id} 实际视频数量: {video_count}")
                        return video_count
            elif purchase_type == '系列':
                # 系列购买：获取整个系列的视频总数
                series_id = purchase.get('series_id') or purchase.get('purchased_entity_id')
                if series_id:
                    series_categories = global_data_manager.get_category_list(page=1, page_size=100, series_id=series_id)
                    if series_categories and series_categories.get('success'):
                        total_videos = 0
                        for category in series_categories.get('data', []):
                            category_detail = global_data_manager.get_category_detail(category.get('id'))
                            if category_detail and category_detail.get('success'):
                                category_data = category_detail.get('data', {})
                                total_videos += len(category_data.get('videos', []))
                        print(f"🔍 系列 {series_id} 总视频数量: {total_videos}")
                        return total_videos
            
            print(f"⚠️ 无法获取视频数量，购买类型={purchase_type}")
            return 0
            
        except Exception as e:
            print(f"❌ 获取购买记录视频数量失败: {e}")
            return 0
    
    def _get_category_video_count(self, purchase_data) -> int | None:
        """获取分类的视频数量"""
        try:
            from cache.global_data_manager import global_data_manager
            
            if not global_data_manager.is_data_loaded():
                print("⚠️ 全局数据未加载，视频数量返回None（待统计）")
                return None
            
            # 🔧 修复：获取purchased_entity_id，处理系列购买场景
            purchased_entity_id = purchase_data.get('purchased_entity_id') or purchase_data.get('category_id')
            purchase_entity_type = purchase_data.get('purchased_entity_type', 'category')
            
            # 🔧 修复：处理series_all_categories场景
            if purchase_entity_type == 'series_all_categories':
                # 系列购买场景，使用category_id查询具体分类的视频数量
                purchased_entity_id = purchase_data.get('category_id')
                purchase_type = 'category'
            else:
                purchase_type = purchase_entity_type
            
            if purchase_type == 'category' and purchased_entity_id:
                # 购买分类：直接获取该分类的视频数量
                category_detail = global_data_manager.get_category_detail(purchased_entity_id)
                if category_detail and category_detail.get('success'):
                    category_data = category_detail.get('data', {})
                    video_count = len(category_data.get('videos', []))
                    print(f"🔍 分类 {purchased_entity_id} 视频数量: {video_count}")
                    return video_count
                else:
                    print(f"⚠️ 未找到分类 {purchased_entity_id} 的详细信息")
                    return 0
            elif purchase_type == 'series':
                # 购买系列：获取该系列下所有分类的视频总数
                series_categories = global_data_manager.get_category_list(page=1, page_size=100, series_id=purchased_entity_id)
                if series_categories and series_categories.get('success'):
                    total_videos = 0
                    for category in series_categories.get('data', []):
                        category_detail = global_data_manager.get_category_detail(category.get('id'))
                        if category_detail and category_detail.get('success'):
                            category_data = category_detail.get('data', {})
                            total_videos += len(category_data.get('videos', []))
                    print(f"🔍 系列 {purchased_entity_id} 总视频数量: {total_videos}")
                    return total_videos
            
            print(f"⚠️ 无法获取视频数量，purchased_entity_id={purchased_entity_id}, type={purchase_type}")
            return 0
            
        except Exception as e:
            print(f"❌ 获取视频数量失败: {e}")
            return 0

    def _add_series_purchases_to_table(self, series_purchase_data):
        """购买整个系列：为每个分类创建购买记录"""
        try:
            series_name = series_purchase_data.get('series_name', '未知系列')
            categories = series_purchase_data.get('categories', [])
            
            print(f"🎯 购买整个系列：{series_name}，包含 {len(categories)} 个分类")
            
            for category in categories:
                # 🔍 调试：打印分类数据结构
                print(f"🔍 调试分类数据: {category}")
                
                # 为每个分类创建单独的购买记录
                # 🔍 修复：正确区分分类ID和购买UUID
                real_category_id = category.get('category_id') or category.get('original_category_id')  # 真实分类ID
                purchase_uuid = category.get('id')  # 购买记录UUID（来自user_detail_window的重新赋值）
                
                print(f"🔍 数据流调试: 真实分类ID={real_category_id}, 购买UUID={purchase_uuid}")
                
                if not real_category_id:
                    print(f"❌ 数据流错误：无法获取真实分类ID，跳过此记录")
                    continue
                
                category_purchase_data = {
                    'purchased_entity_type': 'series_all_categories',  # 🔧 修复：标识为系列购买场景
                    'purchased_entity_id': real_category_id,  # 真实分类ID用于查询
                    'category_id': real_category_id,  # 🔧 修复：确保category_id是真实分类ID
                    'series_id': series_purchase_data.get('series_id'),  # 🔧 修复：从系列购买数据获取series_id
                    'amount': float(category.get('price', 0)),
                    'category_name': category.get('title', '未知分类'),
                    'series_name': series_name,
                    'purchase_type': '系列',  # 🔧 修复：系列购买显示为"系列"
                    'id': purchase_uuid  # 购买记录的UUID
                }
                
                print(f"  📦 添加分类购买记录：{category_purchase_data['category_name']} (UUID: {category_purchase_data['id']})")
                self._add_purchase_to_table(category_purchase_data)
            
            print(f"✅ 系列购买完成：已为 {len(categories)} 个分类创建购买记录")
            
        except Exception as e:
            print(f"❌ 添加系列购买记录失败：{e}")
            import traceback
            traceback.print_exc()

    def _get_table_row_data(self, row):
        """获取表格行数据"""
        try:
            # 🎯 修复：从隐藏的第7列获取真实的购买记录ID
            id_item = self.purchases_table.item(row, 7)
            purchase_id = id_item.text() if id_item else f'temp_id_{row}'
            
            return {
                'id': purchase_id,  # 🎯 使用真实的购买记录ID
                'category_name': self.purchases_table.item(row, 0).text() if self.purchases_table.item(row, 0) else '',
                'series_name': self.purchases_table.item(row, 1).text() if self.purchases_table.item(row, 1) else '',
                'purchase_type': self.purchases_table.item(row, 2).text() if self.purchases_table.item(row, 2) else '',
                'video_count': self.purchases_table.item(row, 3).text() if self.purchases_table.item(row, 3) else '',
                'amount': self.purchases_table.item(row, 4).text() if self.purchases_table.item(row, 4) else '',
                'status': self.purchases_table.item(row, 5).text() if self.purchases_table.item(row, 5) else '',
                'purchase_date': self.purchases_table.item(row, 6).text() if self.purchases_table.item(row, 6) else ''
            }
        except Exception as e:
            print(f"❌ 获取行数据失败：{e}")
            return {}

    def _check_duplicate_purchase(self, purchase_data) -> bool:
        """检查是否重复购买"""
        try:
            # 获取当前表格中的所有已购买的分类名称和系列名称
            existing_categories = set()
            existing_series = set()
            
            for row in range(self.purchases_table.rowCount()):
                # 从第0列获取分类名称
                category_item = self.purchases_table.item(row, 0)
                if category_item:
                    existing_categories.add(category_item.text())
                
                # 从第1列获取系列名称
                series_item = self.purchases_table.item(row, 1)
                if series_item:
                    existing_series.add(series_item.text())
            
            
            # 检查单个分类购买
            if purchase_data.get('type') == 'category':
                category_name = purchase_data.get('category_name', '')
                if category_name in existing_categories:
                    print(f"⚠️ 检测到重复购买：分类 '{category_name}' 已存在于购买记录中")
                    return True
            
            # 检查系列购买（多个分类）
            elif purchase_data.get('purchased_entity_type') == 'series_all_categories':
                categories = purchase_data.get('categories', [])
                for category in categories:
                    category_name = category.get('title', '')
                    if category_name in existing_categories:
                        print(f"⚠️ 检测到重复购买：分类 '{category_name}' 已存在于购买记录中")
                        return True
            
            return False
            
        except Exception as e:
            print(f"❌ 检查重复购买时出错: {e}")
            # 出错时保守起见，允许购买
            return False

    def _rollback_purchase_add(self):
        """回滚购买记录添加（移除顶部新增的行）- 🎯 修复：移除第0行"""
        try:
            row_count = self.purchases_table.rowCount()
            if row_count > 0:
                # 🎯 修复：移除第0行（顶部），因为新记录插入在顶部
                self.purchases_table.removeRow(0)
                print("🔄 已回滚购买记录添加（移除顶部第0行）")
        except Exception as e:
            print(f"❌ 回滚添加失败：{e}")

    def _rollback_purchase_deletions(self, backup_rows):
        """回滚购买记录删除（恢复删除的行）"""
        try:
            for row, row_data in backup_rows:
                self.purchases_table.insertRow(row)
                self.purchases_table.setItem(row, 0, QTableWidgetItem(row_data.get('category_name', '')))
                self.purchases_table.setItem(row, 1, QTableWidgetItem(row_data.get('series_name', '')))
                self.purchases_table.setItem(row, 2, QTableWidgetItem(row_data.get('purchase_type', '')))
                self.purchases_table.setItem(row, 3, QTableWidgetItem(row_data.get('video_count', '')))
                self.purchases_table.setItem(row, 4, QTableWidgetItem(row_data.get('amount', '')))
                self.purchases_table.setItem(row, 5, QTableWidgetItem(row_data.get('status', '')))
                self.purchases_table.setItem(row, 6, QTableWidgetItem(row_data.get('purchase_date', '')))
            print("🔄 已回滚购买记录删除")
        except Exception as e:
            print(f"❌ 回滚删除失败：{e}")

    def _refresh_purchases_data(self):
        """刷新购买记录数据"""
        try:
            purchases = self.user_service.get_user_purchases(self.user_id)
            self.populate_purchases(purchases)
            print("🔄 购买记录数据已刷新")
        except Exception as e:
            print(f"❌ 刷新购买记录数据失败：{e}")

    def _show_simple_add_purchase_dialog(self):
        """显示简化的添加购买记录对话框"""
        try:
            from PyQt6.QtWidgets import QDialog, QFormLayout, QLineEdit, QComboBox, QDialogButtonBox
            
            dialog = QDialog(self)
            dialog.setWindowTitle("添加购买记录")
            dialog.setMinimumSize(400, 300)
            
            layout = QFormLayout(dialog)
            
            # 购买类型选择
            type_combo = QComboBox()
            type_combo.addItems(["category", "series"])
            layout.addRow("购买类型:", type_combo)
            
            # 项目ID输入
            purchased_entity_id_edit = QLineEdit()
            purchased_entity_id_edit.setPlaceholderText("输入分类或系列ID")
            layout.addRow("购买实体ID:", purchased_entity_id_edit)
            
            # 价格输入
            amount_edit = QLineEdit()
            amount_edit.setPlaceholderText("输入价格，如 99.00")
            layout.addRow("价格:", amount_edit)
            
            # 按钮
            buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
            buttons.accepted.connect(dialog.accept)
            buttons.rejected.connect(dialog.reject)
            layout.addWidget(buttons)
            
            if dialog.exec() == QDialog.DialogCode.Accepted:
                purchase_data = {
                    'purchased_entity_type': type_combo.currentText(),
                    'purchased_entity_id': purchased_entity_id_edit.text(),
                    'amount': float(amount_edit.text()) if amount_edit.text() else 0.0
                }
                
                # 调用服务层添加购买记录
                result = self.user_service.add_user_purchase(self.user_id, purchase_data)
                if result and result.get('success'):
                    QMessageBox.information(self, '成功', '购买记录添加成功')
                    self._refresh_purchases_data()
                else:
                    error_msg = result.get('message', '添加失败') if result else '添加失败'
                    QMessageBox.warning(self, '失败', f'购买记录添加失败：{error_msg}')
                    
        except Exception as e:
            QMessageBox.critical(self, '错误', f'显示添加对话框失败：{str(e)}')
    
