---
description: 基于 `$ARGUMENTS` 问题描述，按照 严格证据驱动流程-决策树版 执行
version: 11
---

# 🌳 严格证据驱动流程-决策树版（强制执行保障版）

## 🔒 强制执行保障机制

### 核心防作弊规则
```yaml
违规行为立即惩罚:
  - 假装执行（无输出）: -50分
  - 自己加分: -30分  
  - 选择性跳过: -20分
  - 编造输出: -100分（立即终止）

执行证明要求:
  - 每个命令必须有实际输出
  - 输出为空需解释原因
  - 无输出 = 未执行 = 自动扣分

积分审计系统:
  - AI不能自己声明加分
  - 系统根据执行证明自动计分
  - 每阶段结束强制积分核算
```

## 📊 AI积分奖励机制（增强版）

### 积分规则总览
```yaml
初始积分: 0分
目标积分: >100分（优秀执行）
失败阈值: <-20分（强制重启）
作弊阈值: <-50分（立即终止）

正向激励:
  - 完成检查项（有输出证明）: +5分
  - 发现问题: +10分
  - 一次定位成功: +20分
  - 修复成功: +30分

负向惩罚:
  - 跳过检查项: -10分
  - 假设判断: -15分
  - 修复失败: -20分
  - 重复错误: -30分
  - 无输出执行: -50分

实时显示: 【当前积分：XX分】【执行证明：XX/XX】
```

## 🎯 阶段0：强制命令序列

### 项目全景认知 - 必须执行命令清单
```yaml
检查项1 - 项目识别:
  强制命令: 
    - ls -la .
    - find . -type f -name "*.py" | head -20
  执行证明: 必须显示文件列表
  无输出处理: 扣10分

检查项2 - 技术栈确认:
  强制命令:
    - cat requirements.txt 2>/dev/null || cat setup.py 2>/dev/null || echo "未找到依赖文件"
    - grep -r "import\|from" --include="*.py" | grep -E "PyQt|tkinter|flask|django" | head -10
  执行证明: 必须显示依赖或导入
  无输出处理: 扣10分

检查项3 - WSL环境确认:
  强制命令:
    - pwd
    - wslpath -w . 2>/dev/null || echo "非WSL环境"
    - uname -a
  执行证明: 必须显示路径信息
  无输出处理: 扣10分

检查项4 - 业务流程理解:
  强制命令:
    - grep -r "class.*:" --include="*.py" | grep -E "(User|Course|Student|Teacher)" | head -10
    - find . -name "*model*.py" -o -name "*entity*.py" | xargs ls -la 2>/dev/null
  执行证明: 必须找到核心实体类
  无输出处理: 需解释并扣5分

检查项5 - 数据库结构理解:
  强制命令:
    - find . -name "*.sql" -o -name "*schema*" -o -name "*migration*" | head -10
    - grep -r "CREATE TABLE\|class.*Model" --include="*.py" --include="*.sql" | head -10
  执行证明: 必须显示数据库相关文件
  无输出处理: 需解释并扣5分

检查项6 - API接口理解:
  强制命令:
    - grep -r "@app.route\|@router\|path(" --include="*.py" | head -15
    - find . -name "*api*.py" -o -name "*route*.py" -o -name "*view*.py" | xargs ls -la 2>/dev/null
  执行证明: 必须找到API定义
  无输出处理: 需解释并扣5分

检查项7 - 配置环境理解:
  强制命令:
    - find . -name "*.ini" -o -name "*.conf" -o -name "*.yaml" -o -name "*.json" | grep -v node_modules | head -10
    - ls -la *config* 2>/dev/null || echo "无config文件"
  执行证明: 必须显示配置文件
  无输出处理: 需解释

检查项8 - 规范提取（方法17）:
  强制命令:
    - cat CLAUDE.md | head -50
    - grep -E "技术栈|规范|原则" CLAUDE.md | head -10
  执行证明: 必须显示规范内容
  无输出处理: 扣15分（核心规范）

检查项9 - 服务验证（方法3）:
  强制命令:
    - curl -s http://localhost:8000/health || echo "服务不可用"
    - ps aux | grep -E "python.*server|uvicorn|gunicorn" | grep -v grep || echo "无Python服务进程"
  执行证明: 必须显示服务状态
  无输出处理: 正常（服务可能未启动）
```

## 📊 主流程决策树（带执行锁定）

```mermaid
graph TD
    Start[接收问题描述: $ARGUMENTS<br/>【积分：0】【锁定：阶段0】] --> Phase0[阶段0: 项目全景认知]
    
    %% 阶段0: 项目全景认知 [强制执行]
    Phase0 --> P0_Lock{阶段锁定检查<br/>必须完成所有命令}
    P0_Lock --> P0_Project[1. 项目识别<br/>执行强制命令]
    
    P0_Project --> P0_ProjProof{有执行证明?}
    P0_ProjProof -->|无 -50分| P0_ForceStop[强制停止<br/>假装执行]
    P0_ProjProof -->|有| P0_ProjCheck{输出符合预期?}
    P0_ProjCheck -->|否 -10分| P0_ProjRetry[重新执行]
    P0_ProjRetry --> P0_Project
    P0_ProjCheck -->|是 +5分| P0_Tech[2. 技术栈确认<br/>执行强制命令]
    
    %% 重复模式for其他8个检查项...
    P0_Tech --> P0_TechProof{有执行证明?}
    P0_TechProof -->|无 -50分| P0_ForceStop
    P0_TechProof -->|有 +5分| P0_WSL[3. WSL环境确认]
    
    %% ... 省略中间步骤展示 ...
    
    P0_M3 --> P0_Audit[🔍 积分审计]
    P0_Audit --> P0_Score{当前积分?}
    P0_Score -->|<0| P0_Restart[强制重启阶段0]
    P0_Score -->|0-45| P0_Warn[警告：执行不完整]
    P0_Score -->|>45| P0_Pass[阶段0通过<br/>解锁阶段1]
    
    P0_Pass --> Phase1[阶段1: 强制证据收集<br/>【已解锁】]
    P0_Restart --> Phase0
    
    %% 阶段1: 强制证据收集 [防偷懒核心]
    Phase1 --> P1_Lock{检查阶段0积分≥45?}
    P1_Lock -->|否| P0_Restart
    P1_Lock -->|是| P1_Force[🔒强制并行执行🔒<br/>不允许选择]
    
    %% 后续阶段保持原有结构，但都加入锁定检查
```

## 🔍 自动积分审计系统

### 阶段0积分审计表
```yaml
执行证明检查清单:
  ☐ 项目识别 - 查看ls输出 [自动+5分或-10分]
  ☐ 技术栈确认 - 查看requirements [自动+5分或-10分]
  ☐ WSL环境 - 查看pwd输出 [自动+5分或-10分]
  ☐ 业务流程 - 查看类定义 [自动+5分或-5分]
  ☐ 数据库结构 - 查看schema [自动+5分或-5分]
  ☐ API接口 - 查看路由定义 [自动+5分或-5分]
  ☐ 配置环境 - 查看配置文件 [自动+5分或0分]
  ☐ 规范提取 - 查看CLAUDE.md [自动+5分或-15分]
  ☐ 服务验证 - 查看服务状态 [自动+5分或0分]

积分计算公式:
  实际得分 = Σ(命令执行证明分) - Σ(跳过惩罚分)
  
阶段通过条件:
  - 必须执行所有强制命令
  - 积分 ≥ 45分
  - 无作弊行为
```

## 🚨 强制检查点说明（增强版）

### 检查点0：执行证明验证【新增】
```yaml
位置: 每个命令执行后
强制要求:
  - 必须有实际命令输出
  - 输出不能是编造的
  - 空输出必须解释
验证方式:
  - 检查输出格式是否符合命令预期
  - 检查输出内容是否与项目相关
  - 检查时间戳是否合理
失败后果:
  - 假装执行: -50分
  - 编造输出: -100分并终止
```

### 检查点1：证据有效性验证（方法22）
```yaml
位置: 阶段1中部
强制要求:
  完整性:
    - 查看了所有相关日志（需列出文件名）
    - 检查了完整环境状态（需显示命令输出）
    - 时间范围覆盖问题期间（需显示时间戳）
  相关性:
    - 证据与问题直接相关
    - 时间戳匹配
    - 位置匹配
  具体性:
    - 有明确错误信息（需引用具体行）
    - 有具体数值/代码（需显示内容）
    - 无模糊描述
失败后果:
  - 必须重新收集
  - 不允许继续分析
  - 扣15分
```

### 检查点2：证据驱动分析（方法19）
```yaml
位置: 阶段1末尾
强制要求:
  - 必须引用具体证据（行号+内容）
  - 禁止"可能是"、"应该是"
  - 禁止经验推测
  - 每个结论必须有证据支撑
失败后果:
  - 立即停止
  - 返回证据收集
  - 扣20分
```

### 检查点3：假设验证循环（方法20）
```yaml
位置: 阶段3核心
强制要求:
  - 每个假设必须设计验证命令
  - 必须执行验证命令
  - 必须等待验证结果
  - 必须基于结果判断
失败后果:
  - 不允许修复
  - 必须重新假设
  - 每次失败扣10分
```

## 💡 快速决策指南（防作弊版）

### 失败类型→回退策略
| 失败类型 | 回退目标 | 原因 | 扣分 | 锁定状态 |
|---------|---------|------|------|----------|
| 技术性失败 | 阶段4内重试 | 实现问题 | -10分 | 保持当前阶段 |
| 逻辑性失败 | 回到阶段3 | 根因判断错误 | -20分 | 锁定阶段3 |
| 根本性失败 | 回到阶段1 | 证据不足 | -30分 | 锁定阶段1 |
| 环境性失败 | 回到阶段0 | 基础环境问题 | -20分 | 锁定阶段0 |
| 作弊性失败 | 立即终止 | 假装执行/编造 | -50~-100分 | 终止执行 |

### 强制执行规则（升级版）
1. **阶段锁定** - 前置阶段未完成不能进入后续阶段
2. **命令强制** - 必须执行所有强制命令清单
3. **证明必须** - 每个执行必须有输出证明
4. **审计自动** - 系统自动计算积分，AI不能干预
5. **作弊零容忍** - 发现作弊立即终止

## 📋 方法组件库

### 方法1：powershell.exe执行验证
在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作。
- 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
- 流程：执行程序 → 读取日志 → 验证功能效果
- 执行证明：必须显示程序输出或错误信息

### 方法2：API请求验证  
直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。
- 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
- 流程：调用API → 分析响应 → 验证业务逻辑
- 执行证明：必须显示HTTP响应码和响应体

### 方法3：服务状态验证
检查服务进程的真实运行状态，避免假设服务可用性。

  - 验证策略：端口连通性 → 服务响应 → 进程状态
  - 命令选项：
    - 基础验证：curl -s http://localhost:8000/ -o /dev/null -w "%{http_code}" || echo "000"
    - 根路径探测：curl -s http://localhost:8000/ | head -20
    - 进程验证：netstat -tlnp 2>/dev/null | grep :8000 || ss -tlnp | grep :8000
    - 智能发现：curl -s http://localhost:8000/ | grep -o '"[^"]*health[^"]*":[^,}]*' | head -1
  - 执行证明：HTTP状态码、响应内容摘要或进程信息

### 方法4：代码生效验证
确认代码修改已实际应用并被执行，避免假设修改生效。
- 静态验证：文件变更是否存在（需显示diff）
- 动态验证：修改的代码路径是否被执行到（需显示日志）
- 执行证明：必须有git diff或文件对比输出

### 方法5：功能重复性检查
开发前搜索现有代码库，避免重复实现已存在的功能模块。
- 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数
- 执行证明：必须显示搜索结果，即使为空

### 方法6：日志优先症状收集
优先查看错误日志和异常堆栈，获取最直接证据。
- 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述
- 执行证明：必须列出查看的日志文件和关键内容

### 方法7：用户症状分层确认  
将用户描述分为表象→系统→根因三层，逐层收集症状。
- 表象层：用户直接看到的问题
- 系统层：服务和环境层面异常
- 根因层：代码和技术层面原因
- 执行证明：必须记录每层的具体症状

### 方法8：系统环境状态检查
并行检查服务状态、配置文件、依赖环境的实际状态。
- 检查维度：服务可用性 | 配置完整性 | 依赖满足性
- 并行策略：多维度同时检查，快速定位环境问题
- 执行证明：必须显示ps/netstat/config内容

### 方法9：执行路径反向确认
通过输出特征和日志模式反向定位真实执行的代码路径。
- 反向策略：从输出结果追溯到具体代码位置
- 执行证明：必须显示日志→代码的对应关系

### 方法10：数据流完整追踪
追踪数据从输入到输出的完整生命周期，识别异常节点。
- 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
- 异常识别：数据丢失、格式错误、转换失败、存储异常
- 执行证明：必须显示每个节点的数据状态

### 方法11：逐层隔离定位
按系统架构层次逐层隔离问题，避免跨层复杂化分析。
- 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次
- 执行证明：必须显示每层的测试结果

### 方法12：单一根因优先
优先查找单一明确的根因，避免多因素复杂化假设。
- 判断原则：一个问题对应一个主要原因
- 排除策略：先验证最直接最简单的可能性
- 执行证明：必须显示排除过程

### 方法13：代码逻辑直接验证
基于实际执行路径验证代码逻辑，不分析未执行代码。
- 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支
- 执行证明：必须显示代码执行路径

### 方法14：修复效果实时验证
修复后立即通过相同方法验证问题是否解决。
- 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入
- 执行证明：必须显示修复前后对比

### 方法15：功能完整性测试
验证修复不影响相关功能的正常工作。
- 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程
- 执行证明：必须显示测试用例和结果

### 方法16：根本解决确认
确认修复解决了原始根本问题，不是表面修复。
- 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除
- 执行证明：必须重现原始场景并验证

### 方法17：规范动态提取应用
根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。
- 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
- 应用原则：只应用相关规范，避免无关约束干扰
- 执行证明：必须引用具体规范条款

### 方法18：修复合规性验证
确保所有修复完全符合项目技术规范和架构原则。
- 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则
- 执行证明：必须逐项检查并显示结果

### 方法19：证据驱动分析
所有分析必须基于实际收集的证据，禁止无证据推理。
- 分析前提：先收集证据 → 再分析原因
- 禁止行为：基于经验推测 | 基于可能性判断
- 执行证明：每个结论必须标注证据来源

### 方法20：假设验证循环
每个根因假设都必须通过实际验证才能确认。
- 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定
- 执行证明：必须显示验证命令和结果

### 方法21：失败原因追溯
修复失败后必须基于新证据重新分析，不重复原方案。
- 追溯策略：识别失败点 → 收集新证据 → 调整分析方向
- 执行证明：必须显示失败点和新证据

### 方法22：证据有效性验证
验证收集的证据是否满足分析要求，确保证据质量合格。
- 验证维度：完整性（覆盖范围） | 相关性（直接关联） | 具体性（明确清晰）
- 失败处理：指出缺失内容 → 返回补充收集 → 禁止不完整分析
- 执行证明：必须列出所有证据并评估质量

## 🎯 执行要点（强制版）

1. **阶段锁定** - 未完成阶段不能跳转
2. **命令强制** - 必须执行强制命令清单
3. **证明必须** - 无输出=未执行=扣分
4. **审计自动** - 积分由系统计算
5. **作弊严惩** - 假装执行扣50分
6. **实时监控** - 显示【积分】【执行证明】【阶段锁定】

---
> 版本：11 | 优化：加入强制执行保障，防止AI作弊和偷懒