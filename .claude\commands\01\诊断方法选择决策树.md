# 🌳 诊断方法选择决策树

> 可视化的方法选择指南，帮助AI快速定位使用哪些诊断方法

## 📊 主决策树

```mermaid
graph TD
    Start[用户报告问题] --> Check1{问题类型?}
    
    Check1 -->|服务/启动问题| ServiceBranch[服务相关分支]
    Check1 -->|功能/业务问题| FeatureBranch[功能相关分支]
    Check1 -->|数据/显示问题| DataBranch[数据相关分支]
    Check1 -->|性能/响应问题| PerfBranch[性能相关分支]
    
    %% 服务分支
    ServiceBranch --> S1{能否启动?}
    S1 -->|完全无法启动| M3[方法3:服务状态验证<br/>+<br/>方法6:日志分析]
    S1 -->|启动后崩溃| M1[方法1:执行验证<br/>+<br/>方法6:查看崩溃日志]
    S1 -->|端口占用| M3B[方法3:端口检查<br/>+<br/>系统命令]
    
    %% 功能分支
    FeatureBranch --> F1{有错误信息?}
    F1 -->|有明确报错| M6[方法6:错误日志优先]
    F1 -->|无报错但异常| F2{涉及API?}
    F2 -->|是| M2[方法2:API请求验证<br/>+<br/>方法10:数据流追踪]
    F2 -->|否| M13[方法13:代码逻辑验证<br/>+<br/>方法9:执行路径确认]
    
    %% 数据分支
    DataBranch --> D1{数据在哪个环节异常?}
    D1 -->|存储异常| M10DB[方法10:数据流追踪<br/>重点:存储层]
    D1 -->|传输异常| M10API[方法10:数据流追踪<br/>重点:API层]
    D1 -->|显示异常| M10UI[方法10:数据流追踪<br/>重点:UI层]
    
    %% 性能分支
    PerfBranch --> P1{响应时间?}
    P1 -->|极慢>10s| M11[方法11:逐层隔离<br/>+<br/>数据库分析]
    P1 -->|偶尔慢| M8[方法8:环境检查<br/>+<br/>方法7:症状分层]
    P1 -->|越来越慢| DBCheck[检查数据量<br/>+<br/>索引分析]
```

## 🔍 细分决策树

### 验证阶段方法选择

```mermaid
graph TD
    Verify[需要验证] --> V1{验证什么?}
    
    V1 -->|代码是否生效| M4[方法4:代码生效验证]
    V1 -->|修复是否成功| M14[方法14:修复效果验证]
    V1 -->|是否影响其他| M15[方法15:功能完整性测试]
    V1 -->|是否根本解决| M16[方法16:根本解决确认]
    
    M14 --> V2{验证结果?}
    V2 -->|成功| M15
    V2 -->|失败| M21[方法21:失败原因追溯]
```

### 分析阶段方法选择

```mermaid
graph TD
    Analyze[开始分析] --> A1{有无证据?}
    
    A1 -->|无证据| ForceCollect[强制执行证据收集<br/>方法6+3+8]
    A1 -->|有证据| M19[方法19:证据驱动分析]
    
    M19 --> A2{需要假设?}
    A2 -->|是| M20[方法20:假设验证循环]
    A2 -->|否| DirectFix[直接修复]
    
    M20 --> A3{假设验证结果?}
    A3 -->|通过| DirectFix
    A3 -->|失败| M20
```

## 🎯 快速匹配表

### 症状 → 方法映射

| 症状描述 | 首选方法组合 | 备选方法 |
|---------|------------|---------|
| "报错了" | 6 → 19 → 20 | 3, 8 |
| "启动不了" | 3 → 6 → 1 | 8, 4 |
| "功能不对" | 2 → 10 → 13 | 6, 9 |
| "看不到数据" | 10 → 11 → 7 | 2, 6 |
| "很慢" | 11 → 8 → 性能工具 | 6, 10 |
| "偶尔出问题" | 7 → 8 → 6 → 20 | 11, 10 |
| "中文乱码" | 10 → 8(字符集) | 6 |
| "登录失败" | 2 → 6 → 10 | 19, 20 |

### 环境 → 方法偏好

```yaml
Windows环境:
  优先: 方法1 (powershell.exe)
  次选: 方法3, 6

Linux/WSL环境:
  优先: 方法3, 6, 8
  工具: curl, grep, tail

生产环境:
  必选: 方法3, 6
  快速: 方法B流程
  
开发环境:
  自由: 所有方法
  推荐: 方法A流程
```

## 💡 组合策略

### 高效组合（积分最大化）

```yaml
组合1 - 快速定位组合 (+25分潜力):
  方法6(日志) + 方法3(服务) + 方法8(环境)
  适用: 90%的常见问题

组合2 - 深度分析组合 (+40分潜力):
  方法10(数据流) + 方法11(逐层) + 方法19(证据)
  适用: 复杂的业务问题

组合3 - 验证闭环组合 (+30分潜力):
  方法20(假设) + 方法14(验证) + 方法15(回归)
  适用: 确保修复质量
```

### 禁忌组合（避免扣分）

```yaml
错误组合1 - 无证据分析 (-15分):
  ❌ 直接方法13 → 修改代码
  ✅ 方法6 → 方法19 → 方法13

错误组合2 - 无验证提交 (-20分):
  ❌ 修复 → 结束
  ✅ 修复 → 方法14 → 方法15

错误组合3 - 重复失败 (-30分):
  ❌ 失败 → 同样方法
  ✅ 失败 → 方法21 → 新方法
```

## 📌 特殊情况决策

### 用户提供了日志

```mermaid
graph LR
    UserLog[用户提供日志] --> MustRead[必须分析+5分]
    MustRead --> M6Plus[方法6增强版]
    M6Plus --> FindError{找到错误?}
    FindError -->|是| M19[方法19分析]
    FindError -->|否| RequestMore[请求更多日志]
```

### 多次修复失败

```mermaid
graph TD
    Failed2[失败≥2次] --> Force[强制启动]
    Force --> FC[方案C:深度诊断]
    FC --> M21[方法21:失败追溯]
    M21 --> NewApproach[必须换新思路]
    NewApproach --> M7[方法7:重新分层]
```

## 🚀 决策优化建议

1. **并行优先**：能同时执行的方法要并行（如方法3+6+8）
2. **证据优先**：没有证据前不要进入分析阶段
3. **快速失败**：发现方向错误立即调整，不要硬撑
4. **经验积累**：成功的组合要记住，下次优先使用

---

> 💡 记住：选对方法=成功一半，组合使用=事半功倍