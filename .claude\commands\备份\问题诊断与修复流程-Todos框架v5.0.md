---
description: 基于 `$ARGUMENTS` 按照 问题诊断与修复流程 执行
---

# 🤖 问题诊断与修复流程 

## 🧩 集成框架组件库

### ✅ CheckpointValidator - 检测点验证组件
```python
def validate_checkpoint(operations_done: list, required_evidence: list) -> bool:
    """验证操作是否完成，证据是否充足，返回True通过/False重新执行"""
```

### 🚪 StageGateValidator - 阶段门槛验证组件  
```python
def validate_stage_exit(completed_checkpoints: list, required_checkpoints: list) -> bool:
    """检查阶段退出条件是否满足，返回True可退出/False继续执行"""
```

### 🔄 LoopController - 循环控制组件
```python
def should_continue_loop(current_round: int, findings_history: list) -> bool:
    """
    控制强制循环：最少2轮 + 连续2轮无新发现退出 + 最多5轮
    返回True继续循环/False退出循环
    """
```

### 🔍 SubstantiveAssessment - 实质性判断组件
```python
def has_new_findings(current_findings: list, previous_findings: list) -> bool:
    """判断是否有新发现：有新发现继续，连续2轮无新发现停止"""
```

### 🤔 FiveWhysAnalyzer - Five Whys方法提醒
```python
# 调用时提醒AI使用Five Whys方法深化分析当前症状
```

### 🎯 OODAAnalyzer - OODA循环方法提醒  
```python
# 调用时提醒AI使用OODA循环方法(观察-定向-决策-行动)分析
```

---

## 📋 Todos框架结构设计

### 🏗️ 三层嵌套Todos架构

```
Level 1: 阶段级Todos (使用TodoWrite创建)
├── STAGE0: 项目准备工作
│   ├── Level 2: 步骤级Todos
│   │   ├── Step 0.1: 项目理解
│   │   │   └── Level 3: 操作级Todos
│   │   │       ├── 架构理解：目录结构 + 技术栈识别 + 模块划分
│   │   │       ├── 智能规范提取：claude.md读取 + 问题类型匹配 + 相关规范智选
│   │   │       ├── 上下文基础建立：关键路径 + 配置定位 + 依赖梳理
│   │   │       └── [CheckpointValidator验证]
│   │   ├── Step 0.2: 环境验证  
│   │   │   └── Level 3: 操作级Todos
│   │   │       ├── WSL能力边界确认：文件访问 + 代码分析 + HTTP验证能力
│   │   │       ├── 服务状态检测：curl http://localhost:8000/docs验证连接
│   │   │       ├── API端点发现：通过openapi.json获取端点列表
│   │   │       └── [CheckpointValidator验证]
│   │   └── Step 0.3: 问题复现
│   │       └── Level 3: 操作级Todos
│   │           ├── 步骤建立：用户操作 + 最小化过程 → 复现路径
│   │           ├── 验证确认：基于环境验证结果 + 步骤执行 → 复现成功
│   │           └── [CheckpointValidator验证]
│   └── [StageGateValidator阶段门槛检查]
├── STAGE1: 症状解析与信息收集强制循环
│   ├── Level 2: 循环级Todos (由LoopController控制)
│   │   ├── Round 1
│   │   │   ├── C1: 症状解析强化
│   │   │   │   └── Level 3: 分析操作Todos
│   │   │   │       ├── 前轮整合：整合前轮循环所有发现和线索
│   │   │   │       ├── [FiveWhysAnalyzer深化分析] 针对新线索连续质疑
│   │   │   │       └── 症状层次强化：强化表型层 + 技术层 + 根因层
│   │   │   ├── C2: 信息深挖
│   │   │   │   └── Level 3: 搜索操作Todos
│   │   │   │       ├── 靶向假设生成：基于C1解析结果生成验证假设
│   │   │   │       ├── [Task并行搜索] 假设驱动的代码文件发现
│   │   │   │       ├── [Task并行搜索] API端点深度发现
│   │   │   │       ├── [Task并行搜索] 配置文件关联发现
│   │   │   │       └── 四维证据扩展：代码+数据+环境+历史证据
│   │   │   ├── C3: 假设精炼
│   │   │   │   └── Level 3: 假设操作Todos
│   │   │   │       ├── 信息综合分析：综合C2收集信息
│   │   │   │       ├── [OODAAnalyzer循环分析] 观察定向决策行动
│   │   │   │       └── 验证方案设计：为C4设计验证策略
│   │   │   └── C4: 真实验证确认
│   │   │       └── Level 3: 验证操作Todos
│   │   │           ├── 实际验证执行：使用Read/Bash/Glob等工具，禁止推测
│   │   │           ├── [SubstantiveAssessment实质性判断] 评估本轮发现价值
│   │   │           └── [LoopController循环控制] 判断是否继续循环
│   │   └── Round 2, 3, 4, 5... (最少2轮，连续2轮无实质性发现退出，最多5轮)
│   └── [StageGateValidator阶段门槛检查]
├── STAGE2: 修复实施与验证强制循环
│   ├── Level 2: 循环级Todos (由LoopController控制)
│   │   ├── Round 1
│   │   │   ├── C1: 修复方案设计
│   │   │   │   └── Level 3: 设计操作Todos
│   │   │   │       ├── 前轮修复整合：整合前轮修复经验和效果
│   │   │   │       ├── [OODAAnalyzer循环优化] 基于修复反馈优化方案
│   │   │   │       └── 修复方案深化：针对根因设计精准修复
│   │   │   ├── C2: 修复实施执行
│   │   │   │   └── Level 3: 实施操作Todos
│   │   │   │       ├── [Task并行修复] 代码文件修复实施
│   │   │   │       ├── [Task并行修复] 配置文件修复实施  
│   │   │   │       ├── [MultiEdit工具] 复杂跨文件修复
│   │   │   │       └── 四维修复扩展：代码+配置+文档+测试修复
│   │   │   ├── C3: 修复效果验证
│   │   │   │   └── Level 3: 验证操作Todos
│   │   │   │       ├── 验证方案执行：基于实际测试验证修复效果
│   │   │   │       └── 验证标准检查：对比修复前后差异
│   │   │   └── C4: 修复质量评估
│   │   │       └── Level 3: 评估操作Todos
│   │   │           ├── 实际测试验证：执行真实测试，记录结果
│   │   │           ├── [SubstantiveAssessment修复需求判断] 评估是否需要进一步修复
│   │   │           └── [LoopController循环控制] 判断修复循环是否继续
│   │   └── Round 2, 3, 4, 5... (最少2轮修复循环)
│   └── [StageGateValidator阶段门槛检查]
└── STAGE3: 结果总结
    └── Level 2: 总结级Todos
        ├── 问题诊断总结：整理诊断过程发现的根本原因
        ├── 修复结果总结：汇总实施的修复措施和验证结果
        ├── 经验提取：总结问题解决过程中的关键经验和教训
        └── [CheckpointValidator验证]
```

---

## 🔄 AI执行流程详细设计

### 🚀 流程启动和Todos创建

```python
# AI执行时的标准流程
def execute_diagnosis_repair_with_todos_framework_v5(problem_description: str):
    """
    基于Todos框架v5.0的诊断修复流程执行
    AI按此流程创建todos并调用组件
    """
    
    # 1. 创建Level 1阶段级Todos
    stage_todos = [
        {"content": "STAGE0: 项目准备工作", "status": "pending", "priority": "high", "id": "stage0"},
        {"content": "STAGE1: 症状解析与信息收集强制循环", "status": "pending", "priority": "high", "id": "stage1"},
        {"content": "STAGE2: 修复实施与验证强制循环", "status": "pending", "priority": "high", "id": "stage2"},
        {"content": "STAGE3: 结果总结", "status": "pending", "priority": "high", "id": "stage3"}
    ]
    
    # AI调用TodoWrite创建阶段级todos
    TodoWrite(todos=stage_todos)
    
    # 2. 执行每个阶段
    execution_log = []
    
    for stage in ["STAGE0", "STAGE1", "STAGE2", "STAGE3"]:
        stage_result = execute_stage_with_todos_v5(stage, problem_description, execution_log)
        execution_log.append(stage_result)
        
        # 阶段完成后更新todo状态
        TodoWrite(todos=[{"content": f"{stage}: 已完成", "status": "completed", "priority": "high", "id": stage.lower()}])
    
    return {
        "execution_complete": True,
        "stages_executed": execution_log
    }
```

### 📋 阶段0: 项目准备工作的Todos实现

```python
def execute_stage0_with_todos_v5(problem_description: str):
    """
    阶段0的Todos框架v5.0实现
    """
    
    # 创建Level 2步骤级Todos
    step_todos = [
        {"content": "Step 0.1: 项目理解", "status": "pending", "priority": "high", "id": "step01"},
        {"content": "Step 0.2: 环境验证", "status": "pending", "priority": "high", "id": "step02"},  
        {"content": "Step 0.3: 问题复现", "status": "pending", "priority": "high", "id": "step03"}
    ]
    TodoWrite(todos=step_todos)
    
    stage_context = {"stage_id": "STAGE0", "checkpoints_completed": []}
    
    # === Step 0.1: 项目理解 ===
    TodoWrite(todos=[{"content": "Step 0.1: 项目理解", "status": "in_progress", "priority": "high", "id": "step01"}])
    
    step01_result = execute_step01_with_todos_v5(problem_description)
    
    # 调用CheckpointValidator验证
    checkpoint_passed = CheckpointValidator().validate_checkpoint(
        operations_done=step01_result["operations_performed"],
        required_evidence=step01_result["evidence_collected"]
    )
    
    if not checkpoint_passed:
        # 重新执行step01
        step01_result = execute_step01_with_todos_v5(problem_description)
    
    stage_context["checkpoints_completed"].append("project_understanding")
    TodoWrite(todos=[{"content": "Step 0.1: 项目理解", "status": "completed", "priority": "high", "id": "step01"}])
    
    # === Step 0.2: 环境验证 ===
    TodoWrite(todos=[{"content": "Step 0.2: 环境验证", "status": "in_progress", "priority": "high", "id": "step02"}])
    
    step02_result = execute_step02_with_todos_v5()
    
    # 调用CheckpointValidator验证
    checkpoint_passed = CheckpointValidator().validate_checkpoint(
        operations_done=step02_result["operations_performed"],
        required_evidence=step02_result["evidence_collected"]
    )
    
    if not checkpoint_passed:
        # 重新执行step02
        step02_result = execute_step02_with_todos_v5()
    
    stage_context["checkpoints_completed"].append("environment_verification")
    TodoWrite(todos=[{"content": "Step 0.2: 环境验证", "status": "completed", "priority": "high", "id": "step02"}])
    
    # === Step 0.3: 问题复现 ===
    TodoWrite(todos=[{"content": "Step 0.3: 问题复现", "status": "in_progress", "priority": "high", "id": "step03"}])
    
    step03_result = execute_step03_with_todos_v5(problem_description)
    
    # 调用CheckpointValidator验证
    checkpoint_passed = CheckpointValidator().validate_checkpoint(
        operations_done=step03_result["operations_performed"],
        required_evidence=step03_result["evidence_collected"]
    )
    
    if not checkpoint_passed:
        # 重新执行step03
        step03_result = execute_step03_with_todos_v5(problem_description)
    
    stage_context["checkpoints_completed"].append("problem_reproduction")
    TodoWrite(todos=[{"content": "Step 0.3: 问题复现", "status": "completed", "priority": "high", "id": "step03"}])
    
    # === 调用StageGateValidator检查阶段退出条件 ===
    gate_passed = StageGateValidator().validate_stage_exit(
        completed_checkpoints=stage_context["checkpoints_completed"],
        required_checkpoints=["project_understanding", "environment_verification", "problem_reproduction"]
    )
    
    if gate_passed:
        return {"stage": "STAGE0", "status": "completed", "gate_passed": True}
    else:
        # 重新执行未完成的检测点
        return {"stage": "STAGE0", "status": "failed", "needs_retry": True}

def execute_step01_with_todos_v5(problem_description: str):
    """Step 0.1: 项目理解的详细todos实现"""
    
    # 创建Level 3操作级Todos
    operation_todos = [
        {"content": "架构理解：目录结构 + 技术栈识别 + 模块划分", "status": "pending", "priority": "high", "id": "arch_understand"},
        {"content": "智能规范提取：claude.md读取 + 问题类型匹配 + 相关规范智选", "status": "pending", "priority": "medium", "id": "spec_extract"},
        {"content": "上下文基础建立：关键路径 + 配置定位 + 依赖梳理", "status": "pending", "priority": "medium", "id": "context_build"}
    ]
    TodoWrite(todos=operation_todos)
    
    # 执行具体操作
    step_result = {
        "tools_used": [],
        "files_accessed": [],
        "operations_performed": [],
        "evidence_collected": []
    }
    
    # 1. 架构理解
    TodoWrite(todos=[{"content": "架构理解：目录结构 + 技术栈识别 + 模块划分", "status": "in_progress", "priority": "high", "id": "arch_understand"}])
    
    # AI实际使用工具执行
    # 目录结构分析 - 使用LS和Glob工具
    # 技术栈识别 - 通过Read工具读取package.json, requirements.txt等
    # 模块划分 - 分析项目结构和依赖关系
    
    step_result["tools_used"].extend(["LS", "Glob", "Read"])
    step_result["files_accessed"].extend(["package.json", "src/", "requirements.txt"])
    step_result["operations_performed"].append("项目架构")
    step_result["evidence_collected"].append({"type": "architecture", "content": "项目结构和技术栈信息"})
    
    TodoWrite(todos=[{"content": "架构理解：目录结构 + 技术栈识别 + 模块划分", "status": "completed", "priority": "high", "id": "arch_understand"}])
    
    # 2. 智能规范提取
    TodoWrite(todos=[{"content": "智能规范提取：claude.md读取 + 问题类型匹配 + 相关规范智选", "status": "in_progress", "priority": "medium", "id": "spec_extract"}])
    
    # AI执行智能规范提取
    # claude.md读取 - 使用Read工具
    # 问题类型匹配 - 基于problem_description分析问题类型
    # 相关规范智选 - 只提取与当前问题相关的规范
    
    step_result["operations_performed"].append("技术栈识别")
    step_result["evidence_collected"].append({"type": "specifications", "content": "与问题相关的规范信息"})
    
    TodoWrite(todos=[{"content": "智能规范提取：claude.md读取 + 问题类型匹配 + 相关规范智选", "status": "completed", "priority": "medium", "id": "spec_extract"}])
    
    # 3. 上下文基础建立
    TodoWrite(todos=[{"content": "上下文基础建立：关键路径 + 配置定位 + 依赖梳理", "status": "in_progress", "priority": "medium", "id": "context_build"}])
    
    # AI建立上下文基础
    # 关键路径识别 - 基于问题定位关键代码路径
    # 配置定位 - 找到相关配置文件
    # 依赖梳理 - 分析相关依赖关系
    
    step_result["operations_performed"].append("相关规范")
    step_result["evidence_collected"].append({"type": "context", "content": "上下文和关键路径信息"})
    
    TodoWrite(todos=[{"content": "上下文基础建立：关键路径 + 配置定位 + 依赖梳理", "status": "completed", "priority": "medium", "id": "context_build"}])
    
    return step_result

def execute_step02_with_todos_v5():
    """Step 0.2: 环境验证的详细todos实现"""
    
    # 创建Level 3操作级Todos
    operation_todos = [
        {"content": "WSL能力边界确认：文件访问 + 代码分析 + HTTP验证能力", "status": "pending", "priority": "high", "id": "wsl_boundary"},
        {"content": "服务状态检测：curl http://localhost:8000/docs验证连接", "status": "pending", "priority": "high", "id": "service_check"},
        {"content": "API端点发现：通过openapi.json获取端点列表", "status": "pending", "priority": "medium", "id": "api_discovery"}
    ]
    TodoWrite(todos=operation_todos)
    
    step_result = {
        "tools_used": [],
        "files_accessed": [],
        "operations_performed": [],
        "evidence_collected": []
    }
    
    # 1. WSL能力边界确认
    TodoWrite(todos=[{"content": "WSL能力边界确认：文件访问 + 代码分析 + HTTP验证能力", "status": "in_progress", "priority": "high", "id": "wsl_boundary"}])
    
    # AI确认WSL能力边界
    # 文件访问测试 - 确认可以访问项目文件
    # 代码分析能力 - 确认可以读取和分析代码
    # HTTP验证能力 - 确认可以使用curl命令
    
    step_result["operations_performed"].append("WSL边界")
    step_result["evidence_collected"].append({"type": "environment", "content": "WSL能力边界确认"})
    
    TodoWrite(todos=[{"content": "WSL能力边界确认：文件访问 + 代码分析 + HTTP验证能力", "status": "completed", "priority": "high", "id": "wsl_boundary"}])
    
    # 2. 服务状态检测  
    TodoWrite(todos=[{"content": "服务状态检测：curl http://localhost:8000/docs验证连接", "status": "in_progress", "priority": "high", "id": "service_check"}])
    
    # AI使用Bash工具执行curl命令
    # curl_result = Bash("curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/docs")
    
    step_result["tools_used"].append("Bash")
    step_result["operations_performed"].append("服务状态")
    step_result["evidence_collected"].append({"type": "service_status", "content": "服务连接状态"})
    
    TodoWrite(todos=[{"content": "服务状态检测：curl http://localhost:8000/docs验证连接", "status": "completed", "priority": "high", "id": "service_check"}])
    
    # 3. API端点发现
    TodoWrite(todos=[{"content": "API端点发现：通过openapi.json获取端点列表", "status": "in_progress", "priority": "medium", "id": "api_discovery"}])
    
    # AI通过openapi.json获取API端点
    # api_data = Bash("curl -s http://localhost:8000/openapi.json")
    
    step_result["operations_performed"].append("API端点")
    step_result["evidence_collected"].append({"type": "api_endpoints", "content": "API端点列表"})
    
    TodoWrite(todos=[{"content": "API端点发现：通过openapi.json获取端点列表", "status": "completed", "priority": "medium", "id": "api_discovery"}])
    
    return step_result

def execute_step03_with_todos_v5(problem_description: str):
    """Step 0.3: 问题复现的详细todos实现"""
    
    # 创建Level 3操作级Todos
    operation_todos = [
        {"content": "步骤建立：用户操作 + 最小化过程 → 复现路径", "status": "pending", "priority": "high", "id": "repro_steps"},
        {"content": "验证确认：基于环境验证结果 + 步骤执行 → 复现成功", "status": "pending", "priority": "high", "id": "repro_verify"}
    ]
    TodoWrite(todos=operation_todos)
    
    step_result = {
        "tools_used": [],
        "files_accessed": [],
        "operations_performed": [],
        "evidence_collected": []
    }
    
    # 1. 步骤建立
    TodoWrite(todos=[{"content": "步骤建立：用户操作 + 最小化过程 → 复现路径", "status": "in_progress", "priority": "high", "id": "repro_steps"}])
    
    # AI基于problem_description建立复现步骤
    # 分析用户报告的操作序列
    # 设计最小化复现过程
    # 确定复现路径和验证点
    
    step_result["operations_performed"].append("复现路径")
    step_result["evidence_collected"].append({"type": "reproduction_steps", "content": "问题复现步骤"})
    
    TodoWrite(todos=[{"content": "步骤建立：用户操作 + 最小化过程 → 复现路径", "status": "completed", "priority": "high", "id": "repro_steps"}])
    
    # 2. 验证确认
    TodoWrite(todos=[{"content": "验证确认：基于环境验证结果 + 步骤执行 → 复现成功", "status": "in_progress", "priority": "high", "id": "repro_verify"}])
    
    # AI执行复现验证
    # 基于步骤1建立的复现路径
    # 使用API端点或服务测试
    # 确认问题可以稳定复现
    
    step_result["operations_performed"].append("复现验证")
    step_result["evidence_collected"].append({"type": "reproduction_result", "content": "复现验证结果"})
    
    TodoWrite(todos=[{"content": "验证确认：基于环境验证结果 + 步骤执行 → 复现成功", "status": "completed", "priority": "high", "id": "repro_verify"}])
    
    return step_result
```

### 🔄 第一阶段: 症状解析与信息收集强制循环

```python
def execute_stage1_with_todos_v5(problem_description: str):
    """
    第一阶段的Todos框架v5.0实现 - 强制循环
    """
    
    # 创建循环控制上下文
    loop_context = {
        "stage_id": "STAGE1", 
        "current_round": 0,
        "findings_history": [],
        "max_rounds": 5,
        "min_rounds": 2
    }
    
    # 执行强制循环
    while True:
        loop_context["current_round"] += 1
        current_round = loop_context["current_round"]
        
        # 创建本轮Level 2循环级Todos
        round_todos = [
            {"content": f"Round {current_round} - C1: 症状解析强化", "status": "pending", "priority": "high", "id": f"r{current_round}_c1"},
            {"content": f"Round {current_round} - C2: 信息深挖", "status": "pending", "priority": "high", "id": f"r{current_round}_c2"},
            {"content": f"Round {current_round} - C3: 假设精炼", "status": "pending", "priority": "high", "id": f"r{current_round}_c3"},
            {"content": f"Round {current_round} - C4: 真实验证确认", "status": "pending", "priority": "high", "id": f"r{current_round}_c4"}
        ]
        TodoWrite(todos=round_todos)
        
        # 执行本轮C1-C4循环
        round_findings = execute_analysis_cycle_v5(current_round, problem_description, loop_context)
        loop_context["findings_history"].append(round_findings)
        
        # 调用LoopController判断是否继续循环
        should_continue = LoopController().should_continue_loop(
            current_round=current_round,
            findings_history=loop_context["findings_history"]
        )
        
        if not should_continue and current_round >= loop_context["min_rounds"]:
            break
        elif current_round >= loop_context["max_rounds"]:
            break
    
    # 调用StageGateValidator检查阶段退出
    gate_passed = StageGateValidator().validate_stage_exit(
        completed_checkpoints=[f"analysis_round_{i}" for i in range(1, current_round+1)],
        required_checkpoints=["analysis_round_1", "analysis_round_2"]  # 至少2轮
    )
    
    return {"stage": "STAGE1", "rounds_completed": current_round, "gate_passed": gate_passed}

def execute_analysis_cycle_v5(round_num: int, problem_description: str, loop_context: dict):
    """执行单轮分析循环 C1→C2→C3→C4"""
    
    round_findings = {"round": round_num, "findings": []}
    
    # === C1: 症状解析强化 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C1: 症状解析强化", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c1"}])
    
    # 创建C1的Level 3操作级Todos
    c1_todos = [
        {"content": "前轮整合：整合前轮循环所有发现和线索", "status": "pending", "priority": "high", "id": f"r{round_num}_c1_integrate"},
        {"content": "症状层次强化：强化表型层 + 技术层 + 根因层", "status": "pending", "priority": "medium", "id": f"r{round_num}_c1_layers"}
    ]
    TodoWrite(todos=c1_todos)
    
    # 执行前轮整合
    TodoWrite(todos=[{"content": "前轮整合：整合前轮循环所有发现和线索", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c1_integrate"}])
    
    if round_num > 1:
        # 整合前轮发现
        previous_findings = loop_context["findings_history"]
        # AI分析前轮发现，提取有用信息
    
    TodoWrite(todos=[{"content": "前轮整合：整合前轮循环所有发现和线索", "status": "completed", "priority": "high", "id": f"r{round_num}_c1_integrate"}])
    
    # [FiveWhysAnalyzer] 提醒AI使用Five Whys方法深化分析当前症状
    # AI应该基于前轮发现，对当前症状进行Why1→Why2→Why3→Why4→Why5连续深挖
    
    # 执行症状层次强化
    TodoWrite(todos=[{"content": "症状层次强化：强化表型层 + 技术层 + 根因层", "status": "in_progress", "priority": "medium", "id": f"r{round_num}_c1_layers"}])
    
    # AI执行症状层次强化分析
    
    TodoWrite(todos=[{"content": "症状层次强化：强化表型层 + 技术层 + 根因层", "status": "completed", "priority": "medium", "id": f"r{round_num}_c1_layers"}])
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C1: 症状解析强化", "status": "completed", "priority": "high", "id": f"r{round_num}_c1"}])
    
    # === C2: 信息深挖 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C2: 信息深挖", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c2"}])
    
    # 创建C2的Level 3操作级Todos - 使用Task并行搜索
    c2_todos = [
        {"content": "靶向假设生成：基于C1解析结果生成验证假设", "status": "pending", "priority": "high", "id": f"r{round_num}_c2_hypothesis"},
        {"content": "四维证据扩展：代码+数据+环境+历史证据", "status": "pending", "priority": "high", "id": f"r{round_num}_c2_evidence"}
    ]
    TodoWrite(todos=c2_todos)
    
    # 执行靶向假设生成
    TodoWrite(todos=[{"content": "靶向假设生成：基于C1解析结果生成验证假设", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c2_hypothesis"}])
    
    # AI基于C1结果生成本轮验证假设
    
    TodoWrite(todos=[{"content": "靶向假设生成：基于C1解析结果生成验证假设", "status": "completed", "priority": "high", "id": f"r{round_num}_c2_hypothesis"}])
    
    # 执行Task并行搜索 - 四维证据扩展
    TodoWrite(todos=[{"content": "四维证据扩展：代码+数据+环境+历史证据", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c2_evidence"}])
    
    # AI使用Task工具进行并行搜索
    # Task 1: 代码文件发现
    # Task 2: API端点发现  
    # Task 3: 配置文件发现
    # 并行执行，收集四维证据
    
    c2_findings = ["新发现的代码", "新发现的API", "新发现的配置"]
    round_findings["findings"].extend(c2_findings)
    
    TodoWrite(todos=[{"content": "四维证据扩展：代码+数据+环境+历史证据", "status": "completed", "priority": "high", "id": f"r{round_num}_c2_evidence"}])
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C2: 信息深挖", "status": "completed", "priority": "high", "id": f"r{round_num}_c2"}])
    
    # === C3: 假设精炼 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C3: 假设精炼", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c3"}])
    
    # 创建C3的Level 3操作级Todos
    c3_todos = [
        {"content": "信息综合分析：综合C2收集信息", "status": "pending", "priority": "high", "id": f"r{round_num}_c3_synthesis"},
        {"content": "验证方案设计：为C4设计验证策略", "status": "pending", "priority": "medium", "id": f"r{round_num}_c3_strategy"}
    ]
    TodoWrite(todos=c3_todos)
    
    # 执行信息综合分析
    TodoWrite(todos=[{"content": "信息综合分析：综合C2收集信息", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c3_synthesis"}])
    
    # AI综合C2收集的信息
    
    TodoWrite(todos=[{"content": "信息综合分析：综合C2收集信息", "status": "completed", "priority": "high", "id": f"r{round_num}_c3_synthesis"}])
    
    # [OODAAnalyzer] 提醒AI使用OODA循环方法分析
    # AI应该执行：观察C2收集信息 → 基于前轮经验定向 → 决策下步行动 → 执行具体行动
    
    # 执行验证方案设计
    TodoWrite(todos=[{"content": "验证方案设计：为C4设计验证策略", "status": "in_progress", "priority": "medium", "id": f"r{round_num}_c3_strategy"}])
    
    # AI设计C4验证策略
    
    TodoWrite(todos=[{"content": "验证方案设计：为C4设计验证策略", "status": "completed", "priority": "medium", "id": f"r{round_num}_c3_strategy"}])
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C3: 假设精炼", "status": "completed", "priority": "high", "id": f"r{round_num}_c3"}])
    
    # === C4: 真实验证确认 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C4: 真实验证确认", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c4"}])
    
    # 创建C4的Level 3操作级Todos
    c4_todos = [
        {"content": "实际验证执行：使用Read/Bash/Glob等工具，禁止推测", "status": "pending", "priority": "high", "id": f"r{round_num}_c4_verify"}
    ]
    TodoWrite(todos=c4_todos)
    
    # 执行实际验证
    TodoWrite(todos=[{"content": "实际验证执行：使用Read/Bash/Glob等工具，禁止推测", "status": "in_progress", "priority": "high", "id": f"r{round_num}_c4_verify"}])
    
    # AI使用实际工具进行验证，禁止推测
    # 使用Read工具读取代码文件
    # 使用Bash工具执行测试命令
    # 使用Glob工具搜索相关文件
    
    TodoWrite(todos=[{"content": "实际验证执行：使用Read/Bash/Glob等工具，禁止推测", "status": "completed", "priority": "high", "id": f"r{round_num}_c4_verify"}])
    
    # [SubstantiveAssessment] 判断本轮是否有新发现
    current_findings = round_findings["findings"]
    previous_findings = [h["findings"] for h in loop_context.get("findings_history", [])]
    has_new_findings = SubstantiveAssessment().has_new_findings(current_findings, previous_findings)
    round_findings["has_new_findings"] = has_new_findings
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C4: 真实验证确认", "status": "completed", "priority": "high", "id": f"r{round_num}_c4"}])
    
    return round_findings
```

### 🔄 第二阶段: 修复实施与验证强制循环

```python
def execute_stage2_with_todos_v5(problem_description: str, analysis_results: dict):
    """
    第二阶段的Todos框架v5.0实现 - 修复循环
    """
    
    # 创建修复循环控制上下文
    repair_context = {
        "stage_id": "STAGE2",
        "current_round": 0,
        "repair_history": [],
        "max_rounds": 5,
        "min_rounds": 2,
        "analysis_results": analysis_results
    }
    
    # 执行强制修复循环
    while True:
        repair_context["current_round"] += 1
        current_round = repair_context["current_round"]
        
        # 创建本轮Level 2循环级Todos
        repair_round_todos = [
            {"content": f"Round {current_round} - C1: 修复方案设计", "status": "pending", "priority": "high", "id": f"repair_r{current_round}_c1"},
            {"content": f"Round {current_round} - C2: 修复实施执行", "status": "pending", "priority": "high", "id": f"repair_r{current_round}_c2"},
            {"content": f"Round {current_round} - C3: 修复效果验证", "status": "pending", "priority": "high", "id": f"repair_r{current_round}_c3"},
            {"content": f"Round {current_round} - C4: 修复质量评估", "status": "pending", "priority": "high", "id": f"repair_r{current_round}_c4"}
        ]
        TodoWrite(todos=repair_round_todos)
        
        # 执行本轮修复循环
        repair_results = execute_repair_cycle_v5(current_round, problem_description, repair_context)
        repair_context["repair_history"].append(repair_results)
        
        # 调用LoopController判断是否继续修复循环
        should_continue = LoopController().should_continue_loop(
            current_round=current_round,
            findings_history=repair_context["repair_history"]
        )
        
        if not should_continue and current_round >= repair_context["min_rounds"]:
            break
        elif current_round >= repair_context["max_rounds"]:
            break
    
    # 调用StageGateValidator检查修复阶段退出
    gate_passed = StageGateValidator().validate_stage_exit(
        completed_checkpoints=[f"repair_round_{i}" for i in range(1, current_round+1)],
        required_checkpoints=["repair_round_1", "repair_round_2"]  # 至少2轮修复
    )
    
    return {"stage": "STAGE2", "repair_rounds_completed": current_round, "gate_passed": gate_passed}

def execute_repair_cycle_v5(round_num: int, problem_description: str, repair_context: dict):
    """执行单轮修复循环 C1→C2→C3→C4"""
    
    repair_results = {"round": round_num, "repairs_implemented": [], "verification_results": []}
    
    # === C1: 修复方案设计 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C1: 修复方案设计", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c1"}])
    
    # 创建C1的Level 3操作级Todos
    c1_repair_todos = [
        {"content": "前轮修复整合：整合前轮修复经验和效果", "status": "pending", "priority": "high", "id": f"repair_r{round_num}_c1_integrate"},
        {"content": "修复方案深化：针对根因设计精准修复", "status": "pending", "priority": "high", "id": f"repair_r{round_num}_c1_design"}
    ]
    TodoWrite(todos=c1_repair_todos)
    
    # 执行前轮修复整合
    if round_num > 1:
        # AI整合前轮修复经验
        pass
    
    # [OODAAnalyzer] 提醒AI使用OODA循环优化修复方案
    # AI应该执行：观察前轮修复结果 → 基于分析结果重新定向 → 决策优化方案 → 执行修复设计
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C1: 修复方案设计", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c1"}])
    
    # === C2: 修复实施执行 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C2: 修复实施执行", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c2"}])
    
    # 创建C2的Level 3操作级Todos - 使用Task并行修复
    c2_repair_todos = [
        {"content": "四维修复扩展：代码+配置+文档+测试修复", "status": "pending", "priority": "high", "id": f"repair_r{round_num}_c2_multi"}
    ]
    TodoWrite(todos=c2_repair_todos)
    
    # 执行Task并行修复
    TodoWrite(todos=[{"content": "四维修复扩展：代码+配置+文档+测试修复", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c2_multi"}])
    
    # AI使用Task工具进行并行修复
    # Task 1: 代码文件修复
    # Task 2: 配置文件修复
    # Task 3: 文档修复
    # 使用MultiEdit工具进行复杂跨文件修复
    
    implemented_repairs = ["代码修复1", "配置修复1", "文档修复1"]
    repair_results["repairs_implemented"].extend(implemented_repairs)
    
    TodoWrite(todos=[{"content": "四维修复扩展：代码+配置+文档+测试修复", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c2_multi"}])
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C2: 修复实施执行", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c2"}])
    
    # === C3: 修复效果验证 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C3: 修复效果验证", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c3"}])
    
    # 创建C3的Level 3操作级Todos
    c3_repair_todos = [
        {"content": "验证方案执行：基于实际测试验证修复效果", "status": "pending", "priority": "high", "id": f"repair_r{round_num}_c3_test"},
        {"content": "验证标准检查：对比修复前后差异", "status": "pending", "priority": "medium", "id": f"repair_r{round_num}_c3_compare"}
    ]
    TodoWrite(todos=c3_repair_todos)
    
    # 执行实际测试验证
    TodoWrite(todos=[{"content": "验证方案执行：基于实际测试验证修复效果", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c3_test"}])
    
    # AI执行实际验证测试
    verification_results = ["验证结果1", "验证结果2"]
    repair_results["verification_results"].extend(verification_results)
    
    TodoWrite(todos=[{"content": "验证方案执行：基于实际测试验证修复效果", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c3_test"}])
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C3: 修复效果验证", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c3"}])
    
    # === C4: 修复质量评估 ===
    TodoWrite(todos=[{"content": f"Round {round_num} - C4: 修复质量评估", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c4"}])
    
    # 创建C4的Level 3操作级Todos
    c4_repair_todos = [
        {"content": "实际测试验证：执行真实测试，记录结果", "status": "pending", "priority": "high", "id": f"repair_r{round_num}_c4_final_test"}
    ]
    TodoWrite(todos=c4_repair_todos)
    
    # 执行最终测试验证
    TodoWrite(todos=[{"content": "实际测试验证：执行真实测试，记录结果", "status": "in_progress", "priority": "high", "id": f"repair_r{round_num}_c4_final_test"}])
    
    # AI执行最终测试
    
    TodoWrite(todos=[{"content": "实际测试验证：执行真实测试，记录结果", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c4_final_test"}])
    
    # [SubstantiveAssessment] 判断是否需要进一步修复
    current_results = repair_results["verification_results"] 
    previous_results = [h["verification_results"] for h in repair_context.get("repair_history", [])]
    needs_more_repair = SubstantiveAssessment().has_new_findings(current_results, previous_results)
    repair_results["needs_more_repair"] = needs_more_repair
    
    TodoWrite(todos=[{"content": f"Round {round_num} - C4: 修复质量评估", "status": "completed", "priority": "high", "id": f"repair_r{round_num}_c4"}])
    
    return repair_results
```

### 📝 第三阶段: 结果总结

```python
def execute_stage3_with_todos_v5(problem_description: str, execution_log: list):
    """
    第三阶段的Todos框架v5.0实现 - 结果总结
    """
    
    # 创建Level 2总结级Todos
    summary_todos = [
        {"content": "问题诊断总结：整理诊断过程发现的根本原因", "status": "pending", "priority": "high", "id": "summary_diagnosis"},
        {"content": "修复结果总结：汇总实施的修复措施和验证结果", "status": "pending", "priority": "high", "id": "summary_repair"},
        {"content": "经验提取：总结问题解决过程中的关键经验和教训", "status": "pending", "priority": "medium", "id": "summary_lessons"}
    ]
    TodoWrite(todos=summary_todos)
    
    summary_result = {"stage": "STAGE3", "summaries": []}
    
    # 问题诊断总结
    TodoWrite(todos=[{"content": "问题诊断总结：整理诊断过程发现的根本原因", "status": "in_progress", "priority": "high", "id": "summary_diagnosis"}])
    # AI整理诊断阶段的发现和根本原因
    summary_result["summaries"].append("诊断总结完成")
    TodoWrite(todos=[{"content": "问题诊断总结：整理诊断过程发现的根本原因", "status": "completed", "priority": "high", "id": "summary_diagnosis"}])
    
    # 修复结果总结
    TodoWrite(todos=[{"content": "修复结果总结：汇总实施的修复措施和验证结果", "status": "in_progress", "priority": "high", "id": "summary_repair"}])
    # AI汇总修复措施和验证结果
    summary_result["summaries"].append("修复总结完成")
    TodoWrite(todos=[{"content": "修复结果总结：汇总实施的修复措施和验证结果", "status": "completed", "priority": "high", "id": "summary_repair"}])
    
    # 经验提取
    TodoWrite(todos=[{"content": "经验提取：总结问题解决过程中的关键经验和教训", "status": "in_progress", "priority": "medium", "id": "summary_lessons"}])
    # AI提取关键经验和教训
    summary_result["summaries"].append("经验提取完成")
    TodoWrite(todos=[{"content": "经验提取：总结问题解决过程中的关键经验和教训", "status": "completed", "priority": "medium", "id": "summary_lessons"}])
    
    # 调用CheckpointValidator验证总结完成
    checkpoint_passed = CheckpointValidator().validate_checkpoint(
        operations_done=summary_result["summaries"],
        required_evidence=["诊断总结", "修复总结", "经验提取"]
    )
    
    return {"stage": "STAGE3", "status": "completed", "checkpoint_passed": checkpoint_passed}
```