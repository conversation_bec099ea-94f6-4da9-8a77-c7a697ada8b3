---
description: 严格证据驱动诊断修复流程-决策树版，适合复杂技术问题
version: 06
---

# 🌳 严格证据驱动流程-决策树版

> 通过可视化决策树，确保AI严格遵循证据驱动原则，避免凭推理判断

## 📊 主流程决策树

```mermaid
graph TD
    Start[接收问题描述] --> Phase0[阶段0: 项目全景认知]
    
    %% 阶段0
    Phase0 --> P0_Check{环境认知完整?}
    P0_Check -->|否| P0_Actions[并行执行:<br/>1.项目识别<br/>2.技术栈确认<br/>3.WSL环境确认<br/>4.方法17:规范提取<br/>5.方法3:服务验证]
    P0_Actions --> P0_Check
    P0_Check -->|是| Phase1[阶段1: 强制证据收集]
    
    %% 阶段1 - 防偷懒核心
    Phase1 --> P1_Parallel[并行收集证据]
    P1_Parallel --> P1_M6[方法6: 日志收集]
    P1_Parallel --> P1_M8[方法8: 环境检查]
    P1_M6 & P1_M8 --> P1_M7[方法7: 分层症状确认]
    
    P1_M7 --> P1_Force[🚨强制检查点🚨<br/>方法19: 证据驱动分析]
    P1_Force --> P1_Check{证据充足?}
    P1_Check -->|否| P1_Retry[重新收集更多证据]
    P1_Retry --> P1_Parallel
    P1_Check -->|是| Phase2[阶段2: 逐层隔离验证]
    
    %% 阶段2
    Phase2 --> P2_Parallel[并行验证]
    P2_Parallel --> P2_Service[方法3: 服务验证<br/>+<br/>方法2: API验证]
    P2_Parallel --> P2_Path[方法9: 路径确认<br/>+<br/>方法10: 数据追踪]
    P2_Service & P2_Path --> P2_Check{执行状态明确?}
    P2_Check -->|否| P2_Deep[深入特定层分析]
    P2_Deep --> P2_Parallel
    P2_Check -->|是| Phase3[阶段3: 根因假设循环]
    
    %% 阶段3 - 防推理核心
    Phase3 --> P3_Isolate[方法11: 逐层隔离定位]
    P3_Isolate --> P3_Logic[方法13: 代码逻辑验证]
    P3_Logic --> P3_Single[方法12: 单一根因确定]
    
    P3_Single --> P3_Force[🚨强制验证循环🚨<br/>方法20: 假设验证]
    P3_Force --> P3_Verify{假设验证通过?}
    P3_Verify -->|否| P3_NewHypo[提出新假设]
    P3_NewHypo --> P3_Force
    P3_Verify -->|是| Phase4[阶段4: 修复实施]
    
    %% 阶段4
    Phase4 --> P4_Design[并行准备:<br/>方法17: 规范提取<br/>+<br/>修复方案设计]
    P4_Design --> P4_Implement[实施修复]
    P4_Implement --> P4_Verify[立即验证:<br/>方法4: 代码生效<br/>方法1: 执行验证<br/>方法2: API验证]
    
    P4_Verify --> P4_Check{修复成功?}
    P4_Check -->|技术失败| P4_Retry[调整实现重试]
    P4_Retry --> P4_Implement
    P4_Check -->|逻辑失败| P3_M21_1[方法21: 失败追溯]
    P3_M21_1 --> Phase3
    P4_Check -->|根本失败| P1_M21[方法21: 失败追溯]
    P1_M21 --> Phase1
    P4_Check -->|成功| Phase5[阶段5: 完整性确认]
    
    %% 阶段5
    Phase5 --> P5_Effect[方法14: 修复效果验证]
    P5_Effect --> P5_Complete[并行测试:<br/>方法15: 功能完整性<br/>方法16: 根本解决确认]
    P5_Complete --> P5_Compliance[方法18: 合规性验证]
    
    P5_Compliance --> P5_Check{全部通过?}
    P5_Check -->|否| P5_M21[方法21: 失败追溯]
    P5_M21 --> Phase1
    P5_Check -->|是| Success[✅ 修复完成]
```

## 🔍 阶段详细决策树

### 阶段0：项目全景认知决策树

```mermaid
graph TD
    P0_Start[开始认知建立] --> P0_Q1{项目类型已知?}
    
    P0_Q1 -->|否| P0_Identify[识别项目结构]
    P0_Q1 -->|是| P0_Q2{技术栈确认?}
    
    P0_Identify --> P0_Q2
    P0_Q2 -->|否| P0_Tech[分析技术栈:<br/>- 语言/框架<br/>- 数据库类型<br/>- API架构]
    P0_Q2 -->|是| P0_Q3{环境就绪?}
    
    P0_Tech --> P0_Q3
    P0_Q3 -->|否| P0_Env[环境检查:<br/>- WSL路径映射<br/>- 服务端口<br/>- 日志位置]
    P0_Q3 -->|是| P0_Q4{规范已载入?}
    
    P0_Env --> P0_Q4
    P0_Q4 -->|否| P0_M17[方法17: 提取CLAUDE.md规范]
    P0_Q4 -->|是| P0_Q5{服务可达?}
    
    P0_M17 --> P0_Q5
    P0_Q5 -->|否| P0_M3[方法3: 验证服务连通性]
    P0_Q5 -->|是| P0_Complete[认知建立完成]
```

### 阶段1：证据收集决策细化

```mermaid
graph TD
    Evidence[证据收集开始] --> E_Type{问题类型?}
    
    E_Type -->|错误/异常| E_M6[方法6: 优先查看错误日志]
    E_Type -->|功能问题| E_M7[方法7: 用户症状分层]
    E_Type -->|性能问题| E_M8[方法8: 系统资源检查]
    E_Type -->|未知| E_All[全部并行执行]
    
    E_M6 --> E_Found{找到关键证据?}
    E_M7 --> E_Found
    E_M8 --> E_Found
    E_All --> E_Found
    
    E_Found -->|否| E_Expand{扩大范围?}
    E_Expand -->|历史日志| E_History[查看更早的日志]
    E_Expand -->|相关服务| E_Related[检查关联服务]
    E_Expand -->|用户操作| E_Reproduce[请求重现步骤]
    
    E_Found -->|是| E_M19[🚨方法19: 证据分析🚨]
    E_History & E_Related & E_Reproduce --> E_M19
    
    E_M19 --> E_Valid{证据有效?}
    E_Valid -->|无效证据| E_Type
    E_Valid -->|有效证据| E_Next[进入下一阶段]
```

### 阶段3：假设验证循环决策树

```mermaid
graph TD
    Hypo[根因假设] --> H_Type{假设类型?}
    
    H_Type -->|代码逻辑| H_Code[设计代码验证方案]
    H_Type -->|配置问题| H_Config[设计配置验证方案]
    H_Type -->|环境问题| H_Env[设计环境验证方案]
    H_Type -->|数据问题| H_Data[设计数据验证方案]
    
    H_Code --> H_Execute[执行验证]
    H_Config --> H_Execute
    H_Env --> H_Execute
    H_Data --> H_Execute
    
    H_Execute --> H_Result{验证结果?}
    
    H_Result -->|确认| H_Confirmed[假设成立→修复]
    H_Result -->|否定| H_Why{为什么失败?}
    H_Result -->|部分| H_Refine[细化假设]
    
    H_Why -->|证据不足| H_MoreEvidence[返回收集更多证据]
    H_Why -->|假设错误| H_NewHypo[基于新证据提出新假设]
    H_Why -->|验证不当| H_Redesign[重新设计验证方案]
    
    H_Refine --> H_Execute
    H_NewHypo --> H_Type
    H_Redesign --> H_Execute
```

## 🚨 强制检查点说明

### 检查点1：证据驱动分析（方法19）
```yaml
位置: 阶段1末尾
强制要求:
  - 必须有实际日志/输出/错误信息
  - 禁止"可能是"、"应该是"、"通常是"
  - 禁止基于经验的推测
失败后果:
  - 立即停止分析
  - 返回重新收集证据
```

### 检查点2：假设验证循环（方法20）
```yaml
位置: 阶段3核心
强制要求:
  - 每个假设必须设计验证方案
  - 必须执行验证并等待结果
  - 禁止跳过验证直接修复
失败后果:
  - 修复大概率失败
  - 浪费时间和资源
```

## 💡 快速决策指南

### 症状→起始阶段映射
| 症状特征 | 直接跳转 | 原因 |
|---------|---------|------|
| 明确错误信息 | 阶段1→方法6 | 已有直接证据 |
| 服务完全不可用 | 阶段0→方法3 | 需先确认基础环境 |
| 间歇性问题 | 阶段1→全部方法 | 需要更多证据 |
| 性能退化 | 阶段2→方法11 | 需要逐层分析 |

### 失败类型→回退策略
```mermaid
graph LR
    F1[技术性失败] --> R1[阶段内重试]
    F2[逻辑性失败] --> R2[回退一个阶段]
    F3[根本性失败] --> R3[回到阶段1]
    F4[环境性失败] --> R4[回到阶段0]
```

## 📋 方法组件库

### 方法1：powershell.exe执行验证

 在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作。

  - 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
  - 流程：执行程序 → 读取日志 → 验证功能效果

### 方法2：API请求验证  

直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。

  - 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
  - 流程：调用API → 分析响应 → 验证业务逻辑

### 方法3：服务状态验证

检查服务进程的真实运行状态，避免假设服务可用性。

  - 命令格式：curl -s http://localhost:[端口]/[健康检查端点] || echo "服务不可用"
  - 流程：检查端口 → 验证响应 → 确认服务状态

### 方法4：代码生效验证

确认代码修改已实际应用并被执行，避免假设修改生效。

  - 静态验证：文件变更是否存在
  - 动态验证：修改的代码路径是否被执行到

### 方法5：功能重复性检查

开发前搜索现有代码库，避免重复实现已存在的功能模块。

  - 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数

### 方法6：日志优先症状收集

优先查看错误日志和异常堆栈，获取最直接证据。

  - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述

### 方法7：用户症状分层确认  

将用户描述分为表象→系统→根因三层，逐层收集症状。

  - 表象层：用户直接看到的问题
  - 系统层：服务和环境层面异常
  - 根因层：代码和技术层面原因

### 方法8：系统环境状态检查

并行检查服务状态、配置文件、依赖环境的实际状态。

  - 检查维度：服务可用性 | 配置完整性 | 依赖满足性
  - 并行策略：多维度同时检查，快速定位环境问题

### 方法9：执行路径反向确认

通过输出特征和日志模式反向定位真实执行的代码路径。

  - 反向策略：从输出结果追溯到具体代码位置

### 方法10：数据流完整追踪

追踪数据从输入到输出的完整生命周期，识别异常节点。

  - 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
  - 异常识别：数据丢失、格式错误、转换失败、存储异常

### 方法11：逐层隔离定位

按系统架构层次逐层隔离问题，避免跨层复杂化分析。

  - 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次

### 方法12：单一根因优先

优先查找单一明确的根因，避免多因素复杂化假设。

  - 判断原则：一个问题对应一个主要原因
  - 排除策略：先验证最直接最简单的可能性

### 方法13：代码逻辑直接验证

基于实际执行路径验证代码逻辑，不分析未执行代码。

  - 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支

### 方法14：修复效果实时验证

修复后立即通过相同方法验证问题是否解决。

  - 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入

### 方法15：功能完整性测试

验证修复不影响相关功能的正常工作。

  - 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程

### 方法16：根本解决确认

确认修复解决了原始根本问题，不是表面修复。

  - 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除

### 方法17：规范动态提取应用

根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。

  - 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
  - 应用原则：只应用相关规范，避免无关约束干扰

### 方法18：修复合规性验证

确保所有修复完全符合项目技术规范和架构原则。

  - 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则

### 方法19：证据驱动分析

所有分析必须基于实际收集的证据，禁止无证据推理。

  - 分析前提：先收集证据 → 再分析原因
  - 禁止行为：基于经验推测 | 基于可能性判断

### 方法20：假设验证循环

每个根因假设都必须通过实际验证才能确认。

  - 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定

### 方法21：失败原因追溯

修复失败后必须基于新证据重新分析，不重复原方案。

  - 追溯策略：识别失败点 → 收集新证据 → 调整分析方向

## 🎯 执行要点总结

1. **决策树优势**：每个节点都有明确的判断条件和分支
2. **强制检查**：🚨标记的节点不可跳过
3. **失败处理**：每个阶段都有明确的失败回退路径
4. **并行优化**：可并行的操作都已标注
5. **证据至上**：所有决策基于实际证据，不是推测

---

> 版本：06 | 基于05版本改进为决策树形式