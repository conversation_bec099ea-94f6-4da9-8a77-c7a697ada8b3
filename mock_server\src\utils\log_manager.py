#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器 - 支持运行时日志文件清空
"""

import os
import threading
from datetime import datetime
from typing import Optional

class LogManager:
    """全局日志管理器 - 支持运行时控制日志文件"""
    
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.log_file: Optional[object] = None
        self.uvicorn_log_path = log_file_path.replace("server.log", "uvicorn.log")
        self.lock = threading.Lock()
        self._ensure_log_directory()
        self._open_log_file()
    
    def _ensure_log_directory(self):
        """确保日志目录存在"""
        log_dir = os.path.dirname(self.log_file_path)
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    def _open_log_file(self):
        """打开日志文件"""
        try:
            self.log_file = open(self.log_file_path, 'a', encoding='utf-8', buffering=1)
        except Exception as e:
            print(f"❌ 无法打开日志文件 {self.log_file_path}: {e}")
            self.log_file = None
    
    def write_log(self, message: str, level: str = "INFO"):
        """写入日志"""
        with self.lock:
            if self.log_file:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-4]
                log_line = f"[{timestamp}] {level}: {message}\n"
                try:
                    self.log_file.write(log_line)
                    self.log_file.flush()
                except Exception as e:
                    print(f"❌ 写入日志失败: {e}")
    
    def clear_log(self) -> dict:
        """清空日志文件 - 关闭→清空→重开"""
        with self.lock:
            try:
                # 步骤1：关闭当前日志文件
                if self.log_file:
                    self.log_file.close()
                    self.log_file = None
                
                # 步骤2：清空文件内容
                with open(self.log_file_path, 'w', encoding='utf-8') as f:
                    f.write("")
                
                # 步骤3：重新打开日志文件
                self._open_log_file()
                
                # 🔧 修复死锁：直接写入而不调用write_log()避免重复获取锁
                if self.log_file:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-4]
                    log_line = f"[{timestamp}] SYSTEM: === LOG CLEARED BY ADMIN REQUEST ===\n"
                    self.log_file.write(log_line)
                    self.log_file.flush()
                
                return {
                    "success": True,
                    "message": "日志文件已成功清空",
                    "log_file": self.log_file_path,
                    "timestamp": datetime.now().isoformat()
                }
                
            except Exception as e:
                # 即使出错也要尝试重新打开日志文件
                if not self.log_file:
                    self._open_log_file()
                
                return {
                    "success": False,
                    "message": f"清空日志失败: {str(e)}",
                    "log_file": self.log_file_path
                }
    
    def clear_uvicorn_log(self) -> dict:
        """清空uvicorn日志文件 - 直接清空外部文件"""
        try:
            # 🎯 直接清空uvicorn.log文件（由uvicorn进程管理）
            with open(self.uvicorn_log_path, 'w', encoding='utf-8') as f:
                f.write("")
            
            # 记录清空操作到server.log
            self.write_log("=== UVICORN LOG CLEARED BY ADMIN REQUEST ===", "SYSTEM")
            
            return {
                "success": True,
                "message": "uvicorn日志文件已成功清空",
                "log_file": self.uvicorn_log_path,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"清空uvicorn日志失败: {str(e)}",
                "log_file": self.uvicorn_log_path
            }
    
    def clear_all_logs(self) -> dict:
        """清空所有日志文件"""
        results = []
        
        # 清空server.log
        server_result = self.clear_log()
        results.append(f"Server log: {server_result['message']}")
        
        # 清空uvicorn.log  
        uvicorn_result = self.clear_uvicorn_log()
        results.append(f"Uvicorn log: {uvicorn_result['message']}")
        
        all_success = server_result["success"] and uvicorn_result["success"]
        
        return {
            "success": all_success,
            "message": "; ".join(results),
            "timestamp": datetime.now().isoformat()
        }
    
    def get_log_status(self) -> dict:
        """获取日志文件状态"""
        try:
            if os.path.exists(self.log_file_path):
                file_size = os.path.getsize(self.log_file_path)
                
                # 读取行数
                with open(self.log_file_path, 'r', encoding='utf-8') as f:
                    line_count = sum(1 for line in f)
                
                return {
                    "exists": True,
                    "size": file_size,
                    "lines": line_count,
                    "is_open": self.log_file is not None
                }
            else:
                return {
                    "exists": False,
                    "size": 0,
                    "lines": 0,
                    "is_open": False
                }
        except Exception as e:
            return {
                "exists": False,
                "error": str(e),
                "is_open": self.log_file is not None
            }
    
    def close(self):
        """关闭日志管理器"""
        with self.lock:
            if self.log_file:
                self.log_file.close()
                self.log_file = None

# 全局日志管理器实例
global_log_manager = LogManager("D:/01-shuimu_01/logs/server.log")

def log_info(message: str):
    """便捷的info日志函数"""
    global_log_manager.write_log(message, "INFO")

def log_error(message: str):
    """便捷的error日志函数"""
    global_log_manager.write_log(message, "ERROR")

def log_debug(message: str):
    """便捷的debug日志函数"""
    global_log_manager.write_log(message, "DEBUG")