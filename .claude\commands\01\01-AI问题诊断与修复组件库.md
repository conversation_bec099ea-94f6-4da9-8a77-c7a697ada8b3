## 方法组件库

### 方法1：powershell.exe执行验证

 在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作。

  - 命令格式：powershell.exe -Command "cd '$(wslpath -w WSL路径)'; 执行程序" + Read 日志文件
  - 流程：执行程序 → 读取日志 → 验证功能效果

### 方法2：API请求验证  

直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。

  - 命令格式：curl -X [METHOD] http://localhost:8000/api/[端点] [-d '数据'] [-H '头部']
  - 流程：调用API → 分析响应 → 验证业务逻辑

### 方法3：服务状态验证

检查服务进程的真实运行状态，避免假设服务可用性。

  - 命令格式：curl -s http://localhost:[端口]/[健康检查端点] || echo "服务不可用"
  - 流程：检查端口 → 验证响应 → 确认服务状态

### 方法4：代码生效验证

确认代码修改已实际应用并被执行，避免假设修改生效。

  - 静态验证：文件变更是否存在
  - 动态验证：修改的代码路径是否被执行到

### 方法5：功能重复性检查

开发前搜索现有代码库，避免重复实现已存在的功能模块。

  - 搜索策略：功能名称 → 业务逻辑 → 相似实现 → 工具函数

### 方法6：日志优先症状收集

优先查看错误日志和异常堆栈，获取最直接证据。

  - 收集优先级：错误日志 → 异常堆栈 → 警告信息 → 用户描述

### 方法7：用户症状分层确认  

将用户描述分为表象→系统→根因三层，逐层收集症状。

  - 表象层：用户直接看到的问题
  - 系统层：服务和环境层面异常
  - 根因层：代码和技术层面原因

### 方法8：系统环境状态检查

并行检查服务状态、配置文件、依赖环境的实际状态。

  - 检查维度：服务可用性 | 配置完整性 | 依赖满足性
  - 并行策略：多维度同时检查，快速定位环境问题

### 方法9：执行路径反向确认

通过输出特征和日志模式反向定位真实执行的代码路径。

  - 反向策略：从输出结果追溯到具体代码位置

### 方法10：数据流完整追踪

追踪数据从输入到输出的完整生命周期，识别异常节点。

  - 追踪路径：输入源 → 传输层 → 处理层 → 存储层 → 输出端
  - 异常识别：数据丢失、格式错误、转换失败、存储异常

### 方法11：逐层隔离定位

按系统架构层次逐层隔离问题，避免跨层复杂化分析。

  - 隔离策略：识别系统分层 → 单层独立验证 → 确定问题层次

### 方法12：单一根因优先

优先查找单一明确的根因，避免多因素复杂化假设。

  - 判断原则：一个问题对应一个主要原因
  - 排除策略：先验证最直接最简单的可能性

### 方法13：代码逻辑直接验证

基于实际执行路径验证代码逻辑，不分析未执行代码。

  - 聚焦原则：只分析已确认执行的代码，忽略死代码和未触发分支

### 方法14：修复效果实时验证

修复后立即通过相同方法验证问题是否解决。

  - 验证原则：用发现问题的原方法重新验证 + 确认无新问题引入

### 方法15：功能完整性测试

验证修复不影响相关功能的正常工作。

  - 测试范围：直接相关功能 → 间接依赖功能 → 核心业务流程

### 方法16：根本解决确认

确认修复解决了原始根本问题，不是表面修复。

  - 判断标准：在相同触发条件下问题不再出现 + 根本原因被消除

### 方法17：规范动态提取应用

根据问题和修改代码类型，从CLAUDE.md提取相关规范约束。

  - 提取策略：问题领域匹配 → 代码类型匹配 → 架构层次匹配
  - 应用原则：只应用相关规范，避免无关约束干扰

### 方法18：修复合规性验证

确保所有修复完全符合项目技术规范和架构原则。

  - 验证维度：代码风格 | 架构约束 | 命名规范 | 业务规则

### 方法19：证据驱动分析

所有分析必须基于实际收集的证据，禁止无证据推理。

  - 分析前提：先收集证据 → 再分析原因
  - 禁止行为：基于经验推测 | 基于可能性判断

### 方法20：假设验证循环

每个根因假设都必须通过实际验证才能确认。

  - 验证要求：提出假设 → 设计验证 → 执行验证 → 确认/否定

### 方法21：失败原因追溯

修复失败后必须基于新证据重新分析，不重复原方案。

  - 追溯策略：识别失败点 → 收集新证据 → 调整分析方向
