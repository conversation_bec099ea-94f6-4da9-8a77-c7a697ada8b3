#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户详情标签页 - 乐观更新
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QCheckBox, QGroupBox,
                            QGridLayout, QFrame, QMessageBox)
from PyQt6.QtCore import Qt, QTimer
from PyQt6.QtGui import QFont
from typing import Dict, Any
import threading

class UserDetailTab(QWidget):
    """用户详情标签页 - 支持乐观更新"""
    
    def __init__(self, parent, user_data: Dict[str, Any], api_client):
        super().__init__(parent)
        self.user_data = user_data.copy()  # 本地数据副本
        self.api_client = api_client
        self.is_editing = False
        
        self.setup_ui()
        self.populate_data()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标题栏
        title_layout = QHBoxLayout()
        main_layout.addLayout(title_layout)
        
        self.title_label = QLabel("用户详情")
        title_font = QFont("Arial", 14)
        title_font.setBold(True)
        self.title_label.setFont(title_font)
        title_layout.addWidget(self.title_label)
        
        title_layout.addStretch()
        
        # 操作按钮
        self.edit_button = QPushButton("编辑")
        self.edit_button.clicked.connect(self.toggle_edit_mode)
        title_layout.addWidget(self.edit_button)
        
        self.save_button = QPushButton("保存")
        self.save_button.clicked.connect(self.save_changes)
        self.save_button.setEnabled(False)
        title_layout.addWidget(self.save_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.cancel_edit)
        self.cancel_button.setEnabled(False)
        title_layout.addWidget(self.cancel_button)
        
        # 基本信息
        basic_group = QGroupBox("基本信息")
        main_layout.addWidget(basic_group)
        basic_layout = QGridLayout(basic_group)
        
        # 用户名
        basic_layout.addWidget(QLabel("用户名:"), 0, 0)
        self.username_entry = QLineEdit()
        self.username_entry.setEnabled(False)
        basic_layout.addWidget(self.username_entry, 0, 1)
        
        # 邮箱
        basic_layout.addWidget(QLabel("邮箱:"), 1, 0)
        self.email_entry = QLineEdit()
        self.email_entry.setEnabled(False)
        basic_layout.addWidget(self.email_entry, 1, 1)
        
        # 显示名
        basic_layout.addWidget(QLabel("显示名:"), 2, 0)
        self.display_name_entry = QLineEdit()
        self.display_name_entry.setEnabled(False)
        basic_layout.addWidget(self.display_name_entry, 2, 1)
        
        # 手机号
        basic_layout.addWidget(QLabel("手机号:"), 3, 0)
        self.phone_entry = QLineEdit()
        self.phone_entry.setEnabled(False)
        basic_layout.addWidget(self.phone_entry, 3, 1)
        
        # 状态信息
        status_group = QGroupBox("状态信息")
        main_layout.addWidget(status_group)
        status_layout = QGridLayout(status_group)
        
        # 账户状态
        status_layout.addWidget(QLabel("账户状态:"), 0, 0)
        self.is_active_check = QCheckBox("激活")
        self.is_active_check.setEnabled(False)
        status_layout.addWidget(self.is_active_check, 0, 1)
        
        # 管理员权限
        status_layout.addWidget(QLabel("管理员:"), 1, 0)
        self.is_admin_check = QCheckBox("是")
        self.is_admin_check.setEnabled(False)
        status_layout.addWidget(self.is_admin_check, 1, 1)
        
        # 时间信息
        time_group = QGroupBox("时间信息")
        main_layout.addWidget(time_group)
        time_layout = QGridLayout(time_group)
        
        time_layout.addWidget(QLabel("创建时间:"), 0, 0)
        self.created_at_label = QLabel("")
        time_layout.addWidget(self.created_at_label, 0, 1)
        
        time_layout.addWidget(QLabel("最后登录:"), 1, 0)
        self.last_login_label = QLabel("")
        time_layout.addWidget(self.last_login_label, 1, 1)
        
        # 配置列权重
        basic_layout.setColumnStretch(1, 1)
        status_layout.setColumnStretch(1, 1)
        time_layout.setColumnStretch(1, 1)
        
        # 弹性空间
        main_layout.addStretch()
        
        # 状态栏
        self.status_bar = QLabel("就绪")
        self.status_bar.setFrameStyle(QFrame.Shape.StyledPanel)
        main_layout.addWidget(self.status_bar)
    
    def populate_data(self):
        """填充用户数据"""
        self.username_entry.setText(self.user_data.get('username', ''))
        self.email_entry.setText(self.user_data.get('email', ''))
        self.display_name_entry.setText(self.user_data.get('display_name', ''))
        self.phone_entry.setText(self.user_data.get('phone', ''))
        self.is_active_check.setChecked(self.user_data.get('is_active', False))
        self.is_admin_check.setChecked(self.user_data.get('is_admin', False))
        
        # 格式化时间
        created_at = self.format_datetime(self.user_data.get('created_at'))
        last_login = self.format_datetime(self.user_data.get('last_login_at'))
        
        self.created_at_label.setText(created_at)
        self.last_login_label.setText(last_login)
        
        # 更新标题
        username = self.user_data.get('username', '未知用户')
        self.title_label.setText(f"用户详情 - {username}")
    
    def format_datetime(self, dt_str: str) -> str:
        """格式化日期时间"""
        if not dt_str:
            return "从未"
        try:
            from datetime import datetime
            if 'T' in dt_str:
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%Y-%m-%d %H:%M:%S")
            return dt_str
        except:
            return dt_str
    
    def toggle_edit_mode(self):
        """切换编辑模式"""
        if self.is_editing:
            return
            
        self.is_editing = True
        
        # 启用编辑控件
        self.username_entry.setEnabled(True)
        self.email_entry.setEnabled(True)
        self.display_name_entry.setEnabled(True)
        self.phone_entry.setEnabled(True)
        self.is_active_check.setEnabled(True)
        self.is_admin_check.setEnabled(True)
        
        # 更新按钮状态
        self.edit_button.setEnabled(False)
        self.save_button.setEnabled(True)
        self.cancel_button.setEnabled(True)
        
        self.status_bar.setText("编辑模式")
    
    def cancel_edit(self):
        """取消编辑"""
        # 恢复原始数据
        self.populate_data()
        self.exit_edit_mode()
    
    def exit_edit_mode(self):
        """退出编辑模式"""
        self.is_editing = False
        
        # 禁用编辑控件
        self.username_entry.setEnabled(False)
        self.email_entry.setEnabled(False)
        self.display_name_entry.setEnabled(False)
        self.phone_entry.setEnabled(False)
        self.is_active_check.setEnabled(False)
        self.is_admin_check.setEnabled(False)
        
        # 更新按钮状态
        self.edit_button.setEnabled(True)
        self.save_button.setEnabled(False)
        self.cancel_button.setEnabled(False)
        
        self.status_bar.setText("就绪")
    
    def save_changes(self):
        """保存更改 - 乐观更新"""
        # 收集更改数据
        changes = {
            'username': self.username_entry.text().strip(),
            'email': self.email_entry.text().strip(),
            'display_name': self.display_name_entry.text().strip(),
            'phone': self.phone_entry.text().strip(),
            'is_active': self.is_active_check.isChecked(),
            'is_admin': self.is_admin_check.isChecked()
        }
        
        # 验证数据
        if not changes['username']:
            QMessageBox.critical(self, "错误", "用户名不能为空")
            return
        
        if not changes['email']:
            QMessageBox.critical(self, "错误", "邮箱不能为空")
            return
        
        # 乐观更新：立即更新本地数据和UI
        old_data = self.user_data.copy()
        self.user_data.update(changes)
        self.exit_edit_mode()
        self.status_bar.setText("保存中...")
        
        # 后台同步到服务器
        def sync_to_server():
            try:
                user_id = self.user_data.get('user_id') or self.user_data.get('id')
                if not user_id:
                    raise Exception("用户ID缺失")
                
                # 调用API更新用户
                from api.client import AdminUserAPIClient, APIClient
                api_client = APIClient()
                user_api = AdminUserAPIClient(api_client)
                
                response = user_api.update_user(user_id, changes)
                
                if response.get('success'):
                    # 同步成功
                    QTimer.singleShot(0, lambda: self.status_bar.setText("保存成功"))
                else:
                    # 同步失败，回滚数据
                    error_msg = response.get('message', '保存失败')
                    QTimer.singleShot(0, lambda: self.handle_save_error(old_data, error_msg))
                    
            except Exception as e:
                # 同步异常，回滚数据
                error_msg = f"保存失败: {str(e)}"
                QTimer.singleShot(0, lambda: self.handle_save_error(old_data, error_msg))
        
        threading.Thread(target=sync_to_server, daemon=True).start()
    
    def handle_save_error(self, old_data: Dict[str, Any], error_msg: str):
        """处理保存错误 - 回滚数据"""
        # 回滚到旧数据
        self.user_data = old_data
        self.populate_data()
        
        # 显示错误信息
        self.status_bar.setText(f"保存失败: {error_msg}")
        QMessageBox.critical(self, "保存失败", error_msg)
    
    def update_user_data(self, new_data: Dict[str, Any]):
        """外部更新用户数据"""
        self.user_data.update(new_data)
        if not self.is_editing:
            self.populate_data()