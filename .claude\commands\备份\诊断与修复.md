---
description: 基于 `$ARGUMENTS` 按照问题诊断与修复流程执行
---

# 🔍 问题诊断与修复流程

## 📋 流程概览

```
阶段1: 强制信息收集与真实检查
阶段2: 系统性问题假设建立
阶段3: 逐一验证与根因定位  
阶段4: 真实修复与实施
阶段5: 真实验证与循环优化
```

**核心原则**：

- ✅ 100%真实检查：禁止凭想象猜测，必须基于真实验证
- ✅ 100%根因定位：必须找到根本原因，不是表面现象
- ✅ 100%完整修复：必须完全解决问题，不是临时绕过
- ✅ 100%实际验证：必须通过真实测试验证，不是理论推理

## 🛑 强制执行机制

### 阶段门禁条件（必须满足才能切换阶段）
- **阶段1→阶段2**：完成多维度信息搜索 + 识别核心症状 + TodoWrite标记completed
- **阶段2→阶段3**：建立优先级假设 + 明确验证方法 + TodoWrite记录假设总数
- **阶段3→阶段4**：满足循环退出条件 + 找到确定根因 + TodoWrite完成验证记录
- **阶段4→阶段5**：完成根本性修复 + 无破坏性影响 + TodoWrite记录修复内容
- **阶段5完成**：验证通过率100% + 满足循环退出条件 + TodoWrite标记全部completed

### 违规定义和后果
#### 🚨 严重违规（必须重新执行）
- **跳过循环机制**：阶段3、5未执行循环→返回重新执行完整阶段
- **凭想象猜测**：未提供真实验证证据→必须补充验证步骤
- **提前退出阶段**：未满足门禁条件→必须补充完成所有必需步骤
- **简化实现逃避**：临时方案、伪代码→必须提供完整真实修复

#### ⚠️ 轻微违规（警告并纠正）
- **TodoWrite更新不及时**：立即补充更新
- **进度记录不准确**：重新统计并记录
- **工具使用不充分**：补充必要的验证工具调用

### 强制自检清单（每阶段结束前必须完成）
在进入下一阶段前，AI必须回答以下问题（全部"是"才能继续）：
1. ✅ 我是否完成了本阶段的所有核心目标？
2. ✅ 我是否有任何猜测或未验证的假设？
3. ✅ 我是否满足了所有阶段切换条件？
4. ✅ 我的TodoWrite状态是否准确反映了执行情况？
5. ✅ 我是否执行了必需的循环机制（如适用于阶段3、5）？

## ⚠️ 核心禁止行为

**绝对禁止以下行为**：

- ❌ **凭想象猜测问题** - 必须基于真实检查，不允许基于"经验"猜测
- ❌ **简化/伪代码逃避** - 禁止写简化版、临时方案、伪代码来"通过"
- ❌ **跳过验证步骤** - 每个假设都必须真实验证，不允许跳过
- ❌ **假验证** - 禁止理论推理代替真实测试
- ❌ **依赖安装误判** - WSL环境下不要尝试安装Windows依赖

## 🏗️ 环境理解

### 运行环境架构

```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 文本处理和搜索
    └── ❌ 不能直接运行Python程序
```

### WSL能力边界

**可以做的**：

- 文件读取、编辑、搜索、修改
- 代码分析和静态检查（wsl）
- 服务验证（通过FastAPI）
- 功能验证（端到端）
- 配置文件修改和验证

**不能做的**：

- 直接运行Python程序和PyQt6应用
- 安装Python依赖包
- 直接测试GUI界面
- 系统级操作和服务管理

### 依赖检测规则

**避免误判原则**：

- ❌ 不要尝试在WSL中安装Windows环境的Python依赖
- ❌ 不要执行 `pip install` 等安装命令
- ✅ 假设Windows环境中依赖已正确安装
- ✅ 通过FastAPI端点间接验证功能可用性

---

## **阶段1: 强制信息收集与真实检查**

⚠️ **强制要求：本阶段必须完成多维度信息搜索和真实检查**
📋 **成功标准：识别核心症状 + 制定搜索策略 + 完成系统状态检查**
🚪 **门禁条件：必须满足所有检查要求才能进入阶段2**

### **步骤1.1: 问题信息解析**

- **1.1.1 问题描述分析**
  - 提取问题的核心症状和表现
  - 识别涉及的功能模块和组件
  - 分析问题的触发条件和环境
  - 确定问题的影响范围和严重程度

- **1.1.2 搜索策略制定**
  - **多维度搜索策略**：
    - 文件名模式搜索（Glob工具）
    - 内容关键词搜索（Grep工具）
    - 跨文件引用搜索（Task工具）
    - 配置文件搜索
    - 日志文件搜索
  - **搜索关键词库建立**：
    - 错误信息相关的所有关键词
    - 功能模块相关的所有关键词
    - 可能的变形和相似词汇

### **步骤1.2: 强制真实检查（禁止猜测）**

- **1.2.1 多工具组合搜索**

  ```
  必须使用的搜索组合：
  1. Glob: 按文件名模式搜索所有相关文件类型
  2. Grep: 按内容关键词搜索所有匹配内容
  3. Task: 复杂搜索和跨文件引用分析
  4. Read: 逐文件详细检查重要文件
  ```

- **1.2.2 搜索完整性验证**

  - **交叉验证**：用不同方法搜索相同内容，确保结果一致
  - **边界扩展**：从已发现文件出发，检查其引用和被引用
  - **遗漏检测**：检查是否存在预期但未找到的文件类型
  - **搜索报告**：详细记录所有搜索结果和发现的文件清单

- **1.2.3 系统状态检查**

  - **服务状态检测与智能启动**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，跳过启动
       - 如果返回非200或连接失败 → 需要启动服务
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 验证连接：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    6. 连接失败处理：
       - 如果在WSL环境中启动失败 → 提醒用户在Windows环境中启动服务
       - 记录具体错误，不允许假设成功
       - 暂停诊断流程，等待用户启动服务
    ```

  - **基础环境检查**：
    ```
    1. 检查关键文件是否存在
    2. 验证配置文件的正确性
    3. 检查日志文件的最新状态
    4. 确认数据库连接可用性
    ```

---

## **阶段2: 系统性问题假设建立**

⚠️ **强制要求：本阶段必须建立多层次假设并按优先级排序**
📋 **成功标准：至少3个不同层次的假设 + 优先级排序 + 验证方法制定**
🚪 **门禁条件：每个假设都有明确验证方法才能进入阶段3**

### **步骤2.1: 多层次问题假设建立**

- **2.1.1 基础层假设**
  - **语法错误**：代码语法、拼写错误、导入错误
  - **配置错误**：配置文件格式、路径错误、参数错误
  - **权限错误**：文件权限、数据库权限、网络权限

- **2.1.2 逻辑层假设**
  - **业务逻辑错误**：算法实现、条件判断、数据处理逻辑
  - **状态管理错误**：对象状态、数据状态、会话状态
  - **时序错误**：操作顺序、异步处理、事件时序

- **2.1.3 系统层假设**
  - **架构设计问题**：模块耦合、接口设计、数据流设计
  - **性能问题**：资源消耗、响应时间、并发处理
  - **兼容性问题**：版本兼容、环境差异、平台差异

- **2.1.4 集成层假设**
  - **模块集成问题**：接口调用、数据传递、错误传播
  - **外部集成问题**：数据库连接、API调用、文件系统访问
  - **环境集成问题**：开发环境、测试环境、生产环境差异

### **步骤2.2: 假设优先级排序**

- **2.2.1 影响程度评估**
  - **高影响**：导致系统完全不可用的问题
  - **中影响**：导致功能异常但系统可用的问题
  - **低影响**：导致体验下降但功能正常的问题

- **2.2.2 可能性评估**
  - **高可能性**：基于检查结果有明确证据支持的假设
  - **中可能性**：基于经验和逻辑推理的合理假设
  - **低可能性**：理论上可能但证据不足的假设

- **2.2.3 验证成本评估**
  - **低成本**：简单检查或测试即可验证的假设
  - **中成本**：需要一定准备或配置才能验证的假设
  - **高成本**：需要大量时间或资源才能验证的假设

---

## **阶段3: 逐一验证与根因定位**

⚠️ **强制要求：本阶段必须执行循环机制验证所有假设**
📋 **成功标准：假设验证完成率100% + 找到确定根因 + 有充分证据**
🚪 **门禁条件：满足循环退出条件才能进入阶段4**

### **步骤3.1: 系统性假设验证（禁止跳过）**

- **3.1.1 通用问题检测方法**：

  - **使用Grep工具搜索问题模式**：
    ```
    根据具体诊断需求，定义相应的搜索模式，例如：
    1. 搜索错误信息：grep -r "具体错误信息" --include="*.py" 项目目录/
    2. 搜索异常模式：grep -r "Exception\|Error\|Failed" --include="*.py" 项目目录/
    3. 搜索关键函数：grep -r "问题相关函数名" --include="*.py" 项目目录/
    4. 搜索配置问题：grep -r "配置项名称" --include="*.json" --include="*.yaml" 项目目录/
    ```

  - **量化验证标准**：
    ```
    1. 假设计数统计：记录每次验证的假设数量
    2. 循环退出条件：
       - 所有假设都已验证完成，或
       - 找到确定的根因，或
       - 连续2轮验证结果相同，或
       - 达到最大循环次数3轮
    3. 验证进度追踪：对比前后轮次的验证进度
    4. 问题发现率：计算确认问题数 / 总假设数 × 100%
    ```

- **3.1.2 WSL环境适配验证方法**：

  - **静态验证**：通过代码分析和文件检查验证问题
  - **动态验证**：通过FastAPI端点测试验证功能状态
  - **间接验证**：通过相关功能测试推断问题原因
  - **日志验证**：通过日志分析验证问题发生的时间和条件

### **步骤3.2: 详细验证执行**

- **3.2.1 代码层验证**：

  ```
  1. 语法检查：
     - Read: 详细检查相关代码文件
     - 分析导入语句的正确性
     - 检查函数调用的参数匹配
     - 验证变量名和类型的一致性
  
  2. 逻辑验证：
     - 分析业务逻辑的正确性
     - 检查条件判断的完整性
     - 验证异常处理的充分性
     - 确认数据流向的正确性
  ```

- **3.2.2 配置层验证**：

  ```
  1. 配置文件检查：
     - 验证JSON/YAML格式的正确性
     - 检查配置项的完整性
     - 确认配置值的合理性
     - 验证路径和URL的有效性
  
  2. 环境变量验证：
     - 检查必需环境变量的设置
     - 验证环境变量值的正确性
     - 确认环境变量的优先级
  ```

- **3.2.3 数据层验证**：

  ```
  1. 项目结构发现：
     - find . -name "*.py" | grep -E "(main|app|server|router|api)" | head -5
     - find . -name "*.json" -o -name "*.yaml" -o -name "*.toml" | head -10
     - grep -r "router\|@app\|@api" --include="*.py" . | head -10
  
  2. API端点自动发现：
     - curl -s http://localhost:8000/openapi.json | jq '.paths' | head -20
     - 自动选择测试端点（优先级：GET > 简单参数 > 常见功能）
     - 避免硬编码特定功能（如用户管理）
  
  3. 数据库连接验证：
     - 基于发现的API端点进行连接测试
     - 验证响应状态码和数据格式
     - 检查数据库表结构的正确性
     - 确认数据完整性和约束
  
  4. 数据查询验证：
     - 测试关键查询语句的执行
     - 验证查询结果的正确性
     - 检查数据关联的完整性
     - 确认索引的有效性
  ```

- **3.2.4 功能层验证**：

  ```
  1. 渐进式API功能验证：
     - Level 1: 基础连通性验证（docs端点、openapi.json）
     - Level 2: 核心功能验证（基于发现的API端点）
     - Level 3: 业务流程验证（基于项目特定逻辑）
  
  2. 通用API测试策略：
     - 从OpenAPI文档自动选择测试端点
     - 优先测试GET类型的简单查询端点
     - 测试常见的健康检查和状态查询端点
     - 验证请求参数的处理和响应数据的正确性
  
  3. 业务流程验证：
     - 根据项目实际功能设计验证流程
     - 验证数据传递的正确性
     - 检查状态变化的合理性
     - 确认异常情况的处理
  ```

### **步骤3.3: 强制循环控制机制**

⚠️ **强制要求：本阶段必须执行循环机制，不允许跳过**
📋 **循环追踪：使用TodoWrite工具记录每轮循环状态**
🔄 **退出条件：必须满足明确退出条件才能结束循环**

```
🛑 循环控制参数（强制执行）：
- 最小循环轮次：2轮（不允许第1轮退出）
- 最大循环轮次：3轮（防止无限循环）
- 进度量化：假设验证完成百分比
- 状态记录：每轮开始/结束都必须更新TodoWrite

🔄 循环执行流程：
初始化：
1. 使用TodoWrite记录"阶段3第X轮循环"（X=1,2,3）
2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
3. 记录初始假设数量：统计需要验证的假设总数

每轮循环必须记录：
- 当前轮次：X/3
- 验证进度：已验证X个假设，剩余Y个
- 新发现问题：X个
- 退出条件检查：满足/不满足

循环执行：
3.1(假设验证) → 3.2(详细验证) → 3.3(结果分析) → 自动判断：

✅ 强制退出条件（满足任一即退出）：
- 假设验证完成率100% + 找到确定根因并有充分证据
- 达到最大循环轮次3轮
- 连续2轮验证结果完全相同

❌ 禁止提前退出：
- 第1轮不允许退出（必须至少执行2轮）
- 未找到根因不允许退出
- 验证进度<50%不允许退出

🔧 强制执行要求：
- 每轮开始必须更新TodoWrite状态为in_progress
- 每轮结束必须记录验证进度变化
- 不允许停顿等待用户指令，必须自动进入下一轮
- 违反循环机制必须重新执行完整阶段3
```

---

## **阶段4: 真实修复与实施**

⚠️ **强制要求：本阶段必须进行根本性修复，禁止临时方案**
📋 **成功标准：完全解决根本问题 + 无破坏性影响 + 详细修复记录**
🚪 **门禁条件：修复完整性100%才能进入阶段5**

### **步骤4.1: 修复方案设计（禁止简化）**

- **4.1.1 根本性修复原则**
  - **禁止临时方案**：不允许临时绕过、简化处理、伪代码实现
  - **禁止症状修复**：必须修复根本原因，不是掩盖症状
  - **禁止破坏性修复**：修复不能破坏其他功能或引入新问题

- **4.1.2 修复方案评估**
  - **完整性评估**：修复方案是否完全解决根本问题
  - **影响评估**：修复对其他功能和模块的影响
  - **风险评估**：修复可能引入的新风险和问题
  - **可维护性评估**：修复后的代码可维护性和扩展性

### **步骤4.2: 修复实施**

- **4.2.1 修复顺序规划**
  - 按依赖关系和影响范围规划修复顺序
  - 优先修复基础依赖和核心组件
  - 确保修复过程的原子性和一致性

- **4.2.2 修复执行**
  - **代码修复**：修改相关源代码，确保逻辑正确性
  - **配置修复**：更新配置文件、环境变量、数据库配置
  - **数据修复**：修复数据问题、更新数据结构、恢复数据一致性
  - **文档修复**：更新相关文档、注释、API文档

- **4.2.3 修复记录**
  - 详细记录修复的内容、原因、影响范围
  - 记录修复前后的状态对比
  - 建立修复追踪和回滚方案

---

## **阶段5: 真实验证与循环优化**

⚠️ **强制要求：本阶段必须执行循环机制验证修复效果**
📋 **成功标准：验证通过率100% + 问题不再复现 + 无副作用**
🚪 **门禁条件：满足循环退出条件才能完成整个流程**

### **步骤5.1: 修复效果验证**

- **5.1.1 系统功能验证**

  - **服务端功能验证**：
    ```
    1. 服务端测试：
       - API文档测试：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
       - API结构测试：curl -s http://localhost:8000/openapi.json
       - 业务逻辑测试：基于openapi.json发现的端点进行测试
       - 数据处理测试：验证数据CRUD操作
    ```

  - **管理端功能验证**：
    ```
    1. 管理端测试：
       - 界面功能测试：验证界面响应和交互
       - 数据展示测试：验证数据正确展示
       - 功能完整性测试：验证核心功能可用
    ```

  - **模块间功能验证**：
    ```
    1. 模块间测试：
       - 模块通信测试：验证模块间数据传递
       - 接口调用测试：验证API调用正确性
       - 依赖关系测试：验证模块依赖正确
    ```

  - **端到端功能验证**：
    ```
    1. 端到端测试：
       - 业务流程测试：验证完整业务场景
       - 系统集成测试：验证系统整体功能
       - 性能基准测试：验证系统性能满足要求
    ```

### **步骤5.2: WSL环境适配验证方法**

- **多维度验证体系**：

  ```
  1. 文件系统层验证：
     - find . -type f -name "*.log" | tail -5   # 发现日志文件
     - find . -name "config*" -o -name "settings*" | head -10  # 发现配置文件
     - ls -la [关键文件路径]  # 验证文件权限
  
  2. 服务层验证：
     - curl -s http://localhost:8000/docs  # 健康检查
     - curl -s http://localhost:8000/openapi.json | jq '.info'  # 服务信息
     - 基于发现的API端点进行功能验证
  
  3. 数据层验证：
     - 通过自动发现的API端点查询数据库状态
     - 验证数据一致性和完整性
     - 检查约束和索引的正确性
  
  4. 配置层验证：
     - 检查发现的配置文件正确性
     - 验证环境变量的设置
     - 确认路径和权限的正确性
  ```

- **渐进式验证策略**：

  ```
  Level 1 - 基础连通性验证：
  1. curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
  2. curl -s http://localhost:8000/openapi.json | jq '.paths | keys' | head -10
  3. 基础文件系统检查
  
  Level 2 - 核心功能验证：
  1. 基于OpenAPI文档自动选择测试端点
  2. 优先测试GET类型的简单查询端点
  3. 验证响应格式和状态码
  
  Level 3 - 业务流程验证：
  1. 根据项目实际功能设计验证流程
  2. 测试完整的业务场景
  3. 验证数据一致性和完整性
  ```

- **通用验证模板**：

  ```
  项目适配性检查：
  1. 项目类型识别：
     - grep -r "FastAPI\|Flask\|Django" --include="*.py" . | head -5
     - find . -name "requirements.txt" -o -name "setup.py" -o -name "pyproject.toml"
  
  2. 核心功能发现：
     - 从路由文件分析主要功能模块
     - 从模型文件推断数据结构
     - 从配置文件获取关键设置
  
  3. 验证策略选择：
     - 根据项目类型选择合适的验证方法
     - 根据功能复杂度调整验证深度
     - 根据问题类型定制验证重点
  ```

### **步骤5.3: 强制循环控制机制**

⚠️ **强制要求：本阶段必须执行循环机制，不允许跳过**
📋 **循环追踪：使用TodoWrite工具记录每轮循环状态**
🔄 **退出条件：必须满足明确退出条件才能结束循环**

```
🛑 循环控制参数（强制执行）：
- 最小循环轮次：1轮（但如有失败项必须执行第2轮）
- 最大循环轮次：3轮（防止无限循环）
- 进度量化：验证通过率 = 通过项/总验证项 × 100%
- 目标通过率：100%

🔄 验证维度（必须全覆盖）：
- 功能完整性验证
- 数据一致性验证  
- 业务逻辑验证
- 性能稳定性验证

循环执行流程：
初始化：
1. 使用TodoWrite记录"阶段5第X轮循环"（X=1,2,3）
2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
3. 记录初始验证项数量：统计需要验证的功能总数

每轮循环必须记录：
- 当前轮次：X/3
- 验证通过率：X%（通过项/总项×100%）
- 失败验证项：具体列表
- 退出条件检查：满足/不满足

循环执行：
5.1(功能验证) → 5.2(验证评估) → 5.3(循环判断) → 自动判断：

✅ 强制退出条件（满足任一即退出）：
- 验证通过率达到100%
- 连续2轮验证结果相同
- 达到最大循环轮次3轮

❌ 禁止提前退出：
- 验证通过率<100%且轮次<3不允许退出
- 仍有验证失败项不允许退出
- 未执行完整验证维度不允许退出

🔧 强制执行要求：
- 每轮开始必须更新TodoWrite状态为in_progress
- 每轮结束必须记录验证通过率变化
- 不允许停顿等待用户指令，必须自动进入下一轮
- 违反循环机制必须重新执行完整阶段5
```

### **步骤5.4: 验证结果评估**

- **5.4.1 成功标准**
  - **功能完整性**：所有相关功能正常工作
  - **性能稳定性**：性能指标达到预期水平
  - **错误消除**：原始问题完全消失
  - **无副作用**：修复未引入新问题

- **5.4.2 失败处理**
  - **问题重新分类**：分析验证失败的原因
  - **假设更新**：基于新发现更新问题假设
  - **策略调整**：调整修复策略和方法
  - **循环重启**：必要时回到阶段2重新开始

---

## ✅ **问题解决成功标准**

1. **问题定位准确**: 100% - 必须找到真正的根本原因，不是表面现象
2. **修复完整性**: 100% - 必须完全解决根本问题，不是临时绕过
3. **功能完整性**: 100% - 修复后功能完全正常，无副作用
4. **验证真实性**: 100% - 必须通过真实测试验证，不是理论推理
5. **问题复现性**: 0% - 在相同条件下问题不再复现
6. **旧问题残留**: 0% - 不允许任何相关问题残留未解决