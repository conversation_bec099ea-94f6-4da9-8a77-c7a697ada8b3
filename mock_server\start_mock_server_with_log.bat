@echo off
chcp 65001 > nul
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set LANG=en_US.UTF-8
echo [%date% %time%] Starting Mock Server with Internal Log System...
cd /d D:\01-shuimu_01\mock_server
echo [%date% %time%] Working directory: %cd%
echo [%date% %time%] Environment: PYTHONIOENCODING=%PYTHONIOENCODING%, PYTHONUTF8=%PYTHONUTF8%, LANG=%LANG%
echo [%date% %time%] Executing: uvicorn src.main:app --host 0.0.0.0 --port 8000
set PATH=C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Scripts;%PATH%
C:\Users\<USER>\anaconda3\python.exe -c "import sys; print('Python encoding:', sys.stdout.encoding)"
echo [%date% %time%] Starting server... (Logs will be managed internally)
C:\Users\<USER>\anaconda3\Scripts\uvicorn.exe src.main:app --host 0.0.0.0 --port 8000 > "D:\01-shuimu_01\logs\uvicorn.log"