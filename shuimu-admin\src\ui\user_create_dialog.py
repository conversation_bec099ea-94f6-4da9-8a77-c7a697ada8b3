#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户创建对话框
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QCheckBox, QFormLayout,
                            QMessageBox, QFrame)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from typing import Dict, Any
import threading
import re


class UserCreateDialog(QDialog):
    """用户创建对话框"""
    
    def __init__(self, parent, api_client):
        super().__init__(parent)
        self.api_client = api_client
        self.result = False
        
        # 设置对话框
        self.setWindowTitle("创建新用户")
        self.setFixedSize(500, 500)
        self.setModal(True)
        
        # 创建界面
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面控件"""
        layout = QVBoxLayout(self)
        
        # 标题
        title = QLabel("创建新用户")
        title_font = QFont()
        title_font.setPointSize(14)
        title_font.setBold(True)
        title.setFont(title_font)
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # 表单
        form_layout = QFormLayout()
        layout.addLayout(form_layout)
        
        # 用户名
        self.username_entry = QLineEdit()
        form_layout.addRow("用户名*:", self.username_entry)
        
        # 邮箱
        self.email_entry = QLineEdit()
        form_layout.addRow("邮箱*:", self.email_entry)
        
        # 密码
        self.password_entry = QLineEdit()
        self.password_entry.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("密码*:", self.password_entry)
        
        # 确认密码
        self.confirm_password_entry = QLineEdit()
        self.confirm_password_entry.setEchoMode(QLineEdit.EchoMode.Password)
        form_layout.addRow("确认密码*:", self.confirm_password_entry)
        
        # 显示名
        self.display_name_entry = QLineEdit()
        form_layout.addRow("显示名:", self.display_name_entry)
        
        # 手机号
        self.phone_entry = QLineEdit()
        form_layout.addRow("手机号:", self.phone_entry)
        
        # 状态选项
        options_frame = QFrame()
        options_layout = QVBoxLayout(options_frame)
        
        self.is_active_checkbox = QCheckBox("激活用户")
        self.is_active_checkbox.setChecked(True)
        options_layout.addWidget(self.is_active_checkbox)
        
        self.is_admin_checkbox = QCheckBox("管理员权限")
        self.is_admin_checkbox.setChecked(False)
        options_layout.addWidget(self.is_admin_checkbox)
        
        form_layout.addRow("选项:", options_frame)
        
        # 按钮
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)
        
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        create_btn = QPushButton("创建用户")
        create_btn.clicked.connect(self.create_user)
        create_btn.setDefault(True)
        button_layout.addWidget(create_btn)
        
    def validate_input(self) -> bool:
        """验证输入"""
        # 用户名
        username = self.username_entry.text().strip()
        if not username:
            QMessageBox.warning(self, "验证错误", "用户名不能为空")
            return False
        
        if len(username) < 3:
            QMessageBox.warning(self, "验证错误", "用户名至少需要3个字符")
            return False
        
        # 邮箱
        email = self.email_entry.text().strip()
        if not email:
            QMessageBox.warning(self, "验证错误", "邮箱不能为空")
            return False
        
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            QMessageBox.warning(self, "验证错误", "请输入有效的邮箱地址")
            return False
        
        # 密码
        password = self.password_entry.text()
        if not password:
            QMessageBox.warning(self, "验证错误", "密码不能为空")
            return False
        
        if len(password) < 6:
            QMessageBox.warning(self, "验证错误", "密码至少需要6个字符")
            return False
        
        # 确认密码
        confirm_password = self.confirm_password_entry.text()
        if password != confirm_password:
            QMessageBox.warning(self, "验证错误", "两次输入的密码不一致")
            return False
        
        # 手机号验证（如果提供）
        phone = self.phone_entry.text().strip()
        if phone:
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, phone):
                QMessageBox.warning(self, "验证错误", "请输入有效的手机号")
                return False
        
        return True
    
    def create_user(self):
        """创建用户"""
        if not self.validate_input():
            return
        
        def create_in_thread():
            try:
                # 构建用户数据
                user_data = {
                    'username': self.username_entry.text().strip(),
                    'email': self.email_entry.text().strip(),
                    'password': self.password_entry.text(),
                    'display_name': self.display_name_entry.text().strip() or None,
                    'phone': self.phone_entry.text().strip() or None,
                    'is_active': self.is_active_checkbox.isChecked(),
                    'is_admin': self.is_admin_checkbox.isChecked()
                }
                
                # 调用API
                response = self.api_client.create_user(user_data)
                
                if response.get('success'):
                    self.result = True
                    QMessageBox.information(self, "成功", "用户创建成功！")
                    self.accept()
                else:
                    error_msg = response.get('message', '创建用户失败')
                    QMessageBox.critical(self, "错误", error_msg)
                    
            except Exception as e:
                error_msg = f"创建用户失败: {str(e)}"
                QMessageBox.critical(self, "错误", error_msg)
        
        threading.Thread(target=create_in_thread, daemon=True).start()
    
    def show(self) -> bool:
        """显示对话框并返回是否创建了用户"""
        result = self.exec()
        return self.result and result == QDialog.DialogCode.Accepted