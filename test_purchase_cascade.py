#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试购买记录级联更新机制
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shuimu-admin', 'src'))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from ui.user_detail_window import UserDetailWindow
from services.user_service import UserService

def test_cascade_update():
    """测试级联更新机制"""
    app = QApplication(sys.argv)
    
    # 创建用户服务
    user_service = UserService(use_api=True)
    
    # 创建用户详情窗口
    user_id = "test-api-001"  # 使用测试用户
    window = UserDetailWindow(user_id, parent=None)
    window.show()
    
    # 显示提示
    print("=" * 60)
    print("购买记录级联更新测试")
    print("=" * 60)
    print("1. 请手动添加一个购买记录")
    print("2. 观察控制台输出，查看是否有以下日志：")
    print("   - 🔔 [调试] _on_async_purchase_completed被调用")
    print("   - 📢 收到购买添加通知")
    print("   - 🔄 开始级联更新所有相关标签页")
    print("3. 检查进度、收藏、缓存标签页是否显示新购买的视频")
    print("=" * 60)
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    test_cascade_update()