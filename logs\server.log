[2025-07-24 06:43:01.50] SYSTEM: === LOG CLEARED BY ADMIN REQUEST ===
[2025-07-24 06:43:01.50] INFO: 📤 POST /api/admin/v1/logs/clear - Status: 200 - Time: 0.002s
[2025-07-24 06:43:06.95] INFO: 📥 GET /api/admin/v1/series - Client: 127.0.0.1
[2025-07-24 06:43:06.96] INFO: 📤 GET /api/admin/v1/series - Status: 200 - Time: 0.009s
[2025-07-24 06:43:06.96] INFO: 📥 GET /api/admin/v1/categories - Client: 127.0.0.1
[2025-07-24 06:43:07.00] INFO: 📤 GET /api/admin/v1/categories - Status: 200 - Time: 0.036s
[2025-07-24 06:43:07.00] INFO: 📥 GET /api/admin/v1/videos - Client: 127.0.0.1
[2025-07-24 06:43:07.00] INFO: 📤 GET /api/admin/v1/videos - Status: 200 - Time: 0.003s
[2025-07-24 06:43:07.00] INFO: 📥 GET /api/admin/v1/users - Client: 127.0.0.1
[2025-07-24 06:43:07.00] INFO: 📤 GET /api/admin/v1/users - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.01] INFO: 📥 GET /api/admin/v1/users/test-api-001 - Client: 127.0.0.1
[2025-07-24 06:43:07.01] INFO: 📤 GET /api/admin/v1/users/test-api-001 - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.01] INFO: 📥 GET /api/admin/v1/users/test-api-001/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.01] INFO: 📤 GET /api/admin/v1/users/test-api-001/purchases - Status: 200 - Time: 0.004s
[2025-07-24 06:43:07.01] INFO: 📥 GET /api/admin/v1/users/test-api-001/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.01] DEBUG: 🔍进度API：获取用户 test-api-001 基于购买记录的观看进度
[2025-07-24 06:43:07.02] INFO: 进度API成功：返回 58 条记录（基于购买记录）
[2025-07-24 06:43:07.02] INFO: 📤 GET /api/admin/v1/users/test-api-001/progress - Status: 200 - Time: 0.004s
[2025-07-24 06:43:07.02] INFO: 📥 GET /api/admin/v1/users/test-api-001/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.02] DEBUG: 🔍收藏API：获取用户 test-api-001 基于购买记录的收藏状态
[2025-07-24 06:43:07.02] INFO: 收藏API成功：返回 58 条记录（基于购买记录）
[2025-07-24 06:43:07.02] INFO: 📤 GET /api/admin/v1/users/test-api-001/favorites - Status: 200 - Time: 0.004s
[2025-07-24 06:43:07.02] INFO: 📥 GET /api/admin/v1/users/test-api-001/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.02] INFO: 📤 GET /api/admin/v1/users/test-api-001/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.02] INFO: 📥 GET /api/admin/v1/users/test-api-001/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.02] DEBUG: 🔍缓存API：获取用户 test-api-001 基于购买记录的缓存状态
[2025-07-24 06:43:07.03] INFO: 缓存API成功：返回 58 条记录（基于购买记录）
[2025-07-24 06:43:07.03] INFO: 📤 GET /api/admin/v1/users/test-api-001/cache - Status: 200 - Time: 0.004s
[2025-07-24 06:43:07.03] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001 - Client: 127.0.0.1
[2025-07-24 06:43:07.03] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001 - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.03] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.03] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/purchases - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.03] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.03] DEBUG: 🔍进度API：获取用户 test-plaintext-001 基于购买记录的观看进度
[2025-07-24 06:43:07.04] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.04] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/progress - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.04] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.04] DEBUG: 🔍收藏API：获取用户 test-plaintext-001 基于购买记录的收藏状态
[2025-07-24 06:43:07.04] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.04] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/favorites - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.04] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.04] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.04] INFO: 📥 GET /api/admin/v1/users/test-plaintext-001/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.04] DEBUG: 🔍缓存API：获取用户 test-plaintext-001 基于购买记录的缓存状态
[2025-07-24 06:43:07.04] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.04] INFO: 📤 GET /api/admin/v1/users/test-plaintext-001/cache - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.05] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb - Client: 127.0.0.1
[2025-07-24 06:43:07.05] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.05] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.05] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/purchases - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.05] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.05] DEBUG: 🔍进度API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的观看进度
[2025-07-24 06:43:07.05] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.05] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/progress - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.05] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.05] DEBUG: 🔍收藏API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的收藏状态
[2025-07-24 06:43:07.05] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.05] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/favorites - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.06] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.06] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.06] INFO: 📥 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.06] DEBUG: 🔍缓存API：获取用户 ae2feaee-4423-45fe-ac9f-27f2282982fb 基于购买记录的缓存状态
[2025-07-24 06:43:07.06] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.06] INFO: 📤 GET /api/admin/v1/users/ae2feaee-4423-45fe-ac9f-27f2282982fb/cache - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.06] INFO: 📥 GET /api/admin/v1/users/test-batch-001 - Client: 127.0.0.1
[2025-07-24 06:43:07.06] INFO: 📤 GET /api/admin/v1/users/test-batch-001 - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.06] INFO: 📥 GET /api/admin/v1/users/test-batch-001/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.06] INFO: 📤 GET /api/admin/v1/users/test-batch-001/purchases - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.06] INFO: 📥 GET /api/admin/v1/users/test-batch-001/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.06] DEBUG: 🔍进度API：获取用户 test-batch-001 基于购买记录的观看进度
[2025-07-24 06:43:07.07] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.07] INFO: 📤 GET /api/admin/v1/users/test-batch-001/progress - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.07] INFO: 📥 GET /api/admin/v1/users/test-batch-001/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.07] DEBUG: 🔍收藏API：获取用户 test-batch-001 基于购买记录的收藏状态
[2025-07-24 06:43:07.07] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.07] INFO: 📤 GET /api/admin/v1/users/test-batch-001/favorites - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.07] INFO: 📥 GET /api/admin/v1/users/test-batch-001/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.07] INFO: 📤 GET /api/admin/v1/users/test-batch-001/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.07] INFO: 📥 GET /api/admin/v1/users/test-batch-001/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.07] DEBUG: 🔍缓存API：获取用户 test-batch-001 基于购买记录的缓存状态
[2025-07-24 06:43:07.07] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.07] INFO: 📤 GET /api/admin/v1/users/test-batch-001/cache - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.08] INFO: 📥 GET /api/admin/v1/users/test-batch-002 - Client: 127.0.0.1
[2025-07-24 06:43:07.08] INFO: 📤 GET /api/admin/v1/users/test-batch-002 - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.08] INFO: 📥 GET /api/admin/v1/users/test-batch-002/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.08] INFO: 📤 GET /api/admin/v1/users/test-batch-002/purchases - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.08] INFO: 📥 GET /api/admin/v1/users/test-batch-002/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.08] DEBUG: 🔍进度API：获取用户 test-batch-002 基于购买记录的观看进度
[2025-07-24 06:43:07.08] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.08] INFO: 📤 GET /api/admin/v1/users/test-batch-002/progress - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.08] INFO: 📥 GET /api/admin/v1/users/test-batch-002/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.08] DEBUG: 🔍收藏API：获取用户 test-batch-002 基于购买记录的收藏状态
[2025-07-24 06:43:07.08] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.08] INFO: 📤 GET /api/admin/v1/users/test-batch-002/favorites - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.09] INFO: 📥 GET /api/admin/v1/users/test-batch-002/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.09] INFO: 📤 GET /api/admin/v1/users/test-batch-002/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.09] INFO: 📥 GET /api/admin/v1/users/test-batch-002/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.09] DEBUG: 🔍缓存API：获取用户 test-batch-002 基于购买记录的缓存状态
[2025-07-24 06:43:07.09] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.09] INFO: 📤 GET /api/admin/v1/users/test-batch-002/cache - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.09] INFO: 📥 GET /api/admin/v1/users/test-batch-003 - Client: 127.0.0.1
[2025-07-24 06:43:07.09] INFO: 📤 GET /api/admin/v1/users/test-batch-003 - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.09] INFO: 📥 GET /api/admin/v1/users/test-batch-003/purchases - Client: 127.0.0.1
[2025-07-24 06:43:07.09] INFO: 📤 GET /api/admin/v1/users/test-batch-003/purchases - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.09] INFO: 📥 GET /api/admin/v1/users/test-batch-003/progress - Client: 127.0.0.1
[2025-07-24 06:43:07.09] DEBUG: 🔍进度API：获取用户 test-batch-003 基于购买记录的观看进度
[2025-07-24 06:43:07.10] INFO: 进度API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.10] INFO: 📤 GET /api/admin/v1/users/test-batch-003/progress - Status: 200 - Time: 0.002s
[2025-07-24 06:43:07.10] INFO: 📥 GET /api/admin/v1/users/test-batch-003/favorites - Client: 127.0.0.1
[2025-07-24 06:43:07.10] DEBUG: 🔍收藏API：获取用户 test-batch-003 基于购买记录的收藏状态
[2025-07-24 06:43:07.10] INFO: 收藏API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.10] INFO: 📤 GET /api/admin/v1/users/test-batch-003/favorites - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.10] INFO: 📥 GET /api/admin/v1/users/test-batch-003/settings - Client: 127.0.0.1
[2025-07-24 06:43:07.10] INFO: 📤 GET /api/admin/v1/users/test-batch-003/settings - Status: 200 - Time: 0.001s
[2025-07-24 06:43:07.10] INFO: 📥 GET /api/admin/v1/users/test-batch-003/cache - Client: 127.0.0.1
[2025-07-24 06:43:07.10] DEBUG: 🔍缓存API：获取用户 test-batch-003 基于购买记录的缓存状态
[2025-07-24 06:43:07.10] INFO: 缓存API成功：返回 4 条记录（基于购买记录）
[2025-07-24 06:43:07.10] INFO: 📤 GET /api/admin/v1/users/test-batch-003/cache - Status: 200 - Time: 0.002s
[2025-07-24 06:43:18.19] INFO: 📥 POST /api/admin/v1/users/test-api-001/purchases - Client: 127.0.0.1
[2025-07-24 06:43:18.20] DEBUG: [DEBUG] 服务端调试：收到购买请求 - type=series_all_categories, purchased_entity_id=78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e, amount=112.0
[2025-07-24 06:43:18.20] DEBUG: 服务端调试：开始处理系列购买 - series_id=78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e
[2025-07-24 06:43:18.20] DEBUG: 服务端调试：series_all_categories模式 - 系列ID=78f3364f-0ba3-4a11-87ef-2ce2fa3a4e3e, 分类数=1
[2025-07-24 06:43:18.21] DEBUG: 服务端调试：系列购买完成，成功添加 1 个分类购买记录
[2025-07-24 06:43:18.22] INFO: 📤 POST /api/admin/v1/users/test-api-001/purchases - Status: 200 - Time: 0.025s
