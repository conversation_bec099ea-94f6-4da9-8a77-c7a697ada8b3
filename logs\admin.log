[2025-07-23 14:45:30.79] === NEW ADMIN SESSION START === 
✅ 数据库管理器初始化完成
✅ UUID生成器初始化完成 - 无需映射机制
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ 主窗口乐观更新功能初始化成功
🚀 同步工作线程启动🚀 CRUD工作线程启动

✅ 内容区域清理完成
🚀 开始初始化课程数据...
📡 API模式: True (强制使用服务器数据，确保数据一致性)
✅ 新的数据同步管理器初始化成功
🔄 异步同步后台线程已启动
✅ CourseService API导入成功: API_AVAILABLE = True
🔧 CourseService初始化:
   use_api参数: True
   API_AVAILABLE: True
   最终use_api: True
   use_cache: True
✅ 同步管理器API客户端已设置
🚀 开始一次性加载所有数据到内存...
📂 获取所有分类数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有分类完成: 46 个
📹 获取所有视频数据（无分页）...
🔍 API基础URL: http://localhost:8000
🔍 API响应类型: <class 'dict'>
✅ 获取所有视频完成: 67 个
🔍 预加载用户详情进度: 1/6
✅ 用户详情数据预加载完成: 6 个用户
📊 购买记录预加载统计: 6/6 个用户成功
🎉 所有数据一次性加载完成！
📊 数据统计: 系列=52, 分类=46, 视频=67, 用户=6
✅ 所有数据已一次性加载到内存
🚫 后续操作将完全使用内存数据，禁止网络请求
📊 内存数据统计: 系列=52, 分类=46, 视频=67
🎉 数据加载完成
🔍 检查全局数据管理器状态: loaded=True
📊 更新用户菜单数据: 找到 6 个用户
✅ 用户菜单数据更新完成
📊 概览数据更新成功
🔍 检查全局数据管理器状态: loaded=True
📊 更新用户菜单数据: 找到 6 个用户
✅ 用户菜单数据更新完成
✅ 数据库连接成功: localhost:3306
📊 概览数据更新成功
✅ 主窗口乐观更新信号连接成功
✅ 使用Qt原生事件循环启动
🚀 开始预加载课程管理页面...
✅ CourseService API导入成功: API_AVAILABLE = True
✅ 配置文件加载成功: 2 项配置
🔧 CourseService初始化:
   use_api参数: True
   API_AVAILABLE: True
   最终use_api: True
   use_cache: True
✅ 同步管理器API客户端已设置
🔧 CourseManagementWindow: API模式=True (来自配置文件)
✅ 信号管理器初始化完成
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ UUID架构已启用，无需依赖等待机制
✅ Qt原生乐观更新功能初始化成功
🚀 同步工作线程启动
🚀 CRUD工作线程启动
✅ UUID架构已启用，无需依赖等待机制
🚀 开始初始化课程数据...
📍 当前标签页索引: 0
📹 初始化视频管理数据...
✅ 视频数据已初始化: 67 个
📱 状态: ✅ 课程数据加载完成
✅ 课程管理初始化完成
✅ 课程管理页面预加载完成
✅ 内容区域清理完成
🔧 用户详情页面：配置文件路径=D:\01-shuimu_01\shuimu-admin\config.ini
🔧 用户详情页面：配置文件中use_api='true' -> True
🔍 用户详情窗口初始化：user_id = 'test-api-001'
✅ 从缓存获取用户 test-api-001 购买记录: 29 条
🔄 更新筛选器...
📚 获取系列数据更新视频筛选器...
📚 从内存获取系列列表（无网络请求）
📚 使用预加载的系列数据填充分类筛选下拉框...
🔍 下拉框默认选中: 全部系列, 值: None
✅ 系列筛选下拉框填充完成: 52 个系列
✅ 筛选器更新完成
🔘 用户点击了进入用户管理按钮
✅ 内容区域清理完成
🔧 用户管理页面：配置文件路径=D:\01-shuimu_01\shuimu-admin\config.ini
🔧 用户管理页面：配置文件中use_api='true' -> True
📦 准备创建用户管理窗口...
✅ 并行批量操作管理器已加载
✅ 同步工作线程集成补偿处理器
✅ 线程管理：已启动2个工作线程（CRUD+同步），符合规则v4.0
✅ 工作线程信号连接完成
✅ UI信号连接跳过（由外部调用者处理）
✅ 表格视觉反馈初始化完成: 
🚀 CRUD工作线程启动✅ 表格视觉反馈初始化成功: 

✅ 用户管理：表格视觉反馈初始化完成
✅ 用户管理窗口创建成功
📐 设置为Widget模式
✅ 用户管理页面已添加到内容区域
🚀 同步工作线程启动
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
📊 定时器：不在概览页面，跳过数据刷新
