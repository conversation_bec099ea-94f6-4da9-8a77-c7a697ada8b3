---
description: 基于 `$ARGUMENTS` 按照 功能变更开发流程 执行
---

# 🚀 功能变更开发流程

## 📋 流程概览

```
阶段1: 变更需求分析与架构设计
阶段2: 功能实现与规范合规
阶段3: 系统集成与兼容性保障
```

**适用范围**：
- ✅ **新功能开发**：全新模块、组件、接口的开发
- ✅ **功能增强**：现有功能的扩展、升级、改进
- ✅ **功能重构**：现有功能的架构调整、实现优化
- ✅ **功能修复**：复杂功能缺陷的修复和完善
- ✅ **接口变更**：API接口的新增、修改、废弃
- ✅ **数据结构变更**：数据模型的调整、扩展、优化
- ✅ **业务流程变更**：工作流程的调整、优化、重设计

**核心原则**：

- ✅ **架构一致性**：变更必须符合现有架构模式
- ✅ **规范合规性**：100%符合项目规范，不允许任何妥协
- ✅ **集成完整性**：与现有系统完美集成，不破坏现有功能
- ✅ **功能验证性**：确保功能正确实现，满足业务需求

## 🏗️ 环境理解

### 运行环境架构
```
Windows主机 (实际运行环境)
├── Python项目依赖 (已安装)
├── PyQt6应用 (实际运行)
├── 数据库连接 (实际使用)
└── WSL Ubuntu (Claude Code运行环境)
    ├── 文件系统访问 (/mnt/d/...)
    ├── 代码分析和编辑
    ├── 功能开发和测试
    └── ❌ 不能直接运行Python程序
```

### 功能开发约束
**WSL环境能力**：
- ✅ 创建新文件和目录
- ✅ 编写代码和配置
- ✅ 通过FastAPI端点测试功能
- ✅ 数据库操作和验证

**环境限制**：
- ❌ 不能直接运行PyQt6应用
- ❌ 不能安装新的Python依赖
- ❌ 不能直接测试GUI界面
- ❌ 不能执行系统级操作

### 依赖管理原则
- ✅ 使用项目现有依赖，不添加新依赖
- ✅ 如需新依赖，明确告知用户在Windows环境安装
- ✅ 通过FastAPI端点间接验证功能可用性
- ❌ 不在WSL环境中执行pip install等安装命令

---

## **阶段1: 变更需求分析与架构设计**

### **步骤1.1: 变更需求分析与功能定义**

- **1.1.1 需求完整性分析**
  - 详细分析用户需求和业务场景
  - 识别变更的核心价值和边界
  - 分析与现有功能的关系和依赖
  - 确定变更的优先级和实现范围

- **1.1.2 变更类型识别**
  - **新增变更**：全新功能、模块、接口的开发
  - **修改变更**：现有功能的调整、优化、重构
  - **删除变更**：废弃功能、接口、数据的清理
  - **迁移变更**：功能在模块间的迁移、整合

- **1.1.3 功能规格定义**
  - 定义变更的输入输出规格
  - 明确用户界面和交互流程
  - 确定数据处理和业务逻辑
  - 制定变更验收标准

- **1.1.4 技术可行性评估**
  - 评估现有技术栈的支持能力
  - 分析技术实现的复杂度和风险
  - 确定是否需要新的技术组件
  - 评估开发工作量和时间计划

### **步骤1.2: 架构设计与系统集成分析**

- **1.2.1 架构设计符合性检查**
  - **四层架构适配**：
    - UI层：如何集成到现有PyQt6界面
    - API层：如何扩展现有FastAPI端点
    - 持久层：如何与现有数据库集成
    - 补偿层：如何处理变更的错误和回滚
  
- **1.2.2 系统集成点分析**
  - **数据集成**：与现有数据库表的关系设计
  - **API集成**：与现有端点的兼容性和扩展
  - **UI集成**：与现有界面的集成方式
  - **业务集成**：与现有业务流程的协调

- **1.2.3 设计文档创建**
  - 数据库设计：表结构、关系、索引、约束
  - API设计：端点定义、请求/响应格式、错误处理
  - UI设计：界面布局、交互流程、用户体验
  - 技术架构：模块组织、依赖关系、接口定义

### **步骤1.3: 设计验证与优化循环**

- **1.3.1 设计合规性验证**
  - **命名规范检查**：确保所有命名符合项目规范
  - **架构模式检查**：确保符合四层架构和UUID架构
  - **API规范检查**：确保符合版本化API设计
  - **数据库规范检查**：确保符合字段命名和结构规范

- **1.3.2 集成兼容性验证**
  - **数据兼容性**：新数据结构与现有数据的兼容性
  - **API兼容性**：新端点与现有端点的兼容性
  - **UI兼容性**：新界面与现有界面的集成兼容性
  - **业务兼容性**：新业务流程与现有流程的兼容性

- **1.3.3 设计验证循环**
  - **强制循环控制机制**：
    ```
    初始化：
    1. 使用TodoWrite记录"阶段1第X轮循环"
    2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
    3. 记录初始设计问题数量：统计设计不合规或不兼容的问题数
    
    循环执行：
    设计创建 → 合规性验证 → 兼容性验证 → 自动判断：
    
    继续循环条件（必须同时满足）：
    - 当前轮次 < 3轮
    - 设计问题数量 > 0
    - 设计问题相比上轮有减少
    
    退出循环条件（满足任一）：
    - 所有设计问题都已解决 → 进入阶段2
    - 连续2轮问题数量相同 → 进入阶段2
    - 达到3轮上限 → 生成当前最佳设计
    
    强制执行：
    - 每轮开始必须更新TodoWrite状态为in_progress
    - 每轮结束必须记录设计问题数量变化
    - 不允许停顿等待用户指令，必须自动进入下一轮
    ```

---

## **阶段2: 功能实现与规范合规**

### **步骤2.1: 分层功能实现**

- **2.1.1 数据库层实现**
  - **表结构创建**：按照设计文档创建数据库表
  - **约束和索引**：创建必要的约束、索引和触发器
  - **数据迁移**：如需要，准备数据迁移脚本
  - **测试数据**：准备测试用的示例数据

- **2.1.2 API层实现**
  - **端点实现**：按照设计实现FastAPI端点
  - **数据验证**：实现输入数据的验证逻辑
  - **业务逻辑**：实现核心业务处理逻辑
  - **错误处理**：实现完整的错误处理机制

- **2.1.3 UI层实现**
  - **PyQt6界面**：创建用户界面组件
  - **交互逻辑**：实现用户交互和事件处理
  - **数据绑定**：实现界面与数据的绑定
  - **四层架构集成**：实现乐观更新和异步处理

- **2.1.4 集成层实现**
  - **模块集成**：实现变更功能与现有模块的集成
  - **数据流集成**：实现数据在各层之间的流转
  - **配置集成**：实现配置文件和环境变量的集成
  - **工具集成**：实现与现有工具和服务的集成

### **步骤2.2: 规范合规性验证**

- **2.2.1 代码规范检查**
  - **命名规范**：检查所有变量、函数、类名是否符合规范
  - **代码结构**：检查代码组织和模块划分是否合理
  - **注释规范**：检查注释的完整性和规范性
  - **导入规范**：检查import语句的组织和规范性

- **2.2.2 架构模式检查**
  - **四层架构**：检查是否正确实现四层架构模式
  - **UUID架构**：检查UUID的生成和使用是否符合规范
  - **API版本化**：检查API端点是否符合版本化规范
  - **数据库设计**：检查数据库设计是否符合规范

- **2.2.3 质量标准检查**
  - **代码质量**：检查代码的可读性和可维护性
  - **错误处理**：检查错误处理的完整性和正确性
  - **安全性**：检查数据验证和权限控制
  - **性能**：检查潜在的性能问题

### **步骤2.3: 实现验证循环**

- **2.3.1 单元测试验证**
  - **功能测试**：测试各个功能模块的正确性
  - **边界测试**：测试边界条件和异常情况
  - **集成测试**：测试模块之间的集成
  - **数据测试**：测试数据处理的正确性

- **2.3.2 WSL环境适配验证**
  - **静态验证**：通过代码分析验证实现正确性
  - **配置验证**：验证配置文件和环境变量
  - **API验证**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，继续测试
       - 如果返回非200或连接失败 → 需要启动服务
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 通过curl或工具测试API端点
    ```
  - **数据库验证**：通过FastAPI端点验证数据库操作

- **2.3.3 循环控制**
  - **强制循环控制机制**：
    ```
    初始化：
    1. 使用TodoWrite记录"阶段2第X轮循环"
    2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
    3. 记录初始实现问题数量：统计代码、规范、质量问题数
    
    循环执行：
    实现完成 → 规范检查 → 质量检查 → 功能验证 → 自动判断：
    
    继续循环条件（必须同时满足）：
    - 当前轮次 < 3轮
    - 实现问题数量 > 0
    - 实现问题相比上轮有减少
    
    退出循环条件（满足任一）：
    - 所有实现问题都已解决 → 进入阶段3
    - 连续2轮问题数量相同 → 进入阶段3
    - 达到3轮上限 → 生成当前实现状态
    
    强制执行：
    - 每轮开始必须更新TodoWrite状态为in_progress
    - 每轮结束必须记录实现问题数量变化
    - 不允许停顿等待用户指令，必须自动进入下一轮
    ```

---

## **阶段3: 系统集成与兼容性保障**

### **步骤3.1: 系统集成实施**

- **3.1.1 数据层集成**
  - **数据库集成**：确保新表与现有表的关系正确
  - **数据迁移**：如需要，执行数据迁移和转换
  - **约束检查**：验证外键约束和数据完整性
  - **查询优化**：优化涉及变更功能的查询性能

- **3.1.2 API层集成**
  - **端点集成**：确保新端点与现有端点协调工作
  - **认证集成**：集成现有的认证和权限系统
  - **错误处理集成**：统一错误处理和响应格式
  - **版本兼容**：确保API版本的向后兼容性

- **3.1.3 UI层集成**
  - **界面集成**：将变更功能集成到现有界面
  - **导航集成**：更新导航和菜单结构
  - **样式集成**：确保界面样式的一致性
  - **交互集成**：确保用户交互的一致性

- **3.1.4 业务流程集成**
  - **工作流集成**：集成变更功能到现有业务流程
  - **权限集成**：集成到现有权限和角色系统
  - **日志集成**：集成到现有日志和监控系统
  - **配置集成**：集成到现有配置管理系统

### **步骤3.2: 兼容性验证**

- **3.2.1 功能兼容性验证**
  - **现有功能测试**：确保现有功能不受影响
  - **数据兼容性测试**：测试新旧数据的兼容性
  - **API兼容性测试**：测试API的向后兼容性
  - **UI兼容性测试**：测试界面的兼容性

- **3.2.2 性能兼容性验证**
  - **响应时间测试**：确保变更不影响系统性能
  - **并发处理测试**：测试高并发场景下的稳定性
  - **资源使用测试**：验证资源使用的合理性
  - **负载测试**：测试系统在负载下的表现

- **3.2.3 数据完整性验证**
  - **数据一致性测试**：验证数据的一致性
  - **事务完整性测试**：测试事务的完整性
  - **约束验证测试**：验证数据约束的正确性
  - **备份恢复测试**：测试数据的备份恢复能力

### **步骤3.3: 集成验证循环**

- **3.3.1 WSL环境适配集成验证**
  - **通过FastAPI端点验证**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，继续测试
       - 如果返回非200或连接失败 → 需要启动服务
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 测试变更功能相关的所有API端点
    6. 验证与现有端点的集成
    7. 测试完整的业务流程
    ```
  
  - **数据库集成验证**：
    ```
    1. 通过API端点测试数据库操作
    2. 验证新旧数据的兼容性
    3. 测试数据约束和关系
    4. 验证查询性能
    ```

  - **功能完整性验证**：
    ```
    1. 端到端功能测试
    2. 业务流程完整性验证
    3. 用户场景测试
    4. 异常处理验证
    ```

- **3.3.2 循环控制**
  - **强制循环控制机制**：
    ```
    初始化：
    1. 使用TodoWrite记录"阶段3第X轮循环"
    2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
    3. 记录初始集成问题数量：统计集成和兼容性问题数
    
    循环执行：
    集成实施 → 兼容性验证 → 性能验证 → 数据验证 → 自动判断：
    
    继续循环条件（必须同时满足）：
    - 当前轮次 < 3轮
    - 集成问题数量 > 0
    - 集成问题相比上轮有减少
    
    退出循环条件（满足任一）：
    - 所有集成问题都已解决 → 变更完成
    - 连续2轮问题数量相同 → 变更完成
    - 达到3轮上限 → 生成最终集成报告
    
    强制执行：
    - 每轮开始必须更新TodoWrite状态为in_progress
    - 每轮结束必须记录集成问题数量变化
    - 不允许停顿等待用户指令，必须自动进入下一轮
    ```

## 🔧 项目特定技术约定

### 🏗️ 核心架构：四层同步乐观更新

```
| 层级      | 名称   | 职责                               | 执行时机                                 |
| --------- | ------ | ---------------------------------- | ---------------------------------------- |
| **第1层** | UI层   | 立即更新界面，包括完整级联更新     | 用户操作时立即执行，ui立即更新，启动后台线程不阻塞UI |
| **第2层** | API层  | 异步提交到服务器，支持级联并行调用 | UI更新后异步执行不阻塞主程序             |
| **第3层** | 持久层 | 同步本地MySQL、内存缓存、统计信息  | API成功后并行执行                        |
| **第4层** | 补偿层 | 失败回滚、错误提示、级联重试机制   | API失败时立即执行                        |
```

### 🎯 重要原则

- **UUID架构**：客户端生成UUID，整个项目唯一的uuid来源，一旦生成就是正式的，没有临时id一说；每个业务实体只能有一个主UUID，关联时使用existing UUID，不重新生成。
- **数据库操作**：通过现有FastAPI端点操作数据库；操作前先检查服务状态，无服务时启动mock_server，必须用8000端口

### 🔗 API规范

- **版本化**: `/api/v1/` 服务端、app端，`/api/admin/v1/` 管理端
- **常量类**: 使用`APIEndpoints`，禁止硬编码路径
- **权限头**: `X-User-Id` 和 `X-Is-Admin`

### 🗄️ 数据库规范

- **主键**: `id VARCHAR(50)`
- **外键**: `{实体名}_id` + 约束
- **前后缀**: `is_`布尔，`_at`时间
- **禁用**: `item_id`、`data`、`type`、`value`等模糊命名

### 🔤 命名规范

- **原则：**见名知意
- **全项目**: `snake_case` (数据库、API、Python代码)  
- **Android端**: `camelCase` + HTTP客户端自动转换

---

## ✅ **功能变更成功标准**

1. **需求分析完整性**: 100% - 所有变更需求必须被完整分析和定义
2. **架构设计合规性**: 100% - 设计必须完全符合项目架构和规范
3. **功能实现正确性**: 100% - 所有功能必须正确实现并通过测试
4. **系统集成完整性**: 100% - 变更必须与现有系统完美集成
5. **兼容性保障**: 100% - 变更不能破坏现有功能和数据
6. **质量标准达成**: 100% - 变更必须达到项目质量标准