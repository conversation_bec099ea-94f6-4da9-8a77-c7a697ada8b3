---
description: 基于 `$ARGUMENTS` 按照 数据变更与升级流程 执行
---

# 📊 数据变更与升级流程

## 📋 流程概览

```
阶段1: 数据变更方案设计与风险评估
阶段2: 数据备份与环境准备
阶段3: 数据变更实施与实时监控
阶段4: 数据完整性验证与对账
阶段5: 系统切换与回滚准备
```

**适用范围**：
- ✅ **数据结构变更**：表结构调整、字段增删改、约束变更
- ✅ **数据内容变更**：数据清洗、格式转换、内容修正
- ✅ **数据迁移操作**：数据库迁移、平台迁移、版本升级
- ✅ **数据整合操作**：数据合并、拆分、重组、归档
- ✅ **数据同步操作**：实时同步、批量同步、增量同步
- ✅ **数据恢复操作**：数据恢复、历史数据重建、数据修复
- ✅ **数据优化操作**：索引重建、分区调整、性能优化

**核心原则**：

- ✅ **数据零丢失**：确保100%数据完整性
- ✅ **业务连续性**：最小化业务中断时间
- ✅ **可逆性保障**：随时可以安全回滚
- ✅ **验证驱动**：每步都有严格验证

## 🏗️ 环境理解

### 数据变更类型
```
结构变更
├── 表结构调整（字段增删改）
├── 索引和约束优化
├── 表拆分或合并
├── 数据类型转换
├── 关系模型重构
└── 分区策略调整

内容变更
├── 数据清洗和去重
├── 数据格式标准化
├── 数据内容修正
├── 编码格式转换
├── 数据补全和完善
└── 数据验证规则应用

迁移变更
├── 数据库版本升级
├── 数据库类型迁移
├── 服务器平台迁移
├── 云平台迁移
├── 分布式架构迁移
└── 存储方案迁移

整合变更
├── 多数据源合并
├── 数据拆分和分片
├── 历史数据归档
├── 实时数据同步
├── 批量数据处理
└── 数据仓库构建
```

### WSL环境数据操作限制
**可以执行的操作**：
- ✅ 编写和验证数据变更脚本
- ✅ 通过FastAPI端点操作数据
- ✅ 分析数据结构和依赖关系
- ✅ 生成数据变更SQL和验证查询

**受限的操作**：
- ❌ 不能直接连接生产数据库
- ❌ 不能执行大批量数据操作
- ❌ 不能进行数据库备份还原
- ❌ 不能直接监控数据库性能

### 数据操作安全原则
- 所有数据操作通过FastAPI端点
- 使用事务保证操作原子性
- 分批处理大量数据
- 保留完整的操作日志

---

## **阶段1: 数据变更方案设计与风险评估**

### **步骤1.1: 数据变更需求分析**

- **1.1.1 变更内容识别**
  - **结构变更分析**：
    ```sql
    -- 示例：用户表重构
    -- 原结构：user_id, username, full_name, user_email
    -- 新结构：id, username, first_name, last_name, email
    
    变更点：
    1. 字段重命名：user_id → id, user_email → email
    2. 字段拆分：full_name → first_name + last_name
    3. 保持不变：username
    4. 数据类型调整：VARCHAR长度优化
    ```
  
  - **内容变更分析**：
    ```
    数据转换需求：
    1. 姓名拆分逻辑（处理各种格式）
    2. 数据清洗（去除无效数据）
    3. 格式标准化（统一数据格式）
    4. 默认值填充（新字段默认值）
    5. 数据验证规则应用
    ```
  
  - **依赖关系分析**：
    ```
    影响分析：
    1. 外键引用：其他表对user_id的引用
    2. 应用程序：使用这些字段的代码
    3. 报表系统：依赖这些字段的报表
    4. 第三方集成：外部系统的数据交换
    ```

- **1.1.2 变更复杂度评估**
  - **技术复杂度**：
    - 数据结构变更的复杂程度
    - 数据转换逻辑的复杂度
    - 依赖关系的复杂程度
    - 性能影响的评估
  
  - **业务复杂度**：
    - 业务流程的影响范围
    - 用户操作的影响程度
    - 数据一致性要求
    - 业务连续性要求

### **步骤1.2: 变更方案设计**

- **1.2.1 技术方案设计**
  - **变更策略选择**：
    ```
    策略选项：
    1. 在线变更：业务不中断，逐步迁移
    2. 离线变更：业务短暂中断，批量迁移
    3. 混合策略：关键数据在线，非关键数据离线
    4. 分阶段策略：多个阶段逐步完成
    ```
  
  - **实施步骤设计**：
    ```sql
    -- 阶段1：准备工作
    -- 1. 创建新表结构
    CREATE TABLE users_new (
        id VARCHAR(50) PRIMARY KEY,
        username VARCHAR(50) NOT NULL,
        first_name VARCHAR(50),
        last_name VARCHAR(50),
        email VARCHAR(100),
        created_at TIMESTAMP,
        updated_at TIMESTAMP
    );
    
    -- 2. 创建数据迁移函数
    -- 3. 创建数据验证函数
    -- 4. 创建回滚脚本
    
    -- 阶段2：数据迁移
    -- 1. 分批迁移数据
    -- 2. 实时验证数据
    -- 3. 更新应用程序
    -- 4. 切换表名
    ```

- **1.2.2 数据处理方案**
  - **数据转换逻辑**：
    ```python
    def transform_user_data(old_record):
        # 基本字段映射
        new_record = {
            'id': old_record['user_id'],
            'username': old_record['username'],
            'email': old_record['user_email'],
            'created_at': old_record.get('created_at'),
            'updated_at': datetime.now()
        }
        
        # 姓名拆分逻辑
        full_name = old_record.get('full_name', '')
        if full_name:
            names = full_name.split(' ', 1)
            new_record['first_name'] = names[0]
            new_record['last_name'] = names[1] if len(names) > 1 else ''
        
        # 数据清洗
        new_record = clean_data(new_record)
        
        # 数据验证
        validate_record(new_record)
        
        return new_record
    ```

- **1.2.3 验证方案设计**
  - **数据完整性验证**：
    ```sql
    -- 数据量验证
    SELECT COUNT(*) FROM users_old;
    SELECT COUNT(*) FROM users_new;
    
    -- 关键字段验证
    SELECT COUNT(DISTINCT user_id) FROM users_old;
    SELECT COUNT(DISTINCT id) FROM users_new;
    
    -- 数据内容验证
    SELECT 
        old.user_id,
        old.username,
        old.full_name,
        new.first_name,
        new.last_name
    FROM users_old old
    JOIN users_new new ON old.user_id = new.id
    WHERE old.username != new.username;
    ```

### **步骤1.3: 风险评估与应对**

- **1.3.1 风险识别**
  - **数据风险**：
    - 数据丢失风险
    - 数据损坏风险
    - 数据不一致风险
    - 数据转换错误风险
  
  - **业务风险**：
    - 业务中断风险
    - 功能异常风险
    - 性能下降风险
    - 用户体验影响

- **1.3.2 风险应对策略**
  - **预防措施**：
    ```
    1. 完整数据备份
    2. 详细测试计划
    3. 分阶段实施
    4. 实时监控机制
    5. 快速回滚准备
    ```
  
  - **应急预案**：
    ```
    1. 数据回滚方案
    2. 业务降级方案
    3. 应急通知机制
    4. 技术支持准备
    5. 用户沟通计划
    ```

### **步骤1.4: 设计验证循环**

- **强制循环控制机制**：
  ```
  初始化：
  1. 使用TodoWrite记录"阶段1第X轮循环"
  2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
  3. 记录初始设计问题数量：统计方案设计中的问题和风险数
  
  循环执行：
  需求分析 → 方案设计 → 风险评估 → 验证 → 自动判断：
  
  继续循环条件（必须同时满足）：
  - 当前轮次 < 3轮
  - 设计问题数量 > 0
  - 方案相比上轮有改进
  
  退出循环条件（满足任一）：
  - 所有设计问题都已解决且方案完善 → 进入阶段2
  - 连续2轮设计问题数量相同 → 进入阶段2
  - 达到3轮上限 → 生成当前最佳方案
  
  强制执行：
  - 每轮开始必须更新TodoWrite状态为in_progress
  - 每轮结束必须记录设计问题解决数量
  - 不允许停顿等待用户指令，必须自动进入下一轮
  ```

---

## **阶段2: 数据备份与环境准备**

### **步骤2.1: 数据备份实施**

- **2.1.1 备份策略制定**
  - **完整备份**：
    ```sql
    -- 全量备份策略
    1. 创建完整数据库备份
    2. 导出关键表数据
    3. 备份相关配置文件
    4. 记录当前系统状态
    ```
  
  - **增量备份**：
    ```sql
    -- 增量备份策略
    1. 记录变更开始时间点
    2. 备份变更期间的增量数据
    3. 保留变更日志
    4. 建立数据同步机制
    ```

- **2.1.2 备份验证**
  - **备份完整性验证**：
    ```sql
    -- 验证备份数据完整性
    SELECT 
        table_name,
        COUNT(*) as record_count,
        CHECKSUM(*) as data_checksum
    FROM information_schema.tables
    WHERE table_schema = 'backup_db';
    ```

### **步骤2.2: 环境准备**

- **2.2.1 测试环境准备**
  - **环境配置**：
    - 准备与生产环境一致的测试环境
    - 恢复测试数据到测试环境
    - 配置测试环境的网络和权限
    - 准备监控和日志系统

- **2.2.2 工具和脚本准备**
  - **变更脚本准备**：
    ```python
    # 数据变更脚本
    def migrate_data_batch(batch_size=1000):
        offset = 0
        while True:
            # 获取一批数据
            old_records = get_old_records(offset, batch_size)
            if not old_records:
                break
            
            # 转换数据
            new_records = [transform_user_data(record) for record in old_records]
            
            # 插入新数据
            insert_new_records(new_records)
            
            # 验证数据
            validate_migrated_batch(old_records, new_records)
            
            offset += batch_size
            
            # 记录进度
            log_migration_progress(offset)
    ```

### **步骤2.3: 准备验证**

- **2.3.1 WSL环境适配准备验证**
  - **脚本验证**：
    ```
    1. 验证数据变更脚本语法正确
    2. 验证数据转换逻辑正确
    3. 验证数据验证规则完整
    4. 验证回滚脚本可用
    ```
  
  - **API端点验证**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，继续准备
       - 如果返回非200或连接失败 → 需要启动服务
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 测试数据变更相关的API端点
    6. 验证数据库连接正常
    ```

---

## **阶段3: 数据变更实施与实时监控**

### **步骤3.1: 分阶段数据变更实施**

- **3.1.1 结构变更实施**
  - **表结构调整**：
    ```sql
    -- 第一阶段：创建新表结构
    BEGIN TRANSACTION;
    
    CREATE TABLE users_new (
        id VARCHAR(50) PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        first_name VARCHAR(50),
        last_name VARCHAR(50),
        email VARCHAR(100) UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    -- 创建索引
    CREATE INDEX idx_users_new_username ON users_new(username);
    CREATE INDEX idx_users_new_email ON users_new(email);
    
    COMMIT;
    ```

- **3.1.2 数据迁移实施**
  - **批量数据迁移**：
    ```python
    # 通过API端点进行数据迁移
    def migrate_data_through_api():
        # 获取总数据量
        total_count = get_total_record_count()
        
        batch_size = 1000
        migrated_count = 0
        
        for offset in range(0, total_count, batch_size):
            # 获取一批原始数据
            old_records = fetch_old_records(offset, batch_size)
            
            # 转换数据
            new_records = []
            for record in old_records:
                try:
                    new_record = transform_user_data(record)
                    new_records.append(new_record)
                except Exception as e:
                    log_error(f"转换失败: {record}, 错误: {e}")
                    continue
            
            # 通过API插入新数据
            try:
                insert_records_via_api(new_records)
                migrated_count += len(new_records)
                
                # 记录进度
                progress = (migrated_count / total_count) * 100
                log_progress(f"迁移进度: {progress:.2f}%")
                
            except Exception as e:
                log_error(f"API插入失败: {e}")
                # 实施回滚
                rollback_batch(new_records)
                raise
    ```

### **步骤3.2: 实时监控与验证**

- **3.2.1 迁移过程监控**
  - **进度监控**：
    ```python
    def monitor_migration_progress():
        while migration_in_progress:
            # 监控迁移进度
            progress = get_migration_progress()
            
            # 监控系统资源
            cpu_usage = get_cpu_usage()
            memory_usage = get_memory_usage()
            
            # 监控数据库性能
            db_connections = get_db_connections()
            
            # 记录监控数据
            log_monitoring_data({
                'progress': progress,
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'db_connections': db_connections
            })
            
            # 检查是否需要调整
            if cpu_usage > 80 or memory_usage > 80:
                adjust_migration_speed()
            
            time.sleep(30)  # 每30秒监控一次
    ```

- **3.2.2 数据一致性验证**
  - **实时数据验证**：
    ```python
    def validate_migrated_data():
        # 验证数据量
        old_count = get_old_table_count()
        new_count = get_new_table_count()
        
        if old_count != new_count:
            raise DataMigrationError(f"数据量不一致: {old_count} vs {new_count}")
        
        # 验证关键字段
        validate_key_fields()
        
        # 验证数据转换
        validate_data_transformation()
        
        # 验证数据完整性
        validate_data_integrity()
    ```

### **步骤3.3: 实施验证循环**

- **强制循环控制机制**：
  ```
  初始化：
  1. 使用TodoWrite记录"阶段3第X轮循环"
  2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
  3. 记录初始迁移任务数量：统计需要迁移的数据表和记录数
  
  循环执行：
  变更实施 → 实时监控 → 数据验证 → 自动判断：
  
  继续循环条件（必须同时满足）：
  - 当前轮次 < 3轮
  - 迁移任务数量 > 0
  - 迁移过程中发现问题需要修正
  
  退出循环条件（满足任一）：
  - 所有迁移任务都已完成且验证通过 → 进入阶段4
  - 连续2轮迁移结果相同 → 进入阶段4
  - 达到3轮上限 → 生成当前迁移状态报告
  
  强制执行：
  - 每轮开始必须更新TodoWrite状态为in_progress
  - 每轮结束必须记录迁移完成度
  - 不允许停顿等待用户指令，必须自动进入下一轮
  ```

---

## **阶段4: 数据完整性验证与对账**

### **步骤4.1: 全面数据验证**

- **4.1.1 数据完整性验证**
  - **数据量验证**：
    ```sql
    -- 验证总记录数
    SELECT 
        'users_old' as table_name,
        COUNT(*) as record_count
    FROM users_old
    UNION ALL
    SELECT 
        'users_new' as table_name,
        COUNT(*) as record_count
    FROM users_new;
    
    -- 验证唯一性约束
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT id) as unique_ids
    FROM users_new;
    ```

- **4.1.2 数据一致性验证**
  - **字段对应验证**：
    ```sql
    -- 验证关键字段转换
    SELECT 
        old.user_id,
        old.username,
        old.user_email,
        new.id,
        new.username,
        new.email,
        CASE 
            WHEN old.user_id = new.id THEN 'OK'
            ELSE 'MISMATCH'
        END as id_check,
        CASE 
            WHEN old.username = new.username THEN 'OK'
            ELSE 'MISMATCH'
        END as username_check
    FROM users_old old
    LEFT JOIN users_new new ON old.user_id = new.id
    WHERE old.user_id != new.id 
       OR old.username != new.username
       OR old.user_email != new.email;
    ```

### **步骤4.2: 业务逻辑验证**

- **4.2.1 关联数据验证**
  - **外键关系验证**：
    ```sql
    -- 验证外键引用完整性
    SELECT 
        COUNT(*) as orphaned_records
    FROM orders o
    LEFT JOIN users_new u ON o.user_id = u.id
    WHERE u.id IS NULL;
    
    -- 验证反向关系
    SELECT 
        u.id,
        u.username,
        COUNT(o.id) as order_count
    FROM users_new u
    LEFT JOIN orders o ON u.id = o.user_id
    GROUP BY u.id, u.username
    HAVING COUNT(o.id) = 0;
    ```

- **4.2.2 业务规则验证**
  - **数据有效性验证**：
    ```python
    def validate_business_rules():
        # 验证邮箱格式
        invalid_emails = validate_email_format()
        if invalid_emails:
            raise ValidationError(f"无效邮箱: {invalid_emails}")
        
        # 验证用户名规则
        invalid_usernames = validate_username_rules()
        if invalid_usernames:
            raise ValidationError(f"无效用户名: {invalid_usernames}")
        
        # 验证数据范围
        validate_data_ranges()
        
        # 验证业务约束
        validate_business_constraints()
    ```

### **步骤4.3: 性能验证**

- **4.3.1 查询性能验证**
  - **索引效果验证**：
    ```sql
    -- 验证查询性能
    EXPLAIN SELECT * FROM users_new WHERE username = 'test_user';
    EXPLAIN SELECT * FROM users_new WHERE email = '<EMAIL>';
    
    -- 对比查询时间
    SELECT 
        'Before Migration' as phase,
        AVG(query_time) as avg_time
    FROM query_performance_old
    UNION ALL
    SELECT 
        'After Migration' as phase,
        AVG(query_time) as avg_time
    FROM query_performance_new;
    ```

### **步骤4.4: 验证结果对账**

- **4.4.1 对账报告生成**
  - **详细对账结果**：
    ```
    数据迁移对账报告
    
    1. 数据量对账
       - 原表记录数: 10,000
       - 新表记录数: 10,000
       - 差异记录数: 0
       - 对账状态: 通过
    
    2. 数据内容对账
       - 主键匹配率: 100%
       - 数据转换准确率: 100%
       - 数据完整性: 通过
       - 关联关系: 通过
    
    3. 性能对账
       - 查询性能: 提升 15%
       - 存储空间: 优化 8%
       - 索引效果: 正常
    ```

### **步骤4.5: 验证循环**

- **强制循环控制机制**：
  ```
  初始化：
  1. 使用TodoWrite记录"阶段4第X轮循环"
  2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
  3. 记录初始验证项数量：统计需要验证的数据和业务规则数
  
  循环执行：
  数据验证 → 业务验证 → 性能验证 → 对账报告 → 自动判断：
  
  继续循环条件（必须同时满足）：
  - 当前轮次 < 3轮
  - 验证项数量 > 0
  - 发现新的数据问题需要修正
  
  退出循环条件（满足任一）：
  - 所有验证项都通过且对账完成 → 进入阶段5
  - 连续2轮验证结果相同 → 进入阶段5
  - 达到3轮上限 → 生成最终验证报告
  
  强制执行：
  - 每轮开始必须更新TodoWrite状态为in_progress
  - 每轮结束必须记录验证通过率
  - 不允许停顿等待用户指令，必须自动进入下一轮
  ```

---

## **阶段5: 系统切换与回滚准备**

### **步骤5.1: 系统切换准备**

- **5.1.1 应用程序更新**
  - **代码更新**：
    ```python
    # 更新数据访问层
    class UserRepository:
        def get_user_by_id(self, user_id):
            # 更新查询语句使用新表结构
            query = "SELECT id, username, first_name, last_name, email FROM users_new WHERE id = %s"
            return self.db.execute(query, (user_id,))
        
        def get_user_by_username(self, username):
            query = "SELECT id, username, first_name, last_name, email FROM users_new WHERE username = %s"
            return self.db.execute(query, (username,))
    ```

- **5.1.2 配置更新**
  - **数据库配置更新**：
    ```python
    # 更新数据库配置
    DATABASE_CONFIG = {
        'tables': {
            'users': 'users_new',  # 指向新表
            'orders': 'orders',
            'products': 'products'
        }
    }
    ```

### **步骤5.2: 系统切换实施**

- **5.2.1 渐进式切换**
  - **分批切换策略**：
    ```python
    def gradual_cutover():
        # 第一阶段：只读操作切换到新表
        switch_read_operations_to_new_table()
        
        # 验证读操作正常
        validate_read_operations()
        
        # 第二阶段：写操作切换到新表
        switch_write_operations_to_new_table()
        
        # 验证写操作正常
        validate_write_operations()
        
        # 第三阶段：完全切换
        complete_cutover()
    ```

- **5.2.2 切换验证**
  - **功能验证**：
    ```
    1. 服务状态检测：curl -s -o /dev/null -w "%{http_code}" http://localhost:8000/docs
    2. 状态判断：
       - 如果返回200 → 服务正常，继续验证
       - 如果返回非200或连接失败 → 切换失败，需要回滚
    3. 启动服务：cd mock_server && timeout 10s python src/main.py --port 8000 &
    4. 等待就绪：sleep 3
    5. 测试所有核心功能
    6. 验证数据读写正常
    7. 测试性能指标
    ```

### **步骤5.3: 回滚准备与验证**

- **5.3.1 回滚方案准备**
  - **自动回滚机制**：
    ```python
    def prepare_rollback():
        # 保存当前状态
        save_current_state()
        
        # 准备回滚脚本
        prepare_rollback_scripts()
        
        # 验证回滚能力
        test_rollback_procedure()
        
        # 设置回滚触发条件
        set_rollback_triggers()
    ```

- **5.3.2 回滚验证**
  - **回滚能力测试**：
    ```python
    def test_rollback_capability():
        # 创建测试检查点
        create_test_checkpoint()
        
        # 执行测试变更
        perform_test_changes()
        
        # 执行回滚
        execute_rollback()
        
        # 验证回滚结果
        validate_rollback_result()
        
        # 确认系统状态恢复
        confirm_system_recovery()
    ```

### **步骤5.4: 最终验证循环**

- **强制循环控制机制**：
  ```
  初始化：
  1. 使用TodoWrite记录"阶段5第X轮循环"
  2. 设置循环计数器：当前轮次 = 1，最大轮次 = 3
  3. 记录初始切换任务数量：统计需要切换的系统组件数
  
  循环执行：
  切换准备 → 系统切换 → 回滚准备 → 验证 → 自动判断：
  
  继续循环条件（必须同时满足）：
  - 当前轮次 < 3轮
  - 切换任务数量 > 0
  - 发现切换问题需要修正
  
  退出循环条件（满足任一）：
  - 所有切换任务都已完成且验证通过 → 数据变更完成
  - 连续2轮切换结果相同 → 数据变更完成
  - 达到3轮上限 → 生成最终切换报告
  
  强制执行：
  - 每轮开始必须更新TodoWrite状态为in_progress
  - 每轮结束必须记录切换完成度
  - 不允许停顿等待用户指令，必须自动进入下一轮
  ```

### **步骤5.5: 变更总结与优化**

- **5.5.1 变更成果总结**
  - **变更统计**：
    ```
    数据变更成果报告
    
    1. 变更规模
       - 变更表数量: 3
       - 迁移记录数: 50,000
       - 变更字段数: 12
       - 影响应用数: 5
    
    2. 变更质量
       - 数据完整性: 100%
       - 业务连续性: 99.9%
       - 性能影响: 提升 10%
       - 用户满意度: 95%
    
    3. 变更效率
       - 计划执行率: 100%
       - 时间控制: 按时完成
       - 资源利用: 合理
       - 风险控制: 有效
    ```

- **5.5.2 经验总结与建议**
  - **最佳实践**：
    - 详细的变更计划制定
    - 充分的测试验证
    - 实时的监控机制
    - 完善的回滚准备
  
  - **改进建议**：
    - 自动化工具改进
    - 监控系统优化
    - 流程标准化
    - 团队能力提升

---

## ✅ **数据变更成功标准**

1. **数据完整性**: 100% - 所有数据必须完整无丢失
2. **数据一致性**: 100% - 变更后数据必须完全一致
3. **业务连续性**: 99.9% - 业务中断时间最小化
4. **功能兼容性**: 100% - 所有功能必须正常工作
5. **性能指标**: 不降低 - 性能必须保持或改善
6. **回滚能力**: 100% - 必须具备完整的回滚能力