---
description: 基于 `$ARGUMENTS` 问题描述，按照 严格证据驱动流程 执行诊断和修复
---

# 🎯 严格证据驱动流程

## 📊 全局状态管理

```python
# 积分系统
GLOBAL score = 0
GLOBAL evidence = {}
GLOBAL hypothesis = null
GLOBAL fix_attempts = 0
GLOBAL phase_history = []

# 常量定义
CONST MAX_HYPOTHESIS_RETRIES = 3
CONST MAX_FIX_RETRIES = 3
CONST FAILURE_THRESHOLD = -20
CONST SUCCESS_THRESHOLD = 100
CONST EXCELLENCE_THRESHOLD = 150

# 积分规则
CONST SCORE_RULES = {
    "complete_check": 5,
    "find_issue": 10,
    "locate_success": 20,
    "fix_success": 30,
    "skip_check": -10,
    "assume_without_evidence": -15,
    "fix_failure": -20,
    "repeat_error": -30,
    "fake_execution": -50
}
```

## 🚀 主执行流程

```python
FUNCTION main_diagnose_and_fix($ARGUMENTS):
    PRINT("🎯 开始诊断流程")
    PRINT(f"问题描述: {$ARGUMENTS}")
    PRINT(f"初始积分: {score}")
    
    TRY:
        # 阶段0：项目全景认知
        IF NOT execute_phase0_project_understanding():
            RETURN failure("项目认知失败")
        
        # 阶段1：强制证据收集
        IF NOT execute_phase1_evidence_collection():
            RETURN failure("证据收集失败")
        
        # 阶段2：逐层隔离验证
        problem_layer = execute_phase2_isolation()
        IF problem_layer IS NULL:
            RETURN failure("问题定位失败")
        
        # 阶段3：根因假设循环
        root_cause = execute_phase3_hypothesis_loop()
        IF root_cause IS NULL:
            RETURN failure("根因分析失败")
        
        # 阶段4：修复实施
        fix_result = execute_phase4_implementation(root_cause)
        IF NOT fix_result.success:
            RETURN failure("修复实施失败")
        
        # 阶段5：完整性确认
        IF NOT execute_phase5_validation(fix_result):
            RETURN failure("验证未通过")
        
        RETURN success()
        
    CATCH Exception as e:
        PRINT(f"❌ 流程异常: {e}")
        RETURN handle_exception(e)
```

## 📋 阶段0：项目全景认知

```python
FUNCTION execute_phase0_project_understanding():
    PRINT("\n=== 阶段0：项目全景认知 ===")
    phase_start_score = score
    
    # 检查项列表
    checks = [
        {"name": "项目识别", "func": check_project_structure},
        {"name": "技术栈确认", "func": check_tech_stack},
        {"name": "WSL环境确认", "func": check_wsl_environment},
        {"name": "业务流程理解", "func": check_business_logic},
        {"name": "数据库结构理解", "func": check_database_schema},
        {"name": "API接口理解", "func": check_api_endpoints},
        {"name": "配置环境理解", "func": check_configurations},
        {"name": "规范提取(方法17)", "func": method17_extract_rules},
        {"name": "服务验证(方法3)", "func": method3_verify_service}
    ]
    
    FOR check IN checks:
        PRINT(f"\n检查项: {check.name}")
        result = check.func()
        
        IF result.success:
            score += SCORE_RULES["complete_check"]
            PRINT(f"✅ {check.name} 完成 (+5分)")
            
            IF result.first_try:
                score += 5
                PRINT(f"✅ 一次通过 (+5分)")
        ELSE:
            score += SCORE_RULES["skip_check"]
            PRINT(f"❌ {check.name} 失败 (-10分)")
            
            # 重试逻辑
            PRINT(f"🔄 重试 {check.name}...")
            retry_result = retry_with_guidance(check.func, result.error)
            IF NOT retry_result.success:
                PRINT(f"❌ 重试失败，当前积分: {score}")
                IF score < FAILURE_THRESHOLD:
                    RETURN False
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段0完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN score > 0
```

## 🔍 阶段1：强制证据收集

```python
FUNCTION execute_phase1_evidence_collection():
    PRINT("\n=== 阶段1：强制证据收集 ===")
    phase_start_score = score
    retries = 0
    
    WHILE retries < 3:
        PRINT(f"\n第{retries + 1}次证据收集")
        
        # 强制并行执行
        PRINT("🔒 强制并行执行：方法6 + 方法8")
        parallel_results = PARALLEL_EXECUTE([
            method6_collect_logs,
            method8_check_environment
        ])
        
        # 等待并行结果
        logs = parallel_results[0]
        env_status = parallel_results[1]
        
        IF logs.success AND env_status.success:
            score += 10
            PRINT("✅ 并行收集成功 (+10分)")
        ELSE:
            score -= 15
            PRINT("❌ 并行收集失败 (-15分)")
            retries += 1
            CONTINUE
        
        # 串行执行症状分层
        PRINT("\n执行方法7：分层症状确认")
        symptoms = method7_layer_symptoms(logs.data, env_status.data)
        IF symptoms.success:
            score += 5
            evidence = symptoms.data
            PRINT("✅ 症状分层完成 (+5分)")
        ELSE:
            score -= 5
            PRINT("❌ 症状分层失败 (-5分)")
            retries += 1
            CONTINUE
        
        # 证据验证（方法22）
        PRINT("\n🚨 执行方法22：证据有效性验证")
        validation = method22_validate_evidence(evidence)
        
        IF validation.complete AND validation.relevant AND validation.specific:
            score += 10
            PRINT("✅ 证据验证通过 (+10分)")
            PRINT(f"  完整性: ✓ 相关性: ✓ 具体性: ✓")
            
            # 证据驱动分析（方法19）
            PRINT("\n🚨 执行方法19：证据驱动分析")
            IF can_analyze_with_evidence(evidence):
                score += 10
                PRINT("✅ 可以基于证据分析 (+10分)")
                BREAK
            ELSE:
                score -= 20
                PRINT("❌ 证据不足以分析 (-20分)")
                retries += 1
        ELSE:
            score -= 15
            PRINT("❌ 证据验证失败 (-15分)")
            PRINT(f"  完整性: {'✓' if validation.complete else '✗'}")
            PRINT(f"  相关性: {'✓' if validation.relevant else '✗'}")
            PRINT(f"  具体性: {'✓' if validation.specific else '✗'}")
            
            # 补充收集
            IF NOT validation.complete:
                PRINT("📋 需要补充日志范围")
                evidence = supplement_logs(evidence)
            IF NOT validation.relevant:
                PRINT("📋 需要更相关的证据")
                evidence = get_relevant_evidence(evidence)
            IF NOT validation.specific:
                PRINT("📋 需要更具体的信息")
                evidence = get_specific_details(evidence)
            
            retries += 1
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段1完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN retries < 3
```

## 🎯 阶段2：逐层隔离验证

```python
FUNCTION execute_phase2_isolation():
    PRINT("\n=== 阶段2：逐层隔离验证 ===")
    phase_start_score = score
    problem_layer = NULL
    
    # 并行验证
    PRINT("🔄 并行执行验证")
    parallel_checks = PARALLEL_EXECUTE([
        ["服务层", method3_service_check, method2_api_check],
        ["路径层", method9_path_verification, method10_data_trace]
    ])
    
    # 分析并行结果
    service_ok = parallel_checks[0][0].success AND parallel_checks[0][1].success
    path_ok = parallel_checks[1][0].success AND parallel_checks[1][1].success
    
    IF service_ok AND path_ok:
        score += 20
        PRINT("✅ 并行验证完成 (+20分)")
    ELSE:
        score += 10
        PRINT("⚠️ 部分验证失败 (+10分)")
    
    # 问题层次判断
    IF NOT problem_layer:
        PRINT("\n执行方法11：逐层隔离")
        layers = ["UI层", "API层", "业务逻辑层", "数据库层"]
        
        FOR layer IN layers:
            PRINT(f"\n检查 {layer}")
            layer_result = check_layer(layer)
            
            IF layer_result.has_problem:
                problem_layer = layer
                score += 15
                PRINT(f"✅ 定位到问题层：{layer} (+15分)")
                
                # 深入分析
                deep_analysis = analyze_layer_deeply(layer, evidence)
                IF deep_analysis.success:
                    score += 5
                    evidence.update(deep_analysis.data)
                    PRINT(f"✅ {layer}深入分析完成 (+5分)")
                BREAK
        
        IF NOT problem_layer:
            score -= 10
            PRINT("❌ 无法确定问题层次 (-10分)")
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段2完成，本阶段得分: {phase_score}，总积分: {score}")
    PRINT(f"问题定位：{problem_layer}")
    RETURN problem_layer
```

## 🔬 阶段3：根因假设循环

```python
FUNCTION execute_phase3_hypothesis_loop():
    PRINT("\n=== 阶段3：根因假设循环 ===")
    phase_start_score = score
    hypothesis_count = 0
    confirmed_root_cause = NULL
    
    # 单一根因优先（方法12）
    PRINT("执行方法12：单一根因优先")
    single_cause = method12_single_root_cause(evidence)
    IF single_cause.found:
        score += 5
        hypothesis = single_cause.hypothesis
        PRINT(f"✅ 识别到单一可能根因 (+5分): {hypothesis}")
    ELSE:
        hypothesis = generate_hypothesis_from_evidence(evidence)
        PRINT(f"📝 生成假设: {hypothesis}")
    
    # 假设验证循环（方法20）
    WHILE hypothesis_count < MAX_HYPOTHESIS_RETRIES AND NOT confirmed_root_cause:
        hypothesis_count += 1
        PRINT(f"\n🚨 执行方法20：假设验证循环 (第{hypothesis_count}次)")
        
        # 代码逻辑验证（方法13）
        PRINT("执行方法13：代码逻辑验证")
        code_check = method13_verify_code_logic(hypothesis)
        IF code_check.confirms_hypothesis:
            score += 5
            PRINT("✅ 代码逻辑支持假设 (+5分)")
        ELSE:
            PRINT("❌ 代码逻辑不支持假设")
        
        # 设计验证方案
        PRINT("\n设计验证方案:")
        verification_plan = design_verification(hypothesis)
        PRINT(f"  验证命令: {verification_plan.command}")
        PRINT(f"  预期结果: {verification_plan.expected}")
        score += 5
        
        # 执行验证
        PRINT("\n执行验证...")
        result = EXECUTE_COMMAND(verification_plan.command)
        PRINT(f"实际结果: {result}")
        
        # 判断验证结果
        IF result == verification_plan.expected:
            score += 20
            confirmed_root_cause = hypothesis
            PRINT(f"✅ 假设验证成功！(+20分)")
            PRINT(f"确认根因: {confirmed_root_cause}")
        ELIF partially_matches(result, verification_plan.expected):
            score += 5
            PRINT("⚠️ 部分匹配，细化假设 (+5分)")
            hypothesis = refine_hypothesis(hypothesis, result)
        ELSE:
            score -= 10
            PRINT("❌ 假设验证失败 (-10分)")
            hypothesis = generate_new_hypothesis(evidence, result)
            PRINT(f"新假设: {hypothesis}")
    
    IF NOT confirmed_root_cause:
        score -= 20
        PRINT("❌ 无法确认根因 (-20分)")
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段3完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN confirmed_root_cause
```

## 🛠️ 阶段4：修复实施

```python
FUNCTION execute_phase4_implementation(root_cause):
    PRINT("\n=== 阶段4：修复实施 ===")
    phase_start_score = score
    fix_result = {"success": False}
    attempts = 0
    
    # 修复准备（并行）
    PRINT("🔄 并行准备修复")
    prep_results = PARALLEL_EXECUTE([
        method17_extract_relevant_rules,  # 规范提取
        method5_check_duplicate_functions  # 功能重复检查
    ])
    
    rules = prep_results[0]
    duplicates = prep_results[1]
    
    IF rules.success AND duplicates.success:
        score += 10
        PRINT("✅ 修复准备完成 (+10分)")
        PRINT(f"  适用规范: {rules.data}")
        PRINT(f"  可复用函数: {duplicates.data}")
    
    WHILE attempts < MAX_FIX_RETRIES AND NOT fix_result.success:
        attempts += 1
        PRINT(f"\n第{attempts}次修复尝试")
        
        # 实施修复
        fix_plan = create_fix_plan(root_cause, rules.data, duplicates.data)
        PRINT(f"修复方案: {fix_plan.description}")
        
        # 备份原文件
        backup_files(fix_plan.affected_files)
        
        # 应用修复
        apply_result = apply_fix(fix_plan)
        IF NOT apply_result.success:
            score -= 10
            PRINT(f"❌ 修复应用失败 (-10分): {apply_result.error}")
            restore_backup()
            CONTINUE
        
        # 强制立即验证
        PRINT("\n🔒 强制立即验证")
        verification = PARALLEL_EXECUTE([
            method4_code_effect_check,    # 代码生效验证
            method1_execution_check,       # 执行验证
            method2_api_verification       # API验证
        ])
        
        all_passed = ALL(v.success for v in verification)
        
        IF all_passed:
            score += 30
            fix_result = {"success": True, "details": fix_plan}
            PRINT("✅ 修复验证通过！(+30分)")
        ELSE:
            # 分析失败类型
            failure_type = analyze_failure(verification)
            
            IF failure_type == "TECHNICAL":
                score -= 10
                PRINT("❌ 技术性失败，调整实现 (-10分)")
                fix_plan = adjust_technical_approach(fix_plan)
            ELIF failure_type == "LOGICAL":
                score -= 20
                PRINT("❌ 逻辑性失败，返回阶段3 (-20分)")
                RETURN execute_phase3_hypothesis_loop()
            ELSE:  # FUNDAMENTAL
                score -= 30
                PRINT("❌ 根本性失败，返回阶段1 (-30分)")
                RETURN execute_phase1_evidence_collection()
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段4完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN fix_result
```

## ✅ 阶段5：完整性确认

```python
FUNCTION execute_phase5_validation(fix_result):
    PRINT("\n=== 阶段5：完整性确认 ===")
    phase_start_score = score
    all_tests_passed = True
    
    # 原问题验证（方法14）
    PRINT("执行方法14：修复效果验证")
    original_test = method14_verify_original_problem()
    IF original_test.success:
        score += 10
        PRINT("✅ 原问题已解决 (+10分)")
    ELSE:
        score -= 15
        all_tests_passed = False
        PRINT("❌ 原问题未解决 (-15分)")
    
    # 并行完整性测试
    PRINT("\n🔄 并行执行完整性测试")
    integrity_tests = PARALLEL_EXECUTE([
        method15_functional_integrity,  # 功能完整性
        method16_permanent_solution     # 根本解决确认
    ])
    
    FOR test IN integrity_tests:
        IF test.success:
            score += 5
            PRINT(f"✅ {test.name} 通过 (+5分)")
        ELSE:
            all_tests_passed = False
            PRINT(f"❌ {test.name} 失败")
    
    # 合规性验证（方法18）
    PRINT("\n执行方法18：合规性验证")
    compliance = method18_compliance_check(fix_result.details)
    IF compliance.success:
        score += 5
        PRINT("✅ 符合项目规范 (+5分)")
    ELSE:
        all_tests_passed = False
        PRINT(f"❌ 违反规范: {compliance.violations}")
    
    # 最终判定
    IF NOT all_tests_passed:
        PRINT("\n🚨 执行方法21：失败原因追溯")
        failure_analysis = method21_trace_failure()
        
        IF failure_analysis.type == "INCOMPLETE_FIX":
            score -= 10
            PRINT("📍 修复不完整，返回阶段4")
            RETURN execute_phase4_implementation(root_cause)
        ELIF failure_analysis.type == "WRONG_ROOT_CAUSE":
            score -= 20
            PRINT("📍 根因错误，返回阶段3")
            RETURN execute_phase3_hypothesis_loop()
        ELSE:  # INSUFFICIENT_EVIDENCE
            score -= 30
            PRINT("📍 证据不足，返回阶段1")
            RETURN execute_phase1_evidence_collection()
    
    phase_score = score - phase_start_score
    PRINT(f"\n阶段5完成，本阶段得分: {phase_score}，总积分: {score}")
    RETURN all_tests_passed
```

## 📋 方法函数定义

```python
# 方法1：powershell.exe执行验证
FUNCTION method1_execution_check():
    # 在WSL环境下执行Windows程序，验证AI修改的代码是否按预期工作
    IF is_wsl_environment():
        # 转换路径并执行Windows程序
        windows_path = convert_to_windows_path(current_directory)
        command = construct_powershell_command(windows_path, test_scenario)
        result = EXECUTE_COMMAND(command)
        
        # 读取相关日志验证
        log_content = READ_FILE(determine_log_path())
        RETURN analyze_execution_result(result, log_content)
    ELSE:
        # 非WSL环境的直接执行
        RETURN direct_execution_check()

# 方法2：API请求验证
FUNCTION method2_api_verification():
    # 直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果
    endpoints = determine_api_endpoints_to_test()
    results = []
    
    FOR endpoint IN endpoints:
        # 构建并执行API请求
        response = execute_api_request(endpoint)
        validation = validate_api_response(response, endpoint.expected_behavior)
        results.append(validation)
    
    RETURN aggregate_api_test_results(results)

# 方法3：服务状态验证
FUNCTION method3_service_check():
    # 检查服务进程的真实运行状态，避免假设服务可用性
    checks = {
        "port": check_service_port_listening(),
        "health": check_service_health_endpoint(),
        "process": check_service_process_running()
    }
    
    # 综合判断服务状态
    service_status = analyze_service_health(checks)
    RETURN service_status

# 方法4：代码生效验证
FUNCTION method4_code_effect_check():
    # 确认代码修改已实际应用并被执行，避免假设修改生效
    # 静态验证：检查文件变更
    static_check = verify_file_changes_exist()
    IF NOT static_check.success:
        RETURN static_check
    
    # 动态验证：确认执行路径
    dynamic_check = verify_code_execution_path(static_check.changed_files)
    RETURN combine_verification_results(static_check, dynamic_check)

# 方法5：功能重复性检查
FUNCTION method5_check_duplicate_functions():
    # 开发前搜索现有代码库，避免重复实现已存在的功能模块
    search_patterns = extract_function_patterns(current_task)
    duplicates = []
    
    # 多维度搜索
    duplicates.extend(search_by_function_name(search_patterns))
    duplicates.extend(search_by_business_logic(search_patterns))
    duplicates.extend(search_similar_implementations())
    duplicates.extend(search_utility_functions())
    
    RETURN {"success": True, "data": unique(duplicates), "score": calculate_score(duplicates)}

# 方法6：日志优先症状收集
FUNCTION method6_collect_logs():
    # 优先查看错误日志和异常堆栈，获取最直接证据
    log_data = {"errors": [], "warnings": [], "info": [], "recent": []}
    
    # 查找并收集各级别日志
    log_files = find_relevant_log_files()
    IF NOT log_files:
        RETURN {"success": False, "error": "无日志文件", "score": -5}
    
    # 按优先级收集
    log_data["errors"] = collect_error_logs(log_files)
    log_data["warnings"] = collect_warning_logs(log_files)
    log_data["recent"] = collect_recent_logs(log_files)
    
    # 判断证据价值
    has_valuable_evidence = evaluate_log_evidence(log_data)
    RETURN {"success": has_valuable_evidence, "data": log_data, "score": calculate_score(has_valuable_evidence)}

# 方法7：用户症状分层确认
FUNCTION method7_layer_symptoms(logs, env_status):
    # 将用户描述分为表象→系统→根因三层，逐层收集症状
    symptoms = initialize_symptom_layers()
    
    # 表象层：用户看到的问题
    symptoms["surface"] = extract_surface_symptoms(problem_description)
    
    # 系统层：从日志和环境提取
    symptoms["system"] = extract_system_symptoms(logs, env_status)
    
    # 根因层：技术层面线索
    symptoms["root"] = infer_root_cause_clues(symptoms["system"])
    
    RETURN {"success": validate_symptoms(symptoms), "data": symptoms, "score": 5}

# 方法8：系统环境状态检查
FUNCTION method8_check_environment():
    # 并行检查服务状态、配置文件、依赖环境的实际状态
    env_checks = {
        "processes": check_running_processes(),
        "ports": check_listening_ports(),
        "resources": check_system_resources(),
        "configs": check_configuration_files(),
        "dependencies": check_dependencies_status()
    }
    
    # 并行执行所有检查
    results = PARALLEL_EXECUTE(env_checks.values())
    RETURN {"success": True, "data": compile_environment_report(results), "score": 5}

# 方法9：执行路径反向确认
FUNCTION method9_path_verification():
    # 通过输出特征和日志模式反向定位真实执行的代码路径
    IF NOT evidence.get("errors"):
        RETURN {"success": False, "error": "无错误信息", "score": -5}
    
    paths = []
    FOR error IN evidence["errors"]:
        # 从错误追溯到代码
        code_locations = trace_error_to_code(error)
        FOR location IN code_locations:
            IF verify_location_matches_error(location, error):
                paths.append(create_path_mapping(error, location))
    
    RETURN {"success": len(paths) > 0, "data": paths, "score": calculate_score(paths)}

# 方法10：数据流完整追踪
FUNCTION method10_data_trace():
    # 追踪数据从输入到输出的完整生命周期，识别异常节点
    IF NOT problem_layer:
        RETURN {"success": False, "error": "未确定问题层", "score": -5}
    
    # 构建数据流追踪链
    trace_chain = []
    trace_chain.append(trace_data_input())
    trace_chain.append(trace_data_transmission())
    trace_chain.append(trace_data_processing())
    trace_chain.append(trace_data_storage())
    trace_chain.append(trace_data_output())
    
    # 识别异常节点
    anomalies = identify_anomalies_in_chain(trace_chain)
    
    RETURN {
        "success": len(trace_chain) > 2,
        "data": {"trace": trace_chain, "anomalies": anomalies},
        "score": 5
    }

# 方法11：逐层隔离定位
FUNCTION method11_layer_isolation():
    # 按系统架构层次逐层隔离问题，避免跨层复杂化分析
    layers = define_system_layers()
    problem_layers = []
    
    FOR layer IN layers:
        PRINT(f"\n隔离检查：{layer.name}")
        isolation_result = perform_layer_isolation_test(layer)
        
        IF isolation_result.has_problem:
            problem_layers.append({
                "layer": layer.name,
                "issues": isolation_result.issues,
                "evidence": isolation_result.evidence
            })
            PRINT(f"❌ {layer.name} 存在问题")
        ELSE:
            PRINT(f"✅ {layer.name} 正常")
    
    # 确定主要问题层
    RETURN determine_primary_problem_layer(problem_layers)

# 方法12：单一根因优先
FUNCTION method12_single_root_cause(evidence):
    # 优先查找单一明确的根因，避免多因素复杂化假设
    # 定义常见单一根因模式
    single_cause_patterns = define_common_single_causes()
    
    # 在证据中搜索匹配
    FOR pattern IN single_cause_patterns:
        matches = search_pattern_in_evidence(evidence, pattern)
        IF matches AND matches.confidence > 0.8:
            RETURN {
                "found": True,
                "hypothesis": pattern.cause,
                "evidence": matches,
                "confidence": "high"
            }
    
    # 未找到明确单一原因
    RETURN {"found": False, "hypothesis": None, "confidence": "low"}

# 方法13：代码逻辑直接验证
FUNCTION method13_verify_code_logic(hypothesis):
    # 基于实际执行路径验证代码逻辑，不分析未执行代码
    IF NOT evidence.get("code_paths"):
        RETURN {"confirms_hypothesis": False, "reason": "无代码路径"}
    
    confirmations = []
    # 只检查确认执行的代码
    executed_paths = filter_executed_paths(evidence["code_paths"])
    
    FOR path IN executed_paths:
        code_content = READ_FILE(path.file)
        # 根据假设类型进行针对性验证
        logic_match = verify_hypothesis_in_code(hypothesis, code_content, path)
        IF logic_match:
            confirmations.append(logic_match)
    
    RETURN {
        "confirms_hypothesis": len(confirmations) > 0,
        "confirmations": confirmations
    }

# 方法14：修复效果实时验证
FUNCTION method14_verify_original_problem():
    # 修复后立即通过相同方法验证问题是否解决
    PRINT("重现原始问题场景...")
    
    # 使用原始触发条件
    original_scenario = reconstruct_original_scenario(problem_description)
    test_result = execute_scenario_test(original_scenario)
    
    # 检查原始错误是否消失
    original_errors = evidence.get("errors", [])
    errors_resolved = verify_errors_resolved(test_result, original_errors)
    
    # 验证功能恢复正常
    functionality_test = verify_functionality_restored(problem_description)
    
    RETURN {
        "success": errors_resolved AND functionality_test.passed,
        "data": {
            "errors_resolved": errors_resolved,
            "functionality": functionality_test,
            "test_output": test_result
        }
    }

# 方法15：功能完整性测试
FUNCTION method15_functional_integrity():
    # 验证修复不影响相关功能的正常工作
    affected_features = identify_affected_features(fix_result)
    test_results = []
    
    # 分层测试
    test_results.extend(test_direct_features(affected_features["direct"]))
    test_results.extend(test_indirect_features(affected_features["indirect"]))
    test_results.extend(test_core_business_flows())
    
    all_passed = ALL(test.passed for test in test_results)
    
    RETURN {
        "success": all_passed,
        "data": test_results,
        "name": "功能完整性测试"
    }

# 方法16：根本解决确认
FUNCTION method16_permanent_solution():
    # 确认修复解决了原始根本问题，不是表面修复
    test_rounds = 3
    results = []
    
    FOR i IN RANGE(test_rounds):
        PRINT(f"第{i+1}轮根本解决测试...")
        
        # 重建测试环境
        prepare_clean_test_environment()
        
        # 执行测试
        test_result = execute_root_cause_test(confirmed_root_cause)
        results.append(evaluate_test_result(test_result))
    
    # 验证根因是否被消除
    root_cause_eliminated = verify_root_cause_elimination(confirmed_root_cause)
    all_rounds_passed = ALL(r.success for r in results)
    
    RETURN {
        "success": all_rounds_passed AND root_cause_eliminated,
        "data": {
            "test_rounds": results,
            "root_cause_eliminated": root_cause_eliminated
        },
        "name": "根本解决确认"
    }

# 方法17：规范动态提取应用
FUNCTION method17_extract_relevant_rules():
    # 根据问题和修改代码类型，从CLAUDE.md提取相关规范约束
    rules_file = locate_rules_file()
    IF NOT rules_file:
        RETURN {"success": False, "error": "规范文件不存在", "score": -10}
    
    rules_content = READ_FILE(rules_file)
    relevant_rules = []
    
    # 多维度匹配规范
    relevant_rules.extend(match_by_problem_domain(rules_content, problem_description))
    relevant_rules.extend(match_by_code_type(rules_content, fix_result))
    relevant_rules.extend(match_by_architecture_layer(rules_content, problem_layer))
    
    RETURN {
        "success": len(relevant_rules) > 0,
        "data": unique(relevant_rules),
        "score": 5
    }

# 方法18：修复合规性验证
FUNCTION method18_compliance_check(fix_details):
    # 确保所有修复完全符合项目技术规范和架构原则
    violations = []
    rules = method17_extract_relevant_rules().data
    
    IF NOT fix_details OR NOT fix_details.get("changes"):
        RETURN {"success": False, "violations": ["无修复详情"]}
    
    # 多维度合规检查
    FOR change IN fix_details["changes"]:
        violations.extend(check_code_style_compliance(change, rules))
        violations.extend(check_naming_conventions(change, rules))
        violations.extend(check_architecture_constraints(change, rules))
        violations.extend(check_business_rules(change, rules))
    
    RETURN {
        "success": len(violations) == 0,
        "violations": violations
    }

# 方法19：证据驱动分析
FUNCTION method19_evidence_driven_analysis(evidence):
    # 所有分析必须基于实际收集的证据，禁止无证据推理
    # 检查证据充分性
    evidence_assessment = assess_evidence_quality(evidence)
    
    IF NOT evidence_assessment.sufficient:
        RETURN {
            "can_analyze": False,
            "reason": evidence_assessment.missing_types
        }
    
    # 构建基于证据的分析基础
    analysis_foundation = build_analysis_foundation(evidence)
    
    RETURN {
        "can_analyze": True,
        "analysis_base": analysis_foundation
    }

# 方法20：假设验证循环
FUNCTION method20_hypothesis_verification(hypothesis):
    # 每个根因假设都必须通过实际验证才能确认
    verification_plan = design_verification_plan(hypothesis)
    
    # 执行验证计划
    results = []
    FOR test IN verification_plan.tests:
        test_result = execute_verification_test(test)
        validation = validate_test_result(test_result, test.expected)
        results.append({
            "test": test.name,
            "passed": validation.passed,
            "output": test_result
        })
    
    all_passed = ALL(r.passed for r in results)
    
    RETURN {
        "verified": all_passed,
        "plan": verification_plan,
        "results": results
    }

# 方法21：失败原因追溯
FUNCTION method21_trace_failure():
    # 修复失败后必须基于新证据重新分析，不重复原方案
    failure_evidence = collect_failure_evidence()
    
    # 分析失败类型
    failure_type = analyze_failure_pattern(failure_evidence)
    
    SWITCH failure_type:
        CASE "STILL_HAS_ORIGINAL_ERROR":
            RETURN determine_why_fix_ineffective(failure_evidence)
        CASE "NEW_ERROR_INTRODUCED":
            RETURN analyze_new_error_source(failure_evidence)
        CASE "DIFFERENT_SYMPTOM":
            RETURN trace_symptom_change(failure_evidence)
        CASE "NO_CLEAR_EVIDENCE":
            RETURN {"type": "INSUFFICIENT_EVIDENCE", "reason": "需要更多诊断信息"}
        DEFAULT:
            RETURN {"type": "UNKNOWN", "reason": "需要人工介入"}

# 方法22：证据有效性验证
FUNCTION method22_validate_evidence(evidence):
    # 验证收集的证据是否满足分析要求，确保证据质量合格
    validation = {
        "complete": False,
        "relevant": False,
        "specific": False,
        "missing": []
    }
    
    # 完整性检查
    validation["complete"] = check_evidence_completeness(evidence)
    IF NOT validation["complete"]:
        validation["missing"].extend(identify_missing_evidence_types(evidence))
    
    # 相关性检查
    validation["relevant"] = check_evidence_relevance(evidence, problem_description)
    IF NOT validation["relevant"]:
        validation["missing"].append("与问题直接相关的证据")
    
    # 具体性检查
    validation["specific"] = check_evidence_specificity(evidence)
    IF NOT validation["specific"]:
        validation["missing"].append("具体的错误代码或行号")
    
    RETURN validation
```

## 🎮 执行控制器

```python
# 并行执行器
FUNCTION PARALLEL_EXECUTE(function_list):
    # 创建并管理并行执行的任务
    threads = []
    results = []
    
    FOR func IN function_list:
        thread = CREATE_THREAD(func)
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    FOR thread IN threads:
        thread.join()
        results.append(thread.result)
    
    RETURN results

# 命令执行器
FUNCTION EXECUTE_COMMAND(command):
    # 执行系统命令并捕获输出
    PRINT(f"$ {command}")
    result = system_execute(command)
    PRINT(result.output[:500])  # 显示前500字符
    RETURN result

# 积分监控器
FUNCTION check_score():
    # 实时监控积分状态
    PRINT(f"\n💯 当前积分: {score}")
    
    IF score < FAILURE_THRESHOLD:
        PRINT("❌ 积分过低，强制重启流程")
        RETURN restart_process()
    ELIF score > EXCELLENCE_THRESHOLD:
        PRINT("🌟 表现卓越！")
    ELIF score > SUCCESS_THRESHOLD:
        PRINT("✅ 表现优秀！")

# 异常处理器
FUNCTION handle_exception(exception):
    # 统一处理各类异常
    IF exception.type == "SCORE_TOO_LOW":
        RETURN restart_process()
    ELIF exception.type == "MAX_RETRIES_EXCEEDED":
        RETURN escalate_to_human()
    ELSE:
        log_error(exception)
        RETURN failure(f"未处理异常: {exception}")
```
