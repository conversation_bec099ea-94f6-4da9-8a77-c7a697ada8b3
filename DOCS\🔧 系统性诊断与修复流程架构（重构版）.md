🔧 系统性诊断与修复流程架构（重构版）

  第一阶段：问题定位与诊断规划

  1. 问题域分析

  ├── 现象提取与分类
  │   ├── 提取用户描述、日志、错误信息的关键特征
  │   ├── 区分"实际现象" vs "期望行为" vs "系统设计意图"
  │   ├── 建立问题严重程度分级（致命/严重/中等/轻微）
  │   └── 确定问题影响范围边界
  │
  ├── 环境上下文建模
  │   ├── 快速识别系统架构模式和组件关系
  │   ├── 定位问题相关的核心文件类型和代码区域
  │   ├── 建立时间线：操作序列、版本变更、数据状态变迁
  │   └── 估算诊断复杂度和修复成本
  │
  └── 诊断策略制定
      ├── 确定诊断路径和优先级顺序
      ├── 选择高效的数据回溯策略
      ├── 制定根因验证标准和修复验收条件
      └── 设计问题复现和测试方案

  2. 系统架构探索

  - 组件关系映射：快速识别涉及的文件、类、方法及其依赖关系
  - 数据流路径分析：建立从用户操作到数据存储的完整链路图
  - 状态检查点定义：确定关键的状态验证节点

  3. 诊断复杂度评估

  - 影响域大小：单组件/多组件/跨系统问题
  - 数据一致性风险：只读问题/数据损坏/状态不一致
  - 修复难度预估：配置修改/代码逻辑修复/架构重构

  第二阶段：系统化证据收集与分析

  1. 核心证据快速收集

  ├── 使用工具并行收集关键证据
  │   ├── 日志分析：错误日志、业务日志、系统日志的模式识别
  │   ├── 代码扫描：定位问题实施的核心文件和方法
  │   ├── 数据状态：收集各层级的数据快照和状态信息
  │   └── 环境信息：版本、配置、依赖的一致性检查
  │
  ├── 数据流回溯验证
  │   ├── 从异常点向上游追溯数据来源和处理链路
  │   ├── 识别每个处理环节的输入输出和转换逻辑
  │   ├── 定位数据损坏或丢失的具体环节
  │   └── 验证数据流的完整性和正确性
  │
  └── 状态一致性检查
      ├── 对比不同层级（UI/API/缓存/数据库）的数据状态
      ├── 发现状态不一致的具体环节和数据项
      ├── 分析状态不一致的产生原因和传播路径
      └── 评估状态不一致对业务功能的影响

  2. 分层深度验证

  - 配置层：环境变量、配置文件、系统参数的一致性
  - 应用层：业务逻辑、数据处理、接口调用的正确性
  - 数据层：数据库状态、缓存状态、文件系统状态
  - 接口层：API调用、消息传递、外部系统集成
  - 基础设施层：网络、存储、计算资源的可用性

  3. 边界案例检查

  - 异常处理路径：错误处理逻辑的完整性和正确性
  - 并发场景：多用户、多线程、分布式环境下的行为
  - 边界条件：空值、最大值、异常输入的处理
  - 降级机制：系统在部分故障时的降级行为

  第三阶段：根因识别与修复实施

  1. 根因假设与验证

  ├── 根因模式识别
  │   ├── 直接根因：代码逻辑错误、配置错误、数据损坏
  │   ├── 间接根因：架构设计缺陷、并发控制问题、资源不足
  │   ├── 潜在根因：隐藏的依赖关系、时序问题、环境差异
  │   └── 系统性根因：设计理念错误、技术选型不当、流程缺陷
  │
  ├── 影响评估矩阵
  │   ├── 功能影响：对业务功能的直接影响和用户体验
  │   ├── 数据影响：对数据完整性和一致性的影响
  │   ├── 性能影响：对系统性能和稳定性的影响
  │   └── 风险影响：对系统安全性和可维护性的影响
  │
  └── 根因验证策略
      ├── 实验验证：构造最小复现场景验证根因假设
      ├── 代码分析：通过代码审查确认逻辑错误
      ├── 数据验证：通过数据分析确认状态不一致
      └── 环境对比：通过环境差异分析确认配置问题

  2. 修复方案设计

  - 最小化修复原则：基于根因设计最小改动方案，复用现有架构
  - 风险控制机制：评估修复方案的副作用和回归风险
  - 回滚准备：设计修复失败时的快速回滚方案

  3. 多层验证与质量保证

  - 语法检查：代码语法、配置格式、数据结构的正确性
  - 逻辑验证：业务逻辑、数据流、状态转换的正确性
  - 集成测试：端到端功能、跨组件协作、外部依赖的验证
  - 回归测试：确保修复不影响现有功能
  - 性能测试：确保修复不影响系统性能