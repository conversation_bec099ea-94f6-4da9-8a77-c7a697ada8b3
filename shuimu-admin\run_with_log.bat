@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
set PYTHONLEGACYWINDOWSUTF8MODE=1
set PYTHONUNBUFFERED=1

REM Fix: Ensure running in correct working directory
cd /d "D:\01-shuimu_01\shuimu-admin"
echo Working directory set to: %CD%

REM Generate timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

echo Starting with log recording...

REM Call server API to clear server logs only (uvicorn.log is locked by process)
echo Requesting server to clear server logs via API...
curl -X POST -s -w "%%{http_code}" --connect-timeout 3 --max-time 10 http://localhost:8000/api/admin/v1/logs/clear -o nul 2>nul > temp_http_status.txt
set /p HTTP_STATUS=<temp_http_status.txt
del temp_http_status.txt 2>nul
if "%HTTP_STATUS%"=="200" (
    echo Server logs cleared successfully via API (server.log only)
    echo Note: uvicorn.log is cleared only when server restarts
) else (
    if "%HTTP_STATUS%"=="000" (
        echo Server log clear API request failed: server not running
    ) else (
        echo Server log clear API request failed: HTTP %HTTP_STATUS%
    )
)

echo Creating log directory...
if not exist "D:\01-shuimu_01\logs" mkdir "D:\01-shuimu_01\logs"

echo Activating virtual environment...
call venv\Scripts\activate.bat
if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to activate virtual environment
    echo Please run fix_venv.bat to rebuild the virtual environment
    pause
    exit /b 1
)

REM Fix: Detect virtual environment path conflicts
echo Checking virtual environment integrity...
python -c "import sys; print('Python executable:', sys.executable)" | findstr /C:"venv\Scripts\python.exe" >nul
if %ERRORLEVEL% neq 0 (
    echo WARNING: Virtual environment path conflict detected
    echo Please run fix_venv.bat to rebuild the virtual environment
    pause
    exit /b 1
)

echo Virtual environment activated
echo Checking Python...
python --version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Starting Python application...
echo [%date% %time%] === NEW ADMIN SESSION START === > "D:\01-shuimu_01\logs\admin.log"
python -u src\main.py >> "D:\01-shuimu_01\logs\admin.log" 2>&1

echo Admin log saved to: D:\01-shuimu_01\logs\admin.log
pause 