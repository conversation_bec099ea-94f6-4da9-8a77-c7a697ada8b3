# AI验证方法组件库
> 基于事实驱动的验证方法，避免AI凭推理假设进行开发

## 方法1：powershell.exe执行验证
通过powershell.exe执行Windows命令并读取日志（/logs）输出，验证代码功能的真实执行效果。
- 命令格式：`powershell.exe "cd 目标目录; 执行命令"` + `Read 日志文件`

## 方法2：API请求验证  
直接调用API接口并分析返回数据，验证业务逻辑和数据处理的实际结果。
- 命令格式：`curl -X GET API端点 | 数据验证脚本`

## 方法3：服务状态验证
检查服务进程的真实运行状态，避免假设服务可用性。
- 命令格式：`curl -s 服务端点 && echo "运行中" || echo "离线"`

## 方法4：代码生效验证
确认代码修改已实际应用到运行环境，避免假设修改生效。
- 命令格式：`grep "关键代码" 文件路径` + `API调用验证变更`

## 方法5：功能重复性验证
开发前搜索现有代码库，避免重复实现已存在的功能模块。
- 命令格式：`Grep "功能关键词" 项目目录`

## 核心原则
所有验证必须基于可观测的执行结果，禁止基于推理或假设进行后续开发决策。