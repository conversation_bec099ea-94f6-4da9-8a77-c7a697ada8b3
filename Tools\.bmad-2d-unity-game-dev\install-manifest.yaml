version: 1.2.0
installed_at: '2025-07-23T22:09:56.280Z'
install_type: expansion-pack
expansion_pack_id: bmad-2d-unity-game-dev
expansion_pack_name: bmad-2d-unity-game-dev
ides_setup:
  - cursor
  - claude-code
  - windsurf
  - trae
  - roo
  - cline
  - gemini
  - github-copilot
files:
  - path: .bmad-2d-unity-game-dev/config.yaml
    hash: ab83f05ce22988a6
    modified: false
  - path: .bmad-2d-unity-game-dev/workflows/game-prototype.yaml
    hash: 9a21965193f2b4fe
    modified: false
  - path: .bmad-2d-unity-game-dev/workflows/game-dev-greenfield.yaml
    hash: 12e261d9905eac5e
    modified: false
  - path: .bmad-2d-unity-game-dev/utils/workflow-management.md
    hash: b148df3ebb1f9c61
    modified: false
  - path: .bmad-2d-unity-game-dev/utils/bmad-doc-template.md
    hash: 4b2f7c4408835b9e
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/kb-mode-interaction.md
    hash: 9c73e5ff25ef4890
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/game-design-brainstorming.md
    hash: fe608dd7b1cbfe82
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/facilitate-brainstorming-session.md
    hash: 59e519c1d195c3e3
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/execute-checklist.md
    hash: 35c19b12c411eda4
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/document-project.md
    hash: 62495d0979bb6924
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/create-game-story.md
    hash: bddef3e38ec41c79
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/create-doc.md
    hash: 395719b8a002f7f9
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/create-deep-research-prompt.md
    hash: 5716b19ae78b3afb
    modified: false
  - path: .bmad-2d-unity-game-dev/tasks/advanced-elicitation.md
    hash: 37632703e41499bf
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/project-brief-tmpl.yaml
    hash: cd4b269b0722c361
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/market-research-tmpl.yaml
    hash: 949ab9c006cfaf6f
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/level-design-doc-tmpl.yaml
    hash: 02bcb36646829b18
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/game-story-tmpl.yaml
    hash: 8b29b35907b6cea3
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/game-design-doc-tmpl.yaml
    hash: 9fe5fb2bd43c1945
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/game-brief-tmpl.yaml
    hash: b31b863cafcdbb09
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/game-architecture-tmpl.yaml
    hash: db9c563d1925d04c
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/competitor-analysis-tmpl.yaml
    hash: b58b108e14dac04b
    modified: false
  - path: .bmad-2d-unity-game-dev/templates/brainstorming-output-tmpl.yaml
    hash: e4261b61b915ee9b
    modified: false
  - path: .bmad-2d-unity-game-dev/data/elicitation-methods.md
    hash: 6c4d7716010e8d55
    modified: false
  - path: .bmad-2d-unity-game-dev/data/development-guidelines.md
    hash: c3772f59471e827d
    modified: false
  - path: .bmad-2d-unity-game-dev/data/brainstorming-techniques.md
    hash: 2dae43f4464f1ad2
    modified: false
  - path: .bmad-2d-unity-game-dev/data/bmad-kb.md
    hash: 546232939a730bb6
    modified: false
  - path: .bmad-2d-unity-game-dev/checklists/game-story-dod-checklist.md
    hash: 7afc030630c69069
    modified: false
  - path: .bmad-2d-unity-game-dev/checklists/game-design-checklist.md
    hash: bb5cacdc95c312c7
    modified: false
  - path: .bmad-2d-unity-game-dev/agent-teams/unity-2d-game-team.yaml
    hash: cc53f49815c985dc
    modified: false
  - path: .bmad-2d-unity-game-dev/agents/game-sm.md
    hash: c16f3195dfb1b21e
    modified: false
  - path: .bmad-2d-unity-game-dev/agents/game-developer.md
    hash: 2a81363aa7eec2fb
    modified: false
  - path: .bmad-2d-unity-game-dev/agents/game-designer.md
    hash: 59d723cc8638db1b
    modified: false
  - path: .bmad-2d-unity-game-dev/agents/bmad-orchestrator.md
    hash: 2f7be2f8ed11a4e2
    modified: false
  - path: .bmad-2d-unity-game-dev/agents/analyst.md
    hash: 93604466cf8f18a7
    modified: false
